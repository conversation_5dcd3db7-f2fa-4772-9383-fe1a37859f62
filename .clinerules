# Cline's Project Intelligence: Crossword Generator

## Project Overview & Goals
- (To be filled as understanding deepens)
- Initial Goal: Analyze the existing "crossword-generator" project.

## Key Technologies & Versions
- Frontend: React, TypeScript, Vite, Tailwind CSS
- Backend: PHP 8.3 (custom framework/scripts)
- Database: MySQL/MariaDB (schema defined in `api/schema.sql`, connection via PDO in `api/config/database.php`)

## Coding Conventions & Style Preferences
- (To be observed and documented, e.g., from ESLint config, existing code patterns)
- TypeScript is used on the frontend. Supabase is not used.
- PHP for the main API.
- UUIDs for primary keys in the database.
- JSON for storing complex data structures (grid, words, clues) in the database.

## Important Implementation Paths & Logic
- (To be documented as critical code sections are analyzed)
- Crossword generation logic: Confirmed to be primarily client-side, handled by algorithms in `src/utils/crosswordLogic.ts` (e.g., `findPossiblePlacements`, `optimizeWordPlacement`). The backend is mainly for storing/retrieving the puzzle state.
- Puzzle solving state and creation state management: `CrosswordContext.tsx` is confirmed as central for the frontend. Key actions include `ADD_WORD`, `AUTO_PLACE_WORD` (triggering `crosswordLogic.ts`), `LOAD_PUZZLE` (for initializing the context for play mode), and `RESET_USER_ANSWERS`.
- API interactions:
    - Frontend service `src/services/api.ts` uses `fetch` and targets `http://localhost:1111` as `API_BASE_URL`.
    - Backend routing via `api/index.php` (front controller, configured with base `/api`) dispatches to PHP controllers in `api/controllers/`. Controllers use models in `api/models/` for database operations.
- Database IDs: UUIDs are generated in PHP models (e.g., `CrosswordModel.php` has a private `generateUuid()`; `CategoryModel.php` uses the global `generateUuid()` from `api/utils/helpers.php`).
- Data Storage: Complex puzzle data (grid_data, words, clues, word_positions) stored as JSON in the database, decoded/encoded in PHP models.
- Backend Helper Functions: `api/utils/helpers.php` provides common functions like `sanitizeInput()` (recursive sanitization) and `validateRequiredFields()` (checks for presence/non-emptiness of fields) used by controllers. It also contains the global `generateUuid()` and `jsonResponse()` (standardized JSON output for `api/index.php`).
- **Crossword Editor Features & Logic Invocation:**
    - `WordInputForm.tsx` is key for adding words:
        - Supports single (manual/auto-place), batch, AI (OpenRouter.ai), and Excel (via `xlsx`) import methods.
        - Dispatches `ADD_WORD` or `AUTO_PLACE_WORD` actions, which trigger `crosswordLogic.ts` functions via context reducers.
        - Contains an internal `analyzeWordCompatibility` function for AI-generated words (distinct from `src/utils/wordAnalysis.ts`).
    - `Controls.tsx` provides editor actions:
        - Client-side "Save to JSON" of the entire crossword state.
        - Directly calls `optimizeWordPlacement` from `src/utils/crosswordLogic.ts` for layout optimization.
        - Dispatches actions like `OPTIMIZE_GRID`, `SET_GRID_SIZE`, `SET_MODE`, `RESET`.
- **User Authentication Backend (Initial Setup):**
    - `api/schema.sql`: `user_profiles` table extended with `email` (UNIQUE), `password_hash`, and `role` (ENUM 'user', 'admin' DEFAULT 'user') fields.
    - `api/models/UserModel.php`: Created to manage user data, including `create()` (with `password_hash()` and default 'user' role) and find methods (which now include `role`).
    - `api/controllers/UserController.php`: Implemented `register()`, `login()` (with password verification, session creation including `user_role`), `logout()` (session destruction), and `getCurrentUser()` (session-based retrieval including `role`) methods.
    - `api/index.php`: PHP sessions are initialized (`session_start()`). Routing for `/api/users/(register|login|logout|me)` added, directing to `UserController`.
- **User Authentication Frontend (Initial Setup):**
    - `src/context/AuthContext.tsx`: Created to manage frontend authentication state (user object now includes `role`, isAuthenticated, isLoading, error). Uses actual API calls (via `services/api.ts`) for `login`, `register`, `logout`, and `fetchCurrentUser`.
    - `src/services/api.ts`: Extended with `registerUser`, `loginUser`, `logoutUser`, and `fetchCurrentUser` functions. `User` type updated to include `role`.
    - `src/pages/RegisterPage.tsx` & `src/pages/LoginPage.tsx`: Basic UI forms created and now use `AuthContext` for submissions.
    - `src/components/Header.tsx`: Updated to use `AuthContext` for dynamic auth links.
    - `src/App.tsx`: Wrapped with `AuthProvider`, Login/Register routes added. `ProtectedRoute` component used to guard `/create` (any authenticated user) and admin routes (restricted to 'admin' role).
    - `src/components/ProtectedRoute.tsx`: Created to redirect unauthenticated users to login page; now also handles role-based authorization via an `allowedRoles` prop.

## User Preferences & Workflow
- User initiated an "analisa projek ini" (analyze this project) task.
- Cline's Memory Bank is a custom requirement and must be maintained.

## Known Challenges & Solutions
- Initial challenge: Understanding a new, undocumented codebase. Solution: Iterative analysis, starting with file structure, then key files, and documenting in Memory Bank.
- **Bug (Fixed):** Word numbers were not displayed in the grid cells on `PlayPage.tsx` because the component's custom grid rendering logic omitted this feature. Added logic to fetch and render numbers.

## Tool Usage Patterns
- `list_files` to explore directory structures.
- `write_to_file` for creating initial Memory Bank documents and `.clinerules`.
- `read_file` will be used extensively for code analysis.

## Evolution of Project Decisions
- (To be documented if architectural or significant technical decisions change over time)

## Critical Files to Monitor/Understand
- `memory-bank/` directory (all files)
- `package.json` (frontend dependencies, scripts)
- `api/schema.sql` (database structure)
- `api/config/config.php`, `api/config/database.php` (backend config)
- `src/App.tsx` (main frontend entry/router, auth setup)
- `src/context/CrosswordContext.tsx` (core crossword state)
- `src/context/AuthContext.tsx` (core authentication state - New)
- `src/components/ProtectedRoute.tsx` (route guarding - New)
- `src/services/api.ts` (frontend API interaction layer)
- `api/index.php` (backend entry point and router)
- Key controllers in `api/controllers/` (e.g., `CrosswordController.php`, `CategoryController.php`, `UserController.php`)
- Key models in `api/models/` (e.g., `CrosswordModel.php`, `CategoryModel.php`, `UserModel.php`)
- `api/utils/helpers.php` (shared utility functions for backend)

## Notes on Communication
- Provide clear summaries after analysis steps.
- Refer to Memory Bank documents when relevant.
