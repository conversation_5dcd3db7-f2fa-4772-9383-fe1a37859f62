# Crossword Generator API Documentation

## Overview

This document provides comprehensive documentation for the Crossword Generator API. The API allows clients to interact with the crossword generation, management, and user systems.

**Base URL:** `http://API_URL/api`

## Authentication

Most endpoints require authentication. The API uses session-based authentication with CSRF protection.

### Authentication Flow

1. Client calls `/api/users/login` or `/api/users/register` with credentials
2. Server returns user data and a CSRF token
3. Client stores the CSRF token and includes it in subsequent requests
4. Session cookie is automatically included in requests when using `credentials: 'include'`

### CSRF Protection

All state-changing requests (POST, PUT, DELETE) require a CSRF token to be included in either:
- The `X-CSRF-Token` header
- The request body as `csrf_token`

## Security Measures

The API implements several security measures:

1. **CSRF Protection**: Prevents cross-site request forgery attacks
2. **Rate Limiting**: Limits the number of requests from a single IP address
3. **Bot Protection**: Detects and blocks scraper bots
4. **Input Validation**: Validates and sanitizes all input data
5. **Prepared Statements**: Prevents SQL injection attacks
6. **Secure Sessions**: Uses secure, HttpOnly cookies
7. **HTTPS Enforcement**: Redirects all traffic to HTTPS in production

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Window**: 60 seconds
- **Max Requests**: 60 requests per window
- **Response**: 429 Too Many Requests when limit is exceeded

## Endpoints

### Crosswords

#### Get All Crosswords

```
GET /api/crosswords
```

Query parameters:
- `category_id` - Filter by category
- `difficulty` - Filter by difficulty (mudah, sedang, sulit)
- `is_public` - Filter by public status (1 or 0)
- `user_id` - Filter by creator
- `limit` - Number of results per page (default: 12)
- `page` - Page number (default: 1)

Response:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "title": "Crossword Title",
      "description": "Description",
      "difficulty": "sedang",
      "category_id": "uuid",
      "category_name": "Category Name",
      "is_public": 1,
      "created_by": "uuid",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00",
      "play_count": 10,
      "rating": 4.5
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 12,
    "total": 100,
    "totalPages": 9
  }
}
```

#### Get Featured Crosswords

```
GET /api/featured
```

Query parameters:
- `limit` - Number of results (default: 4)

Response:
```json
{
    "status": "success",
    "data": [
        {
            "title": "Pengetahuan Umum 1",
            "id": "25ed640a-d93f-4921-8b1d-fc18febf97e9",
            "slug": "pengetahuan-umum-1",
            "difficulty": "mudah",
            "plays": 191,
            "creator": "<EMAIL>",
            "category_slug": "pengetahuan-umum",
            "category_name": "Pengetahuan Umum"
        },
        ...
    ]
}
```

#### Get a Specific Crossword

```
GET /api/crosswords/{id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "title": "Crossword Title",
    "description": "Description",
    "difficulty": "sedang",
    "category_id": "uuid",
    "category_name": "Category Name",
    "is_public": 1,
    "created_by": "uuid",
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00",
    "play_count": 10,
    "rating": 4.5,
    "grid": [...],
    "words": [...],
    "clues": [...]
  }
}
```

#### Create a Crossword

```
POST /api/crosswords
```

Request body:
```json
{
  "title": "Crossword Title",
  "description": "Description",
  "difficulty": "sedang",
  "category_id": "uuid",
  "is_public": 1,
  "grid": [...],
  "words": [...],
  "clues": [...]
}
```

Response:
```json
{
  "status": "success",
  "message": "Crossword created successfully",
  "data": {
    "id": "uuid"
  }
}
```

#### Update a Crossword

```
PUT /api/crosswords/{id}
```

Request body: Same as create

Response:
```json
{
  "status": "success",
  "message": "Crossword updated successfully"
}
```

#### Delete a Crossword

```
DELETE /api/crosswords/{id}
```

Response:
```json
{
  "status": "success",
  "message": "Crossword deleted successfully"
}
```

#### Record a Play

```
POST /api/crosswords/{id}/play
```

Response:
```json
{
  "status": "success",
  "message": "Play recorded successfully"
}
```

#### Rate a Crossword

```
POST /api/crosswords/{id}/rate
```

Request body:
```json
{
  "rating": 4.5
}
```

Response:
```json
{
  "status": "success",
  "message": "Rating saved successfully"
}
```

### Categories

#### Get All Categories

```
GET /api/categories
```

Response:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "name": "Category Name",
      "slug": "category-name",
      "description": "Description",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00"
    }
  ]
}
```

#### Get a Specific Category

```
GET /api/categories/{id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "name": "Category Name",
    "slug": "category-name",
    "description": "Description",
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00"
  }
}
```

#### Create a Category

```
POST /api/categories
```

Request body:
```json
{
  "name": "Category Name",
  "description": "Description"
}
```

Response:
```json
{
  "status": "success",
  "message": "Category created successfully",
  "data": {
    "id": "uuid"
  }
}
```

#### Update a Category

```
PUT /api/categories/{id}
```

Request body: Same as create

Response:
```json
{
  "status": "success",
  "message": "Category updated successfully"
}
```

#### Delete a Category

```
DELETE /api/categories/{id}
```

Response:
```json
{
  "status": "success",
  "message": "Category deleted successfully"
}
```

### Users

#### Register

```
POST /api/users/register
```

Request body:
```json
{
  "username": "username",
  "email": "<EMAIL>",
  "password": "Password123",
  "display_name": "Display Name"
}
```

Response:
```json
{
  "status": "success",
  "message": "User registered successfully",
  "data": {
    "userId": "uuid",
    "csrf_token": "token"
  }
}
```

#### Login

```
POST /api/users/login
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "displayName": "Display Name",
      "avatar_url": "url",
      "bio": "Bio",
      "role": "user",
      "auth_provider": "email"
    },
    "csrf_token": "token"
  }
}
```

#### Google Login

```
POST /api/users/google-login
```

Request body:
```json
{
  "id_token": "google_id_token"
}
```

Response: Same as regular login

#### Logout

```
POST /api/users/logout
```

Response:
```json
{
  "status": "success",
  "message": "Logged out successfully"
}
```

#### Get Current User

```
GET /api/users/me
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "displayName": "Display Name",
      "avatar_url": "url",
      "bio": "Bio",
      "role": "user",
      "auth_provider": "email"
    },
    "csrf_token": "token"
  }
}
```

#### Update Profile

```
PUT /api/users/profile
```

Request body:
```json
{
  "display_name": "New Display Name",
  "bio": "New Bio",
  "avatar_url": "New URL"
}
```

Response:
```json
{
  "status": "success",
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "displayName": "New Display Name",
      "avatar_url": "New URL",
      "bio": "New Bio",
      "role": "user",
      "auth_provider": "email"
    }
  }
}
```

### User Progress

#### Get All Progress

```
GET /api/progress
```

Response:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "crossword_id": "uuid",
      "progress_data": {...},
      "is_completed": 0,
      "last_played_at": "2023-01-01 00:00:00",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00"
    }
  ]
}
```

#### Get Progress for a Specific Crossword

```
GET /api/progress/{crossword_id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "crossword_id": "uuid",
    "progress_data": {...},
    "is_completed": 0,
    "last_played_at": "2023-01-01 00:00:00",
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00"
  }
}
```

#### Save Progress

```
POST /api/progress/{crossword_id}
```

Request body:
```json
{
  "progress_data": {...},
  "is_completed": 0
}
```

Response:
```json
{
  "status": "success",
  "message": "Progress saved successfully"
}
```

### Blogs

#### Get All Blogs

```
GET /api/blogs
```

Query parameters:
- `limit` - Number of results per page (default: 10)
- `page` - Page number (default: 1)

Response:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "title": "Blog Title",
      "slug": "blog-title",
      "content": "Content",
      "excerpt": "Excerpt",
      "featured_image": "url",
      "author_id": "uuid",
      "author_name": "Author Name",
      "published_at": "2023-01-01 00:00:00",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## Error Handling

All errors return a JSON response with a status code and message:

```json
{
  "status": "error",
  "message": "Error message"
}
```

Common error codes:
- 400: Bad Request (invalid input)
- 401: Unauthorized (not logged in)
- 403: Forbidden (no permission)
- 404: Not Found
- 405: Method Not Allowed
- 429: Too Many Requests (rate limit exceeded)
- 500: Internal Server Error

## Best Practices

1. Always include the CSRF token in state-changing requests
2. Use `credentials: 'include'` in fetch requests to include cookies
3. Handle 429 responses with exponential backoff
4. Validate user input on the client side before sending to the API
5. Implement proper error handling for all API calls
