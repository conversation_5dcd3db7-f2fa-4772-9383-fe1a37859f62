# Dokumentasi API Teka-Teki Silang

## Gambaran Umum

Dokumentasi ini menyediakan informasi lengkap tentang API Teka-Teki Silang. API ini memungkinkan klien untuk berinteraksi dengan sistem pembuatan teka-teki silang, manajemen, dan sistem pengguna.

**URL Dasar:** `http://localhost:1111/api`

## Struktur Data Teka-Teki Silang

Berikut adalah penjelasan detail tentang struktur data teka-teki silang yang digunakan dalam API:

### 1. Properti Utama Teka-Teki Silang

| Properti | Tipe | Deskripsi |
|----------|------|-----------|
| `id` | string | ID unik teka-teki silang (UUID) |
| `title` | string | Judul teka-teki silang |
| `slug` | string | Slug URL-friendly dari judul |
| `description` | string | Deskripsi lengkap teka-teki silang |
| `grid_size` | integer | Ukuran grid (jumlah baris dan kolom) |
| `difficulty` | string | Tingkat kesulitan ("mudah", "sedang", "sulit") |
| `user_id` | string | ID pembuat teka-teki silang |
| `creator` | string | Nama/username pembuat |
| `is_public` | integer | Status publik (1 = publik, 0 = privat) |
| `plays` | integer | Jumlah kali dimainkan |
| `rating` | float | Rating rata-rata (1-5) |
| `category_id` | string | ID kategori |
| `category_name` | string | Nama kategori |
| `category_slug` | string | Slug kategori |
| `created_at` | string | Tanggal pembuatan (format: YYYY-MM-DD HH:MM:SS) |
| `updated_at` | string | Tanggal pembaruan terakhir |
| `state` | object | Objek yang berisi data grid, kata, petunjuk, dll. |

### 2. Struktur Objek `state`

Objek `state` berisi semua informasi yang diperlukan untuk menampilkan dan memainkan teka-teki silang:

| Properti | Tipe | Deskripsi |
|----------|------|-----------|
| `gridSize` | integer | Ukuran grid (jumlah baris dan kolom) |
| `grid` | array | Array 2D yang merepresentasikan grid teka-teki silang |
| `words` | array | Array yang berisi semua kata dalam teka-teki silang |
| `clues` | object | Objek yang berisi petunjuk untuk kata-kata mendatar dan menurun |
| `wordPositions` | array | Array yang berisi informasi tentang posisi setiap kata dalam grid |
| `wordNumber` | integer | Jumlah kata dalam teka-teki silang + 1 (untuk kata berikutnya) |
| `selectedWordId` | string/null | ID kata yang dipilih (null saat inisialisasi) |
| `mode` | string | Mode teka-teki silang ("play" atau "edit") |

#### 2.1 Struktur `grid`

Array 2D di mana setiap sel berisi objek dengan properti:

```json
{
  "char": "A",  // Karakter dalam sel (spasi " " untuk sel kosong/hitam)
  "wordIds": [1, 2]  // Array ID kata yang melewati sel ini (kosong untuk sel hitam)
}
```

#### 2.2 Struktur `words`

Array string yang berisi semua kata dalam teka-teki silang:

```json
[
  "LANGGAR",
  "PASAR",
  "TUMPENG",
  // ... kata-kata lainnya
]
```

#### 2.3 Struktur `clues`

Objek yang berisi petunjuk untuk kata-kata mendatar (`across`) dan menurun (`down`):

```json
{
  "across": {
    "1": "Mushola kecil di lingkungan kampung Jawa",
    "4": "Minuman tradisional hangat khas Jawa",
    // ... petunjuk mendatar lainnya
  },
  "down": {
    "2": "Tempat jual beli tradisional masyarakat Jawa",
    "3": "Nasi berbentuk kerucut yang disajikan dalam acara syukuran",
    // ... petunjuk menurun lainnya
  }
}
```

#### 2.4 Struktur `wordPositions`

Array objek yang berisi informasi tentang posisi setiap kata dalam grid:

```json
[
  {
    "col": 1,  // Kolom awal (0-based)
    "row": 8,  // Baris awal (0-based)
    "number": 1,  // Nomor kata yang ditampilkan di grid
    "direction": "across"  // Arah kata ("across" untuk mendatar, "down" untuk menurun)
  },
  // ... posisi kata lainnya
]
```

### 3. Hubungan Antar Komponen Data

Untuk memahami bagaimana semua komponen data ini bekerja bersama, berikut contoh untuk kata "PANGKON" (ID 8):

1. Dalam array `words`, "PANGKON" adalah kata dengan indeks 7 (ID 8)
2. Dalam `wordPositions`, kata ini dimulai pada `row: 0, col: 5` dengan `direction: "across"` dan `number: 8`
3. Dalam `clues.across`, petunjuk untuk nomor 8 adalah "Tempat duduk dalam gamelan"
4. Dalam `grid`, sel-sel yang membentuk kata ini memiliki `wordIds` yang mengandung 8

## Pengenalan

API Teka-Teki Silang adalah antarmuka pemrograman aplikasi yang memungkinkan pengembang untuk mengintegrasikan fungsionalitas teka-teki silang ke dalam aplikasi mereka. API ini menyediakan akses ke berbagai fitur, termasuk:

1. **Manajemen Teka-Teki Silang**: Membuat, membaca, memperbarui, dan menghapus teka-teki silang.
2. **Manajemen Kategori**: Mengorganisir teka-teki silang berdasarkan kategori.
3. **Manajemen Pengguna**: Registrasi, login, dan manajemen profil pengguna.
4. **Pelacakan Kemajuan**: Menyimpan dan mengambil kemajuan pengguna dalam menyelesaikan teka-teki silang.
5. **Manajemen Blog**: Membuat dan mengelola konten blog terkait teka-teki silang.
6. **Sitemap**: Menghasilkan sitemap untuk SEO.

API ini menggunakan format respons JSON standar dengan struktur berikut:

```json
{
  "status": "success" | "error",
  "message": "Pesan informatif (opsional)",
  "data": { ... } | [ ... ],
  "pagination": { ... } (opsional untuk endpoint dengan paginasi)
}
```

Kode status HTTP standar digunakan untuk mengindikasikan hasil permintaan:
- 200 OK: Permintaan berhasil
- 201 Created: Sumber daya berhasil dibuat
- 400 Bad Request: Permintaan tidak valid
- 401 Unauthorized: Autentikasi diperlukan
- 403 Forbidden: Tidak memiliki izin
- 404 Not Found: Sumber daya tidak ditemukan
- 500 Internal Server Error: Kesalahan server

## Autentikasi

Sebagian besar endpoint memerlukan autentikasi. API menggunakan autentikasi berbasis sesi.

### Alur Autentikasi

1. Klien memanggil `/api/users/login` atau `/api/users/register` dengan kredensial
2. Server mengembalikan data pengguna
3. Cookie sesi secara otomatis disertakan dalam permintaan saat menggunakan `credentials: 'include'`

## Endpoint API

### 1. Teka-Teki Silang (Crosswords)

#### Mendapatkan Semua Teka-Teki Silang

```
GET /api/crosswords
```

Parameter query:
- `category_id` - Filter berdasarkan kategori
- `difficulty` - Filter berdasarkan tingkat kesulitan (mudah, sedang, sulit)
- `is_public` - Filter berdasarkan status publik (1 atau 0)
- `user_id` - Filter berdasarkan pembuat
- `limit` - Jumlah hasil per halaman (default: 12)
- `page` - Nomor halaman (default: 1)

Respons:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "title": "Judul Teka-Teki",
      "slug": "judul-teka-teki",
      "description": "Deskripsi teka-teki",
      "grid_size": 15,
      "difficulty": "sedang",
      "user_id": "uuid",
      "creator": "username",
      "is_public": 1,
      "plays": 100,
      "rating": 4.5,
      "category_id": "uuid",
      "category_name": "Nama Kategori",
      "category_slug": "nama-kategori",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00",
      "state": {
        "gridSize": 15,
        "grid": [...],
        "words": [...],
        "clues": {
          "across": {...},
          "down": {...}
        },
        "wordPositions": [...],
        "wordNumber": 20,
        "selectedWordId": null,
        "mode": "play"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 12,
    "total": 100,
    "totalPages": 9
  }
}
```

#### Mendapatkan Teka-Teki Silang Berdasarkan ID atau Slug

```
GET /api/crosswords/{id}
GET /api/crosswords/{slug}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "id": "d92b6c26-b418-43d5-8720-64152c906778",
    "title": "Budaya Jawa 3",
    "slug": "budaya-jawa-3",
    "description": "Uji ketajaman pikiran Anda dengan Budaya Jawa #3, teka-teki silang sedang yang menantang dengan tema Budaya Indonesia.",
    "grid_size": 17,
    "difficulty": "sedang",
    "user_id": "5b4dc128-8ba5-4bd3-b990-6de4ddd11522",
    "creator": "<EMAIL>",
    "is_public": 1,
    "plays": 88,
    "rating": null,
    "category_id": "c001a980-6d7e-11ee-8c99-0242ac120002",
    "category_name": "Budaya Indonesia",
    "category_slug": "budaya-indonesia",
    "created_at": "2023-05-14 14:16:08",
    "updated_at": "2023-05-14 14:16:08",
    "state": {
      "gridSize": 17,
      "grid": [
        [
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": "P", "wordIds": [8] },
          { "char": "A", "wordIds": [8] },
          { "char": "N", "wordIds": [8] },
          { "char": "G", "wordIds": [7, 8] },
          { "char": "K", "wordIds": [8] },
          { "char": "O", "wordIds": [8] },
          { "char": "N", "wordIds": [8] },
          { "char": " ", "wordIds": [] },
          { "char": "T", "wordIds": [16] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] }
        ],
        [
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": "E", "wordIds": [7] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": "E", "wordIds": [16] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] },
          { "char": " ", "wordIds": [] }
        ],
        // ... baris-baris lainnya
      ],
      "words": [
        "LANGGAR",
        "PASAR",
        "TUMPENG",
        "WEDANG",
        "JAMU",
        "TEMANTEN",
        "GENDING",
        "PANGKON",
        "REBAB",
        "SEMAR",
        "GARENG",
        "PETRUK",
        "BAGONG",
        "SARUNG",
        "NGABEN",
        "TEMBANG"
      ],
      "clues": {
        "down": {
          "2": "Tempat jual beli tradisional masyarakat Jawa",
          "3": "Nasi berbentuk kerucut yang disajikan dalam acara syukuran",
          "7": "Komposisi musik Jawa",
          "9": "Alat musik gesek dalam gamelan",
          "12": "Punakawan bertubuh tinggi dengan hidung panjang",
          "13": "Punakawan bertubuh besar",
          "16": "Nyanyian dalam budaya Jawa"
        },
        "across": {
          "1": "Mushola kecil di lingkungan kampung Jawa",
          "4": "Minuman tradisional hangat khas Jawa",
          "5": "Minuman herbal tradisional Jawa",
          "6": "Pengantin dalam bahasa Jawa",
          "8": "Tempat duduk dalam gamelan",
          "10": "Tokoh punakawan bijak dalam pewayangan",
          "11": "Salah satu punakawan yang bertubuh kecil",
          "14": "Kain panjang yang biasa dipakai masyarakat Jawa",
          "15": "Upacara kremasi dalam budaya Bali (bukan Jawa, tapi sering dibandingkan)"
        }
      },
      "wordPositions": [
        {
          "col": 1,
          "row": 8,
          "number": 1,
          "direction": "across"
        },
        {
          "col": 2,
          "row": 7,
          "number": 2,
          "direction": "down"
        },
        {
          "col": 4,
          "row": 2,
          "number": 3,
          "direction": "down"
        },
        {
          "col": 3,
          "row": 6,
          "number": 4,
          "direction": "across"
        },
        {
          "col": 1,
          "row": 10,
          "number": 5,
          "direction": "across"
        },
        {
          "col": 4,
          "row": 2,
          "number": 6,
          "direction": "across"
        },
        {
          "col": 8,
          "row": 0,
          "number": 7,
          "direction": "down"
        },
        {
          "col": 5,
          "row": 0,
          "number": 8,
          "direction": "across"
        },
        {
          "col": 7,
          "row": 8,
          "number": 9,
          "direction": "down"
        },
        {
          "col": 2,
          "row": 4,
          "number": 10,
          "direction": "across"
        },
        {
          "col": 6,
          "row": 11,
          "number": 11,
          "direction": "across"
        },
        {
          "col": 9,
          "row": 10,
          "number": 12,
          "direction": "down"
        },
        {
          "col": 11,
          "row": 9,
          "number": 13,
          "direction": "down"
        },
        {
          "col": 6,
          "row": 14,
          "number": 14,
          "direction": "across"
        },
        {
          "col": 8,
          "row": 5,
          "number": 15,
          "direction": "across"
        },
        {
          "col": 13,
          "row": 0,
          "number": 16,
          "direction": "down"
        }
      ],
      "wordNumber": 17,
      "selectedWordId": null,
      "mode": "play"
    }
  }
}
```

#### Membuat Teka-Teki Silang Baru

```
POST /api/crosswords
```

Body permintaan:
```json
{
  "title": "Judul Teka-Teki",
  "description": "Deskripsi teka-teki",
  "state": {
    "gridSize": 15,
    "grid": [...],
    "words": [...],
    "clues": {
      "across": {...},
      "down": {...}
    },
    "wordPositions": [...]
  },
  "difficulty": "sedang",
  "category_id": "uuid",
  "is_public": 1
}
```

Respons:
```json
{
  "status": "success",
  "message": "Teka-teki silang berhasil dibuat",
  "id": "uuid"
}
```

#### Memperbarui Teka-Teki Silang

```
PUT /api/crosswords/{id}
```

Body permintaan:
```json
{
  "title": "Judul Teka-Teki Baru",
  "description": "Deskripsi baru",
  "state": {
    "gridSize": 15,
    "grid": [...],
    "words": [...],
    "clues": {...},
    "wordPositions": [...]
  },
  "difficulty": "sulit",
  "category_id": "uuid",
  "is_public": 0
}
```

Respons:
```json
{
  "status": "success",
  "message": "Teka-teki silang berhasil diperbarui"
}
```

#### Menghapus Teka-Teki Silang

```
DELETE /api/crosswords/{id}
```

Respons:
```json
{
  "status": "success",
  "message": "Teka-teki silang berhasil dihapus"
}
```

#### Mencatat Permainan

```
POST /api/crosswords/{id}/play
```

Respons:
```json
{
  "status": "success",
  "message": "Permainan berhasil dicatat"
}
```

#### Memberikan Rating

```
POST /api/crosswords/{id}/rate
```

Body permintaan:
```json
{
  "rating": 4.5
}
```

Respons:
```json
{
  "status": "success",
  "message": "Rating berhasil diperbarui"
}
```

#### Mendapatkan Teka-Teki Silang Unggulan

```
GET /api/featured
```

Respons:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "title": "Judul Teka-Teki",
      "slug": "judul-teka-teki",
      "description": "Deskripsi teka-teki",
      "difficulty": "sedang",
      "plays": 100,
      "rating": 4.5,
      "category_id": "uuid",
      "category_name": "Nama Kategori",
      "category_slug": "nama-kategori"
    }
  ]
}
```

### 2. Kategori (Categories)

#### Mendapatkan Semua Kategori

```
GET /api/categories
```

Respons:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "name": "Nama Kategori",
      "slug": "nama-kategori",
      "description": "Deskripsi kategori",
      "image_url": "url-gambar",
      "created_at": "2023-01-01 00:00:00",
      "crossword_count": 10
    }
  ]
}
```

#### Mendapatkan Kategori Berdasarkan ID atau Slug

```
GET /api/categories/{id}
GET /api/categories/{slug}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "name": "Nama Kategori",
    "slug": "nama-kategori",
    "description": "Deskripsi kategori",
    "image_url": "url-gambar",
    "created_at": "2023-01-01 00:00:00",
    "crossword_count": 10
  }
}
```

#### Membuat Kategori Baru

```
POST /api/categories
```

Body permintaan:
```json
{
  "name": "Nama Kategori",
  "slug": "nama-kategori",
  "description": "Deskripsi kategori",
  "image_url": "url-gambar"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Kategori berhasil dibuat",
  "id": "uuid"
}
```

#### Memperbarui Kategori

```
PUT /api/categories/{id}
```

Body permintaan:
```json
{
  "name": "Nama Kategori Baru",
  "slug": "nama-kategori-baru",
  "description": "Deskripsi baru",
  "image_url": "url-gambar-baru"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Kategori berhasil diperbarui"
}
```

#### Menghapus Kategori

```
DELETE /api/categories/{id}
```

Respons:
```json
{
  "status": "success",
  "message": "Kategori berhasil dihapus"
}
```

### 3. Pengguna (Users)

#### Registrasi

```
POST /api/users/register
```

Body permintaan:
```json
{
  "username": "username",
  "email": "<EMAIL>",
  "password": "Password123",
  "display_name": "Nama Tampilan"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Pengguna berhasil terdaftar",
  "data": {
    "userId": "uuid"
  }
}
```

#### Login

```
POST /api/users/login
```

Body permintaan:
```json
{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "display_name": "Nama Tampilan",
      "avatar_url": "url",
      "bio": "Bio",
      "role": "user",
      "auth_provider": "email"
    }
  }
}
```

#### Login dengan Google

```
POST /api/users/google-login
```

Body permintaan:
```json
{
  "token": "google-token",
  "email": "<EMAIL>",
  "display_name": "Nama Tampilan",
  "avatar_url": "url"
}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "display_name": "Nama Tampilan",
      "avatar_url": "url",
      "bio": null,
      "role": "user",
      "auth_provider": "google"
    }
  }
}
```

#### Logout

```
POST /api/users/logout
```

Respons:
```json
{
  "status": "success",
  "message": "Berhasil logout"
}
```

#### Mendapatkan Pengguna Saat Ini

```
GET /api/users/me
```

Respons:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "display_name": "Nama Tampilan",
      "avatar_url": "url",
      "bio": "Bio",
      "role": "user",
      "auth_provider": "email"
    }
  }
}
```

#### Memperbarui Profil

```
PUT /api/users/profile
```

Body permintaan:
```json
{
  "display_name": "Nama Tampilan Baru",
  "avatar_url": "url-baru",
  "bio": "Bio baru"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Profil berhasil diperbarui",
  "data": {
    "user": {
      "id": "uuid",
      "username": "username",
      "email": "<EMAIL>",
      "display_name": "Nama Tampilan Baru",
      "avatar_url": "url-baru",
      "bio": "Bio baru",
      "role": "user",
      "auth_provider": "email"
    }
  }
}
```

### 4. Kemajuan Pengguna (User Progress)

#### Mendapatkan Semua Kemajuan

```http
GET /api/progress
```

Respons:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "crossword_id": "uuid",
      "progress_data": {
        "userAnswers": [[]],
        "revealedCells": [[0, 0]],
        "progress": 75
      },
      "is_completed": 1,
      "time_spent": 300,
      "crossword_title": "Judul Teka-Teki",
      "difficulty": "sedang",
      "slug": "judul-teka-teki",
      "category_id": "uuid",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00"
    }
  ]
}
```

#### Mendapatkan Kemajuan untuk Teka-Teki Tertentu

```http
GET /api/progress/{crossword_id}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "crossword_id": "uuid",
    "progress_data": {
      "userAnswers": [[]],
      "revealedCells": [[0, 0]],
      "progress": 75
    },
    "is_completed": 1,
    "time_spent": 300,
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00"
  }
}
```

#### Menyimpan Kemajuan

```http
POST /api/progress/{crossword_id}
```

Body permintaan:
```json
{
  "progress_data": {
    "userAnswers": [[]],
    "revealedCells": [[0, 0]],
    "progress": 75
  },
  "is_completed": 1,
  "time_spent": 300
}
```

Respons:
```json
{
  "status": "success",
  "message": "Kemajuan berhasil disimpan",
  "data": {
    "id": "uuid"
  }
}
```

#### Menghapus Kemajuan

```http
DELETE /api/progress/{crossword_id}
```

Respons:
```json
{
  "status": "success",
  "message": "Kemajuan berhasil dihapus"
}
```

### 5. Blog

#### Mendapatkan Semua Blog

```http
GET /api/blogs
```

Parameter query:
- `status` - Filter berdasarkan status (draft, published)
- `author_id` - Filter berdasarkan penulis
- `limit` - Jumlah hasil per halaman (default: 10)
- `page` - Nomor halaman (default: 1)

Respons:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "title": "Judul Blog",
      "slug": "judul-blog",
      "content": "Konten blog",
      "excerpt": "Ringkasan blog",
      "featured_image": "url-gambar",
      "author_id": "uuid",
      "author_name": "Nama Penulis",
      "author_username": "username",
      "status": "published",
      "created_at": "2023-01-01 00:00:00",
      "updated_at": "2023-01-01 00:00:00"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

#### Mendapatkan Blog Berdasarkan ID atau Slug

```http
GET /api/blogs/{id}
GET /api/blogs?slug={slug}
```

Respons:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "title": "Judul Blog",
    "slug": "judul-blog",
    "content": "Konten blog",
    "excerpt": "Ringkasan blog",
    "featured_image": "url-gambar",
    "author_id": "uuid",
    "author_name": "Nama Penulis",
    "author_username": "username",
    "status": "published",
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00"
  }
}
```

#### Membuat Blog Baru (Admin)

```http
POST /api/blogs
```

Body permintaan:
```json
{
  "title": "Judul Blog",
  "slug": "judul-blog",
  "content": "Konten blog",
  "excerpt": "Ringkasan blog",
  "featured_image": "url-gambar",
  "status": "draft"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Blog berhasil dibuat",
  "data": {
    "id": "uuid",
    "title": "Judul Blog",
    "slug": "judul-blog",
    "content": "Konten blog",
    "excerpt": "Ringkasan blog",
    "featured_image": "url-gambar",
    "author_id": "uuid",
    "author_name": "Nama Penulis",
    "author_username": "username",
    "status": "draft",
    "created_at": "2023-01-01 00:00:00",
    "updated_at": "2023-01-01 00:00:00"
  }
}
```

#### Memperbarui Blog (Admin)

```http
PUT /api/blogs/{id}
```

Body permintaan:
```json
{
  "title": "Judul Blog Baru",
  "slug": "judul-blog-baru",
  "content": "Konten blog baru",
  "excerpt": "Ringkasan blog baru",
  "featured_image": "url-gambar-baru",
  "status": "published"
}
```

Respons:
```json
{
  "status": "success",
  "message": "Blog berhasil diperbarui"
}
```

#### Menghapus Blog (Admin)

```http
DELETE /api/blogs/{id}
```

Respons:
```json
{
  "status": "success",
  "message": "Blog berhasil dihapus"
}
```

### 6. Sitemap

```http
GET /api/sitemap
```

Respons: XML sitemap yang berisi URL untuk semua teka-teki silang, kategori, dan blog yang dipublikasikan.