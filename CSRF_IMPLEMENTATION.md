# CSRF Protection Implementation

This document explains how Cross-Site Request Forgery (CSRF) protection is implemented in the Crossword Generator application.

## Overview

CSRF is an attack that forces authenticated users to execute unwanted actions on a web application in which they're currently authenticated. The application has been secured against CSRF attacks using a token-based approach.

## Implementation Details

### Backend (PHP)

1. **Configuration**
   - CSRF protection is enabled in `api/config/config.php` with the `CSRF_PROTECTION` constant
   - Session management is configured with secure parameters

2. **Token Generation and Validation**
   - `generateCsrfToken()` in `api/utils/helpers.php` creates a random token and stores it in the session
   - `verifyCsrfToken()` validates tokens against the one stored in the session

3. **Token Distribution**
   - Tokens are included in API responses for authentication endpoints:
     - Login
     - Registration
     - Google Login
     - Current User

4. **Request Validation**
   - `api/index.php` checks for CSRF tokens in all state-changing requests (POST, PUT, DELETE)
   - Tokens can be provided in the request header (`X-CSRF-Token`) or in the request body (`csrf_token`)
   - Google login requests are exempted from CSRF validation as they come from an external source

### Frontend (TypeScript/React)

1. **Token Storage**
   - `src/services/csrf.ts` manages CSRF tokens in memory
   - Functions include `setCsrfToken()`, `getCsrfToken()`, and `clearCsrfToken()`

2. **Token Management**
   - Tokens are extracted from API responses and stored in memory
   - Tokens are cleared on logout or authentication failures

3. **Request Enhancement**
   - `addCsrfHeader()` adds the CSRF token to request headers
   - All state-changing API calls include the CSRF token
   - All requests include credentials to ensure cookies are sent

4. **Authentication Integration**
   - `AuthContext.tsx` ensures CSRF tokens are cleared on logout
   - API service functions handle token extraction and inclusion

## Security Considerations

1. **Token Lifecycle**
   - Tokens are generated on login/registration
   - Tokens are included in subsequent requests
   - Tokens are cleared on logout

2. **Cookie Security**
   - Session cookies are configured with secure parameters
   - HttpOnly flag is enabled to prevent JavaScript access
   - SameSite attribute is set to 'Lax' to prevent cross-site requests

3. **Error Handling**
   - Failed CSRF validation returns a 403 Forbidden response
   - Errors are logged for security monitoring

## Testing

To test CSRF protection:

1. Log in to the application
2. Attempt to make a state-changing request without a CSRF token
3. Verify that the request is rejected with a 403 Forbidden response

## Implementation in API Calls

All state-changing API calls have been updated to include CSRF tokens:

```typescript
// Example of a state-changing API call with CSRF protection
export const createCrossword = async (crossword: CrosswordData): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/crosswords`, {
      method: 'POST',
      headers: addCsrfHeader({
        'Content-Type': 'application/json',
      }),
      body: JSON.stringify(crossword),
      credentials: 'include', // Include cookies in the request
    });
    // ...
  }
};
```

## Conclusion

This CSRF protection implementation provides a robust defense against cross-site request forgery attacks by ensuring that all state-changing requests include a valid CSRF token that is known only to the legitimate user's browser.
