# Deployment Guide for Crossword Generator

This guide provides instructions for deploying the Crossword Generator application to a live hosting environment, specifically focusing on cPanel hosting.

## Prerequisites

- cPanel hosting account with:
  - PHP 8.0 or higher
  - MySQL/MariaDB database
  - Node.js (for building the application locally)
- FTP client or cPanel File Manager access
- Database management tool (phpMyAdmin or similar)

## Preparation

1. Update the configuration files:
   - `.env.production` - Frontend environment variables
   - `api/.env.production` - Backend environment variables

2. Build the application:
   - Run `deploy.bat` (Windows) or `deploy.sh` (Linux/Mac)
   - This will create two deployment packages:
     - `deploy/frontend.zip` - Frontend files
     - `deploy/api.zip` - Backend API files

## Database Setup

1. Create a new database in cPanel:
   - Log in to cPanel
   - Navigate to MySQL Databases
   - Create a new database (e.g., `yourdomain_crosswords`)
   - Create a new user with a strong password
   - Add the user to the database with all privileges

2. Import the database schema:
   - Download the `api/schema.sql` file
   - Log in to phpMyAdmin
   - Select your database
   - Go to the Import tab
   - Upload and import the schema file

## Frontend Deployment

1. Upload the frontend files:
   - Log in to cPanel File Manager
   - Navigate to the public_html directory (or your desired subdirectory)
   - Upload `deploy/frontend.zip`
   - Extract the zip file
   - Make sure all files are in the correct directory

2. Configure the web server:
   - The `.htaccess` file should be included in the frontend package
   - Make sure mod_rewrite is enabled in Apache

## Backend Deployment

1. Upload the API files:
   - Log in to cPanel File Manager
   - Create an `api` directory in your public_html folder (or your desired location)
   - Upload `deploy/api.zip`
   - Extract the zip file
   - Make sure all files are in the correct directory

2. Configure the API:
   - Rename `.env.production` to `.env`
   - Update the database credentials
   - Update the Google OAuth credentials
   - Update the APP_URL to match your domain

3. Set file permissions:
   - Set the `api/logs` directory to 755 (or 775)
   - Set the `api/cache` directory to 755 (or 775)

## WordPress Blog Setup

1. Create a `/blog` directory:
   - Create a directory named `blog` in your public_html folder

2. Install WordPress:
   - Download WordPress from [wordpress.org](https://wordpress.org/download/)
   - Upload and extract WordPress to the `/blog` directory
   - Create a database for WordPress (separate from the main application database)
   - Run the WordPress installation

3. Configure WordPress:
   - Edit `wp-config.php` and add:

     ```php
     define('WP_HOME', 'https://yourdomain.com/blog');
     define('WP_SITEURL', 'https://yourdomain.com/blog');
     ```

   - Set up permalinks (Settings > Permalinks > Post name)
   - Install and configure necessary plugins (Yoast SEO, etc.)
   - Set up your theme and customize as needed

4. Update .htaccess:
   - Make sure the main .htaccess file in public_html contains:

     ```apache
     # Exclude the blog directory from React routing
     RewriteRule ^blog(/.*)?$ - [L]
     ```

   - This ensures requests to /blog are handled by WordPress

## Final Configuration

1. Update the allowed origins in `api/index.php`:
   - Find the `$allowedOrigins` array
   - Replace `'https://yourdomain.com'` with your actual domain

2. Test the application:
   - Visit your domain in a web browser
   - Test the API endpoints
   - Test user authentication
   - Test crossword creation and solving
   - Test WordPress blog access and functionality

## SEO Integration

1. Sitemap Integration:
   - The main application sitemap is at `/sitemap.xml`
   - WordPress sitemap (with Yoast SEO) will be at `/blog/sitemap_index.xml`
   - Register both sitemaps in Google Search Console

2. Robots.txt Configuration:
   - Update the main robots.txt to include both sitemaps:

     ```
     User-agent: *
     Allow: /
     Disallow: /admin/
     Disallow: /profile/

     Sitemap: https://yourdomain.com/sitemap.xml
     Sitemap: https://yourdomain.com/blog/sitemap_index.xml
     ```

3. Metadata Consistency:
   - Ensure consistent branding across both the main application and WordPress blog
   - Use similar meta descriptions and title formats
   - Cross-link between the main application and blog where appropriate

## Troubleshooting

### CORS Issues

If you encounter CORS issues:
1. Check the `$allowedOrigins` array in `api/index.php`
2. Make sure your domain is listed
3. Check the browser console for specific CORS errors

### Database Connection Issues

If the API cannot connect to the database:
1. Check the database credentials in `.env`
2. Make sure the database user has the correct privileges
3. Check the database server hostname (usually `localhost`)

### 404 Errors on Page Refresh

If you get 404 errors when refreshing pages:
1. Make sure the `.htaccess` file is properly uploaded
2. Make sure mod_rewrite is enabled in Apache
3. Contact your hosting provider for assistance

## Maintenance

### Updating the Application

To update the application:
1. Make changes to the codebase locally
2. Run the deployment script
3. Upload and extract the new packages
4. Update the database schema if necessary

### Backing Up

Regularly back up:
1. The database
2. The `.env` files
3. Any user-uploaded content

## Security Considerations

1. Keep PHP and MySQL updated
2. Use HTTPS for all connections
3. Protect sensitive files with proper permissions
4. Regularly review server logs for suspicious activity
5. Consider implementing additional security measures like:
   - IP-based access restrictions for admin areas
   - Two-factor authentication
   - Regular security audits
