# Environment Switching Instructions

This document provides clear instructions for switching between development and production environments in the Crossword Generator application.

## Development Environment

### Frontend (Vite/React)

1. Create a `.env` file in the project root with the following content:
```
# App Configuration
APP_NAME=Crossword Generator
APP_ENV=development
APP_DEBUG=true

# API URL Configuration
VITE_API_URL=http://localhost:1111
```

2. Start the development server:
```bash
npm run dev
```

3. The frontend will be available at: http://localhost:5173

### Backend (PHP)

1. Create a `.env` file in the `api` directory with the following content:
```
# App Configuration
APP_NAME=Crossword Generator
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:1111

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=crosswords_db
DB_USERNAME=root
DB_PASSWORD=

# Other Configurations
API_BASE_URL=${APP_URL}/api
FRONTEND_URL=http://localhost:5173
```

2. Start your PHP server (e.g., using XAMPP, WAMP, or built-in PHP server):
```bash
# If using PHP built-in server
cd api
php -S localhost:1111
```

## Production Environment

### Frontend (Vite/React)

1. Create a `.env.production` file in the project root with the following content:
```
# App Configuration
APP_NAME=Crossword Generator
APP_ENV=production
APP_DEBUG=false

# API URL Configuration
VITE_API_URL=https://tts-api.widiyanata.com
```

2. Build the frontend for production:
```bash
npm run build:prod
```

3. The built files will be in the `dist` directory, ready for deployment.

### Backend (PHP)

1. Create a `.env` file in the `api` directory on your production server with the following content:
```
# App Configuration
APP_NAME=Crossword Generator
APP_ENV=production
APP_DEBUG=false
APP_URL=https://tts-api.widiyanata.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_production_db
DB_USERNAME=your_production_user
DB_PASSWORD=your_production_password

# Other Configurations
API_BASE_URL=${APP_URL}/api
FRONTEND_URL=https://ttspintar.vercel.app
```

2. Deploy the API files to your production server.

## Allowed Origins Configuration

The API allows specific origins to make cross-origin requests. These are configured in `api/index.php`:

```php
$allowedOrigins = [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:4173',  // Vite preview server
    'http://localhost:3000',  // Alternative dev port
    'http://localhost:1111',  // Same as API server
    'https://tts-api.widiyanata.com', // Production API domain
    'https://ttspintar.vercel.app', // Production frontend domain
    'https://ttspintar.widiyanata.com' // Alternative production domain
];
```

If you're using a different domain, add it to this list.

## Quick Reference

### Development URLs
- Frontend: http://localhost:5173
- Backend API: http://localhost:1111

### Production URLs
- Frontend: https://ttspintar.vercel.app or https://ttspintar.widiyanata.com
- Backend API: https://tts-api.widiyanata.com

## Troubleshooting

### CORS Issues
If you encounter CORS errors:
1. Check that your frontend domain is in the `$allowedOrigins` array in `api/index.php`
2. Ensure your API server is running and accessible
3. Check browser console for specific error messages

### API Connection Issues
If the frontend can't connect to the API:
1. Verify the API URL in your `.env` file
2. Ensure the API server is running
3. Check for network errors in the browser console
