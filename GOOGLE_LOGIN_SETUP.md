# Setting Up Google Login

This guide will help you set up Google OAuth for your Crossword Generator application.

## Prerequisites

1. A Google account
2. Access to the [Google Cloud Console](https://console.cloud.google.com/)

## Steps to Set Up Google OAuth

### 1. Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click on "Select a project" at the top of the page
3. <PERSON>lick on "New Project"
4. Enter a name for your project (e.g., "Crossword Generator")
5. Click "Create"

### 2. Configure OAuth Consent Screen

1. In your project, go to "APIs & Services" > "OAuth consent screen"
2. Select "External" as the user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - App name: "Crossword Generator"
   - User support email: Your email
   - Developer contact information: Your email
5. Click "Save and Continue"
6. Add the following scopes:
   - `./auth/userinfo.email`
   - `./auth/userinfo.profile`
7. Click "Save and Continue"
8. Add test users if needed (for development)
9. Click "Save and Continue"
10. Review your settings and click "Back to Dashboard"

### 3. Create OAuth Client ID

1. In your project, go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Select "Web application" as the application type
4. Name: "Crossword Generator Web Client"
5. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - `http://localhost:1111` (for PHP backend)
   - Add your production URL if applicable
6. Add authorized redirect URIs:
   - `http://localhost:3000`
   - `http://localhost:1111/api/auth/google/callback`
   - Add your production redirect URL if applicable
7. Click "Create"
8. Note your Client ID and Client Secret

### 4. Update Environment Variables

1. Open your `.env` file
2. Update the Google OAuth configuration:
   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   GOOGLE_REDIRECT_URI=http://localhost:1111/api/auth/google/callback
   VITE_GOOGLE_CLIENT_ID=your_client_id
   ```

## Testing Google Login

1. Start your development server
2. Navigate to the login page
3. Click the "Sign in with Google" button
4. You should be redirected to Google's login page
5. After successful authentication, you should be redirected back to your application

## Troubleshooting

- **Invalid Client ID**: Make sure the client ID in your `.env` file matches the one in the Google Cloud Console
- **Redirect URI Mismatch**: Ensure the redirect URI in your code matches the one configured in the Google Cloud Console
- **CORS Issues**: Check that your authorized JavaScript origins include your frontend URL
- **Token Verification Fails**: Ensure your backend is correctly verifying the Google token

## Security Considerations

- Never commit your Client Secret to version control
- Use environment variables for sensitive information
- Implement proper token validation on the backend
- Consider implementing CSRF protection
- Use HTTPS in production
