# Crossword Generator

A web application for creating and solving crossword puzzles, built with React, TypeScript, and PHP.

## Features

- Create custom crossword puzzles
- Solve puzzles with an interactive interface
- User authentication and progress tracking
- Admin panel for managing content
- Mobile-friendly responsive design
- SEO optimized

## Tech Stack

### Frontend
- React 18
- TypeScript
- Vite (build tool)
- Tailwind CSS
- React Router
- Framer Motion (animations)

### Backend
- PHP 8.3
- MySQL/MariaDB
- Custom RESTful API

## Development Setup

### Prerequisites
- Node.js 18+ and npm
- PHP 8.0+
- MySQL/MariaDB
- Apache/Nginx web server

### Frontend Setup
1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`
4. Start the development server:
   ```
   npm run dev
   ```

### Backend Setup
1. Configure your web server to point to the `api` directory
2. Create a database and import the schema from `api/schema.sql`
3. Update the database configuration in `api/config/config.php`
4. Make sure the `api/logs` and `api/cache` directories are writable

## Production Deployment

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

Quick steps:
1. Update the production environment files:
   - `.env.production`
   - `api/.env.production`
2. Build the frontend:
   ```
   npm run build:prod
   ```
3. Run the deployment script:
   ```
   deploy.bat
   ```
   or
   ```
   ./deploy.sh
   ```
4. Upload the generated packages to your hosting provider

## Optimization Features

The application includes several optimization features for production:

- Code splitting and lazy loading
- Tree shaking to eliminate unused code
- Minification and compression
- Image optimization
- Caching strategies
- GZIP compression via .htaccess
- CSS and JS bundling

## Security Features

- CSRF protection
- XSS prevention
- Content Security Policy
- HTTPS enforcement
- Rate limiting
- Bot protection

## License

[MIT License](LICENSE)

## Acknowledgements

- [React](https://reactjs.org/)
- [Vite](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/)
