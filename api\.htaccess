# Enable URL rewriting
RewriteEngine On

# If the requested file or directory exists, serve it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Special rule for sitemap.php - direct access
RewriteRule ^sitemap\.xml$ sitemap.php [L]
# Also handle /api/sitemap.xml
RewriteRule ^api/sitemap\.xml$ sitemap.php [L]

# Handle short link redirects
RewriteRule ^s/([a-zA-Z0-9]+)$ s.php?code=$1 [L,QSA]

# Otherwise, route all requests to index.php
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers removed for simplicity

# Disable directory listing
Options -Indexes

# PHP settings - simplified for development
<IfModule mod_php8.c>
    # Set environment to development
    SetEnv APP_ENV development

    # Enable showing PHP errors for debugging
    php_flag display_errors On

    # Maximum execution time - increased for development
    php_value max_execution_time 60

    # Maximum file upload size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M

    # Character encoding
    php_value default_charset UTF-8

    # Session settings - improved security and cross-browser compatibility
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1

    # SameSite=None is required for cross-origin requests with credentials
    # But it requires Secure=true in modern browsers (especially Chrome)
    php_value session.cookie_samesite "None"

    # Session lifetime (in seconds) - 24 hours
    php_value session.gc_maxlifetime 86400
    php_value session.cookie_lifetime 86400

    # Additional security settings
    php_value session.use_strict_mode 1
    php_value session.use_trans_sid 0

    # Cookie security is handled dynamically in setSecureSessionParams()
    # based on whether we're using HTTPS and localhost
</IfModule>

# File access restrictions removed for simplicity
