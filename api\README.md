# Crossword Generator API

A PHP-based RESTful API for the Crossword Generator application.

## Requirements

- PHP 8.0 or higher
- MySQL/MariaDB
- Apache with mod_rewrite enabled (or equivalent)

## Directory Structure

```text
api/
├── config/             # Configuration files
│   ├── config.php      # Main configuration
│   └── database.php    # Database connection
├── controllers/        # Controller classes
│   └── CrosswordController.php
├── models/             # Model classes
│   └── CrosswordModel.php
├── utils/              # Utility functions
│   └── helpers.php
├── .htaccess           # URL rewriting and security
├── index.php           # Entry point
├── schema.sql          # Database schema
└── README.md           # This file
```

## Setup Instructions

1. Configure your web server to point to the project directory
2. Create a MySQL database for the application
3. Update the database configuration in `config/config.php`
4. Create the required database tables (see below)

## Database Schema

The database schema is defined in `schema.sql`. Here's a summary:

```sql
CREATE TABLE crosswords (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    grid_size INT NOT NULL DEFAULT 15,
    grid_data JSON NOT NULL,
    words JSON NOT NULL,
    clues JSON NOT NULL,
    word_positions JSON NOT NULL,
    difficulty ENUM('mudah', 'sedang', 'sulit') NOT NULL DEFAULT 'sedang',
    user_id VARCHAR(36),
    is_public TINYINT(1) NOT NULL DEFAULT 1,
    plays INT NOT NULL DEFAULT 0,
    rating DECIMAL(3,1),
    category_id VARCHAR(36),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

CREATE TABLE categories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    created_at DATETIME NOT NULL
);

CREATE TABLE user_profiles (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100),
    avatar_url VARCHAR(255),
    bio TEXT,
    created_at DATETIME NOT NULL
);

CREATE TABLE user_progress (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    crossword_id VARCHAR(36) NOT NULL,
    progress_data JSON NOT NULL,
    is_completed TINYINT(1) NOT NULL DEFAULT 0,
    time_spent INT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE,
    FOREIGN KEY (crossword_id) REFERENCES crosswords(id) ON DELETE CASCADE
);
```

## API Endpoints

### Crossword Endpoints

- `GET /api/crosswords` - Get all crosswords
  - Query parameters:
    - `category_id` - Filter by category
    - `difficulty` - Filter by difficulty (mudah, sedang, sulit)
    - `is_public` - Filter by public status (1 or 0)
    - `user_id` - Filter by creator
    - `limit` - Number of results per page (default: 12)
    - `page` - Page number (default: 1)

- `GET /api/crosswords/{id}` - Get a specific crossword

- `POST /api/crosswords` - Create a new crossword

- `PUT /api/crosswords/{id}` - Update a crossword

- `DELETE /api/crosswords/{id}` - Delete a crossword

- `POST /api/crosswords/{id}/play` - Record a play for a crossword

- `POST /api/crosswords/{id}/rate` - Rate a crossword
  - Request body: `{ "rating": 4.5 }`

## Example Usage

### Create a new crossword

```http
POST /api/crosswords
Content-Type: application/json

{
  "title": "Daily Crossword #1",
  "description": "A simple crossword puzzle",
  "difficulty": "sedang",
  "state": {
    "gridSize": 15,
    "grid": [
      [{"char": "A", "wordIds": [1, 2]}, {"char": "B", "wordIds": [1]}, {"char": "C", "wordIds": [1]}],
      [{"char": "D", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}],
      [{"char": "E", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]
    ],
    "words": ["ABC", "ADE"],
    "clues": {
      "across": {
        "1": "First three letters of the alphabet"
      },
      "down": {
        "2": "First, fourth, and fifth letters of the alphabet"
      }
    },
    "wordPositions": [
      {"direction": "across", "row": 0, "col": 0, "number": 1},
      {"direction": "down", "row": 0, "col": 0, "number": 2}
    ],
    "wordNumber": 3,
    "selectedWordId": null,
    "mode": "edit"
  }
}
```

### Get all crosswords

```http
GET /api/crosswords?category_id=1&difficulty=sedang&limit=10&page=1
```

### Get a specific crossword

```http
GET /api/crosswords/123e4567-e89b-12d3-a456-426614174000
```

### Update a crossword

```http
PUT /api/crosswords/123e4567-e89b-12d3-a456-426614174000
Content-Type: application/json

{
  "title": "Updated Crossword Title",
  "description": "Updated description",
  "difficulty": "sulit"
}
```

### Record a play

```http
POST /api/crosswords/123e4567-e89b-12d3-a456-426614174000/play
```

### Rate a crossword

```http
POST /api/crosswords/123e4567-e89b-12d3-a456-426614174000/rate
Content-Type: application/json

{
  "rating": 4.5
}
```

## Security Considerations

- Input validation and sanitization is implemented
- Database queries use prepared statements to prevent SQL injection
- Error handling is configured to hide sensitive information in production
- Security headers are set in .htaccess
