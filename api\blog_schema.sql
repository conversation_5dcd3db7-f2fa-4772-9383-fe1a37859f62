-- Blog and Settings tables for Crossword Generator

-- Blog posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id VARCHAR(36) NOT NULL COLLATE 'utf8mb4_unicode_ci',
    title VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_ci',
    slug VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_ci',
    content TEXT NOT NULL COLLATE 'utf8mb4_unicode_ci',
    excerpt TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_ci',
    featured_image VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_ci',
    author_id VARCHAR(36) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_ci',
    status ENUM('draft', 'published') NOT NULL DEFAULT 'draft' COLLATE 'utf8mb4_unicode_ci',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    PRIMARY KEY (id) USING BTREE,
    UNIQUE INDEX slug (slug) USING BTREE,
    FOREIGN KEY (author_id) REFERENCES user_profiles(id) ON DELETE SET NULL
) COLLATE='utf8mb4_unicode_ci' ENGINE=InnoDB;

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id VARCHAR(36) NOT NULL COLLATE 'utf8mb4_unicode_ci',
    setting_key VARCHAR(100) NOT NULL COLLATE 'utf8mb4_unicode_ci',
    setting_value TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_ci',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    PRIMARY KEY (id) USING BTREE,
    UNIQUE INDEX setting_key (setting_key) USING BTREE
) COLLATE='utf8mb4_unicode_ci' ENGINE=InnoDB;

-- Insert default settings
INSERT INTO settings (id, setting_key, setting_value, created_at, updated_at)
VALUES
    (UUID(), 'site_title', 'Teka-Teki Silang Online', NOW(), NOW()),
    (UUID(), 'site_description', 'Platform teka-teki silang online terbaik di Indonesia', NOW(), NOW()),
    (UUID(), 'max_hints', '3', NOW(), NOW()),
    (UUID(), 'enable_google_login', 'true', NOW(), NOW()),
    (UUID(), 'default_difficulty', 'sedang', NOW(), NOW());
