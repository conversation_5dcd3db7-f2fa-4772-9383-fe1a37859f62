<?php
/**
 * Configuration settings for the Tekateki.io API
 * PHP version 8.3
 */

// Define a single environment (no distinction between development and production)
define('APP_ENV', 'development');

// Error reporting settings
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'crosswords_db');
define('DB_USER', 'root');
define('DB_PASS', '');

// Database configuration
// define('DB_HOST', 'localhost');
// define('DB_NAME', 'u186813940_ttsapi');
// define('DB_USER', 'u186813940_ttsapi');
// define('DB_PASS', '|uT9Ih~;3DWy');

// API configuration
define('API_VERSION', '1.0.0');
define('API_BASE_URL', '/api');

// Security configuration
define('CSRF_PROTECTION', false); // Disabled for simplicity
define('SESSION_LIFETIME', 86400); // 24 hours in seconds
define('SECURE_COOKIES', true); // Enable secure cookies for better security

// Rate limiting configuration - all disabled for simplicity
define('ENABLE_RATE_LIMITING', false); // Rate limiting disabled
define('RATE_LIMIT_WINDOW', 60); // Time window in seconds (not used)
define('RATE_LIMIT_MAX_REQUESTS', 60); // Maximum requests per window (not used)
define('ENABLE_BOT_PROTECTION', false); // Bot detection disabled

// API caching configuration
define('ENABLE_API_CACHING', true); // Enable API response caching
define('CACHE_DIRECTORY', __DIR__ . '/../cache'); // Directory to store cache files
define('DEFAULT_CACHE_TTL', 3600); // Default cache time-to-live in seconds (1 hour)
define('CACHE_EXCLUDED_ENDPOINTS', serialize([
    '/api/users/login',
    '/api/users/register',
    '/api/users/logout',
    '/api/users/me',
    '/api/users/profile',
    '/api/progress',
])); // Endpoints to exclude from caching

// Google OAuth configuration
define('ENABLE_GOOGLE_LOGIN', true);
define('GOOGLE_CLIENT_ID', getenv('GOOGLE_CLIENT_ID') ?: '');
define('GOOGLE_CLIENT_SECRET', getenv('GOOGLE_CLIENT_SECRET') ?: '');
define('GOOGLE_REDIRECT_URI', getenv('GOOGLE_REDIRECT_URI') ?: 'http://localhost:1111/api/auth/google/callback');

// Timezone setting
date_default_timezone_set('UTC');

// Create logs directory if it doesn't exist
if (!is_dir(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0755, true);
}

// Include database connection file
require_once __DIR__ . '/database.php';

// Include utility functions
require_once __DIR__ . '/../utils/helpers.php';

// Rate limiting and bot protection are disabled for simplicity
