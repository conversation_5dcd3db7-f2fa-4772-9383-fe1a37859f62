<?php
/**
 * Controller for blog-related operations
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/BlogModel.php';

class BlogController {
    private $model;

    public function __construct() {
        $this->model = new BlogModel();
    }

    /**
     * Get all blog posts with optional filters
     *
     * @return array
     */
    public function getAll() {
        try {
            // Extract filters from query parameters
            $filters = [];
            if (isset($_GET['status'])) {
                $filters['status'] = $_GET['status'];
            }
            if (isset($_GET['author_id'])) {
                $filters['author_id'] = $_GET['author_id'];
            }

            // Extract pagination parameters
            $pagination = [];
            if (isset($_GET['limit'])) {
                $pagination['limit'] = (int)$_GET['limit'];
            }
            if (isset($_GET['page'])) {
                $pagination['page'] = (int)$_GET['page'];
            }

            $result = $this->model->getAll($filters, $pagination);
            
            return [
                'status' => 'success',
                'data' => $result['posts'],
                'pagination' => $result['pagination']
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a blog post by ID
     *
     * @param string $id
     * @return array
     */
    public function get($id) {
        try {
            $post = $this->model->getById($id);
            
            if (!$post) {
                throw new Exception("Blog post not found", 404);
            }
            
            return [
                'status' => 'success',
                'data' => $post
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a blog post by slug
     *
     * @param string $slug
     * @return array
     */
    public function getBySlug($slug) {
        try {
            $post = $this->model->getBySlug($slug);
            
            if (!$post) {
                throw new Exception("Blog post not found", 404);
            }
            
            return [
                'status' => 'success',
                'data' => $post
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Create a new blog post
     *
     * @param array $data
     * @return array
     */
    public function create($data) {
        try {
            // Validate required fields
            $requiredFields = ['title', 'content', 'slug'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("Missing required field: {$field}", 400);
                }
            }
            
            // Check if user is logged in and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Only admins can create blog posts", 403);
            }
            
            // Set author_id to current user
            $data['author_id'] = $_SESSION['user_id'];
            
            // Set default values if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'draft';
            }
            if (!isset($data['excerpt']) && isset($data['content'])) {
                // Generate excerpt from content (first 150 characters)
                $plainText = strip_tags($data['content']);
                $data['excerpt'] = substr($plainText, 0, 150);
                if (strlen($plainText) > 150) {
                    $data['excerpt'] .= '...';
                }
            }
            
            // Create the blog post
            $id = $this->model->create($data);
            
            // Get the created post
            $post = $this->model->getById($id);
            
            return [
                'status' => 'success',
                'message' => 'Blog post created successfully',
                'data' => $post
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Update a blog post
     *
     * @param string $id
     * @param array $data
     * @return array
     */
    public function update($id, $data) {
        try {
            // Check if blog post exists
            $post = $this->model->getById($id);
            if (!$post) {
                throw new Exception("Blog post not found", 404);
            }
            
            // Check if user is logged in and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Only admins can update blog posts", 403);
            }
            
            // Update the blog post
            $success = $this->model->update($id, $data);
            
            if (!$success) {
                throw new Exception("Failed to update blog post", 500);
            }
            
            // Get the updated post
            $updatedPost = $this->model->getById($id);
            
            return [
                'status' => 'success',
                'message' => 'Blog post updated successfully',
                'data' => $updatedPost
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Delete a blog post
     *
     * @param string $id
     * @return array
     */
    public function delete($id) {
        try {
            // Check if blog post exists
            $post = $this->model->getById($id);
            if (!$post) {
                throw new Exception("Blog post not found", 404);
            }
            
            // Check if user is logged in and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Only admins can delete blog posts", 403);
            }
            
            // Delete the blog post
            $success = $this->model->delete($id);
            
            if (!$success) {
                throw new Exception("Failed to delete blog post", 500);
            }
            
            return [
                'status' => 'success',
                'message' => 'Blog post deleted successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }
}
