<?php
/**
 * Controller for category-related operations
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/CategoryModel.php';

class CategoryController {
    private $model;

    public function __construct() {
        $this->model = new CategoryModel();
    }

    /**
     * Get all categories
     *
     * @return array
     */
    public function getAll() {
        try {
            $categories = $this->model->getAll();

            // Enhance categories with crossword counts
            foreach ($categories as &$category) {
                $category['crossword_count'] = $this->model->countCrosswords($category['id']);
            }

            return [
                'status' => 'success',
                'data' => $categories
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a specific category by ID
     *
     * @param string $id The category ID
     * @return array
     */
    public function get($id) {
        try {
            $category = $this->model->getById($id);
            if (!$category) {
                throw new Exception("Category not found", 404);
            }

            // Add crossword count
            $category['crossword_count'] = $this->model->countCrosswords($id);

            return [
                'status' => 'success',
                'data' => $category
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a specific category by slug
     *
     * @param string $slug The category slug
     * @return array
     */
    public function getBySlug($slug) {
        try {
            // Special case for "semua" (all) slug
            if ($slug === 'semua') {
                return [
                    'status' => 'success',
                    'data' => [
                        'id' => 'all',
                        'name' => 'Semua Kategori',
                        'slug' => 'semua',
                        'description' => 'Semua teka-teki silang dari berbagai kategori',
                        'crossword_count' => -1 // Special value to indicate all crosswords
                    ]
                ];
            }

            $category = $this->model->getBySlug($slug);
            if (!$category) {
                throw new Exception("Category not found", 404);
            }

            // Add crossword count
            $category['crossword_count'] = $this->model->countCrosswords($category['id']);

            return [
                'status' => 'success',
                'data' => $category
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Create a new category
     *
     * @param array $data The category data
     * @return array
     */
    public function create($data) {
        try {
            // Validate required fields
            if (!isset($data['name']) || empty(trim($data['name']))) {
                throw new Exception("Category name is required", 400);
            }

            // Sanitize input
            $data['name'] = sanitizeInput($data['name']);
            $data['description'] = isset($data['description']) ? sanitizeInput($data['description']) : null;
            $data['image_url'] = isset($data['image_url']) ? sanitizeInput($data['image_url']) : null;

            $id = $this->model->create($data);
            return [
                'status' => 'success',
                'message' => 'Category created successfully',
                'id' => $id
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode() ?: 400);
        }
    }

    /**
     * Update an existing category
     *
     * @param string $id The category ID
     * @param array $data The updated category data
     * @return array
     */
    public function update($id, $data) {
        try {
            // Check if category exists
            $category = $this->model->getById($id);
            if (!$category) {
                throw new Exception("Category not found", 404);
            }

            // Sanitize input
            if (isset($data['name'])) {
                if (empty(trim($data['name']))) {
                    throw new Exception("Category name cannot be empty", 400);
                }
                $data['name'] = sanitizeInput($data['name']);
            }

            if (isset($data['description'])) {
                $data['description'] = sanitizeInput($data['description']);
            }

            if (isset($data['image_url'])) {
                $data['image_url'] = sanitizeInput($data['image_url']);
            }

            $this->model->update($id, $data);
            return [
                'status' => 'success',
                'message' => 'Category updated successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode() ?: 400);
        }
    }

    /**
     * Delete a category
     *
     * @param string $id The category ID
     * @return array
     */
    public function delete($id) {
        try {
            // Check if category exists
            $category = $this->model->getById($id);
            if (!$category) {
                throw new Exception("Category not found", 404);
            }

            // Check if category has crosswords
            $crosswordCount = $this->model->countCrosswords($id);
            if ($crosswordCount > 0) {
                throw new Exception("Cannot delete category with associated crosswords", 400);
            }

            $this->model->delete($id);
            return [
                'status' => 'success',
                'message' => 'Category deleted successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode() ?: 400);
        }
    }
}
