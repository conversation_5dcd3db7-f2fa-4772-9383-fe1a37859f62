<?php
/**
 * Controller for crossword-related operations
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/CrosswordModel.php';

class CrosswordController {
    private $model;

    public function __construct() {
        $this->model = new CrosswordModel();
    }

    /**
     * Get all crosswords
     *
     * @return array
     */
    public function getAll() {
        try {
            $filters = [];
            $limit = 12;
            $page = 1;

            // Get query parameters
            if (isset($_GET['category_id'])) {
                $filters['category_id'] = $_GET['category_id'];
            }

            if (isset($_GET['difficulty'])) {
                $filters['difficulty'] = $_GET['difficulty'];
            }

            if (isset($_GET['is_public'])) {
                $filters['is_public'] = (bool)$_GET['is_public'];
            }

            if (isset($_GET['user_id'])) {
                $filters['user_id'] = $_GET['user_id'];
            }

            if (isset($_GET['limit']) && is_numeric($_GET['limit'])) {
                $limit = (int)$_GET['limit'];
            }

            if (isset($_GET['page']) && is_numeric($_GET['page'])) {
                $page = (int)$_GET['page'];
            }

            $offset = ($page - 1) * $limit;

            $result = $this->model->getAll($filters, $limit, $offset);

            // Extract crosswords and pagination info
            $crosswords = $result['crosswords'];
            $pagination = $result['pagination'];

            // Add current page to pagination info
            $pagination['page'] = $page;

            return [
                'status' => 'success',
                'data' => $crosswords,
                'pagination' => $pagination
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    public function getFeatured() {
        try {
            $crosswords = $this->model->getFeatured();

            return [
                'status' => 'success',
                'data' => $crosswords
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a specific crossword by ID
     *
     * @param int $id The crossword ID
     * @return array
     */
    public function get($id) {
        try {
            $crossword = $this->model->getById($id);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }
            return [
                'status' => 'success',
                'data' => $crossword
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a specific crossword by slug
     *
     * @param string $slug The crossword slug
     * @return array
     */
    public function getBySlug($slug) {
        try {
            $crossword = $this->model->getBySlug($slug);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }
            return [
                'status' => 'success',
                'data' => $crossword
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Create a new crossword
     *
     * @param array $data The crossword data
     * @return array
     */
    public function create($data) {
        try {
            // Check if we have state data or direct data
            if (isset($data['state'])) {
                // Validate state data
                validateRequiredFields($data, ['title', 'state']);
                validateRequiredFields($data['state'], ['gridSize', 'grid', 'words', 'clues', 'wordPositions']);
            } else {
                // Validate direct data
                validateRequiredFields($data, ['title', 'grid_size', 'grid_data', 'words', 'clues']);
            }

            // Sanitize input (except for JSON data)
            $data['title'] = sanitizeInput($data['title']);
            $data['description'] = isset($data['description']) ? sanitizeInput($data['description']) : null;

            $id = $this->model->create($data);
            return [
                'status' => 'success',
                'message' => 'Crossword created successfully',
                'id' => $id
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode() ?: 400);
        }
    }

    /**
     * Update an existing crossword
     *
     * @param string $id The crossword ID
     * @param array $data The updated crossword data
     * @return array
     */
    public function update($id, $data) {
        try {
            // Check if crossword exists
            $crossword = $this->model->getById($id);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            // Sanitize input (except for JSON data)
            if (isset($data['title'])) {
                $data['title'] = sanitizeInput($data['title']);
            }

            if (isset($data['description'])) {
                $data['description'] = sanitizeInput($data['description']);
            }

            $this->model->update($id, $data);
            return [
                'status' => 'success',
                'message' => 'Crossword updated successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode() ?: 400);
        }
    }

    /**
     * Delete a crossword
     *
     * @param string $id The crossword ID
     * @return array
     */
    public function delete($id) {
        try {
            // Check if crossword exists
            $crossword = $this->model->getById($id);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            $this->model->delete($id);
            return [
                'status' => 'success',
                'message' => 'Crossword deleted successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Record a play for a crossword
     *
     * @param string $id The crossword ID
     * @return array
     */
    public function recordPlay($id) {
        try {
            // Check if crossword exists
            $crossword = $this->model->getById($id);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            $this->model->incrementPlays($id);
            return [
                'status' => 'success',
                'message' => 'Play recorded successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Rate a crossword
     *
     * @param string $id The crossword ID
     * @param array $data The rating data
     * @return array
     */
    public function rate($id, $data) {
        try {
            // Validate required fields
            validateRequiredFields($data, ['rating']);

            // Check if crossword exists
            $crossword = $this->model->getById($id);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            // Validate rating
            $rating = (float)$data['rating'];
            if ($rating < 1 || $rating > 5) {
                throw new Exception("Rating must be between 1 and 5", 400);
            }

            $this->model->updateRating($id, $rating);
            return [
                'status' => 'success',
                'message' => 'Rating updated successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }
}
