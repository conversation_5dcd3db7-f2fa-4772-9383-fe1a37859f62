<?php
/**
 * Controller for settings-related operations
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/SettingsModel.php';

class SettingsController {
    private $model;

    public function __construct() {
        $this->model = new SettingsModel();
    }

    /**
     * Get all settings
     *
     * @return array
     */
    public function getAll() {
        try {
            $settings = $this->model->getAll();
            
            return [
                'status' => 'success',
                'data' => $settings
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get a setting by key
     *
     * @param string $key
     * @return array
     */
    public function get($key) {
        try {
            $value = $this->model->get($key);
            
            if ($value === null) {
                throw new Exception("Setting not found", 404);
            }
            
            return [
                'status' => 'success',
                'data' => [
                    'key' => $key,
                    'value' => $value
                ]
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Update settings
     *
     * @param array $data Key-value pairs of settings
     * @return array
     */
    public function update($data) {
        try {
            // Check if user is logged in and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                logError("Unauthorized attempt to update settings by user ID: " . $_SESSION['role']);
                throw new Exception("Unauthorized: Only admins can update settings", 403);
            }
            
            // Validate data
            if (!is_array($data) || empty($data)) {
                throw new Exception("Invalid settings data", 400);
            }
            
            // Update settings
            $success = $this->model->setMultiple($data);
            
            if (!$success) {
                throw new Exception("Failed to update settings", 500);
            }
            
            // Get updated settings
            $updatedSettings = $this->model->getAll();
            
            return [
                'status' => 'success',
                'message' => 'Settings updated successfully',
                'data' => $updatedSettings
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }
}
