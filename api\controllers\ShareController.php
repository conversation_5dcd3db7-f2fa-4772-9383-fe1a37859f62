<?php

require_once __DIR__ . '/../models/ShareModel.php';
require_once __DIR__ . '/../utils/Response.php';

/**
 * Controller for handling sharing functionality
 */
class ShareController
{
    private $model;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->model = new ShareModel();
    }

    /**
     * Create a short share link
     * 
     * @param string $id The crossword ID
     * @return array Response with the short link
     */
    public function createShareLink($id)
    {
        // Validate input
        if (empty($id)) {
            return Response::error('Crossword ID is required', 400);
        }

        try {
            // Generate a short code for the crossword
            $shortCode = $this->model->createShareLink($id);
            
            if ($shortCode) {
                $shareUrl = $_SERVER['HTTP_HOST'] . '/s/' . $shortCode;
                
                return Response::success([
                    'short_code' => $shortCode,
                    'share_url' => $shareUrl
                ]);
            } else {
                return Response::error('Failed to create share link', 500);
            }
        } catch (Exception $e) {
            return Response::error('Error creating share link: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get the crossword ID from a short code
     * 
     * @param string $shortCode The short code
     * @return array Response with the crossword ID
     */
    public function getShareLink($shortCode)
    {
        // Validate input
        if (empty($shortCode)) {
            return Response::error('Short code is required', 400);
        }

        try {
            // Get the crossword ID from the short code
            $crosswordId = $this->model->getShareLink($shortCode);
            
            if ($crosswordId) {
                return Response::success([
                    'crossword_id' => $crosswordId
                ]);
            } else {
                return Response::error('Share link not found', 404);
            }
        } catch (Exception $e) {
            return Response::error('Error retrieving share link: ' . $e->getMessage(), 500);
        }
    }
}
