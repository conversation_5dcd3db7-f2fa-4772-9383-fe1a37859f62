<?php
/**
 * Controller for generating dynamic sitemaps
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/CrosswordModel.php';
require_once __DIR__ . '/../models/CategoryModel.php';
require_once __DIR__ . '/../models/BlogModel.php';

class SitemapController {
    private $crosswordModel;
    private $categoryModel;
    private $blogModel;
    private $baseUrl;

    public function __construct() {
        $this->crosswordModel = new CrosswordModel();
        $this->categoryModel = new CategoryModel();
        $this->blogModel = new BlogModel();

        // Get base URL from environment or use a default
        $this->baseUrl = getenv('APP_URL') ?: 'https://tekateki.io';
    }

    /**
     * Generate XML sitemap
     *
     * @return string XML sitemap content
     */
    public function generateSitemap() {
        // Start XML
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

        // Add static pages
        $xml .= $this->addStaticPages();

        // Add categories
        $xml .= $this->addCategories();

        // Add crosswords
        $xml .= $this->addCrosswords();

        // Add blog posts
        // $xml .= $this->addBlogPosts();

        // Close XML
        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Add static pages to sitemap
     *
     * @return string XML for static pages
     */
    private function addStaticPages() {
        $xml = '';

        // Define static pages with their priorities and change frequencies
        $staticPages = [
            [
                'url' => '/',
                'priority' => '1.0',
                'changefreq' => 'weekly'
            ],
            [
                'url' => '/play',
                'priority' => '0.9',
                'changefreq' => 'daily'
            ],
            [
                'url' => '/create',
                'priority' => '0.8',
                'changefreq' => 'monthly'
            ],
            // Blog URLs removed - now handled by WordPress in /blog subfolder
            // SEO-enhancing pages
            [
                'url' => '/bantuan/faq',
                'priority' => '0.7',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/cara-bermain',
                'priority' => '0.7',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/kamus-istilah-tts',
                'priority' => '0.6',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/manfaat-tts',
                'priority' => '0.7',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/tips-strategi-tts',
                'priority' => '0.7',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/tentang-kami',
                'priority' => '0.6',
                'changefreq' => 'monthly'
            ],
            [
                'url' => '/kebijakan-privasi',
                'priority' => '0.5',
                'changefreq' => 'yearly'
            ],
            [
                'url' => '/syarat-ketentuan',
                'priority' => '0.5',
                'changefreq' => 'yearly'
            ],
            [
                'url' => '/kontak',
                'priority' => '0.6',
                'changefreq' => 'monthly'
            ]
        ];

        // Generate XML for each static page
        foreach ($staticPages as $page) {
            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . $this->baseUrl . $page['url'] . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>' . $page['changefreq'] . '</changefreq>' . PHP_EOL;
            $xml .= '    <priority>' . $page['priority'] . '</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;
        }

        return $xml;
    }

    /**
     * Add categories to sitemap
     *
     * @return string XML for categories
     */
    private function addCategories() {
        $xml = '';

        // Get all categories
        $categories = $this->categoryModel->getAll();

        // Generate XML for each category
        foreach ($categories as $category) {
            $xml .= '  <url>' . PHP_EOL;
            // Use slug if available, otherwise fall back to ID
            $categoryUrl = isset($category['slug']) && !empty($category['slug']) ?
                '/teka-teki-silang/' . $category['slug'] :
                '/category/' . $category['id'];
            $xml .= '    <loc>' . $this->baseUrl . $categoryUrl . '</loc>' . PHP_EOL;

            // Use updated_at if available, otherwise use current date
            $lastmod = isset($category['updated_at']) ? date('Y-m-d', strtotime($category['updated_at'])) : date('Y-m-d');
            $xml .= '    <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;

            $xml .= '    <changefreq>weekly</changefreq>' . PHP_EOL;
            $xml .= '    <priority>0.7</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;
        }

        return $xml;
    }

    /**
     * Add crosswords to sitemap
     *
     * @return string XML for crosswords
     */
    private function addCrosswords() {
        $xml = '';

        // Get all public crosswords - getAllRow already filters for public crosswords
        $crosswords = $this->crosswordModel->getAllRow();

        // Generate XML for each crossword
        foreach ($crosswords as $crossword) {
            // Skip if no slug is available
            if (!isset($crossword['slug']) || empty($crossword['slug'])) {
                continue;
            }

            // Determine last modification date
            $lastmod = isset($crossword['updated_at'])
                ? date('Y-m-d', strtotime($crossword['updated_at']))
                : (isset($crossword['created_at'])
                    ? date('Y-m-d', strtotime($crossword['created_at']))
                    : date('Y-m-d'));

            // Add URL entries based on available data
            // $this->addCrosswordUrlToSitemap($xml, [
            //     'type' => 'next',
            //     'url' => '/teka-teki/' . $crossword['slug'],
            //     'lastmod' => $lastmod,
            //     'priority' => '0.8'
            // ]);

            // Add SEO-friendly URL with category if available
            if (isset($crossword['category_slug']) && !empty($crossword['category_slug'])) {
                $this->addCrosswordUrlToSitemap($xml, [
                    'type' => 'category',
                    'url' => '/teka-teki-silang/' . $crossword['category_slug'] . '/' . $crossword['slug'],
                    'lastmod' => $lastmod,
                    'priority' => '0.7'
                ]);
            }

            // Add direct play URL as fallback
            // $this->addCrosswordUrlToSitemap($xml, [
            //     'type' => 'play',
            //     'url' => '/play/' . $crossword['id'],
            //     'lastmod' => $lastmod,
            //     'priority' => '0.6'
            // ]);
        }

        return $xml;
    }

    /**
     * Helper method to add a crossword URL to the sitemap
     *
     * @param string &$xml Reference to the XML string being built
     * @param array $urlData URL data including type, url, lastmod, and priority
     * @return void
     */
    private function addCrosswordUrlToSitemap(&$xml, $urlData) {
        $xml .= '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . $this->baseUrl . $urlData['url'] . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . $urlData['lastmod'] . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>monthly</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . $urlData['priority'] . '</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;
    }
    
    /**
     * Add blog posts to sitemap
     *
     * @return string XML for blog posts
     */
    private function addBlogPosts() {
        $xml = '';

        // Get all published blog posts
        $filters = ['status' => 'published'];
        $result = $this->blogModel->getAll($filters, ['limit' => 1000]); // Limit to 1000 most recent
        $posts = $result['posts'];

        // Generate XML for each blog post
        foreach ($posts as $post) {
            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . $this->baseUrl . '/blog/' . $post['slug'] . '</loc>' . PHP_EOL;

            // Use updated_at if available, otherwise use created_at or current date
            if (isset($post['updated_at'])) {
                $lastmod = date('Y-m-d', strtotime($post['updated_at']));
            } elseif (isset($post['created_at'])) {
                $lastmod = date('Y-m-d', strtotime($post['created_at']));
            } else {
                $lastmod = date('Y-m-d');
            }

            $xml .= '    <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>weekly</changefreq>' . PHP_EOL;
            $xml .= '    <priority>0.7</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;
        }

        return $xml;
    }
}
