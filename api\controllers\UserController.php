<?php
/**
 * Controller for user-related operations
 * PHP version 8.3
 */

require_once __DIR__ . '/../models/UserModel.php';
// helpers.php is included via config.php -> index.php

class UserController {
    private $userModel;

    public function __construct() {
        $this->userModel = new UserModel();
    }

    /**
     * Register a new user - simplified version with minimal validation
     *
     * @param array $data Request data (username, email, password, displayName?)
     * @return array JSON response
     */
    public function register($data) {
        try {
            // Validate required fields
            validateRequiredFields($data, ['username', 'email', 'password']);

            // Sanitize inputs
            $username = sanitizeInput($data['username']);
            $email = sanitizeInput($data['email']);
            $password = $data['password']; // Password will be hashed, not htmlspecialchar'd
            $displayName = isset($data['displayName']) ? sanitizeInput($data['displayName']) : null;

            // Minimal validation
            if (strlen($username) < 2) {
                throw new Exception("Username must be at least 2 characters", 400);
            }

            // Basic email validation
            validateEmail($email);

            // Minimal password validation (handled by the helper function)
            validatePassword($password, 4);

            // Check if username or email already exists
            if ($this->userModel->findByUsername($username)) {
                throw new Exception("Username already taken", 409); // 409 Conflict
            }
            if ($this->userModel->findByEmail($email)) {
                throw new Exception("Email already registered", 409); // 409 Conflict
            }

            // Create user
            $userId = $this->userModel->create($username, $email, $password, $displayName);

            if ($userId) {
                return [
                    'status' => 'success',
                    'message' => 'User registered successfully',
                    'data' => [
                        'userId' => $userId
                    ]
                ];
            } else {
                throw new Exception("User registration failed", 500);
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Log in a user - simplified version with minimal security
     */
    public function login($data) {
        try {
            validateRequiredFields($data, ['email', 'password']);

            $email = sanitizeInput($data['email']);
            $password = $data['password'];

            // Basic email validation
            validateEmail($email);

            $user = $this->userModel->findByEmail($email);

            if (!$user || !password_verify($password, $user['password_hash'])) {
                // Log failed login attempt
                logError("Failed login attempt for email: {$email}");
                throw new Exception("Invalid email or password", 401); // Unauthorized
            }

            // Regenerate session ID to prevent session fixation attacks
            session_regenerate_id(true);

            // Store user information in session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['auth_provider'] = 'email';
            $_SESSION['last_activity'] = time();

            // Ensure the session cookie is properly set
            $params = session_get_cookie_params();
            $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $isLocalhost = isset($_SERVER['HTTP_HOST']) && (
                $_SERVER['HTTP_HOST'] === 'localhost' ||
                $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
                strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
            );

            // For cross-origin requests with credentials, we need SameSite=None and Secure=true
            // But for localhost testing, we might need to be more flexible with the Secure flag
            $secure = SECURE_COOKIES ? true : ($isSecure || !$isLocalhost);

            // If using SameSite=None, secure must be true for Chrome to accept the cookie
            if ($params['samesite'] === 'None' && !$secure && !$isLocalhost) {
                $secure = true; // Force secure=true when SameSite=None for compatibility
            }

            // Manually set the session cookie with the correct parameters
            setcookie(
                session_name(),
                session_id(),
                [
                    'expires' => time() + SESSION_LIFETIME,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $secure,
                    'httponly' => true,
                    'samesite' => $params['samesite']
                ]
            );

            // Log session information for debugging
            logError("User logged in: {$user['id']} with session ID: " . session_id(), "info");
            logError("Session cookie params: " . json_encode([
                'name' => session_name(),
                'id' => session_id(),
                'expires' => time() + SESSION_LIFETIME,
                'path' => $params['path'],
                'domain' => $params['domain'],
                'secure' => $secure,
                'httponly' => true,
                'samesite' => $params['samesite'],
                'isLocalhost' => $isLocalhost,
                'host' => $_SERVER['HTTP_HOST'] ?? 'unknown'
            ]), "info");

            // Prepare user data to return (excluding password hash)
            $userDataToReturn = [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'displayName' => $user['display_name'],
                'avatar_url' => $user['avatar_url'],
                'bio' => $user['bio'],
                'role' => $user['role'],
                'auth_provider' => $user['auth_provider']
            ];

            return [
                'status' => 'success',
                'message' => 'Login successful',
                'data' => [
                    'user' => $userDataToReturn
                ],
            ];

        } catch (Exception $e) {
            logError("Login error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Log out a user
     */
    public function logout() {
        // Log the logout if user is authenticated
        if (isset($_SESSION['user_id'])) {
            logError("User logged out: {$_SESSION['user_id']}", "info");
        }

        // Unset all of the session variables
        $_SESSION = [];

        // Delete the session cookie with the same parameters that were used to set it
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $isLocalhost = isset($_SERVER['HTTP_HOST']) && (
                $_SERVER['HTTP_HOST'] === 'localhost' ||
                $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
                strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
            );

            // For cross-origin requests with credentials, we need SameSite=None and Secure=true
            // But for localhost testing, we might need to be more flexible with the Secure flag
            $secure = SECURE_COOKIES ? true : ($isSecure || !$isLocalhost);

            // If using SameSite=None, secure must be true for Chrome to accept the cookie
            if ($params['samesite'] === 'None' && !$secure && !$isLocalhost) {
                $secure = true; // Force secure=true when SameSite=None for compatibility
            }

            // Log cookie deletion
            logError("Deleting session cookie with params: " . json_encode([
                'name' => session_name(),
                'path' => $params["path"],
                'domain' => $params["domain"],
                'secure' => $secure,
                'httponly' => true,
                'samesite' => $params['samesite']
            ]), "info");

            // Set the cookie with an expiration time in the past to delete it
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 42000,
                    'path' => $params["path"],
                    'domain' => $params["domain"],
                    'secure' => $secure,
                    'httponly' => true,
                    'samesite' => $params['samesite']
                ]
            );
        }

        // Finally, destroy the session
        session_destroy();

        return [
            'status' => 'success',
            'message' => 'Logout successful'
        ];
    }

    /**
     * Get current logged-in user's details - improved version with better debugging
     */
    public function getCurrentUser() {
        // Log session information for debugging
        logError("Session check - Session ID: " . session_id(), "info");
        logError("Session data: " . json_encode($_SESSION), "info");

        // Ensure session is started (already done in index.php)
        if (isset($_SESSION['user_id'])) {
            // Log the user ID from session
            logError("User ID from session: {$_SESSION['user_id']}", "info");

            $userId = $_SESSION['user_id'];
            $user = $this->userModel->findById($userId);

            if ($user) {
                // Log successful user retrieval
                logError("User found in database: {$user['username']}", "info");

                // User data already excludes password_hash from findById
                return [
                    'status' => 'success',
                    'data' => [
                        'user' => $user
                    ]
                ];
            } else {
                // User ID in session but not in DB
                logError("User ID {$userId} in session but not found in database", "warning");
                $this->logout(); // Clear session
                throw new Exception("User not found", 401);
            }
        } else {
            // Log that no user ID was found in session
            logError("No user ID in session - not authenticated", "info");
            throw new Exception("Not authenticated", 401); // Unauthorized
        }
    }

    /**
     * Handle Google login
     *
     * @param array $data Request data (token_id, email, name, picture)
     * @return array JSON response
     */
    public function googleLogin($data) {
        try {
            // Check if Google login is enabled
            if (!ENABLE_GOOGLE_LOGIN) {
                logError("Google login attempt when disabled", "warning");
                throw new Exception("Google login is not enabled", 403);
            }

            // Log the request data for debugging (excluding sensitive parts)
            if (APP_ENV === 'development') {
                logError("Google login request: " . json_encode([
                    'email' => $data['email'] ?? 'not provided',
                    'name' => $data['name'] ?? 'not provided',
                    'has_token' => isset($data['token_id']),
                    'has_picture' => isset($data['picture'])
                ]), "info");
            }

            // Validate required fields
            try {
                validateRequiredFields($data, ['token_id', 'email', 'name']);
            } catch (Exception $e) {
                logError("Google login missing required fields: " . $e->getMessage(), "warning");
                throw new Exception("Missing required fields for Google login: " . $e->getMessage(), 400);
            }

            $tokenId = $data['token_id'];
            $email = sanitizeInput($data['email']);
            $name = sanitizeInput($data['name']);
            $picture = isset($data['picture']) ? sanitizeInput($data['picture']) : null;

            // Verify the token with Google
            $payload = verifyGoogleIdToken($tokenId);

            if (!$payload) {
                logError("Google token verification failed for email: {$email}", "error");
                throw new Exception("Invalid Google token. Please try again or use another login method.", 401);
            }

            // Verify that the email in the token matches the provided email
            if (isset($payload['email'])) {
                if ($payload['email'] !== $email) {
                    logError("Email mismatch in Google login: token email {$payload['email']} vs provided {$email}", "warning");

                    // In development, we'll be more permissive
                    if (APP_ENV === 'development') {
                        logError("Accepting email mismatch in development mode", "info");
                    } else {
                        throw new Exception("Email verification failed", 401);
                    }
                } else {
                    logError("Email verification successful for: {$email}", "info");
                }
            } else {
                logError("Token doesn't contain email claim", "warning");
                // Continue anyway, we'll use the provided email
            }

            // Check if user exists by Google ID
            $user = $this->userModel->findByGoogleId($tokenId);

            if (!$user) {
                // Check if user exists by email
                $user = $this->userModel->findByEmail($email);

                if ($user) {
                    // User exists but hasn't logged in with Google before
                    // Link Google account to existing user
                    $this->userModel->updateGoogleId($user['id'], $tokenId);
                    logError("Linked Google account to existing user: {$user['id']}", "info");
                } else {
                    // Create new user with Google data
                    $userId = $this->userModel->createWithGoogle($email, $tokenId, $name, $picture);
                    if (!$userId) {
                        throw new Exception("Failed to create user account", 500);
                    }
                    $user = $this->userModel->findById($userId);
                    logError("Created new user via Google login: {$userId}", "info");
                }
            }

            // Set session data
            session_regenerate_id(true);
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['auth_provider'] = 'google';
            $_SESSION['last_activity'] = time();

            // Ensure the session cookie is properly set (same as in login method)
            $params = session_get_cookie_params();
            $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $isLocalhost = isset($_SERVER['HTTP_HOST']) && (
                $_SERVER['HTTP_HOST'] === 'localhost' ||
                $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
                strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
            );

            // For cross-origin requests with credentials, we need SameSite=None and Secure=true
            $secure = SECURE_COOKIES ? true : ($isSecure || !$isLocalhost);

            // If using SameSite=None, secure must be true for Chrome to accept the cookie
            if ($params['samesite'] === 'None' && !$secure && !$isLocalhost) {
                $secure = true; // Force secure=true when SameSite=None for compatibility
            }

            // Manually set the session cookie with the correct parameters
            setcookie(
                session_name(),
                session_id(),
                [
                    'expires' => time() + SESSION_LIFETIME,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $secure,
                    'httponly' => true,
                    'samesite' => $params['samesite']
                ]
            );

            // Log session information for debugging
            logError("Google login successful for user: {$user['id']} with session ID: " . session_id(), "info");

            // Prepare user data to return
            $userDataToReturn = [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'displayName' => $user['display_name'],
                'avatar_url' => $user['avatar_url'] ?: $picture, // Use Google profile picture if no avatar set
                'bio' => $user['bio'],
                'role' => $user['role'],
                'auth_provider' => $user['auth_provider']
            ];

            return [
                'status' => 'success',
                'message' => 'Google login successful',
                'data' => [
                    'user' => $userDataToReturn
                ]
            ];
        } catch (Exception $e) {
            logError("Google login error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update user profile
     *
     * @param array $data Profile data to update (display_name, avatar_url, bio)
     * @return array JSON response
     */
    public function updateProfile($data) {
        try {
            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                throw new Exception("Authentication required", 401);
            }

            $userId = $_SESSION['user_id'];

            // Sanitize inputs
            $profileData = [];

            if (isset($data['displayName'])) {
                $profileData['display_name'] = sanitizeInput($data['displayName']);

                // Validate display name length
                if (strlen($profileData['display_name']) > 100) {
                    throw new Exception("Display name must be less than 100 characters", 400);
                }
            }

            if (isset($data['avatarUrl'])) {
                $profileData['avatar_url'] = sanitizeInput($data['avatarUrl']);

                // Basic URL validation
                if (!empty($profileData['avatar_url']) && !filter_var($profileData['avatar_url'], FILTER_VALIDATE_URL)) {
                    throw new Exception("Invalid avatar URL format", 400);
                }
            }

            if (isset($data['bio'])) {
                $profileData['bio'] = sanitizeInput($data['bio']);

                // Limit bio length if needed
                if (strlen($profileData['bio']) > 500) {
                    throw new Exception("Bio must be less than 500 characters", 400);
                }
            }

            // Update profile
            $success = $this->userModel->updateProfile($userId, $profileData);

            if (!$success) {
                throw new Exception("Failed to update profile", 500);
            }

            // Get updated user data
            $updatedUser = $this->userModel->findById($userId);

            if (!$updatedUser) {
                throw new Exception("Failed to retrieve updated user data", 500);
            }

            // Prepare user data to return
            $userDataToReturn = [
                'id' => $updatedUser['id'],
                'username' => $updatedUser['username'],
                'email' => $updatedUser['email'],
                'displayName' => $updatedUser['display_name'],
                'avatar_url' => $updatedUser['avatar_url'],
                'bio' => $updatedUser['bio'],
                'role' => $updatedUser['role'],
                'auth_provider' => $updatedUser['auth_provider']
            ];

            return [
                'status' => 'success',
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => $userDataToReturn
                ]
            ];
        } catch (Exception $e) {
            logError("Update profile error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get all users (admin only)
     *
     * @return array JSON response
     */
    public function getAllUsers() {
        try {
            // Check if user is authenticated and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Admin access required", 403);
            }

            // Extract pagination parameters
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

            // Get users with pagination
            $users = $this->userModel->getAllUsers($limit, $page);

            return [
                'status' => 'success',
                'data' => $users['users'],
                'pagination' => $users['pagination']
            ];
        } catch (Exception $e) {
            logError("Get all users error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a user by ID (admin only)
     *
     * @param string $id User ID
     * @return array JSON response
     */
    public function getUserById($id) {
        try {
            // Check if user is authenticated and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Admin access required", 403);
            }

            $user = $this->userModel->findById($id);

            if (!$user) {
                throw new Exception("User not found", 404);
            }

            return [
                'status' => 'success',
                'data' => $user
            ];
        } catch (Exception $e) {
            logError("Get user by ID error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a user (admin only)
     *
     * @param string $id User ID
     * @param array $data User data to update
     * @return array JSON response
     */
    public function updateUser($id, $data) {
        try {
            // Check if user is authenticated and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Admin access required", 403);
            }

            // Check if user exists
            $user = $this->userModel->findById($id);
            if (!$user) {
                throw new Exception("User not found", 404);
            }

            // Sanitize inputs
            $userData = [];

            if (isset($data['displayName'])) {
                $userData['display_name'] = sanitizeInput($data['displayName']);
            }

            if (isset($data['avatarUrl'])) {
                $userData['avatar_url'] = sanitizeInput($data['avatarUrl']);
            }

            if (isset($data['bio'])) {
                $userData['bio'] = sanitizeInput($data['bio']);
            }

            if (isset($data['role']) && in_array($data['role'], ['user', 'admin'])) {
                $userData['role'] = $data['role'];
            }

            // Update user
            $success = $this->userModel->updateProfile($id, $userData);

            if (!$success) {
                throw new Exception("Failed to update user", 500);
            }

            // Get updated user data
            $updatedUser = $this->userModel->findById($id);

            return [
                'status' => 'success',
                'message' => 'User updated successfully',
                'data' => $updatedUser
            ];
        } catch (Exception $e) {
            logError("Update user error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a user (admin only)
     *
     * @param string $id User ID
     * @return array JSON response
     */
    public function deleteUser($id) {
        try {
            // Check if user is authenticated and is an admin
            if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                throw new Exception("Unauthorized: Admin access required", 403);
            }

            // Check if user exists
            $user = $this->userModel->findById($id);
            if (!$user) {
                throw new Exception("User not found", 404);
            }

            // Prevent deleting self
            if ($id === $_SESSION['user_id']) {
                throw new Exception("Cannot delete your own account", 400);
            }

            // Delete user
            $success = $this->userModel->deleteUser($id);

            if (!$success) {
                throw new Exception("Failed to delete user", 500);
            }

            return [
                'status' => 'success',
                'message' => 'User deleted successfully'
            ];
        } catch (Exception $e) {
            logError("Delete user error: " . $e->getMessage());
            throw $e;
        }
    }
}
