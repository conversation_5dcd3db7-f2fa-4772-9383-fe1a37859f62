<?php
/**
 * User Progress Controller
 *
 * Handles API requests for user progress
 */

require_once __DIR__ . '/../models/UserProgressModel.php';
require_once __DIR__ . '/../models/CrosswordModel.php';
require_once __DIR__ . '/../utils/helpers.php';

class UserProgressController {
    private $progressModel;
    private $crosswordModel;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->progressModel = new UserProgressModel($db);
        $this->crosswordModel = new CrosswordModel($db);
    }

    /**
     * Get user progress for a specific crossword
     *
     * @param string $crosswordId Crossword ID
     * @return array Response data
     */
    public function getProgress($crosswordId) {
        try {
            // Debug logging for session
            if (APP_ENV === 'development') {
                logError('Session debug in getProgress: ' . json_encode([
                    'session_id' => session_id(),
                    'has_user_id' => isset($_SESSION['user_id']),
                    'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'not set',
                    'crossword_id' => $crosswordId,
                    'session_status' => session_status(),
                    'cookie_params' => session_get_cookie_params()
                ]), 'info');
            }

            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                logError('Authentication required for progress: ' . $crosswordId, 'warning');
                throw new Exception("Authentication required", 401);
            }

            $userId = $_SESSION['user_id'];

            // Log the user ID and crossword ID
            if (APP_ENV === 'development') {
                logError("Getting progress for user $userId, crossword $crosswordId", 'info');
            }

            // Check if crossword exists
            $crossword = $this->crosswordModel->getById($crosswordId);
            if (!$crossword) {
                logError("Crossword not found: $crosswordId", 'warning');
                throw new Exception("Crossword not found", 404);
            }

            // Get progress
            $progress = $this->progressModel->getUserProgress($userId, $crosswordId);

            // Log success
            if (APP_ENV === 'development') {
                logError("Progress retrieved successfully for user $userId, crossword $crosswordId", 'info');
            }

            return [
                'status' => 'success',
                'data' => $progress
            ];
        } catch (Exception $e) {
            logError('Error in getProgress: ' . $e->getMessage() . ' (Code: ' . $e->getCode() . ')', 'error');
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get all progress entries for the current user
     *
     * @return array Response data
     */
    public function getAllProgress() {
        try {
            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                throw new Exception("Authentication required", 401);
            }

            $userId = $_SESSION['user_id'];

            // Get all progress
            $progress = $this->progressModel->getAllUserProgress($userId);

            return [
                'status' => 'success',
                'data' => $progress
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Save user progress for a crossword
     *
     * @param string $crosswordId Crossword ID
     * @param array $data Progress data
     * @return array Response data
     */
    public function saveProgress($crosswordId, $data) {
        try {
            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                throw new Exception("Authentication required", 401);
            }

            $userId = $_SESSION['user_id'];

            // Check if crossword exists
            $crossword = $this->crosswordModel->getById($crosswordId);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            // Validate required fields
            if (!isset($data['progress_data'])) {
                throw new Exception("Progress data is required", 400);
            }

            // Extract data
            $progressData = $data['progress_data'];
            $isCompleted = isset($data['is_completed']) ? (bool)$data['is_completed'] : false;
            $timeSpent = isset($data['time_spent']) ? (int)$data['time_spent'] : null;

            // Save progress
            $progressId = $this->progressModel->saveProgress(
                $userId,
                $crosswordId,
                $progressData,
                $isCompleted,
                $timeSpent
            );

            return [
                'status' => 'success',
                'message' => 'Progress saved successfully',
                'data' => [
                    'id' => $progressId
                ]
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Delete user progress for a crossword
     *
     * @param string $crosswordId Crossword ID
     * @return array Response data
     */
    public function deleteProgress($crosswordId) {
        try {
            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                throw new Exception("Authentication required", 401);
            }

            $userId = $_SESSION['user_id'];

            // Check if crossword exists
            $crossword = $this->crosswordModel->getById($crosswordId);
            if (!$crossword) {
                throw new Exception("Crossword not found", 404);
            }

            // Delete progress
            $result = $this->progressModel->deleteProgress($userId, $crosswordId);

            return [
                'status' => 'success',
                'message' => 'Progress deleted successfully'
            ];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }
}
