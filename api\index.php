<?php
/**
 * Main entry point for the Crossword Generator API
 * PHP version 8.3
 */

// Include configuration first to get constants
require_once __DIR__ . '/config/config.php'; // This already includes helpers.php

// Include API Key Middleware
require_once __DIR__ . '/middleware/ApiKeyMiddleware.php';

// Log all messages

// Set secure session parameters before starting session
setSecureSessionParams();

// Start session with error handling
if (session_status() === PHP_SESSION_NONE) {
    $sessionStarted = session_start();
    if (!$sessionStarted) {
        logError('Failed to start session', 'error');
    }
}

// Include cache manager if API caching is enabled
$cacheManager = null;
if (defined('ENABLE_API_CACHING') && ENABLE_API_CACHING) {
    require_once __DIR__ . '/utils/cache_manager.php';

    // Define custom TTLs for different endpoints
    $customTtls = [
        '/api/crosswords' => 3600,       // 1 hour for crosswords list
        '/api/crosswords/' => 7200,      // 2 hours for individual crosswords
        '/api/categories' => 7200,       // 2 hours for categories list
        '/api/categories/' => 7200,      // 2 hours for individual categories
        '/api/blogs' => 86400,           // 24 hours for blogs list
        '/api/blogs/' => 86400,          // 24 hours for individual blogs
        '/api/featured' => 1800,         // 30 minutes for featured content
        '/api/sitemap' => 86400,         // 24 hours for sitemap
    ];

    $cacheManager = new CacheManager(
        CACHE_DIRECTORY,
        DEFAULT_CACHE_TTL,
        unserialize(CACHE_EXCLUDED_ENDPOINTS),
        $customTtls
    );
}

// Rate limiting and bot protection are disabled for simplicity

// No HTTPS redirection for development

// Parse the URI to check if this is a sitemap request
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$isSitemapRequest = (strpos($requestUri, '/api/sitemap') !== false);

// Set headers for API responses (except for sitemap which needs XML)
if (!$isSitemapRequest) {
    header('Content-Type: application/json');
}


// Define allowed origins for CORS
$allowedOrigins = [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:4173',  // Vite preview server
    'http://localhost:3000',  // Next.js dev server
    'http://localhost:1111',  // API server (same-origin)
    'https://tts-api.widiyanata.com',
    'https://ttspintar.vercel.app',
    'https://ttspintar.widiyanata.com'
];

// Check if the origin is allowed
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // If origin is not in the allowed list, check if we're in development mode
    // In development, be more permissive
    if (APP_ENV === 'development' && $origin) {
        logError("Allowing non-whitelisted origin in development: {$origin}", "warning");
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        // In production, only allow whitelisted origins
        // Default to the first allowed origin if none match
        header('Access-Control-Allow-Origin: ' . $allowedOrigins[0]);
        logError("Blocked request from non-whitelisted origin: {$origin}", "warning");
    }
}

// Log request information
logError('Request: ' . $_SERVER['REQUEST_METHOD'] . ' ' . $_SERVER['REQUEST_URI'], 'info');

// Set CORS headers
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token, X-Requested-With, Accept, Origin, X-API-Key');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400'); // 24 hours cache for preflight requests

// Security headers removed for simplicity

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple router
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = explode('/', trim($uri, '/'));

// API base path - adjust if needed
$apiBasePath = 'api'; // If URL is like example.com/api/resource
$resourceIndex = array_search($apiBasePath, $uri);
$resource = $resourceIndex !== false && isset($uri[$resourceIndex + 1]) ? $uri[$resourceIndex + 1] : null;
$id = $resourceIndex !== false && isset($uri[$resourceIndex + 2]) ? $uri[$resourceIndex + 2] : null;

// Request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request body for POST/PUT requests
$data = null;
if ($method === 'POST' || $method === 'PUT') {
    $data = json_decode(file_get_contents('php://input'), true);
    // No CSRF protection for simplicity
}

// Validate API key for all API endpoints (with exceptions)
try {
    ApiKeyMiddleware::validateApiKey($_SERVER['REQUEST_URI']);
} catch (Exception $e) {
    // API key validation failed
    http_response_code($e->getCode());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'type' => 'API_KEY_VALIDATION'
    ]);
    exit;
}

// Check for cached response if caching is enabled
$cachedResponse = null;
if ($cacheManager && $cacheManager->shouldCache()) {
    $cacheKey = $cacheManager->generateCacheKey();
    if ($cacheManager->hasValidCache($cacheKey)) {
        $cachedResponse = $cacheManager->getCache($cacheKey);
        // Add cache headers to indicate this is a cached response
        header('X-API-Cache: HIT');
        header('Cache-Control: public, max-age=60'); // Allow browser caching for 1 minute

        // Add cache statistics
        $stats = $cacheManager->getStats();
        header('X-API-Cache-Stats: ' . json_encode($stats));

        echo $cachedResponse;
        exit;
    }
    // Add cache header to indicate this is not a cached response
    header('X-API-Cache: MISS');
    header('Cache-Control: no-store, max-age=0'); // Prevent browser caching for uncached responses
}

// Route the request
try {
    switch ($resource) {
        case 'crosswords':
            require_once __DIR__ . '/controllers/CrosswordController.php';
            $controller = new CrosswordController();

            // Check for special actions
            $action = isset($uri[$resourceIndex + 3]) ? $uri[$resourceIndex + 3] : null;

            if ($method === 'GET') {
                if ($id) {
                    // Check if the ID is a slug (contains only letters, numbers, and hyphens)
                    if (preg_match('/^[a-z0-9\-]+$/', $id) && !preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $id)) {
                        $response = $controller->getBySlug($id);
                    } else {
                        $response = $controller->get($id);
                    }
                } else {
                    $response = $controller->getAll();
                }
            } elseif ($method === 'POST') {
                if ($id && $action === 'play') {
                    // Record a play
                    $response = $controller->recordPlay($id);
                } elseif ($id && $action === 'rate') {
                    // Rate a crossword
                    $response = $controller->rate($id, $data);
                } else {
                    // Create a new crossword
                    $response = $controller->create($data);
                }
            } elseif ($method === 'PUT' && $id) {
                $response = $controller->update($id, $data);
            } elseif ($method === 'DELETE' && $id) {
                $response = $controller->delete($id);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'categories':
            require_once __DIR__ . '/controllers/CategoryController.php';
            $controller = new CategoryController();

            if ($method === 'GET') {
                if ($id) {
                    // Check if the ID is a slug (contains only letters, numbers, and hyphens)
                    if (preg_match('/^[a-z0-9\-]+$/', $id) && !preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $id)) {
                        $response = $controller->getBySlug($id);
                    } else {
                        $response = $controller->get($id);
                    }
                } else {
                    $response = $controller->getAll();
                }
            } elseif ($method === 'POST') {
                $response = $controller->create($data);
            } elseif ($method === 'PUT' && $id) {
                $response = $controller->update($id, $data);
            } elseif ($method === 'DELETE' && $id) {
                $response = $controller->delete($id);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'users':
            require_once __DIR__ . '/controllers/UserController.php';
            $controller = new UserController();
            $userAction = $id; // Using $id as the action for /users/{action}

            if ($method === 'POST' && $userAction === 'register') {
                $response = $controller->register($data);
            } elseif ($method === 'POST' && $userAction === 'login') {
                $response = $controller->login($data);
            } elseif ($method === 'POST' && $userAction === 'google-login') {
                $response = $controller->googleLogin($data);
            } elseif ($method === 'POST' && $userAction === 'logout') { // Typically POST for logout
                $response = $controller->logout();
            } elseif ($method === 'GET' && $userAction === 'me') {
                $response = $controller->getCurrentUser();
            } elseif ($method === 'PUT' && $userAction === 'profile') {
                $response = $controller->updateProfile($data);
            } elseif ($method === 'GET' && $userAction === 'admin') {
                // Admin endpoint to get all users
                $response = $controller->getAllUsers();
            } elseif ($method === 'GET' && $userAction === 'admin' && isset($uri[$resourceIndex + 3])) {
                // Admin endpoint to get a specific user
                $userId = $uri[$resourceIndex + 3];
                $response = $controller->getUserById($userId);
            } elseif ($method === 'PUT' && $userAction === 'admin' && isset($uri[$resourceIndex + 3])) {
                // Admin endpoint to update a specific user
                $userId = $uri[$resourceIndex + 3];
                $response = $controller->updateUser($userId, $data);
            } elseif ($method === 'DELETE' && $userAction === 'admin' && isset($uri[$resourceIndex + 3])) {
                // Admin endpoint to delete a specific user
                $userId = $uri[$resourceIndex + 3];
                $response = $controller->deleteUser($userId);
            } else {
                throw new Exception('User action not found or method not allowed', 404);
            }
            break;

        // Simple health check endpoint
        case 'health':
            $response = [
                'status' => 'success',
                'message' => 'API is working',
                'data' => [
                    'version' => API_VERSION,
                    'timestamp' => time(),
                    'date' => date('Y-m-d H:i:s')
                ]
            ];
            break;

        case 'progress':
            require_once __DIR__ . '/controllers/UserProgressController.php';
            $controller = new UserProgressController();

            if ($method === 'GET') {
                if ($id) {
                    // Get progress for a specific crossword
                    $response = $controller->getProgress($id);
                } else {
                    // Get all progress for the current user
                    $response = $controller->getAllProgress();
                }
            } elseif ($method === 'POST' && $id) {
                // Save progress for a specific crossword
                $response = $controller->saveProgress($id, $data);
            } elseif ($method === 'DELETE' && $id) {
                // Delete progress for a specific crossword
                $response = $controller->deleteProgress($id);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'blogs':
            require_once __DIR__ . '/controllers/BlogController.php';
            $controller = new BlogController();

            if ($method === 'GET') {
                if ($id) {
                    $response = $controller->get($id);
                } elseif (isset($_GET['slug'])) {
                    $response = $controller->getBySlug($_GET['slug']);
                } else {
                    $response = $controller->getAll();
                }
            } elseif ($method === 'POST') {
                $response = $controller->create($data);
            } elseif ($method === 'PUT' && $id) {
                $response = $controller->update($id, $data);
            } elseif ($method === 'DELETE' && $id) {
                $response = $controller->delete($id);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'settings':
            require_once __DIR__ . '/controllers/SettingsController.php';
            $controller = new SettingsController();

            if ($method === 'GET') {
                if ($id) {
                    $response = $controller->get($id);
                } else {
                    $response = $controller->getAll();
                }
            } elseif ($method === 'PUT') {
                $response = $controller->update($data);
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'cache':
            // Cache management endpoint (admin only)
            if (!isUserAdmin()) {
                throw new Exception('Unauthorized', 403);
            }

            if ($method === 'DELETE') {
                if ($cacheManager) {
                    $specificKey = isset($_GET['key']) ? $_GET['key'] : null;
                    $success = $cacheManager->clearCache($specificKey);
                    $response = [
                        'status' => $success ? 'success' : 'error',
                        'message' => $success ? 'Cache cleared successfully' : 'Failed to clear cache'
                    ];
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => 'Caching is not enabled'
                    ];
                }
            } elseif ($method === 'GET') {
                // Get cache status
                if ($cacheManager) {
                    $cacheDir = CACHE_DIRECTORY;
                    $files = glob($cacheDir . '/*.cache');
                    $cacheSize = 0;
                    foreach ($files as $file) {
                        $cacheSize += filesize($file);
                    }

                    $response = [
                        'status' => 'success',
                        'data' => [
                            'enabled' => true,
                            'cache_count' => count($files),
                            'cache_size_bytes' => $cacheSize,
                            'cache_size_kb' => round($cacheSize / 1024, 2),
                            'cache_directory' => $cacheDir,
                            'default_ttl' => DEFAULT_CACHE_TTL
                        ]
                    ];
                } else {
                    $response = [
                        'status' => 'success',
                        'data' => [
                            'enabled' => false
                        ]
                    ];
                }
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'sitemap':
            // Handle sitemap request
            require_once __DIR__ . '/controllers/SitemapController.php';
            $controller = new SitemapController();

            // Set content type to XML
            header('Content-Type: application/xml; charset=utf-8');

            // Generate and output sitemap directly
            echo $controller->generateSitemap();
            exit; // Exit to prevent additional JSON encoding

        // Add more resources/endpoints as needed
        case 'featured':
            require_once __DIR__ . '/controllers/CrosswordController.php';
            $controller = new CrosswordController();
            $response = $controller->getFeatured();
            break;

        case 'share':
            require_once __DIR__ . '/controllers/ShareController.php';
            $controller = new ShareController();

            if ($method === 'POST' && $id) {
                // Create a share link for a crossword
                $response = $controller->createShareLink($id);
            } elseif ($method === 'GET' && $id) {
                // Get the crossword ID from a short code
                $response = $controller->getShareLink($id);
            } else {
                throw new Exception('Method not allowed or missing parameters', 405);
            }
            break;

        default:
            // Default response for root API endpoint
            if (empty($resource)) {
                $response = [
                    'status' => 'success',
                    'message' => 'Crossword Generator API',
                    'version' => '1.0.0'
                ];
            } else {
                throw new Exception('Resource not found', 404);
            }
    }

    // Prepare the JSON response
    $jsonResponse = json_encode($response);

    // Set appropriate cache control headers based on the endpoint
    $uri = $_SERVER['REQUEST_URI'];
    if (strpos($uri, '/api/crosswords') !== false || strpos($uri, '/api/categories') !== false) {
        // Content that changes infrequently
        header('Cache-Control: public, max-age=300'); // 5 minutes
    } elseif (strpos($uri, '/api/blogs') !== false) {
        // Blog content changes less frequently
        header('Cache-Control: public, max-age=3600'); // 1 hour
    } elseif (strpos($uri, '/api/featured') !== false) {
        // Featured content changes more frequently
        header('Cache-Control: public, max-age=60'); // 1 minute
    } else {
        // Default for other endpoints
        header('Cache-Control: no-cache, must-revalidate');
    }

    // Add ETag for client-side caching
    $etag = md5($jsonResponse);
    header('ETag: "' . $etag . '"');

    // Check if client has a valid cached version
    if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && trim($_SERVER['HTTP_IF_NONE_MATCH'], '"') === $etag) {
        // Client has current version, return 304 Not Modified
        header('HTTP/1.1 304 Not Modified');
        exit;
    }

    // Cache the response if caching is enabled and this request should be cached
    if ($cacheManager && $cacheManager->shouldCache()) {
        $cacheKey = $cacheManager->generateCacheKey();
        $cacheManager->setCache($cacheKey, $jsonResponse);

        // Add cache debugging header
        header('X-API-Cache-Key: ' . $cacheKey);
    }

    // Output response
    echo $jsonResponse;

} catch (Exception $e) {
    // Ensure CORS headers are set even for error responses
    // Use the same origin handling logic as above
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    if (in_array($origin, $allowedOrigins)) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        // In development, be more permissive
        if (APP_ENV === 'development' && $origin) {
            header('Access-Control-Allow-Origin: ' . $origin);
        } else {
            // In production, only allow whitelisted origins
            header('Access-Control-Allow-Origin: ' . $allowedOrigins[0]);
        }
    }

    // Set other CORS headers
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token, X-Requested-With, Accept, Origin, X-API-Key');
    header('Access-Control-Allow-Credentials: true');

    // Get the error code
    $errorCode = $e->getCode();

    // Ensure error code is valid HTTP status code
    if (!$errorCode || $errorCode < 100 || $errorCode > 599) {
        $errorCode = 500; // Default to 500 Internal Server Error
    }

    // Set response code
    http_response_code($errorCode);

    // Prepare error response
    $errorResponse = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $errorCode
    ];

    // Add more details for specific error types
    if ($errorCode === 404) {
        $errorResponse['message'] = $e->getMessage() ?: 'Resource tidak ditemukan';
        $errorResponse['type'] = 'NOT_FOUND';
    } elseif ($errorCode === 401) {
        $errorResponse['message'] = $e->getMessage() ?: 'Sesi Anda telah berakhir. Silakan login kembali.';
        $errorResponse['type'] = 'AUTHENTICATION';
    } elseif ($errorCode === 403) {
        $errorResponse['message'] = $e->getMessage() ?: 'Anda tidak memiliki izin untuk mengakses resource ini.';
        $errorResponse['type'] = 'AUTHORIZATION';
    } elseif ($errorCode >= 400 && $errorCode < 500) {
        $errorResponse['type'] = 'VALIDATION';
    } elseif ($errorCode >= 500) {
        $errorResponse['type'] = 'SERVER';
    }

    // Output error response
    echo json_encode($errorResponse);

    // Log the error
    logError('API Error: ' . $e->getMessage() . ' (Code: ' . $errorCode . ')', 'error');
}
