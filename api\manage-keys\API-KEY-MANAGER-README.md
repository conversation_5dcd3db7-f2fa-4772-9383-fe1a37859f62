# API Key Manager

Interface sederhana untuk mengelola API keys pada API v2.

## Fitur

- Login dan registrasi pengguna
- Melihat daftar API key yang dimiliki
- Membuat API key baru
- Mencabut (revoke) API key yang tidak digunakan
- Melihat status API key (aktif, kedaluwarsa, atau dicabut)
- Melihat informasi penggunaan terakhir API key

## Cara Menggunakan

1. Buka halaman API Key Manager di `/manage-keys.php`
2. Login dengan akun yang sudah ada atau daftar akun baru
3. <PERSON><PERSON><PERSON> login, Anda akan melihat daftar API key yang dimiliki (jika ada)
4. Untuk membuat API key baru:
   - Isi nama API key (wajib)
   - Tambahkan deskripsi (opsional)
   - Pilih masa berlaku API key (dalam hari)
   - Klik tombol "Generate API Key"
   - Salin API key yang muncul (ini adalah satu-satunya kesempatan untuk melihat API key lengkap)
5. Untuk mencabut API key, klik tombol "Revoke" pada API key yang ingin dicabut

## Keamanan

- API key hanya ditampilkan satu kali saat pembuatan
- API key disimpan dalam bentuk terenkripsi di database
- Untuk membuat API key baru, Anda perlu memasukkan password sebagai konfirmasi
- API key yang dicabut tidak dapat digunakan lagi

## Penggunaan API Key

Setelah mendapatkan API key, Anda dapat menggunakannya untuk mengakses API v2 dengan salah satu cara berikut:

1. **Header Authorization (Direkomendasikan)**:
   ```
   Authorization: Bearer YOUR_API_KEY
   ```

2. **Header Kustom**:
   ```
   X-API-Key: YOUR_API_KEY
   ```

3. **Parameter Query** (kurang aman, hanya untuk pengujian):
   ```
   ?api_key=YOUR_API_KEY
   ```

## Penyesuaian

Jika perlu menyesuaikan API Key Manager:

1. Edit file `api-key-manager.html` untuk mengubah tampilan
2. Edit file `api-key-manager.js` untuk mengubah logika dan interaksi
3. Pastikan untuk menyesuaikan `API_BASE_URL` di `api-key-manager.js` sesuai dengan URL API v2 Anda

## Troubleshooting

- **Tidak bisa login**: Pastikan email dan password benar
- **API key tidak muncul**: Refresh halaman atau logout dan login kembali
- **Tidak bisa membuat API key**: Pastikan Anda memasukkan password yang benar saat konfirmasi
- **API key tidak berfungsi**: Pastikan API key belum kedaluwarsa atau dicabut
