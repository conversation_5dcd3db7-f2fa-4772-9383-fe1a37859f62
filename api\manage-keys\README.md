# Crossword Generator API

A PHP-based RESTful API for the Crossword Generator application.

## API Versions

This API is available in two versions:

- **API v1**: Session-based authentication (legacy)
- **API v2**: Token-based authentication with API keys (recommended)

## Requirements

- PHP 8.0 or higher
- MySQL/MariaDB
- Apache with mod_rewrite enabled (or equivalent)

## Directory Structure

```
/
├── v1/                 # API v1 (session-based auth)
│   ├── config/         # Configuration files
│   ├── controllers/    # Controller classes
│   ├── models/         # Model classes
│   ├── utils/          # Utility functions
│   ├── .htaccess       # URL rewriting and security
│   ├── index.php       # Entry point
│   └── README.md       # Documentation
│
├── v2/                 # API v2 (token-based auth)
│   ├── controllers/    # Controller classes
│   ├── middleware/     # Middleware classes
│   ├── models/         # Model classes
│   ├── migrations/     # Database migrations
│   ├── .htaccess       # URL rewriting and security
│   ├── index.php       # Entry point
│   └── README.md       # Documentation
│
└── README.md           # This file
```

## Setup Instructions

1. Configure your web server to point to the project directory
2. Create a MySQL database for the application
3. Update the database configuration in `v1/config/config.php`
4. Run the database migrations:
   - `v1/schema.sql` - Base schema
   - `v2/migrations/create_api_keys_table.sql` - API keys table for v2

## API v2 Authentication

API v2 uses API key authentication. You need to include your API key in each request using one of the following methods:

1. **Authorization Header (Recommended)**:
   ```
   Authorization: Bearer YOUR_API_KEY
   ```

2. **Custom Header**:
   ```
   X-API-Key: YOUR_API_KEY
   ```

3. **Query Parameter** (less secure, use only for testing):
   ```
   ?api_key=YOUR_API_KEY
   ```

### Getting an API Key

You can obtain an API key in several ways:

1. **Register a new account**:
   ```
   POST /api/v2/auth/register
   {
     "username": "your_username",
     "email": "<EMAIL>",
     "password": "your_password",
     "displayName": "Your Name"
   }
   ```

2. **Log in with existing account**:
   ```
   POST /api/v2/auth/login
   {
     "email": "<EMAIL>",
     "password": "your_password"
   }
   ```

3. **Create a new API key**:
   ```
   POST /api/v2/apikeys/create
   {
     "email": "<EMAIL>",
     "password": "your_password",
     "name": "My API Key",
     "description": "Used for my application",
     "expires_in_days": 30
   }
   ```

## API Endpoints

See the README files in each version directory for detailed endpoint documentation:

- [API v1 Documentation](v1/README.md)
- [API v2 Documentation](v2/README.md)

## Migrating from v1 to v2

If you're migrating from API v1 to v2, you'll need to:

1. Obtain an API key using one of the methods above
2. Update your API endpoints from `/api/resource` to `/api/v2/resource`
3. Add your API key to all requests
4. Update your code to handle the new authentication flow

## Security Considerations

- API v2 is more secure than v1 as it uses token-based authentication
- API keys can be revoked at any time
- API keys can have expiration dates
- All API requests should use HTTPS
- Keep your API keys secure and don't share them publicly
