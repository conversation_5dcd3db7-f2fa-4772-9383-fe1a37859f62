<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: 600;
        }
        .api-key {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            word-break: break-all;
        }
        .badge {
            font-size: 0.75rem;
        }
        .btn-copy {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .alert-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1050;
        }
        .alert {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">API Key Manager</h4>
                    </div>
                    <div class="card-body">
                        <div id="login-form" class="mb-4">
                            <h5>Login or Register</h5>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" placeholder="Enter your email">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" placeholder="Enter your password">
                            </div>
                            <div class="mb-3" id="register-fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" placeholder="Choose a username">
                                </div>
                                <div class="mb-3">
                                    <label for="displayName" class="form-label">Display Name</label>
                                    <input type="text" class="form-control" id="displayName" placeholder="Enter your display name">
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button id="login-btn" class="btn btn-primary">Login</button>
                                <button id="register-toggle-btn" class="btn btn-outline-secondary">Switch to Register</button>
                            </div>
                        </div>

                        <div id="api-key-manager" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5>Your API Keys</h5>
                                <button id="logout-btn" class="btn btn-outline-danger btn-sm">Logout</button>
                            </div>
                            
                            <div id="api-keys-list" class="mb-4">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">Create New API Key</div>
                                <div class="card-body">
                                    <form id="create-key-form">
                                        <div class="mb-3">
                                            <label for="key-name" class="form-label">Name</label>
                                            <input type="text" class="form-control" id="key-name" placeholder="Enter a name for this API key">
                                        </div>
                                        <div class="mb-3">
                                            <label for="key-description" class="form-label">Description (optional)</label>
                                            <textarea class="form-control" id="key-description" rows="2" placeholder="What will this API key be used for?"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="key-expiry" class="form-label">Expires In (days)</label>
                                            <select class="form-select" id="key-expiry">
                                                <option value="0">Never</option>
                                                <option value="7">7 days</option>
                                                <option value="30" selected>30 days</option>
                                                <option value="90">90 days</option>
                                                <option value="365">1 year</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-success">Generate API Key</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert-container" id="alert-container"></div>

    <div class="modal fade" id="newKeyModal" tabindex="-1" aria-labelledby="newKeyModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newKeyModalLabel">Your New API Key</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>Important:</strong> This is the only time your API key will be shown. Please copy it now and store it securely.
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <div class="api-key p-2 border rounded" id="new-api-key"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="copy-new-key-btn">Copy to Clipboard</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="api-key-manager.js"></script>
</body>
</html>
