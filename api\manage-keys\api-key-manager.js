// API Configuration
const API_BASE_URL = '/v2'; // Change this to match your API base URL

// DOM Elements
const loginForm = document.getElementById('login-form');
const apiKeyManager = document.getElementById('api-key-manager');
const apiKeysList = document.getElementById('api-keys-list');
const createKeyForm = document.getElementById('create-key-form');
const loginBtn = document.getElementById('login-btn');
const registerToggleBtn = document.getElementById('register-toggle-btn');
const logoutBtn = document.getElementById('logout-btn');
const registerFields = document.getElementById('register-fields');
const alertContainer = document.getElementById('alert-container');
const newKeyModal = new bootstrap.Modal(document.getElementById('newKeyModal'));
const newApiKeyElement = document.getElementById('new-api-key');
const copyNewKeyBtn = document.getElementById('copy-new-key-btn');

// State
let isRegisterMode = false;
let currentApiKey = null;
let currentUser = null;

// Event Listeners
document.addEventListener('DOMContentLoaded', initialize);
loginBtn.addEventListener('click', handleAuth);
registerToggleBtn.addEventListener('click', toggleRegisterMode);
logoutBtn.addEventListener('click', handleLogout);
createKeyForm.addEventListener('submit', handleCreateKey);
copyNewKeyBtn.addEventListener('click', copyNewKeyToClipboard);

// Initialize the application
function initialize() {
    // Check if we have a stored API key
    const storedApiKey = localStorage.getItem('apiKey');
    const storedUser = localStorage.getItem('user');
    
    if (storedApiKey && storedUser) {
        try {
            currentApiKey = storedApiKey;
            currentUser = JSON.parse(storedUser);
            showApiKeyManager();
            fetchApiKeys();
        } catch (error) {
            console.error('Failed to parse stored user data', error);
            showLoginForm();
        }
    } else {
        showLoginForm();
    }
}

// Toggle between login and register modes
function toggleRegisterMode() {
    isRegisterMode = !isRegisterMode;
    
    if (isRegisterMode) {
        registerFields.style.display = 'block';
        loginBtn.textContent = 'Register';
        registerToggleBtn.textContent = 'Switch to Login';
    } else {
        registerFields.style.display = 'none';
        loginBtn.textContent = 'Login';
        registerToggleBtn.textContent = 'Switch to Register';
    }
}

// Handle login or registration
async function handleAuth() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        showAlert('Please enter both email and password', 'danger');
        return;
    }
    
    try {
        let endpoint, data;
        
        if (isRegisterMode) {
            const username = document.getElementById('username').value;
            const displayName = document.getElementById('displayName').value;
            
            if (!username) {
                showAlert('Please enter a username', 'danger');
                return;
            }
            
            endpoint = `${API_BASE_URL}/auth/register`;
            data = { email, password, username, displayName };
        } else {
            endpoint = `${API_BASE_URL}/auth/login`;
            data = { email, password };
        }
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || 'Authentication failed');
        }
        
        // Store the API key and user data
        currentApiKey = result.data.api_key;
        currentUser = result.data.user;
        
        localStorage.setItem('apiKey', currentApiKey);
        localStorage.setItem('user', JSON.stringify(currentUser));
        
        showAlert(isRegisterMode ? 'Registration successful!' : 'Login successful!', 'success');
        showApiKeyManager();
        fetchApiKeys();
        
    } catch (error) {
        showAlert(error.message, 'danger');
    }
}

// Handle logout
function handleLogout() {
    // Clear stored data
    localStorage.removeItem('apiKey');
    localStorage.removeItem('user');
    
    currentApiKey = null;
    currentUser = null;
    
    // Reset form fields
    document.getElementById('email').value = '';
    document.getElementById('password').value = '';
    document.getElementById('username').value = '';
    document.getElementById('displayName').value = '';
    
    showLoginForm();
    showAlert('Logged out successfully', 'success');
}

// Fetch API keys for the current user
async function fetchApiKeys() {
    if (!currentApiKey) {
        showAlert('No API key available', 'danger');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/apikeys/list`, {
            headers: {
                'Authorization': `Bearer ${currentApiKey}`
            }
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || 'Failed to fetch API keys');
        }
        
        renderApiKeys(result.data.keys);
        
    } catch (error) {
        showAlert(error.message, 'danger');
        apiKeysList.innerHTML = '<div class="alert alert-danger">Failed to load API keys</div>';
    }
}

// Render the list of API keys
function renderApiKeys(keys) {
    if (!keys || keys.length === 0) {
        apiKeysList.innerHTML = '<div class="alert alert-info">You don\'t have any API keys yet. Create one below.</div>';
        return;
    }
    
    const keysHtml = keys.map(key => {
        const isRevoked = key.is_revoked === 1;
        const isExpired = key.expires_at && new Date(key.expires_at) < new Date();
        const status = isRevoked ? 'Revoked' : (isExpired ? 'Expired' : 'Active');
        const statusClass = isRevoked || isExpired ? 'danger' : 'success';
        
        return `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>${key.name}</div>
                    <span class="badge bg-${statusClass}">${status}</span>
                </div>
                <div class="card-body">
                    ${key.description ? `<p class="card-text">${key.description}</p>` : ''}
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">Created: ${formatDate(key.created_at)}</small>
                            ${key.last_used_at ? `<br><small class="text-muted">Last used: ${formatDate(key.last_used_at)}</small>` : ''}
                            ${key.expires_at ? `<br><small class="text-muted">Expires: ${formatDate(key.expires_at)}</small>` : ''}
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="revokeApiKey('${key.id}')" ${isRevoked ? 'disabled' : ''}>
                            ${isRevoked ? 'Revoked' : 'Revoke'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    apiKeysList.innerHTML = keysHtml;
}

// Handle creating a new API key
async function handleCreateKey(event) {
    event.preventDefault();
    
    const name = document.getElementById('key-name').value;
    const description = document.getElementById('key-description').value;
    const expiresInDays = parseInt(document.getElementById('key-expiry').value);
    
    if (!name) {
        showAlert('Please enter a name for your API key', 'danger');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/apikeys/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${currentApiKey}`
            },
            body: JSON.stringify({
                email: currentUser.email,
                password: prompt('Please enter your password to confirm:'),
                name,
                description,
                expires_in_days: expiresInDays
            })
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || 'Failed to create API key');
        }
        
        // Show the new API key
        newApiKeyElement.textContent = result.data.api_key;
        newKeyModal.show();
        
        // Reset the form
        document.getElementById('key-name').value = '';
        document.getElementById('key-description').value = '';
        
        // Refresh the API keys list
        fetchApiKeys();
        
    } catch (error) {
        showAlert(error.message, 'danger');
    }
}

// Revoke an API key
async function revokeApiKey(keyId) {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/apikeys/${keyId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${currentApiKey}`
            }
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || 'Failed to revoke API key');
        }
        
        showAlert('API key revoked successfully', 'success');
        fetchApiKeys();
        
    } catch (error) {
        showAlert(error.message, 'danger');
    }
}

// Copy the new API key to clipboard
function copyNewKeyToClipboard() {
    const apiKey = newApiKeyElement.textContent;
    
    navigator.clipboard.writeText(apiKey)
        .then(() => {
            showAlert('API key copied to clipboard', 'success');
        })
        .catch(err => {
            console.error('Failed to copy API key', err);
            showAlert('Failed to copy API key', 'danger');
        });
}

// Helper function to format dates
function formatDate(dateString) {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleString();
}

// Show an alert message
function showAlert(message, type = 'info') {
    const alertId = `alert-${Date.now()}`;
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            const bsAlert = bootstrap.Alert.getOrCreateInstance(alertElement);
            bsAlert.close();
        }
    }, 5000);
}

// Show the login form
function showLoginForm() {
    loginForm.style.display = 'block';
    apiKeyManager.style.display = 'none';
}

// Show the API key manager
function showApiKeyManager() {
    loginForm.style.display = 'none';
    apiKeyManager.style.display = 'block';
}

// Make revokeApiKey available globally
window.revokeApiKey = revokeApiKey;
