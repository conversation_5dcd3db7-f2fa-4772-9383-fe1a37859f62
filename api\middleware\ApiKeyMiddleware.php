<?php
/**
 * API Key Middleware for Tekateki.io API
 * Validates X-API-Key header for API access
 */

class ApiKeyMiddleware {

    /**
     * Static API key for authentication
     * This can be made configurable later via environment variables
     */
    private const VALID_API_KEY = '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5';

    /**
     * Endpoints that don't require API key validation
     * These are typically public endpoints or health checks
     */
    private const EXCLUDED_ENDPOINTS = [
        '/api/health',
        '/api/sitemap',
        '/api/sitemap.xml'
    ];

    /**
     * Validate API key from request headers
     *
     * @param string $requestUri The current request URI
     * @return bool True if API key is valid or endpoint is excluded
     * @throws Exception If API key is missing or invalid
     */
    public static function validateApiKey($requestUri = null) {
        // Get the current request URI if not provided
        if ($requestUri === null) {
            $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        }

        // Parse the URI to get the path without query parameters
        $path = parse_url($requestUri, PHP_URL_PATH);

        // Check if this endpoint is excluded from API key validation
        if (self::isExcludedEndpoint($path)) {
            return true;
        }

        // Get API key from headers
        $apiKey = self::getApiKeyFromRequest();

        // Validate API key
        if (!$apiKey) {
            throw new Exception('API key is required. Please include X-API-Key header.', 401);
        }

        if (!self::isValidApiKey($apiKey)) {
            throw new Exception('Invalid API key provided.', 401);
        }

        return true;
    }

    /**
     * Extract API key from request headers
     * Supports multiple header formats for flexibility
     *
     * @return string|null The API key or null if not found
     */
    private static function getApiKeyFromRequest() {
        // Check X-API-Key header (primary method)
        if (isset($_SERVER['HTTP_X_API_KEY'])) {
            return trim($_SERVER['HTTP_X_API_KEY']);
        }

        // Check Authorization header (Bearer token format)
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authHeader = trim($_SERVER['HTTP_AUTHORIZATION']);
            if (preg_match('/^Bearer\s+(.+)$/i', $authHeader, $matches)) {
                return trim($matches[1]);
            }
        }

        // Check query parameter (less secure, for testing only)
        if (isset($_GET['api_key'])) {
            return trim($_GET['api_key']);
        }

        return null;
    }

    /**
     * Validate if the provided API key is correct
     *
     * @param string $apiKey The API key to validate
     * @return bool True if valid, false otherwise
     */
    private static function isValidApiKey($apiKey) {
        // Use hash_equals for timing-safe comparison
        return hash_equals(self::VALID_API_KEY, $apiKey);
    }

    /**
     * Check if the endpoint is excluded from API key validation
     *
     * @param string $path The request path
     * @return bool True if excluded, false otherwise
     */
    private static function isExcludedEndpoint($path) {
        foreach (self::EXCLUDED_ENDPOINTS as $excludedPath) {
            if (strpos($path, $excludedPath) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get information about API key requirements for debugging
     *
     * @return array Information about API key configuration
     */
    public static function getApiKeyInfo() {
        return [
            'required' => true,
            'header_name' => 'X-API-Key',
            'alternative_methods' => [
                'Authorization: Bearer {api_key}',
                'Query parameter: ?api_key={api_key} (testing only)'
            ],
            'excluded_endpoints' => self::EXCLUDED_ENDPOINTS
        ];
    }
}
