-- Migration to add rate limiting table
-- Run this script to add rate limiting support to the database

-- Rate limits table for API protection
CREATE TABLE IF NOT EXISTS rate_limits (
    id VARCHAR(36) PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_count INT NOT NULL DEFAULT 1,
    first_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_blocked TINYINT(1) NOT NULL DEFAULT 0,
    block_expiry TIMESTAMP NULL,
    INDEX (ip_address, endpoint)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_rate_limits_ip_endpoint ON rate_limits (ip_address, endpoint);

-- Add index for cleanup operations
CREATE INDEX IF NOT EXISTS idx_rate_limits_last_request_time ON rate_limits (last_request_time);

-- Add settings for rate limiting
INSERT INTO settings (id, setting_key, setting_value, created_at, updated_at)
VALUES
    (UUID(), 'enable_rate_limiting', 'true', NOW(), NOW()),
    (UUID(), 'rate_limit_window', '60', NOW(), NOW()),
    (UUID(), 'rate_limit_max_requests', '60', NOW(), NOW()),
    (UUID(), 'enable_bot_protection', 'true', NOW(), NOW())
ON DUPLICATE KEY UPDATE
    setting_value = VALUES(setting_value),
    updated_at = NOW();

-- Log the migration
INSERT INTO migrations (id, migration_name, applied_at)
VALUES (UUID(), 'add_rate_limiting', NOW());
