-- Migration to add slug columns to categories and crosswords tables

-- Add slug column to categories table
ALTER TABLE categories
ADD COLUMN slug VARCHAR(255) AFTER name,
ADD UNIQUE INDEX (slug);

-- Add slug column to crosswords table
ALTER TABLE crosswords
ADD COLUMN slug VARCHAR(255) AFTER title,
ADD UNIQUE INDEX (slug);

-- Update existing categories with slugs based on name
UPDATE categories
SET slug = LOWER(REPLACE(REPLACE(REPLACE(name, ' ', '-'), '&', 'dan'), '.', ''));

-- Update existing crosswords with slugs based on title
UPDATE crosswords
SET slug = LOWER(REPLACE(REPLACE(REPLACE(title, ' ', '-'), '&', 'dan'), '.', ''));
