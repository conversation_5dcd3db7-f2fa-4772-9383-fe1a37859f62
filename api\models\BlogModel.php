<?php
/**
 * Model for blog-related operations
 * PHP version 8.3
 */

class BlogModel {
    private $db;
    private $table = 'blog_posts';

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Get all blog posts with optional filters
     *
     * @param array $filters Optional filters (status, author_id)
     * @param array $pagination Optional pagination (limit, page)
     * @return array
     */
    public function getAll($filters = [], $pagination = []) {
        try {
            $limit = isset($pagination['limit']) ? (int)$pagination['limit'] : 10;
            $page = isset($pagination['page']) ? (int)$pagination['page'] : 1;
            $offset = ($page - 1) * $limit;
    
            // Start building the query
            $query = "SELECT b.*, u.display_name as author_name, u.username as author_username 
                     FROM {$this->table} b
                     LEFT JOIN user_profiles u ON b.author_id = u.id";
    
            // Add WHERE clauses for filters
            $whereConditions = [];
            $params = [];
    
            if (isset($filters['status'])) {
                $whereConditions[] = "b.status = :status";
                $params[':status'] = $filters['status'];
            }
    
            if (isset($filters['author_id'])) {
                $whereConditions[] = "b.author_id = :author_id";
                $params[':author_id'] = $filters['author_id'];
            }
    
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
    
            // Add ORDER BY
            $query .= " ORDER BY b.created_at DESC";
    
            // Count query (before adding LIMIT)
            $countQuery = "SELECT COUNT(*) FROM {$this->table} b";
            if (!empty($whereConditions)) {
                $countQuery .= " WHERE " . implode(' AND ', $whereConditions);
            }
    
            // Add LIMIT and OFFSET for pagination
            $query .= " LIMIT :limit OFFSET :offset";
            
            // Prepare and execute main query
            $stmt = $this->db->prepare($query);
    
            // Bind parameters for the main query
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            // Explicitly bind limit and offset parameters
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
            $stmt->execute();
            $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
            // Execute count query
            $countStmt = $this->db->prepare($countQuery);
            
            // Bind parameters for count query
            foreach ($params as $key => $value) {
                $countStmt->bindValue($key, $value);
            }
            
            $countStmt->execute();
            $totalCount = $countStmt->fetchColumn();
    
            return [
                'posts' => $posts,
                'pagination' => [
                    'total' => (int)$totalCount,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($totalCount / $limit)
                ]
            ];
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a blog post by ID
     *
     * @param string $id
     * @return array|false
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare(
                "SELECT b.*, u.display_name as author_name, u.username as author_username 
                 FROM {$this->table} b
                 LEFT JOIN user_profiles u ON b.author_id = u.id
                 WHERE b.id = :id"
            );
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a blog post by slug
     *
     * @param string $slug
     * @return array|false
     */
    public function getBySlug($slug) {
        try {
            $stmt = $this->db->prepare(
                "SELECT b.*, u.display_name as author_name, u.username as author_username 
                 FROM {$this->table} b
                 LEFT JOIN user_profiles u ON b.author_id = u.id
                 WHERE b.slug = :slug"
            );
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Create a new blog post
     *
     * @param array $data
     * @return string The ID of the created blog post
     */
    public function create($data) {
        try {
            $id = generateUuid();
            $now = date('Y-m-d H:i:s');

            $stmt = $this->db->prepare(
                "INSERT INTO {$this->table} (
                    id, title, slug, content, excerpt, featured_image, 
                    author_id, status, created_at, updated_at
                ) VALUES (
                    :id, :title, :slug, :content, :excerpt, :featured_image, 
                    :author_id, :status, :created_at, :updated_at
                )"
            );

            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
            $stmt->bindParam(':slug', $data['slug'], PDO::PARAM_STR);
            $stmt->bindParam(':content', $data['content'], PDO::PARAM_STR);
            $stmt->bindParam(':excerpt', $data['excerpt'], PDO::PARAM_STR);
            $stmt->bindParam(':featured_image', $data['featured_image'], PDO::PARAM_STR);
            $stmt->bindParam(':author_id', $data['author_id'], PDO::PARAM_STR);
            $stmt->bindParam(':status', $data['status'], PDO::PARAM_STR);
            $stmt->bindParam(':created_at', $now, PDO::PARAM_STR);
            $stmt->bindParam(':updated_at', $now, PDO::PARAM_STR);

            $stmt->execute();
            return $id;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Update a blog post
     *
     * @param string $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data) {
        try {
            $now = date('Y-m-d H:i:s');
            
            // Build the SET part of the query dynamically based on provided data
            $setFields = [];
            $params = [':id' => $id, ':updated_at' => $now];
            
            // Add fields that are present in the data
            foreach (['title', 'slug', 'content', 'excerpt', 'featured_image', 'author_id', 'status'] as $field) {
                if (isset($data[$field])) {
                    $setFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            // Always update the updated_at timestamp
            $setFields[] = "updated_at = :updated_at";
            
            $query = "UPDATE {$this->table} SET " . implode(', ', $setFields) . " WHERE id = :id";
            
            $stmt = $this->db->prepare($query);
            
            // Bind all parameters
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a blog post
     *
     * @param string $id
     * @return bool
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
}
