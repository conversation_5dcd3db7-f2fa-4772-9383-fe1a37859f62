<?php
/**
 * Model for category-related database operations
 * PHP version 8.3
 */

class CategoryModel {
    private $db;
    private $table = 'categories';

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Get all categories
     *
     * @return array
     */
    public function getAll() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} ORDER BY name ASC");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a category by ID
     *
     * @param string $id The category ID
     * @return array|false
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a category by slug
     *
     * @param string $slug The category slug
     * @return array|false
     */
    public function getBySlug($slug) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE slug = :slug");
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Create a new category
     *
     * @param array $data The category data
     * @return string The ID of the created category
     */
    public function create($data) {
        try {
            // Generate UUID
            $id = generateUuid();

            // Generate slug if not provided
            if (!isset($data['slug']) || empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['name']);
            }

            $stmt = $this->db->prepare(
                "INSERT INTO {$this->table} (
                    id, name, slug, description, image_url, created_at
                ) VALUES (
                    :id, :name, :slug, :description, :image_url, NOW()
                )"
            );

            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
            $stmt->bindParam(':slug', $data['slug'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
            $stmt->bindParam(':image_url', $data['image_url'], PDO::PARAM_STR);

            $stmt->execute();
            return $id;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Generate a slug from a string
     *
     * @param string $text The text to convert to a slug
     * @return string The generated slug
     */
    private function generateSlug($text) {
        // Transliterate non-ASCII characters to ASCII
        $text = transliterator_transliterate('Any-Latin; Latin-ASCII', $text);

        // Convert to lowercase
        $text = strtolower($text);

        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);

        // Remove leading and trailing hyphens
        $text = trim($text, '-');

        // Ensure slug is unique
        $baseSlug = $text;
        $counter = 1;

        while ($this->slugExists($text)) {
            $text = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $text;
    }

    /**
     * Check if a slug already exists
     *
     * @param string $slug The slug to check
     * @return bool True if the slug exists, false otherwise
     */
    private function slugExists($slug) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->table} WHERE slug = :slug");
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            return (int)$stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Update an existing category
     *
     * @param string $id The category ID
     * @param array $data The updated category data
     * @return bool
     */
    public function update($id, $data) {
        try {
            $setFields = [];
            $params = [':id' => $id];

            // Only update provided fields
            if (isset($data['name'])) {
                $setFields[] = "name = :name";
                $params[':name'] = $data['name'];

                // Auto-update slug if name changes and slug not provided
                if (!isset($data['slug'])) {
                    $data['slug'] = $this->generateSlug($data['name']);
                }
            }

            // Update slug if provided or generated
            if (isset($data['slug'])) {
                $setFields[] = "slug = :slug";
                $params[':slug'] = $data['slug'];
            }

            if (isset($data['description'])) {
                $setFields[] = "description = :description";
                $params[':description'] = $data['description'];
            }

            if (isset($data['image_url'])) {
                $setFields[] = "image_url = :image_url";
                $params[':image_url'] = $data['image_url'];
            }

            // If no fields to update, return
            if (empty($setFields)) {
                return true;
            }

            $setClause = implode(', ', $setFields);
            $stmt = $this->db->prepare("UPDATE {$this->table} SET {$setClause} WHERE id = :id");

            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
            }

            $stmt->execute();
            return true;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a category
     *
     * @param string $id The category ID
     * @return bool
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return true;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Count crosswords in a category
     *
     * @param string $id The category ID
     * @return int
     */
    public function countCrosswords($id) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM crosswords WHERE category_id = :category_id");
            $stmt->bindParam(':category_id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return (int)$stmt->fetchColumn();
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
}
