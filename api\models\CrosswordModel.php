<?php
/**
 * Model for crossword-related database operations
 * PHP version 8.3
 */

class CrosswordModel {
    private $db;
    private $table = 'crosswords';

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Get all crosswords
     *
     * @param array $filters Optional filters (category_id, difficulty, etc.)
     * @param int $limit Limit results
     * @param int $offset Pagination offset
     * @return array
     */
    public function getAll($filters = [], $limit = 12, $offset = 0) {
        try {
            $whereConditions = [];
            $params = [];

            // Add filters
            if (!empty($filters['category_id'])) {
                // Special case for 'all' category ID
                if ($filters['category_id'] !== 'all' && $filters['category_id'] !== '') {
                    $whereConditions[] = "c.category_id = :category_id";
                    $params[':category_id'] = $filters['category_id'];
                    error_log("Backend: Filtering by category_id: " . $filters['category_id']);
                } else {
                    error_log("Backend: Not filtering by category_id because it's 'all' or empty: " . $filters['category_id']);
                }
            } else {
                error_log("Backend: No category_id filter provided");
            }

            if (!empty($filters['difficulty'])) {
                $whereConditions[] = "c.difficulty = :difficulty";
                $params[':difficulty'] = $filters['difficulty'];
            }

            if (isset($filters['is_public'])) {
                $whereConditions[] = "c.is_public = :is_public";
                $params[':is_public'] = $filters['is_public'] ? 1 : 0;
            }

            if (!empty($filters['user_id'])) {
                $whereConditions[] = "c.user_id = :user_id";
                $params[':user_id'] = $filters['user_id'];
            }

            // Build the base query for data
            $query = "SELECT c.*, u.username as creator, cat.slug as category_slug, cat.name as category_name
                     FROM {$this->table} c
                     LEFT JOIN user_profiles u ON c.user_id = u.id
                     LEFT JOIN categories cat ON c.category_id = cat.id";

            // Build the count query for pagination
            $countQuery = "SELECT COUNT(*) FROM {$this->table} c";

            if (!empty($whereConditions)) {
                $whereClause = " WHERE " . implode(' AND ', $whereConditions);
                $query .= $whereClause;
                $countQuery .= $whereClause;
            }

            // Add sorting and pagination
            $query .= " ORDER BY c.plays DESC, c.created_at DESC LIMIT :limit OFFSET :offset";

            // First, get the total count for pagination
            $countStmt = $this->db->prepare($countQuery);

            // Bind parameters for count query
            foreach ($params as $key => $value) {
                if (is_int($value)) {
                    $countStmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $countStmt->bindValue($key, $value, PDO::PARAM_STR);
                }
            }

            $countStmt->execute();
            $totalCount = (int)$countStmt->fetchColumn();
            $totalPages = ceil($totalCount / $limit);

            // Now get the actual data
            $stmt = $this->db->prepare($query);

            // Bind parameters for main query
            foreach ($params as $key => $value) {
                if (is_int($value)) {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value, PDO::PARAM_STR);
                }
            }

            // Explicitly bind limit and offset parameters
            $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);

            $stmt->execute();
            $crosswords = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Return both the crosswords and pagination info
            return [
                'crosswords' => $crosswords,
                'pagination' => [
                    'total' => $totalCount,
                    'totalPages' => $totalPages,
                    'currentPage' => $offset == 0 ? 1 : ceil($offset / $limit) + 1,
                    'limit' => $limit
                ]
            ];
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
    
    public function getAllRow() {
        try {
            $query = "SELECT c.title, c.id, c.slug, c.difficulty, c.plays, u.username as creator, cat.slug as category_slug, cat.name as category_name
                     FROM {$this->table} c
                     LEFT JOIN user_profiles u ON c.user_id = u.id
                     LEFT JOIN categories cat ON c.category_id = cat.id
                     WHERE c.is_public = 1";

            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $crosswords = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $crosswords;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
    
    public function getFeatured() {
        try {
            $query = "SELECT c.title, c.id, c.slug, c.difficulty, c.plays, u.username as creator, cat.slug as category_slug, cat.name as category_name
                     FROM {$this->table} c
                     LEFT JOIN user_profiles u ON c.user_id = u.id
                     LEFT JOIN categories cat ON c.category_id = cat.id
                     WHERE c.is_public = 1
                     ORDER BY c.plays DESC, c.created_at DESC LIMIT 4";

            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $crosswords = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $crosswords;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a crossword by ID
     *
     * @param string $id The crossword ID
     * @return array|false
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare(
                "SELECT c.*, u.username as creator, cat.slug as category_slug, cat.name as category_name
                 FROM {$this->table} c
                 LEFT JOIN user_profiles u ON c.user_id = u.id
                 LEFT JOIN categories cat ON c.category_id = cat.id
                 WHERE c.id = :id"
            );
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            $crossword = $stmt->fetch();

            if ($crossword) {
                return $this->formatCrosswordData($crossword);
            }

            return $crossword;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a crossword by slug
     *
     * @param string $slug The crossword slug
     * @return array|false
     */
    public function getBySlug($slug) {
        try {
            $stmt = $this->db->prepare(
                "SELECT c.*, u.username as creator, cat.slug as category_slug, cat.name as category_name
                 FROM {$this->table} c
                 LEFT JOIN user_profiles u ON c.user_id = u.id
                 LEFT JOIN categories cat ON c.category_id = cat.id
                 WHERE c.slug = :slug"
            );
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            $crossword = $stmt->fetch();

            if ($crossword) {
                return $this->formatCrosswordData($crossword);
            }

            return $crossword;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Format crossword data for frontend
     *
     * @param array $crossword Raw crossword data
     * @return array Formatted crossword data
     */
    private function formatCrosswordData($crossword) {
        // Convert JSON fields to arrays
        $crossword['grid_data'] = json_decode($crossword['grid_data'], true);
        $crossword['words'] = json_decode($crossword['words'], true);
        $crossword['clues'] = json_decode($crossword['clues'], true);
        $crossword['word_positions'] = json_decode($crossword['word_positions'] ?? '[]', true);

        // Format for frontend
        $crossword['state'] = [
            'gridSize' => $crossword['grid_size'],
            'grid' => $crossword['grid_data'],
            'words' => $crossword['words'],
            'clues' => $crossword['clues'],
            'wordPositions' => $crossword['word_positions'],
            'wordNumber' => count($crossword['words']) + 1,
            'selectedWordId' => null,
            'mode' => 'play'
        ];

        return $crossword;
    }

    /**
     * Create a new crossword
     *
     * @param array $data The crossword data
     * @return string The ID of the created crossword
     */
    public function create($data) {
        try {
            // Generate UUID if not provided
            $id = $data['id'] ?? $this->generateUuid();

            // Extract CrosswordState from data if present
            $state = $data['state'] ?? null;

            if ($state) {
                // Use state data for grid, words, etc.
                $gridSize = $state['gridSize'];
                $gridData = $state['grid'];
                $words = $state['words'];
                $clues = $state['clues'];
                $wordPositions = $state['wordPositions'];
            } else {
                // Use direct data
                $gridSize = $data['grid_size'] ?? 15;
                $gridData = $data['grid_data'] ?? [];
                $words = $data['words'] ?? [];
                $clues = $data['clues'] ?? ['across' => [], 'down' => []];
                $wordPositions = $data['word_positions'] ?? [];
            }

            // Generate slug if not provided
            if (!isset($data['slug']) || empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['title']);
            }

            $stmt = $this->db->prepare(
                "INSERT INTO {$this->table} (
                    id, title, slug, description, grid_size, grid_data, words, clues, word_positions,
                    difficulty, user_id, is_public, plays, rating, category_id,
                    created_at, updated_at
                ) VALUES (
                    :id, :title, :slug, :description, :grid_size, :grid_data, :words, :clues, :word_positions,
                    :difficulty, :user_id, :is_public, :plays, :rating, :category_id,
                    NOW(), NOW()
                )"
            );

            // Prepare all values first
            $title = $data['title'];
            $slug = $data['slug'];
            $description = $data['description'] ?? null;
            $gridSizeParam = $gridSize;
            $gridDataJson = json_encode($gridData);
            $wordsJson = json_encode($words);
            $cluesJson = json_encode($clues);
            $wordPositionsJson = json_encode($wordPositions);

            // Optional parameters with defaults
            $difficulty = $data['difficulty'] ?? 'sedang';
            $userId = $data['user_id'] ?? null;
            $isPublic = isset($data['is_public']) ? ($data['is_public'] ? 1 : 0) : 1;
            $plays = $data['plays'] ?? 0;
            $rating = $data['rating'] ?? null;
            $categoryId = $data['category_id'] ?? null;

            // Bind parameters using variables
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':title', $title, PDO::PARAM_STR);
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->bindParam(':description', $description, PDO::PARAM_STR);
            $stmt->bindParam(':grid_size', $gridSizeParam, PDO::PARAM_INT);
            $stmt->bindParam(':grid_data', $gridDataJson, PDO::PARAM_STR);
            $stmt->bindParam(':words', $wordsJson, PDO::PARAM_STR);
            $stmt->bindParam(':clues', $cluesJson, PDO::PARAM_STR);
            $stmt->bindParam(':word_positions', $wordPositionsJson, PDO::PARAM_STR);
            $stmt->bindParam(':difficulty', $difficulty, PDO::PARAM_STR);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_public', $isPublic, PDO::PARAM_INT);
            $stmt->bindParam(':plays', $plays, PDO::PARAM_INT);
            $stmt->bindParam(':rating', $rating, PDO::PARAM_STR);
            $stmt->bindParam(':category_id', $categoryId, PDO::PARAM_STR);

            $stmt->execute();
            return $id;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Generate a slug from a string
     *
     * @param string $text The text to convert to a slug
     * @return string The generated slug
     */
    private function generateSlug($text) {
        // Transliterate non-ASCII characters to ASCII
        $text = transliterator_transliterate('Any-Latin; Latin-ASCII', $text);

        // Convert to lowercase
        $text = strtolower($text);

        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);

        // Remove leading and trailing hyphens
        $text = trim($text, '-');

        // Ensure slug is unique
        $baseSlug = $text;
        $counter = 1;

        while ($this->slugExists($text)) {
            $text = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $text;
    }

    /**
     * Check if a slug already exists
     *
     * @param string $slug The slug to check
     * @return bool True if the slug exists, false otherwise
     */
    private function slugExists($slug) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->table} WHERE slug = :slug");
            $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
            $stmt->execute();
            return (int)$stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Update an existing crossword
     *
     * @param string $id The crossword ID
     * @param array $data The updated crossword data
     * @return bool
     */
    public function update($id, $data) {
        try {
            // Extract CrosswordState from data if present
            $state = $data['state'] ?? null;

            if ($state) {
                // Update data with state values
                $data['grid_size'] = $state['gridSize'];
                $data['grid_data'] = $state['grid'];
                $data['words'] = $state['words'];
                $data['clues'] = $state['clues'];
                $data['word_positions'] = $state['wordPositions'];
            }

            // Auto-update slug if title changes and slug not provided
            if (isset($data['title']) && !isset($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['title']);
            }

            // Build the SET part of the query dynamically based on provided data
            $setFields = [];
            $params = [':id' => $id];

            // Map fields to their parameter names and values
            $fieldMap = [
                'title' => [':title', $data['title'] ?? null, PDO::PARAM_STR],
                'slug' => [':slug', $data['slug'] ?? null, PDO::PARAM_STR],
                'description' => [':description', $data['description'] ?? null, PDO::PARAM_STR],
                'grid_size' => [':grid_size', $data['grid_size'] ?? null, PDO::PARAM_INT],
                'grid_data' => [':grid_data', isset($data['grid_data']) ? json_encode($data['grid_data']) : null, PDO::PARAM_STR],
                'words' => [':words', isset($data['words']) ? json_encode($data['words']) : null, PDO::PARAM_STR],
                'clues' => [':clues', isset($data['clues']) ? json_encode($data['clues']) : null, PDO::PARAM_STR],
                'word_positions' => [':word_positions', isset($data['word_positions']) ? json_encode($data['word_positions']) : null, PDO::PARAM_STR],
                'difficulty' => [':difficulty', $data['difficulty'] ?? null, PDO::PARAM_STR],
                'is_public' => [':is_public', isset($data['is_public']) ? ($data['is_public'] ? 1 : 0) : null, PDO::PARAM_INT],
                'plays' => [':plays', $data['plays'] ?? null, PDO::PARAM_INT],
                'rating' => [':rating', $data['rating'] ?? null, PDO::PARAM_STR],
                'category_id' => [':category_id', $data['category_id'] ?? null, PDO::PARAM_STR],
            ];

            // Add fields that are present in the data
            foreach ($fieldMap as $field => [$param, $value]) {
                if (isset($data[$field]) || ($state && in_array($field, ['grid_size', 'grid_data', 'words', 'clues', 'word_positions']))) {
                    $setFields[] = "{$field} = {$param}";
                    $params[$param] = $value;
                }
            }

            // Add updated_at timestamp
            $setFields[] = "updated_at = NOW()";

            if (empty($setFields)) {
                return true; // Nothing to update
            }

            $setClause = implode(', ', $setFields);
            $stmt = $this->db->prepare("UPDATE {$this->table} SET {$setClause} WHERE id = :id");

            // Bind parameters
            foreach ($params as $key => $value) {
                $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
                $stmt->bindValue($key, $value, $paramType);
            }

            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a crossword
     *
     * @param string $id The crossword ID
     * @return bool
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Increment the play count for a crossword
     *
     * @param string $id The crossword ID
     * @return bool
     */
    public function incrementPlays($id) {
        try {
            $stmt = $this->db->prepare("UPDATE {$this->table} SET plays = plays + 1 WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Update the rating for a crossword
     *
     * @param string $id The crossword ID
     * @param float $rating The new rating
     * @return bool
     */
    public function updateRating($id, $rating) {
        try {
            $stmt = $this->db->prepare("UPDATE {$this->table} SET rating = :rating WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':rating', $rating, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Generate a UUID v4
     *
     * @return string
     */
    private function generateUuid() {
        // Generate 16 random bytes
        $data = random_bytes(16);

        // Set version to 0100
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        // Set bits 6-7 to 10
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

        // Output the 36 character UUID
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }


}
