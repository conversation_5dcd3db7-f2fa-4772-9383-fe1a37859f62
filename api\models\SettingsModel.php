<?php
/**
 * Model for settings-related operations
 * PHP version 8.3
 */

class SettingsModel {
    private $db;
    private $table = 'settings';

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Get all settings
     *
     * @return array
     */
    public function getAll() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} ORDER BY setting_key");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Convert to key-value format for easier use
            $formattedSettings = [];
            foreach ($settings as $setting) {
                $formattedSettings[$setting['setting_key']] = $setting['setting_value'];
            }
            
            return $formattedSettings;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get a setting by key
     *
     * @param string $key
     * @return string|null
     */
    public function get($key) {
        try {
            $stmt = $this->db->prepare("SELECT setting_value FROM {$this->table} WHERE setting_key = :key");
            $stmt->bindParam(':key', $key, PDO::PARAM_STR);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result['setting_value'] : null;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param string $value
     * @return bool
     */
    public function set($key, $value) {
        try {
            // Check if the setting already exists
            $stmt = $this->db->prepare("SELECT id FROM {$this->table} WHERE setting_key = :key");
            $stmt->bindParam(':key', $key, PDO::PARAM_STR);
            $stmt->execute();
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $now = date('Y-m-d H:i:s');
            
            if ($existing) {
                // Update existing setting
                $stmt = $this->db->prepare(
                    "UPDATE {$this->table} 
                     SET setting_value = :value, updated_at = :updated_at 
                     WHERE setting_key = :key"
                );
                $stmt->bindParam(':key', $key, PDO::PARAM_STR);
                $stmt->bindParam(':value', $value, PDO::PARAM_STR);
                $stmt->bindParam(':updated_at', $now, PDO::PARAM_STR);
                $stmt->execute();
            } else {
                // Create new setting
                $id = generateUuid();
                $stmt = $this->db->prepare(
                    "INSERT INTO {$this->table} (id, setting_key, setting_value, created_at, updated_at) 
                     VALUES (:id, :key, :value, :created_at, :updated_at)"
                );
                $stmt->bindParam(':id', $id, PDO::PARAM_STR);
                $stmt->bindParam(':key', $key, PDO::PARAM_STR);
                $stmt->bindParam(':value', $value, PDO::PARAM_STR);
                $stmt->bindParam(':created_at', $now, PDO::PARAM_STR);
                $stmt->bindParam(':updated_at', $now, PDO::PARAM_STR);
                $stmt->execute();
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Set multiple settings at once
     *
     * @param array $settings Key-value pairs of settings
     * @return bool
     */
    public function setMultiple($settings) {
        try {
            $this->db->beginTransaction();
            
            foreach ($settings as $key => $value) {
                $this->set($key, $value);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw new Exception("Failed to update settings: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a setting
     *
     * @param string $key
     * @return bool
     */
    public function delete($key) {
        try {
            $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE setting_key = :key");
            $stmt->bindParam(':key', $key, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
}
