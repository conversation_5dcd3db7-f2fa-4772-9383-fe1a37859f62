<?php

require_once __DIR__ . '/../config/Database.php';

/**
 * Model for handling share links
 */
class ShareModel
{
    private $conn;
    private $table = 'share_links';

    /**
     * Constructor
     */
    public function __construct()
    {
        $database = new Database();
        $this->conn = $database->connect();
        
        // Create the share_links table if it doesn't exist
        $this->createShareLinksTable();
    }

    /**
     * Create the share_links table if it doesn't exist
     */
    private function createShareLinksTable()
    {
        $query = "CREATE TABLE IF NOT EXISTS " . $this->table . " (
            id INT AUTO_INCREMENT PRIMARY KEY,
            short_code VARCHAR(10) NOT NULL UNIQUE,
            crossword_id VARCHAR(36) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->conn->exec($query);
    }

    /**
     * Generate a random short code
     * 
     * @param int $length The length of the short code
     * @return string The generated short code
     */
    private function generateShortCode($length = 6)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $shortCode = '';
        
        for ($i = 0; $i < $length; $i++) {
            $shortCode .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $shortCode;
    }

    /**
     * Create a share link for a crossword
     * 
     * @param string $crosswordId The crossword ID
     * @return string|false The short code or false on failure
     */
    public function createShareLink($crosswordId)
    {
        // Check if a share link already exists for this crossword
        $query = "SELECT short_code FROM " . $this->table . " WHERE crossword_id = :crossword_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':crossword_id', $crosswordId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Return the existing short code
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['short_code'];
        }
        
        // Generate a new short code
        $shortCode = $this->generateShortCode();
        
        // Make sure the short code is unique
        $isUnique = false;
        $maxAttempts = 5;
        $attempts = 0;
        
        while (!$isUnique && $attempts < $maxAttempts) {
            $query = "SELECT id FROM " . $this->table . " WHERE short_code = :short_code";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':short_code', $shortCode);
            $stmt->execute();
            
            if ($stmt->rowCount() === 0) {
                $isUnique = true;
            } else {
                $shortCode = $this->generateShortCode();
                $attempts++;
            }
        }
        
        if (!$isUnique) {
            return false;
        }
        
        // Insert the share link
        $query = "INSERT INTO " . $this->table . " (short_code, crossword_id) VALUES (:short_code, :crossword_id)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':short_code', $shortCode);
        $stmt->bindParam(':crossword_id', $crosswordId);
        
        if ($stmt->execute()) {
            return $shortCode;
        }
        
        return false;
    }

    /**
     * Get the crossword ID from a short code
     * 
     * @param string $shortCode The short code
     * @return string|false The crossword ID or false if not found
     */
    public function getShareLink($shortCode)
    {
        $query = "SELECT crossword_id FROM " . $this->table . " WHERE short_code = :short_code";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':short_code', $shortCode);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['crossword_id'];
        }
        
        return false;
    }
}
