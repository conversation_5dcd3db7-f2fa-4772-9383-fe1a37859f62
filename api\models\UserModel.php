<?php
/**
 * Model for user-related database operations
 * PHP version 8.3
 */

class UserModel {
    private $db;
    private $table = 'user_profiles';

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Create a new user with email/password
     *
     * @param string $username
     * @param string $email
     * @param string $password Raw password
     * @param string|null $displayName
     * @return string|false The ID of the created user or false on failure
     */
    public function create($username, $email, $password, $displayName = null) {
        try {
            $id = generateUuid(); // Assumes generateUuid() is available globally (from helpers.php)
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $defaultRole = 'user';
            $authProvider = 'email';

            $stmt = $this->db->prepare(
                "INSERT INTO {$this->table} (id, username, email, password_hash, display_name, role, auth_provider, created_at)
                 VALUES (:id, :username, :email, :password_hash, :display_name, :role, :auth_provider, NOW())"
            );

            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':password_hash', $passwordHash, PDO::PARAM_STR);
            $stmt->bindParam(':display_name', $displayName, PDO::PARAM_STR);
            $stmt->bindParam(':role', $defaultRole, PDO::PARAM_STR);
            $stmt->bindParam(':auth_provider', $authProvider, PDO::PARAM_STR);

            $stmt->execute();
            return $id;
        } catch (PDOException $e) {
            // Log error or handle as appropriate
            // error_log("UserModel::create Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new user with Google authentication
     *
     * @param string $email
     * @param string $googleId
     * @param string $displayName
     * @param string|null $avatarUrl
     * @return string|false The ID of the created user or false on failure
     */
    public function createWithGoogle($email, $googleId, $displayName, $avatarUrl = null) {
        try {
            $id = generateUuid();
            $defaultRole = 'user';
            $authProvider = 'google';

            // Generate a username from the email (before the @ symbol)
            $username = explode('@', $email)[0];
            $baseUsername = $username;
            $counter = 1;

            // Check if username exists, if so, append a number
            while ($this->findByUsername($username)) {
                $username = $baseUsername . $counter;
                $counter++;
            }

            $stmt = $this->db->prepare(
                "INSERT INTO {$this->table} (id, username, email, display_name, avatar_url, role, google_id, auth_provider, created_at)
                 VALUES (:id, :username, :email, :display_name, :avatar_url, :role, :google_id, :auth_provider, NOW())"
            );

            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':display_name', $displayName, PDO::PARAM_STR);
            $stmt->bindParam(':avatar_url', $avatarUrl, PDO::PARAM_STR);
            $stmt->bindParam(':role', $defaultRole, PDO::PARAM_STR);
            $stmt->bindParam(':google_id', $googleId, PDO::PARAM_STR);
            $stmt->bindParam(':auth_provider', $authProvider, PDO::PARAM_STR);

            $stmt->execute();
            return $id;
        } catch (PDOException $e) {
            // Log error or handle as appropriate
            error_log("UserModel::createWithGoogle Error: " . $e->getMessage() . " google id: " . $googleId);
            return false;
        }
    }

    /**
     * Find a user by email
     *
     * @param string $email
     * @return array|false User data as an associative array or false if not found
     */
    public function findByEmail($email) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE email = :email");
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // error_log("UserModel::findByEmail Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find a user by username
     *
     * @param string $username
     * @return array|false User data as an associative array or false if not found
     */
    public function findByUsername($username) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE username = :username");
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // error_log("UserModel::findByUsername Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find a user by ID
     *
     * @param string $id
     * @return array|false User data as an associative array or false if not found
     */
    public function findById($id) {
        try {
            $stmt = $this->db->prepare("SELECT id, username, email, display_name, avatar_url, bio, role, auth_provider, created_at FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC); // Return user data without password hash
        } catch (PDOException $e) {
            // error_log("UserModel::findById Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find a user by Google ID
     *
     * @param string $googleId
     * @return array|false User data as an associative array or false if not found
     */
    public function findByGoogleId($googleId) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE google_id = :google_id");
            $stmt->bindParam(':google_id', $googleId, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // error_log("UserModel::findByGoogleId Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a user's Google ID
     *
     * @param string $userId
     * @param string $googleId
     * @return bool Success or failure
     */
    public function updateGoogleId($userId, $googleId) {
        try {
            $stmt = $this->db->prepare("UPDATE {$this->table} SET google_id = :google_id, auth_provider = 'google' WHERE id = :id");
            $stmt->bindParam(':google_id', $googleId, PDO::PARAM_STR);
            $stmt->bindParam(':id', $userId, PDO::PARAM_STR);
            return $stmt->execute();
        } catch (PDOException $e) {
            // error_log("UserModel::updateGoogleId Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a user's profile information
     *
     * @param string $userId
     * @param array $profileData Data to update (display_name, avatar_url, bio, role)
     * @return bool Success or failure
     */
    public function updateProfile($userId, $profileData) {
        try {
            // Build the SQL query dynamically based on provided fields
            $updateFields = [];
            $params = [':id' => $userId];

            // Check which fields are provided and add them to the update query
            if (isset($profileData['display_name'])) {
                $updateFields[] = "display_name = :display_name";
                $params[':display_name'] = $profileData['display_name'];
            }

            if (isset($profileData['avatar_url'])) {
                $updateFields[] = "avatar_url = :avatar_url";
                $params[':avatar_url'] = $profileData['avatar_url'];
            }

            if (isset($profileData['bio'])) {
                $updateFields[] = "bio = :bio";
                $params[':bio'] = $profileData['bio'];
            }

            if (isset($profileData['role'])) {
                $updateFields[] = "role = :role";
                $params[':role'] = $profileData['role'];
            }

            // If no fields to update, return true (no changes needed)
            if (empty($updateFields)) {
                return true;
            }

            $sql = "UPDATE {$this->table} SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);

            // Bind all parameters
            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value, PDO::PARAM_STR);
            }

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("UserModel::updateProfile Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all users with pagination
     *
     * @param int $limit Number of users per page
     * @param int $page Page number
     * @return array Users and pagination data
     */
    public function getAllUsers($limit = 10, $page = 1) {
        try {
            $offset = ($page - 1) * $limit;

            // Get users
            $stmt = $this->db->prepare(
                "SELECT id, username, email, display_name, avatar_url, bio, role, auth_provider, created_at
                 FROM {$this->table}
                 ORDER BY created_at DESC
                 LIMIT :limit OFFSET :offset"
            );
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $countStmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->table}");
            $countStmt->execute();
            $totalCount = $countStmt->fetchColumn();

            return [
                'users' => $users,
                'pagination' => [
                    'total' => (int)$totalCount,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($totalCount / $limit)
                ]
            ];
        } catch (PDOException $e) {
            error_log("UserModel::getAllUsers Error: " . $e->getMessage());
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a user
     *
     * @param string $id User ID
     * @return bool Success or failure
     */
    public function deleteUser($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_STR);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("UserModel::deleteUser Error: " . $e->getMessage());
            return false;
        }
    }
}
