<?php
/**
 * User Progress Model
 *
 * Handles database operations for user progress
 */

class UserProgressModel {
    private $db;
    private $table = 'user_progress';

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Get user progress for a specific user and crossword
     *
     * @param string $userId User ID
     * @param string $crosswordId Crossword ID
     * @return array|null User progress data or null if not found
     */
    public function getUserProgress($userId, $crosswordId) {
        try {
            $stmt = $this->db->prepare(
                "SELECT * FROM {$this->table}
                WHERE user_id = :user_id AND crossword_id = :crossword_id"
            );
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':crossword_id', $crosswordId, PDO::PARAM_STR);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) {
                // Parse JSON data
                $result['progress_data'] = json_decode($result['progress_data'], true);

                // Ensure progress_data has the expected structure
                if (!isset($result['progress_data']['userAnswers'])) {
                    $result['progress_data']['userAnswers'] = [];
                }

                // Log the data for debugging
                error_log("User progress data: " . json_encode($result['progress_data']));
            }
            return $result ?: null;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Get all progress entries for a specific user
     *
     * @param string $userId User ID
     * @return array User progress data
     */
    public function getAllUserProgress($userId) {
        try {
            $stmt = $this->db->prepare(
                "SELECT up.*, c.title as crossword_title, c.difficulty, c.slug, c.category_id
                FROM {$this->table} up
                JOIN crosswords c ON up.crossword_id = c.id
                WHERE up.user_id = :user_id
                ORDER BY up.updated_at DESC"
            );
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($results as &$result) {
                // Parse JSON data
                $result['progress_data'] = json_decode($result['progress_data'], true);
            }
            return $results;
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Create or update user progress
     *
     * @param string $userId User ID
     * @param string $crosswordId Crossword ID
     * @param array $progressData Progress data (userAnswers, revealedCells, etc.)
     * @param bool $isCompleted Whether the crossword is completed
     * @param int|null $timeSpent Time spent in seconds
     * @return string ID of the created/updated progress
     */
    public function saveProgress($userId, $crosswordId, $progressData, $isCompleted = false, $timeSpent = null) {
        try {
            // Check if progress already exists
            $existingProgress = $this->getUserProgress($userId, $crosswordId);

            if ($existingProgress) {
                // Update existing progress
                $stmt = $this->db->prepare(
                    "UPDATE {$this->table} SET
                    progress_data = :progress_data,
                    is_completed = :is_completed,
                    time_spent = :time_spent,
                    updated_at = NOW()
                    WHERE user_id = :user_id AND crossword_id = :crossword_id"
                );

                // Ensure progressData has the expected structure
                if (!isset($progressData['userAnswers'])) {
                    $progressData['userAnswers'] = [];
                }

                // Log the data for debugging
                error_log("Saving progress data: " . json_encode($progressData));

                $progressDataJson = json_encode($progressData);
                $isCompletedInt = $isCompleted ? 1 : 0;

                $stmt->bindParam(':progress_data', $progressDataJson, PDO::PARAM_STR);
                $stmt->bindParam(':is_completed', $isCompletedInt, PDO::PARAM_INT);
                $stmt->bindParam(':time_spent', $timeSpent, PDO::PARAM_INT);
                $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
                $stmt->bindParam(':crossword_id', $crosswordId, PDO::PARAM_STR);

                $stmt->execute();
                return $existingProgress['id'];
            } else {
                // Create new progress
                $id = generateUuid();
                $stmt = $this->db->prepare(
                    "INSERT INTO {$this->table} (
                        id, user_id, crossword_id, progress_data, is_completed, time_spent, created_at, updated_at
                    ) VALUES (
                        :id, :user_id, :crossword_id, :progress_data, :is_completed, :time_spent, NOW(), NOW()
                    )"
                );

                // Ensure progressData has the expected structure
                if (!isset($progressData['userAnswers'])) {
                    $progressData['userAnswers'] = [];
                }

                // Log the data for debugging
                error_log("Saving new progress data: " . json_encode($progressData));

                $progressDataJson = json_encode($progressData);
                $isCompletedInt = $isCompleted ? 1 : 0;

                $stmt->bindParam(':id', $id, PDO::PARAM_STR);
                $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
                $stmt->bindParam(':crossword_id', $crosswordId, PDO::PARAM_STR);
                $stmt->bindParam(':progress_data', $progressDataJson, PDO::PARAM_STR);
                $stmt->bindParam(':is_completed', $isCompletedInt, PDO::PARAM_INT);
                $stmt->bindParam(':time_spent', $timeSpent, PDO::PARAM_INT);

                $stmt->execute();
                return $id;
            }
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete user progress
     *
     * @param string $userId User ID
     * @param string $crosswordId Crossword ID
     * @return bool Success status
     */
    public function deleteProgress($userId, $crosswordId) {
        try {
            $stmt = $this->db->prepare(
                "DELETE FROM {$this->table}
                WHERE user_id = :user_id AND crossword_id = :crossword_id"
            );
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':crossword_id', $crosswordId, PDO::PARAM_STR);
            return $stmt->execute();
        } catch (PDOException $e) {
            throw new Exception("Database error: " . $e->getMessage(), 500);
        }
    }
}
