-- Rate limits table for API protection
CREATE TABLE IF NOT EXISTS rate_limits (
    id VARCHAR(36) PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_count INT NOT NULL DEFAULT 1,
    first_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_blocked TINYINT(1) NOT NULL DEFAULT 0,
    block_expiry TIMESTAMP NULL,
    INDEX (ip_address, endpoint)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_rate_limits_ip_endpoint ON rate_limits (ip_address, endpoint);

-- Add index for cleanup operations
CREATE INDEX IF NOT EXISTS idx_rate_limits_last_request_time ON rate_limits (last_request_time);
