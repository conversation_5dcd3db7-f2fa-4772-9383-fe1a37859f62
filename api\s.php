<?php
/**
 * Share link redirect handler
 * 
 * This file handles redirects from short share links to the actual crossword page
 */

// Include required files
require_once __DIR__ . '/config/Database.php';
require_once __DIR__ . '/models/ShareModel.php';

// Get the short code from the URL
$shortCode = isset($_GET['code']) ? $_GET['code'] : '';

if (empty($shortCode)) {
    // Redirect to home page if no short code is provided
    header('Location: /');
    exit;
}

// Create a new ShareModel instance
$shareModel = new ShareModel();

// Get the crossword ID from the short code
$crosswordId = $shareModel->getShareLink($shortCode);

if ($crosswordId) {
    // Redirect to the crossword page
    header('Location: /play/' . $crosswordId);
    exit;
} else {
    // Redirect to home page if the short code is invalid
    header('Location: /');
    exit;
}
