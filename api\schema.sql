-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               8.4.3 - MySQL Community Server - GPL
-- Server OS:                    Win64
-- HeidiSQL Version:             12.8.0.6908
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- =================================================================
-- STEP 1: INDEPENDENT TABLES (No foreign key dependencies)
-- =================================================================

-- 1. User Profiles (Referenced by: api_keys, blog_posts, crosswords, crossword_ratings, user_progress)
CREATE TABLE IF NOT EXISTS `user_profiles` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bio` text COLLATE utf8mb4_unicode_ci,
  `role` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `google_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `auth_provider` enum('email','google') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'email',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Categories (Referenced by: crosswords)
CREATE TABLE IF NOT EXISTS `categories` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Settings (No dependencies)
CREATE TABLE IF NOT EXISTS `settings` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `setting_key` (`setting_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Rate Limits (No dependencies)
CREATE TABLE IF NOT EXISTS `rate_limits` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `endpoint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_count` int NOT NULL DEFAULT '1',
  `first_request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_blocked` tinyint(1) NOT NULL DEFAULT '0',
  `block_expiry` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ip_address` (`ip_address`,`endpoint`),
  KEY `idx_rate_limits_last_request_time` (`last_request_time`),
  KEY `idx_rate_limits_ip_endpoint` (`ip_address`,`endpoint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =================================================================
-- STEP 2: TABLES WITH SINGLE DEPENDENCIES
-- =================================================================

-- 5. API Keys (Depends on: user_profiles)
CREATE TABLE IF NOT EXISTS `api_keys` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `api_key` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_revoked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `last_used_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_key` (`api_key`),
  KEY `user_id` (`user_id`),
  KEY `idx_api_keys_user_id` (`user_id`),
  KEY `idx_api_keys_is_revoked` (`is_revoked`),
  KEY `idx_api_keys_expires_at` (`expires_at`),
  CONSTRAINT `api_keys_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Blog Posts (Depends on: user_profiles)
CREATE TABLE IF NOT EXISTS `blog_posts` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `excerpt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `featured_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `author_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('draft','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `slug` (`slug`) USING BTREE,
  KEY `author_id` (`author_id`),
  CONSTRAINT `blog_posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `user_profiles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. Crosswords (Depends on: user_profiles, categories)
CREATE TABLE IF NOT EXISTS `crosswords` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `grid_size` int NOT NULL DEFAULT '15',
  `grid_data` json NOT NULL,
  `words` json NOT NULL,
  `clues` json NOT NULL,
  `word_positions` json NOT NULL,
  `difficulty` enum('mudah','sedang','sulit') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sedang',
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '1',
  `plays` int NOT NULL DEFAULT '0',
  `rating` decimal(3,1) DEFAULT NULL COMMENT 'Average rating calculated from individual ratings',
  `rating_count` int NOT NULL DEFAULT '0' COMMENT 'Number of users who have rated this crossword',
  `category_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_crosswords_category` (`category_id`),
  KEY `idx_crosswords_user` (`user_id`),
  KEY `idx_crosswords_public` (`is_public`),
  KEY `idx_crosswords_difficulty` (`difficulty`),
  KEY `idx_crosswords_rating_count` (`rating_count`),
  KEY `idx_crosswords_rating_combo` (`rating`,`rating_count`,`is_public`),
  KEY `idx_crosswords_public_rating` (`is_public`,`rating` DESC,`created_at` DESC),
  KEY `idx_crosswords_category_public` (`category_id`,`is_public`,`rating` DESC),
  KEY `idx_crosswords_user_created` (`user_id`,`created_at` DESC),
  KEY `idx_crosswords_slug` (`slug`),
  KEY `idx_crosswords_difficulty_public` (`difficulty`,`is_public`,`rating` DESC),
  CONSTRAINT `fk_crosswords_category_id` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_crosswords_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =================================================================
-- STEP 3: TABLES WITH MULTIPLE DEPENDENCIES
-- =================================================================

-- 8. Share Links (Depends on: crosswords)
CREATE TABLE IF NOT EXISTS `share_links` (
  `id` int NOT NULL AUTO_INCREMENT,
  `short_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `crossword_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `short_code` (`short_code`),
  KEY `crossword_id` (`crossword_id`),
  KEY `short_code_2` (`short_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. Crossword Ratings (Depends on: user_profiles, crosswords)
CREATE TABLE IF NOT EXISTS `crossword_ratings` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `crossword_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rating` tinyint(1) NOT NULL,
  `review` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_crossword_rating` (`user_id`,`crossword_id`),
  KEY `idx_crossword_ratings_crossword` (`crossword_id`),
  KEY `idx_crossword_ratings_user` (`user_id`),
  KEY `idx_crossword_ratings_rating` (`rating`),
  KEY `idx_crossword_ratings_created_at` (`created_at`),
  KEY `idx_crossword_ratings_crossword_created` (`crossword_id`,`created_at` DESC),
  KEY `idx_crossword_ratings_user_crossword` (`user_id`,`crossword_id`),
  CONSTRAINT `fk_crossword_ratings_crossword_id` FOREIGN KEY (`crossword_id`) REFERENCES `crosswords` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_crossword_ratings_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `crossword_ratings_chk_1` CHECK (((`rating` >= 1) and (`rating` <= 5)))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 10. User Progress (Depends on: user_profiles, crosswords)
CREATE TABLE IF NOT EXISTS `user_progress` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `crossword_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `progress_data` json NOT NULL,
  `is_completed` tinyint(1) NOT NULL DEFAULT '0',
  `time_spent` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_progress_user_crossword` (`user_id`,`crossword_id`),
  KEY `idx_user_progress_user` (`user_id`),
  KEY `idx_user_progress_crossword` (`crossword_id`),
  KEY `idx_user_progress_user_updated` (`user_id`,`updated_at` DESC),
  KEY `idx_user_progress_crossword_user` (`crossword_id`,`user_id`),
  CONSTRAINT `fk_user_progress_crossword_id` FOREIGN KEY (`crossword_id`) REFERENCES `crosswords` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_progress_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =================================================================
-- STEP 4: VIEWS AND PROCEDURES
-- =================================================================

-- Crossword Stats View (Depends on: crosswords, crossword_ratings)
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `crossword_stats` AS 
select 
    `c`.`id` AS `id`,
    `c`.`title` AS `title`,
    `c`.`slug` AS `slug`,
    `c`.`rating` AS `rating`,
    `c`.`rating_count` AS `rating_count`,
    `c`.`plays` AS `plays`,
    `c`.`difficulty` AS `difficulty`,
    `c`.`is_public` AS `is_public`,
    `c`.`created_at` AS `created_at`,
    coalesce(avg(`cr`.`rating`),0) AS `calculated_rating`,
    count(`cr`.`id`) AS `calculated_rating_count`,
    count((case when (`cr`.`rating` = 5) then 1 end)) AS `five_star_count`,
    count((case when (`cr`.`rating` = 4) then 1 end)) AS `four_star_count`,
    count((case when (`cr`.`rating` = 3) then 1 end)) AS `three_star_count`,
    count((case when (`cr`.`rating` = 2) then 1 end)) AS `two_star_count`,
    count((case when (`cr`.`rating` = 1) then 1 end)) AS `one_star_count` 
from (`crosswords` `c` left join `crossword_ratings` `cr` on((`c`.`id` = `cr`.`crossword_id`))) 
group by `c`.`id`,`c`.`title`,`c`.`slug`,`c`.`rating`,`c`.`rating_count`,`c`.`plays`,`c`.`difficulty`,`c`.`is_public`,`c`.`created_at`;

-- Update Crossword Rating Procedure
DELIMITER //
CREATE PROCEDURE `UpdateCrosswordRating`(IN p_crossword_id VARCHAR(36))
BEGIN
    DECLARE avg_rating DECIMAL(3,1);
    DECLARE rating_count INT;
    
    SELECT 
        COALESCE(ROUND(AVG(rating), 1), 0),
        COUNT(*)
    INTO avg_rating, rating_count
    FROM crossword_ratings 
    WHERE crossword_id = p_crossword_id;
    
    UPDATE crosswords 
    SET 
        rating = CASE WHEN rating_count > 0 THEN avg_rating ELSE NULL END,
        rating_count = rating_count
    WHERE id = p_crossword_id;
END//
DELIMITER ;

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;