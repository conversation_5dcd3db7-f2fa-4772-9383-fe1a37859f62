<?php
/**
 * <PERSON><PERSON><PERSON> to populate the categories table with initial data
 * PHP version 8.3
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../models/CategoryModel.php';

// Categories to create
$categories = [
    [
        'id' => 'umum',
        'name' => 'Pengetahuan Umum',
        'description' => 'Teka-teki silang dengan tema pengetahuan umum dan berbagai topik.',
        'image_url' => 'https://source.unsplash.com/random/300x200?knowledge'
    ],
    [
        'id' => 'film',
        'name' => 'Film & TV',
        'description' => 'Teka-teki silang dengan tema film, acara TV, aktor, dan dunia hiburan.',
        'image_url' => 'https://source.unsplash.com/random/300x200?movie'
    ],
    [
        'id' => 'sains',
        'name' => 'Sains & Teknologi',
        'description' => 'Teka-teki silang dengan tema sains, teknologi, penemuan, dan inovasi.',
        'image_url' => 'https://source.unsplash.com/random/300x200?science'
    ],
    [
        'id' => 'sejarah',
        'name' => 'Sejarah',
        'description' => 'Teka-teki silang dengan tema sejarah, peristiwa bersejarah, dan tokoh-tokoh penting.',
        'image_url' => 'https://source.unsplash.com/random/300x200?history'
    ],
    [
        'id' => 'olahraga',
        'name' => 'Olahraga',
        'description' => 'Teka-teki silang dengan tema olahraga, atlet, dan kompetisi.',
        'image_url' => 'https://source.unsplash.com/random/300x200?sports'
    ],
    [
        'id' => 'musik',
        'name' => 'Musik',
        'description' => 'Teka-teki silang dengan tema musik, musisi, band, dan lagu-lagu populer.',
        'image_url' => 'https://source.unsplash.com/random/300x200?music'
    ],
    [
        'id' => 'sastra',
        'name' => 'Sastra',
        'description' => 'Teka-teki silang dengan tema sastra, buku, penulis, dan karya-karya terkenal.',
        'image_url' => 'https://source.unsplash.com/random/300x200?literature'
    ],
    [
        'id' => 'kesehatan',
        'name' => 'Kesehatan',
        'description' => 'Teka-teki silang dengan tema kesehatan, anatomi, dan istilah medis.',
        'image_url' => 'https://source.unsplash.com/random/300x200?health'
    ]
];

// Create CategoryModel instance
$categoryModel = new CategoryModel();

// Insert categories
$inserted = 0;
$skipped = 0;

foreach ($categories as $category) {
    try {
        // Check if category already exists
        $existingCategory = $categoryModel->getById($category['id']);
        
        if ($existingCategory) {
            echo "Category '{$category['name']}' already exists, skipping...\n";
            $skipped++;
            continue;
        }
        
        // Insert category
        $categoryModel->create($category);
        echo "Category '{$category['name']}' created successfully.\n";
        $inserted++;
    } catch (Exception $e) {
        echo "Error creating category '{$category['name']}': " . $e->getMessage() . "\n";
    }
}

echo "\nPopulation complete. Inserted: {$inserted}, Skipped: {$skipped}\n";
