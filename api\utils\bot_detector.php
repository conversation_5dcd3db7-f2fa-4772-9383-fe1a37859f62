<?php
/**
 * Bot detection utilities for the Crossword Generator API
 * PHP version 8.3
 */

/**
 * Bot detector class to identify and block scraper bots
 */
class BotDetector {
    // List of known bot user agents (partial matches)
    private $knownBotAgents = [
        'bot', 'crawl', 'spider', 'scrape', 'fetch', 'http', 'java', 'curl', 
        'wget', 'python-requests', 'go-http', 'ruby', 'perl', 'harvest', 
        'extract', 'scan', 'check', 'HeadlessChrome', 'PhantomJS', 'Selenium'
    ];
    
    // List of legitimate bot user agents to allow
    private $allowedBots = [
        'Googlebot', 'Bingbot', 'Slurp', 'DuckDuckBot', 'Baiduspider',
        'YandexBot', 'Sogou', 'facebookexternalhit', 'ia_archiver'
    ];
    
    /**
     * Check if the current request is from a bot
     * 
     * @param bool $allowGoodBots Whether to allow legitimate search engine bots
     * @return bool True if the request is from a bot, false otherwise
     */
    public function isBot($allowGoodBots = true) {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Empty user agent is suspicious
        if (empty($userAgent)) {
            return true;
        }
        
        // Check for allowed bots first
        if ($allowGoodBots) {
            foreach ($this->allowedBots as $bot) {
                if (stripos($userAgent, $bot) !== false) {
                    return false; // This is a legitimate bot
                }
            }
        }
        
        // Check for known bot signatures
        foreach ($this->knownBotAgents as $botAgent) {
            if (stripos($userAgent, $botAgent) !== false) {
                return true;
            }
        }
        
        // Check for suspicious behavior
        return $this->hasSuspiciousBehavior();
    }
    
    /**
     * Check for suspicious behavior that might indicate a bot
     * 
     * @return bool True if suspicious behavior is detected
     */
    private function hasSuspiciousBehavior() {
        // Check for missing or suspicious headers
        if (!isset($_SERVER['HTTP_ACCEPT']) || 
            !isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            return true;
        }
        
        // Check for unusual request patterns
        if (isset($_SERVER['HTTP_REFERER']) && empty($_SERVER['HTTP_REFERER'])) {
            // Missing referer for non-direct navigation can be suspicious
            if (isset($_SERVER['HTTP_CACHE_CONTROL']) && 
                $_SERVER['HTTP_CACHE_CONTROL'] === 'no-cache') {
                return true;
            }
        }
        
        // Check request speed (if session data available)
        if (isset($_SESSION['last_request_time'])) {
            $timeDiff = time() - $_SESSION['last_request_time'];
            if ($timeDiff < 1) { // Less than 1 second between requests
                return true;
            }
        }
        
        // Update last request time
        $_SESSION['last_request_time'] = time();
        
        return false;
    }
    
    /**
     * Get the client's real IP address
     * 
     * @return string The client's IP address
     */
    public static function getClientIp() {
        // Check for proxy headers first
        $headers = [
            'HTTP_CF_CONNECTING_IP', // Cloudflare
            'HTTP_X_FORWARDED_FOR',  // Common proxy header
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'            // Direct connection
        ];
        
        foreach ($headers as $header) {
            if (isset($_SERVER[$header])) {
                // For X-Forwarded-For, get the first IP in the list
                if ($header === 'HTTP_X_FORWARDED_FOR') {
                    $ips = explode(',', $_SERVER[$header]);
                    $ip = trim($ips[0]);
                } else {
                    $ip = $_SERVER[$header];
                }
                
                // Validate the IP
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        // Default fallback
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Generate a browser fingerprint based on request data
     * 
     * @return string A hash representing the browser fingerprint
     */
    public function generateFingerprint() {
        $data = [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'accept' => $_SERVER['HTTP_ACCEPT'] ?? '',
            'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            'accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            'ip' => self::getClientIp(),
            // Don't include screen resolution or other JS-dependent values
        ];
        
        return hash('sha256', json_encode($data));
    }
}
