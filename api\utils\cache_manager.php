<?php
/**
 * API Response Caching for the Crossword Generator API
 * PHP version 8.3
 *
 * Optimized cache manager with memory caching and improved file handling
 */

/**
 * Cache Manager class to handle API response caching
 */
class CacheManager {
    // Cache directory
    private $cacheDir;

    // Default TTL in seconds
    private $defaultTtl;

    // Excluded endpoints
    private $excludedEndpoints;

    // In-memory cache for frequently accessed items
    private $memoryCache = [];

    // Cache hit counter for statistics
    private $cacheHits = 0;

    // Cache miss counter for statistics
    private $cacheMisses = 0;

    // Custom TTLs for specific endpoints
    private $customTtls = [];

    /**
     * Constructor
     *
     * @param string $cacheDir Directory to store cache files
     * @param int $defaultTtl Default cache time-to-live in seconds
     * @param array $excludedEndpoints Endpoints to exclude from caching
     * @param array $customTtls Custom TTLs for specific endpoints
     */
    public function __construct($cacheDir, $defaultTtl = 3600, $excludedEndpoints = [], $customTtls = []) {
        $this->cacheDir = $cacheDir;
        $this->defaultTtl = $defaultTtl;
        $this->excludedEndpoints = $excludedEndpoints;
        $this->customTtls = $customTtls;

        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }

        // Create subdirectories for better organization
        $this->createSubdirectories();
    }

    /**
     * Create subdirectories for better cache organization
     */
    private function createSubdirectories() {
        // Create subdirectories for different types of cached content
        $subdirs = ['crosswords', 'categories', 'blogs', 'users', 'misc'];
        foreach ($subdirs as $dir) {
            $path = $this->cacheDir . '/' . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }

    /**
     * Generate a cache key for the current request
     *
     * @return string The cache key
     */
    public function generateCacheKey() {
        $uri = $_SERVER['REQUEST_URI'];
        $method = $_SERVER['REQUEST_METHOD'];

        // Extract the endpoint type for subdirectory organization
        $endpointType = $this->getEndpointType($uri);

        // For GET requests, include query parameters in the cache key
        if ($method === 'GET') {
            $key = $method . '_' . $uri;
        } else {
            // For other methods, include the request body in the cache key
            $body = file_get_contents('php://input');
            $key = $method . '_' . $uri . '_' . md5($body);
        }

        // Add user ID to cache key if user is logged in (for personalized content)
        if (isset($_SESSION['user_id'])) {
            $key .= '_user_' . $_SESSION['user_id'];
        }

        // Add language if available
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
            $key .= '_lang_' . $lang;
        }

        return $endpointType . '/' . md5($key);
    }

    /**
     * Determine the endpoint type for better cache organization
     *
     * @param string $uri The request URI
     * @return string The endpoint type (subdirectory)
     */
    private function getEndpointType($uri) {
        if (strpos($uri, '/api/crosswords') === 0) {
            return 'crosswords';
        } elseif (strpos($uri, '/api/categories') === 0) {
            return 'categories';
        } elseif (strpos($uri, '/api/blogs') === 0 || strpos($uri, '/api/blog') === 0) {
            return 'blogs';
        } elseif (strpos($uri, '/api/users') === 0 || strpos($uri, '/api/user') === 0) {
            return 'users';
        } else {
            return 'misc';
        }
    }

    /**
     * Check if the current request should be cached
     *
     * @return bool True if the request should be cached
     */
    public function shouldCache() {
        // Only cache GET requests
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            return false;
        }

        // Check if the endpoint is excluded
        $uri = $_SERVER['REQUEST_URI'];
        foreach ($this->excludedEndpoints as $endpoint) {
            if (strpos($uri, $endpoint) === 0) {
                return false;
            }
        }

        // Don't cache requests with specific query parameters that might indicate dynamic content
        if (isset($_GET['nocache']) || isset($_GET['refresh']) || isset($_GET['timestamp'])) {
            return false;
        }

        // Don't cache admin requests
        if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
            return false;
        }

        return true;
    }

    /**
     * Get the TTL for a specific endpoint
     *
     * @param string $uri The request URI
     * @return int The TTL in seconds
     */
    private function getTtlForEndpoint($uri) {
        // Check if there's a custom TTL for this endpoint
        foreach ($this->customTtls as $pattern => $ttl) {
            if (strpos($uri, $pattern) === 0) {
                return $ttl;
            }
        }

        // Use default TTL based on endpoint type
        if (strpos($uri, '/api/crosswords') === 0) {
            return 3600; // 1 hour for crosswords
        } elseif (strpos($uri, '/api/categories') === 0) {
            return 7200; // 2 hours for categories
        } elseif (strpos($uri, '/api/blogs') === 0) {
            return 86400; // 24 hours for blogs
        } elseif (strpos($uri, '/api/featured') === 0) {
            return 1800; // 30 minutes for featured content
        }

        return $this->defaultTtl;
    }

    /**
     * Get the cache file path for a cache key
     *
     * @param string $key The cache key
     * @return string The cache file path
     */
    private function getCacheFilePath($key) {
        return $this->cacheDir . '/' . $key . '.cache';
    }

    /**
     * Check if a cached response exists and is valid
     *
     * @param string $key The cache key
     * @return bool True if a valid cache exists
     */
    public function hasValidCache($key) {
        // First check in-memory cache
        if (isset($this->memoryCache[$key])) {
            $cacheData = $this->memoryCache[$key];
            if ($cacheData['expiry'] > time()) {
                $this->cacheHits++;
                return true;
            }
            // Remove expired in-memory cache
            unset($this->memoryCache[$key]);
        }

        $cacheFile = $this->getCacheFilePath($key);

        if (!file_exists($cacheFile)) {
            $this->cacheMisses++;
            return false;
        }

        // Get the URI from the request to determine TTL
        $uri = $_SERVER['REQUEST_URI'];
        $ttl = $this->getTtlForEndpoint($uri);

        // Check if cache has expired
        $cacheTime = filemtime($cacheFile);
        $expiryTime = $cacheTime + $ttl;

        $isValid = $expiryTime > time();

        if ($isValid) {
            $this->cacheHits++;
        } else {
            $this->cacheMisses++;
        }

        return $isValid;
    }

    /**
     * Get a cached response
     *
     * @param string $key The cache key
     * @return string|null The cached response or null if not found
     */
    public function getCache($key) {
        // First check in-memory cache
        if (isset($this->memoryCache[$key]) && $this->memoryCache[$key]['expiry'] > time()) {
            return $this->memoryCache[$key]['data'];
        }

        $cacheFile = $this->getCacheFilePath($key);

        if (!$this->hasValidCache($key)) {
            return null;
        }

        $data = file_get_contents($cacheFile);

        // Store in memory cache for faster access next time
        $uri = $_SERVER['REQUEST_URI'];
        $ttl = $this->getTtlForEndpoint($uri);
        $this->memoryCache[$key] = [
            'data' => $data,
            'expiry' => time() + $ttl
        ];

        return $data;
    }

    /**
     * Save a response to the cache
     *
     * @param string $key The cache key
     * @param string $data The response data to cache
     * @return bool True if the cache was saved successfully
     */
    public function setCache($key, $data) {
        $cacheFile = $this->getCacheFilePath($key);

        // Get the URI from the request to determine TTL
        $uri = $_SERVER['REQUEST_URI'];
        $ttl = $this->getTtlForEndpoint($uri);

        // Store in memory cache for faster access
        $this->memoryCache[$key] = [
            'data' => $data,
            'expiry' => time() + $ttl
        ];

        // Ensure directory exists
        $dir = dirname($cacheFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        // Write to file with locking to prevent race conditions
        $result = file_put_contents($cacheFile, $data, LOCK_EX);

        // Set file permissions
        if ($result !== false) {
            chmod($cacheFile, 0644);
        }

        return $result !== false;
    }

    /**
     * Clear the entire cache or a specific cache entry
     *
     * @param string|null $key The cache key to clear, or null to clear all
     * @param string|null $pattern A pattern to match cache keys to clear
     * @return bool True if the cache was cleared successfully
     */
    public function clearCache($key = null, $pattern = null) {
        // Clear memory cache
        if ($key !== null) {
            if (isset($this->memoryCache[$key])) {
                unset($this->memoryCache[$key]);
            }

            $cacheFile = $this->getCacheFilePath($key);
            if (file_exists($cacheFile)) {
                return unlink($cacheFile);
            }
            return true;
        }

        // Clear by pattern
        if ($pattern !== null) {
            // Clear matching memory cache entries
            foreach ($this->memoryCache as $cacheKey => $value) {
                if (strpos($cacheKey, $pattern) !== false) {
                    unset($this->memoryCache[$cacheKey]);
                }
            }

            // Clear matching file cache entries
            $files = $this->findCacheFilesByPattern($pattern);
            foreach ($files as $file) {
                unlink($file);
            }

            return true;
        }

        // Clear all cache
        $this->memoryCache = [];

        // Clear all cache files in all subdirectories
        $subdirs = ['crosswords', 'categories', 'blogs', 'users', 'misc'];
        foreach ($subdirs as $dir) {
            $path = $this->cacheDir . '/' . $dir;
            if (is_dir($path)) {
                $files = glob($path . '/*.cache');
                foreach ($files as $file) {
                    unlink($file);
                }
            }
        }

        // Also clear any files in the root cache directory
        $files = glob($this->cacheDir . '/*.cache');
        foreach ($files as $file) {
            unlink($file);
        }

        return true;
    }

    /**
     * Find cache files by pattern
     *
     * @param string $pattern The pattern to match
     * @return array Array of matching file paths
     */
    private function findCacheFilesByPattern($pattern) {
        $result = [];

        // Search in all subdirectories
        $subdirs = ['crosswords', 'categories', 'blogs', 'users', 'misc'];
        foreach ($subdirs as $dir) {
            $path = $this->cacheDir . '/' . $dir;
            if (is_dir($path)) {
                $files = glob($path . '/*.cache');
                foreach ($files as $file) {
                    // Check if file content contains the pattern
                    $content = file_get_contents($file);
                    if (strpos($content, $pattern) !== false) {
                        $result[] = $file;
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function getStats() {
        return [
            'hits' => $this->cacheHits,
            'misses' => $this->cacheMisses,
            'memory_cache_size' => count($this->memoryCache),
            'file_cache_size' => $this->countCacheFiles(),
            'hit_ratio' => ($this->cacheHits + $this->cacheMisses > 0)
                ? round(($this->cacheHits / ($this->cacheHits + $this->cacheMisses)) * 100, 2)
                : 0
        ];
    }

    /**
     * Count the number of cache files
     *
     * @return int Number of cache files
     */
    private function countCacheFiles() {
        $count = 0;

        // Count files in all subdirectories
        $subdirs = ['crosswords', 'categories', 'blogs', 'users', 'misc'];
        foreach ($subdirs as $dir) {
            $path = $this->cacheDir . '/' . $dir;
            if (is_dir($path)) {
                $files = glob($path . '/*.cache');
                $count += count($files);
            }
        }

        // Also count any files in the root cache directory
        $files = glob($this->cacheDir . '/*.cache');
        $count += count($files);

        return $count;
    }
}
