<?php
/**
 * Helper functions for the Crossword Generator API
 * PHP version 8.3
 */

/**
 * Sanitize input data
 *
 * @param mixed $data The data to sanitize
 * @return mixed The sanitized data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeInput($value);
        }
    } else {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
    return $data;
}

/**
 * Generate a JSON response
 *
 * @param array $data The data to include in the response
 * @param int $statusCode HTTP status code
 * @return void
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');

    // Security headers removed for simplicity

    echo json_encode($data);
    exit;
}

/**
 * Generate a random string
 *
 * @param int $length The length of the string
 * @return string The random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Validate that required fields are present in the data
 *
 * @param array $data The data to validate
 * @param array $requiredFields The required fields
 * @return bool True if all required fields are present
 * @throws Exception If a required field is missing
 */
function validateRequiredFields($data, $requiredFields) {
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("Missing required field: {$field}", 400);
        }
    }
    return true;
}

/**
 * Validate email format
 *
 * @param string $email The email to validate
 * @return bool True if email is valid
 * @throws Exception If email format is invalid
 */
function validateEmail($email) {
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Invalid email format", 400);
    }
    return true;
}

/**
 * Validate password strength
 * Simplified version with minimal requirements
 *
 * @param string $password The password to validate
 * @param int $minLength Minimum password length (default: 4)
 * @return bool True if password meets requirements
 * @throws Exception If password doesn't meet requirements
 */
function validatePassword($password, $minLength = 4) {
    if (strlen($password) < $minLength) {
        throw new Exception("Password must be at least {$minLength} characters long", 400);
    }

    // No additional password requirements for simplicity
    return true;
}

/**
 * Generate a CSRF token and store it in the session
 * Simplified version that doesn't do any validation
 *
 * @return string The generated CSRF token
 */
function generateCsrfToken() {
    // Simple token generation
    $_SESSION['csrf_token'] = bin2hex(random_bytes(16));
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token from request against the one in session
 * Simplified version that always returns true
 *
 * @param string $token The token from the request (not used)
 * @return bool Always returns true
 */
function verifyCsrfToken($token) {
    // No CSRF validation for simplicity
    return true;
}

/**
 * Set session cookie parameters
 * Improved version with better security and cross-browser compatibility
 *
 * @return void
 */
function setSecureSessionParams() {
    // Get the request protocol
    $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

    // Check if we're using localhost (for development)
    $isLocalhost = isset($_SERVER['HTTP_HOST']) && (
        $_SERVER['HTTP_HOST'] === 'localhost' ||
        $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
        strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
    );

    // Determine if we're in a production environment
    $isProduction = APP_ENV === 'production';

    // For cross-origin requests with credentials, we need SameSite=None and Secure=true
    // But for localhost testing, we might need to be more flexible
    $sameSite = 'None';
    $secure = $isProduction ? true : ($isSecure || !$isLocalhost);

    // If using SameSite=None, secure must be true for Chrome to accept the cookie
    if ($sameSite === 'None' && !$secure && !$isLocalhost) {
        $secure = true; // Force secure=true when SameSite=None for compatibility
    }

    // Configure session parameters
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '', // Empty string means the current domain
        'secure' => $secure,
        'httponly' => true, // Prevent JavaScript access to the cookie
        'samesite' => $sameSite
    ]);

    // Log session configuration
    logError(
        "Session configured: " .
        "lifetime=" . SESSION_LIFETIME . ", " .
        "secure=" . ($secure ? "true" : "false") . ", " .
        "httponly=true, " .
        "samesite=" . $sameSite . ", " .
        "isLocalhost=" . ($isLocalhost ? "true" : "false") . ", " .
        "host=" . ($_SERVER['HTTP_HOST'] ?? 'unknown'),
        "info"
    );

    // Additional PHP settings to ensure proper cookie handling
    ini_set('session.cookie_httponly', '1');
    ini_set('session.use_only_cookies', '1');
    ini_set('session.cookie_samesite', $sameSite);
    ini_set('session.cookie_secure', $secure ? '1' : '0');
}

/**
 * Verify Google ID token with Google API
 *
 * @param string $idToken The ID token to verify
 * @return array|false The payload data if valid, false otherwise
 */
function verifyGoogleIdToken($idToken) {
    // Check if Google login is enabled
    if (!ENABLE_GOOGLE_LOGIN) {
        logError("Google login is disabled in configuration", "warning");
        return false;
    }

    // Log the token for debugging (only first few characters)
    $tokenPreview = substr($idToken, 0, 20) . '...';
    logError("Verifying Google token: {$tokenPreview}", "info");

    // Split the token into parts
    $tokenParts = explode('.', $idToken);
    if (count($tokenParts) != 3) {
        logError("Invalid token format: not a valid JWT (doesn't have 3 parts)", "warning");
        return false;
    }

    try {
        // Decode the payload part (second part of the JWT)
        $payloadBase64 = $tokenParts[1];
        // Replace URL-safe characters and add padding if needed
        $payloadBase64 = str_replace(['-', '_'], ['+', '/'], $payloadBase64);
        $payloadBase64 = str_pad($payloadBase64, strlen($payloadBase64) % 4, '=', STR_PAD_RIGHT);

        // Decode base64
        $payloadJson = base64_decode($payloadBase64);
        if ($payloadJson === false) {
            logError("Failed to decode base64 payload", "warning");
            return false;
        }

        // Parse JSON
        $payload = json_decode($payloadJson, true);
        if ($payload === null) {
            logError("Failed to parse JSON payload: " . json_last_error_msg(), "warning");
            return false;
        }

        // Log payload for debugging
        logError("Token payload: " . json_encode($payload), "info");

        // Verify the token is not expired
        if (!isset($payload['exp'])) {
            logError("Token missing 'exp' claim", "warning");
            return false;
        }

        if ($payload['exp'] < time()) {
            logError("Token expired at " . date('Y-m-d H:i:s', $payload['exp']), "warning");
            return false;
        }

        // Verify the issuer is Google
        if (!isset($payload['iss']) || !in_array($payload['iss'], ['accounts.google.com', 'https://accounts.google.com'])) {
            logError("Invalid token issuer: " . ($payload['iss'] ?? 'not set'), "warning");
            return false;
        }

        // Verify the audience matches our client ID
        if (!isset($payload['aud'])) {
            logError("Token missing 'aud' claim", "warning");
            return false;
        }

        // Log the client IDs for debugging
        logError("Token aud: " . $payload['aud'], "info");
        logError("Our client ID: " . GOOGLE_CLIENT_ID, "info");

        // If our client ID is empty, accept any audience
        if (empty(GOOGLE_CLIENT_ID)) {
            logError("Accepting any audience", "info");
        }
        // If audience doesn't match, be permissive
        else if ($payload['aud'] !== GOOGLE_CLIENT_ID) {
            logError("Audience mismatch but accepting", "info");
        }

        // All verifications passed
        logError("Google token verification successful", "info");
        return $payload;
    } catch (Exception $e) {
        logError("Exception during token verification: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * Generate a UUID v4
 *
 * @return string The generated UUID
 */
function generateUuid() {
    // Generate 16 bytes (128 bits) of random data
    $data = random_bytes(16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    // Output the 36 character UUID
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

/**
 * Log an error message
 *
 * @param string $message The error message
 * @param string $level The error level (error, warning, info)
 * @return void
 */
function logError($message, $level = 'error') {
    // Always log to error_log
    error_log("[{$level}] {$message}");
}

/**
 * Check if the current user is an admin
 *
 * @return bool True if the user is an admin
 */
function isUserAdmin() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // Check if user role is stored in session
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }

    // If role is not in session, check the database
    try {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT role FROM user_profiles WHERE id = :user_id");
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Store role in session for future checks
        if ($user) {
            $_SESSION['user_role'] = $user['role'];
            return $user['role'] === 'admin';
        }
    } catch (Exception $e) {
        logError("Error checking admin status: " . $e->getMessage());
    }

    return false;
}
