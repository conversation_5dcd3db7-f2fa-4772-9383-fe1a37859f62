<?php
/**
 * Rate limiting and bot protection for the Crossword Generator API
 * PHP version 8.3
 */

/**
 * Rate limiter class to prevent abuse and bot scraping
 */
class RateLimiter {
    // Database connection
    private $db;
    
    // Rate limit settings
    private $windowSeconds;
    private $maxRequests;
    private $tableName = 'rate_limits';
    
    /**
     * Constructor
     * 
     * @param PDO $db Database connection
     * @param int $windowSeconds Time window in seconds
     * @param int $maxRequests Maximum requests allowed in the time window
     */
    public function __construct($db, $windowSeconds = 60, $maxRequests = 60) {
        $this->db = $db;
        $this->windowSeconds = $windowSeconds;
        $this->maxRequests = $maxRequests;
        
        // Ensure the rate limits table exists
        $this->createTableIfNotExists();
    }
    
    /**
     * Create the rate limits table if it doesn't exist
     * 
     * @return void
     */
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->tableName} (
            id VARCHAR(36) PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            endpoint VARCHAR(255) NOT NULL,
            request_count INT NOT NULL DEFAULT 1,
            first_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_request_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            is_blocked TINYINT(1) NOT NULL DEFAULT 0,
            block_expiry TIMESTAMP NULL,
            INDEX (ip_address, endpoint)
        )";
        
        try {
            $this->db->exec($sql);
        } catch (PDOException $e) {
            logError("Failed to create rate limits table: " . $e->getMessage());
        }
    }
    
    /**
     * Check if a request is allowed based on rate limits
     * 
     * @param string $ipAddress The client IP address
     * @param string $endpoint The API endpoint being accessed
     * @return bool True if request is allowed, false if rate limited
     */
    public function isAllowed($ipAddress, $endpoint) {
        // Clean up old records first
        $this->cleanupOldRecords();
        
        // Check if IP is currently blocked
        if ($this->isBlocked($ipAddress)) {
            return false;
        }
        
        // Get current rate limit record
        $record = $this->getRateLimitRecord($ipAddress, $endpoint);
        
        if (!$record) {
            // First request from this IP for this endpoint
            $this->createRateLimitRecord($ipAddress, $endpoint);
            return true;
        }
        
        // Check if we're still in the time window
        $currentTime = time();
        $firstRequestTime = strtotime($record['first_request_time']);
        $timeDiff = $currentTime - $firstRequestTime;
        
        if ($timeDiff > $this->windowSeconds) {
            // Time window has passed, reset the record
            $this->resetRateLimitRecord($record['id']);
            return true;
        }
        
        // Check if request count exceeds the limit
        if ($record['request_count'] >= $this->maxRequests) {
            // Rate limit exceeded, block the IP temporarily
            $this->blockIp($ipAddress);
            return false;
        }
        
        // Increment the request count
        $this->incrementRequestCount($record['id']);
        return true;
    }
    
    /**
     * Get rate limit record for an IP and endpoint
     * 
     * @param string $ipAddress The client IP address
     * @param string $endpoint The API endpoint
     * @return array|null The rate limit record or null if not found
     */
    private function getRateLimitRecord($ipAddress, $endpoint) {
        $sql = "SELECT * FROM {$this->tableName} 
                WHERE ip_address = :ip_address 
                AND endpoint = :endpoint 
                AND is_blocked = 0";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':ip_address' => $ipAddress,
                ':endpoint' => $endpoint
            ]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            logError("Failed to get rate limit record: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create a new rate limit record
     * 
     * @param string $ipAddress The client IP address
     * @param string $endpoint The API endpoint
     * @return void
     */
    private function createRateLimitRecord($ipAddress, $endpoint) {
        $sql = "INSERT INTO {$this->tableName} 
                (id, ip_address, endpoint, request_count, first_request_time, last_request_time) 
                VALUES (:id, :ip_address, :endpoint, 1, NOW(), NOW())";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':id' => generateUuid(),
                ':ip_address' => $ipAddress,
                ':endpoint' => $endpoint
            ]);
        } catch (PDOException $e) {
            logError("Failed to create rate limit record: " . $e->getMessage());
        }
    }
    
    /**
     * Reset a rate limit record
     * 
     * @param string $id The record ID
     * @return void
     */
    private function resetRateLimitRecord($id) {
        $sql = "UPDATE {$this->tableName} 
                SET request_count = 1, 
                    first_request_time = NOW(), 
                    last_request_time = NOW() 
                WHERE id = :id";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            logError("Failed to reset rate limit record: " . $e->getMessage());
        }
    }
    
    /**
     * Increment the request count for a record
     * 
     * @param string $id The record ID
     * @return void
     */
    private function incrementRequestCount($id) {
        $sql = "UPDATE {$this->tableName} 
                SET request_count = request_count + 1, 
                    last_request_time = NOW() 
                WHERE id = :id";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            logError("Failed to increment request count: " . $e->getMessage());
        }
    }
    
    /**
     * Check if an IP is currently blocked
     * 
     * @param string $ipAddress The IP address to check
     * @return bool True if blocked, false otherwise
     */
    private function isBlocked($ipAddress) {
        $sql = "SELECT * FROM {$this->tableName} 
                WHERE ip_address = :ip_address 
                AND is_blocked = 1 
                AND block_expiry > NOW()";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':ip_address' => $ipAddress]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            logError("Failed to check if IP is blocked: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Block an IP address temporarily
     * 
     * @param string $ipAddress The IP address to block
     * @param int $blockMinutes Number of minutes to block the IP
     * @return void
     */
    private function blockIp($ipAddress, $blockMinutes = 15) {
        $sql = "UPDATE {$this->tableName} 
                SET is_blocked = 1, 
                    block_expiry = DATE_ADD(NOW(), INTERVAL :block_minutes MINUTE) 
                WHERE ip_address = :ip_address";
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':ip_address' => $ipAddress,
                ':block_minutes' => $blockMinutes
            ]);
            
            logError("IP address {$ipAddress} blocked for {$blockMinutes} minutes due to rate limiting", "warning");
        } catch (PDOException $e) {
            logError("Failed to block IP: " . $e->getMessage());
        }
    }
    
    /**
     * Clean up old rate limit records
     * 
     * @return void
     */
    private function cleanupOldRecords() {
        // Remove records older than 24 hours
        $sql = "DELETE FROM {$this->tableName} 
                WHERE last_request_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        
        try {
            $this->db->exec($sql);
        } catch (PDOException $e) {
            logError("Failed to clean up old rate limit records: " . $e->getMessage());
        }
    }
}
