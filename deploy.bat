@echo off
REM Crossword Generator Deployment Script for Windows
REM This script builds the frontend and prepares it for deployment to a live hosting environment

echo Starting deployment process...

REM 1. Check if we're in the right directory
if not exist "package.json" (
    echo Error: package.json not found. Make sure you're in the project root directory.
    exit /b 1
)

REM 2. Install dependencies
echo Installing dependencies...
call npm install

REM 3. Build the frontend for production
echo Building frontend for production...
call npm run build

REM 4. Copy .htaccess to the dist folder
echo Copying .htaccess to dist folder...
copy public\.htaccess dist\

REM 5. Create a deployment directory if it doesn't exist
if not exist "deploy" mkdir deploy

REM 6. Create a deployment package
echo Creating deployment package...
if exist "deploy\frontend.zip" del /f /q "deploy\frontend.zip"
powershell -command "Compress-Archive -Path dist\* -DestinationPath deploy\frontend.zip -Force"

REM 7. Copy the API folder to the deployment directory
echo Preparing API for deployment...
if exist "deploy\api" rmdir /s /q "deploy\api"
mkdir "deploy\api"
xcopy /e /i /y api\* deploy\api\

REM 8. Create a .env.production file in the API folder if it doesn't exist
if not exist "deploy\api\.env.production" (
    echo Creating .env.production file for the API...
    copy .env.production deploy\api\.env.production
)

REM 9. Create a deployment package for the API
echo Creating API deployment package...
cd deploy
if exist "api.zip" del /f /q "api.zip"
powershell -command "Compress-Archive -Path api\* -DestinationPath api.zip -Force"
cd ..

echo Deployment preparation complete!
echo Frontend package: deploy\frontend.zip
echo API package: deploy\api.zip
echo.
echo To deploy to your hosting:
echo 1. Upload and extract frontend.zip to your web root directory
echo 2. Upload and extract api.zip to your API directory (usually a subdirectory of web root)
echo 3. Configure your database and update the .env.production file on the server
echo 4. Set the appropriate file permissions on the server
echo.
echo For cPanel hosting:
echo 1. Upload the zip files via File Manager
echo 2. Extract them to the appropriate directories
echo 3. Update the configuration files as needed

pause
