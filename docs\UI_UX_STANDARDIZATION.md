# Standardisasi UI/UX

Dokumen ini menjelaskan standar UI/UX yang digunakan dalam aplikasi Teka-<PERSON><PERSON>, termasuk loading state, error handling, dan feedback visual untuk operasi asinkron.

## Komponen UI Standar

### 1. Loading States

Aplikasi menggunakan beberapa jenis loading state yang konsisten:

#### LoadingSpinner

Komponen spinner loading standar dengan beberapa ukuran:

```tsx
<LoadingSpinner size="md" text="Memuat..." />
```

- **Props**:
  - `size`: 'sm' | 'md' | 'lg' (default: 'md')
  - `text`: Teks yang ditampilkan di bawah spinner (default: 'Memuat...')
  - `fullPage`: Boolean untuk menampilkan spinner di tengah halaman (default: false)
  - `className`: Kelas CSS tambahan

#### ContentSkeleton

Skeleton loader untuk konten teks:

```tsx
<ContentSkeleton rows={3} />
```

- **Props**:
  - `rows`: Jumlah baris skeleton (default: 3)
  - `className`: Kelas CSS tambahan

#### CardSkeleton

Skeleton loader untuk kartu/card:

```tsx
<CardSkeleton count={4} />
```

- **Props**:
  - `count`: Jumlah kartu skeleton (default: 1)
  - `className`: Kelas CSS tambahan

### 2. Feedback Messages

#### FeedbackMessage

Komponen pesan feedback standar dengan beberapa tipe:

```tsx
<FeedbackMessage type="success" message="Operasi berhasil" />
```

- **Props**:
  - `type`: 'success' | 'error' | 'info' | 'warning' (default: 'info')
  - `message`: Pesan yang ditampilkan
  - `className`: Kelas CSS tambahan
  - `onClose`: Fungsi callback saat tombol close diklik

#### EmptyState

Komponen untuk menampilkan state kosong:

```tsx
<EmptyState message="Tidak ada data yang tersedia" />
```

- **Props**:
  - `message`: Pesan yang ditampilkan (default: 'Tidak ada data yang tersedia')
  - `className`: Kelas CSS tambahan

### 3. Toast Notifications

Aplikasi menggunakan `react-hot-toast` untuk notifikasi toast:

```tsx
import { toastUtils } from '../components/ui/ToastProvider';

// Menampilkan toast sukses
toastUtils.success('Operasi berhasil');

// Menampilkan toast error
toastUtils.error('Terjadi kesalahan');

// Menampilkan toast info
toastUtils.info('Informasi penting');

// Menampilkan toast loading
const toastId = toastUtils.loading('Memproses...');

// Update toast yang sudah ada
toastUtils.update(toastId, 'Proses selesai!', 'success');

// Menutup toast
toastUtils.dismiss(toastId);

// Menutup semua toast
toastUtils.dismissAll();
```

## Hooks untuk Operasi Asinkron

### useAsyncOperation

Hook untuk menangani operasi asinkron dengan loading state dan error handling:

```tsx
const { loading, error, data, execute } = useAsyncOperation(
  asyncFunction,
  {
    showLoadingToast: true,
    showSuccessToast: true,
    loadingMessage: 'Memuat...',
    successMessage: 'Operasi berhasil',
    errorMessage: 'Terjadi kesalahan'
  }
);
```

- **Parameters**:
  - `asyncFunction`: Fungsi asinkron yang akan dieksekusi
  - `options`: Konfigurasi untuk toast notifications

- **Returns**:
  - `loading`: Boolean yang menunjukkan apakah operasi sedang berjalan
  - `error`: String error message jika operasi gagal, null jika tidak ada error
  - `data`: Data hasil operasi
  - `execute`: Fungsi untuk mengeksekusi operasi asinkron

### withAsyncFeedback

Utility function untuk membungkus fungsi asinkron dengan feedback toast:

```tsx
const handleWithFeedback = withAsyncFeedback(
  asyncFunction,
  {
    loadingMessage: 'Memproses...',
    successMessage: 'Operasi berhasil',
    errorMessage: 'Terjadi kesalahan'
  }
);

// Penggunaan
await handleWithFeedback(param1, param2);
```

## Standar Error Handling

### API Error Handling

Aplikasi menggunakan pendekatan standar untuk menangani error API:

1. **fetchWithErrorHandling**: Fungsi fetch standar dengan error handling
2. **handleApiResponse**: Fungsi untuk menangani respons API

```tsx
// Contoh penggunaan
export const getDataFromApi = async (): Promise<DataType> => {
  const apiResponse = await fetchWithErrorHandling<DataType>(
    `${API_BASE_URL}/api/endpoint`,
    {
      credentials: 'include',
      headers: getHeaders()
    },
    true // Show error toast
  );
  
  return handleApiResponse(apiResponse, 'Pesan error kustom');
};
```

## Contoh Implementasi

Lihat file `src/components/ui/ExampleUsage.tsx` untuk contoh penggunaan komponen UI standar.

## Panduan Penggunaan

### Loading States

- Gunakan `LoadingSpinner` untuk loading state sederhana
- Gunakan `ContentSkeleton` untuk loading state konten teks
- Gunakan `CardSkeleton` untuk loading state kartu/card

### Error Handling

- Gunakan `FeedbackMessage` dengan `type="error"` untuk menampilkan pesan error
- Gunakan `toastUtils.error()` untuk menampilkan toast error

### Feedback Visual

- Gunakan `toastUtils` untuk menampilkan feedback toast
- Gunakan `FeedbackMessage` dengan `type="success"` untuk menampilkan pesan sukses
- Gunakan `EmptyState` untuk menampilkan state kosong

### Operasi Asinkron

- Gunakan `useAsyncOperation` untuk menangani operasi asinkron dengan loading state dan error handling
- Gunakan `withAsyncFeedback` untuk membungkus fungsi asinkron dengan feedback toast
