-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               8.4.3 - MySQL Community Server - GPL
-- Server OS:                    Win64
-- HeidiSQL Version:             12.8.0.6908
-- --------------------------------------------------------

-- Dumping structure for table crosswords_db.blog_posts
CREATE TABLE IF NOT EXISTS `blog_posts` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `excerpt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `featured_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `author_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('draft','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `slug` (`slug`) USING BTREE,
  KEY `author_id` (`author_id`),
  CONSTRAINT `blog_posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `user_profiles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.blog_posts: ~3 rows (approximately)
REPLACE INTO `blog_posts` (`id`, `title`, `slug`, `content`, `excerpt`, `featured_image`, `author_id`, `status`, `created_at`, `updated_at`) VALUES
	('220e8b63-456a-4ada-aa42-6183279f156a', 'TTS Level Sulit? Ini Dia 10 Strategi Jitu Menaklukkannya!', 'menaklukan-tts-level-sulit', '<p>Pernahkah Anda merasa frustrasi saat mencoba menyelesaikan TTS (Teka-Teki Silang) level sulit? Kata-kata yang terasa asing, clue yang membingungkan, dan perasaan terjebak adalah hal yang umum dialami. Jangan khawatir! Anda tidak sendirian. Artikel ini akan menjadi panduan lengkap Anda untuk <strong>menaklukkan TTS level sulit</strong>, dilengkapi dengan <strong>strategi tts level sulit</strong>, <strong>tips dan trik tts</strong>, serta informasi tentang <strong>aplikasi pembantu tts</strong> yang bisa menjadi penyelamat Anda. Mari kita ubah <strong>tts susah</strong> menjadi tantangan yang menyenangkan!</p>\n<h2>Mengapa TTS Level Sulit Terasa Sangat Menantang?</h2>\n<p>TTS level sulit memang dirancang untuk menguji kemampuan berpikir, pengetahuan umum, dan kosakata Anda. Namun, ada beberapa faktor spesifik yang membuatnya terasa begitu menantang.</p>\n<h3>Jenis-jenis Soal TTS yang Paling Membuat Frustrasi</h3>\n<ul>\n<li><strong>Soal dengan Clue Ambigu:</strong> Clue yang tidak jelas atau memiliki banyak interpretasi bisa sangat membingungkan. Contohnya, “Tempat berteduh dari panas matahari” bisa merujuk pada pohon, payung, atau bahkan rumah.</li>\n<li><strong>Soal Kata Serapan &amp; Bahasa Asing:</strong>  TTS seringkali menggunakan kata serapan dari bahasa asing, terutama Inggris dan Belanda. Jika Anda kurang familiar dengan bahasa-bahasa ini, <strong>soal tts sulit</strong> ini bisa menjadi batu sandungan.</li>\n<li><strong>Soal dengan Makna Ganda:</strong> Kata-kata dalam bahasa Indonesia seringkali memiliki lebih dari satu makna. Clue yang tidak spesifik bisa mengarahkan Anda ke jawaban yang salah.</li>\n<li><strong>Soal Trivia Spesifik:</strong>  Soal-soal yang membutuhkan pengetahuan mendalam tentang sejarah, sains, atau budaya tertentu bisa sangat sulit jika Anda tidak memiliki latar belakang di bidang tersebut.</li>\n</ul>\n<h3>Mengatasi <em>Mental Block</em> Saat Berhadapan dengan TTS Susah</h3>\n<p>Saat menghadapi <strong>tts susah</strong>, <em>mental block</em> adalah musuh terbesar. Jangan panik! Tarik napas dalam-dalam dan ingatlah bahwa setiap soal memiliki solusi. Jangan terpaku pada satu soal terlalu lama. Terkadang, menjauh sejenak dan kembali lagi dengan pikiran segar bisa membantu Anda menemukan jawabannya.</p>\n<h2>Strategi Jitu Menaklukkan TTS Level Sulit</h2>\n<p>Kunci untuk <strong>membantu menyelesaikan tts</strong> level sulit adalah pendekatan yang sistematis dan kemampuan untuk berpikir di luar kotak.</p>\n<h3>Pendekatan Sistematis: Mulai dari yang Mudah</h3>\n<ul>\n<li><strong>Identifikasi dan Isi Soal yang Paling Yakin:</strong> Mulailah dengan soal-soal yang Anda yakini jawabannya. Ini akan memberikan Anda momentum dan membantu mengisi beberapa huruf yang bisa menjadi petunjuk untuk soal lain.</li>\n<li><strong>Manfaatkan Soal Silang untuk Petunjuk:</strong> Perhatikan soal-soal yang berpotongan. Huruf-huruf yang sudah terisi dari soal lain bisa memberikan Anda petunjuk penting untuk menebak jawaban.</li>\n</ul>\n<h3>Mengasah Kemampuan Penalaran &amp; Kosakata</h3>\n<ul>\n<li><strong>Memahami Pola Pembentukan Kata (Awalan, Akhiran, Gabungan Kata):</strong>  Perhatikan awalan (misalnya, “ter-”, “me-”), akhiran (misalnya, “-an”, “-kan”), dan gabungan kata. Memahami pola ini bisa membantu Anda menebak kata yang tepat.</li>\n<li><strong>Menggunakan Kamus &amp; Tesaurus (Online &amp; Offline):</strong> Jangan ragu untuk menggunakan kamus dan tesaurus untuk mencari sinonim, antonim, atau definisi kata yang mungkin relevan dengan clue. Saat ini, banyak <strong>aplikasi pembantu tts</strong> yang sudah dilengkapi dengan fitur kamus.</li>\n</ul>\n<h3>Teknik Memecahkan Clue yang Rumit</h3>\n<ul>\n<li><strong>Memecah Clue Menjadi Bagian-Bagian Kecil:</strong> Jika clue terlalu panjang atau kompleks, cobalah pecah menjadi bagian-bagian yang lebih kecil dan fokus pada setiap bagian secara terpisah.</li>\n<li><strong>Mencari Sinonim &amp; Antonim:</strong>  Seringkali, jawaban yang tepat adalah sinonim atau antonim dari kata kunci dalam clue.</li>\n<li><strong>Mempertimbangkan Konteks Clue:</strong>  Perhatikan konteks keseluruhan clue. Apakah clue tersebut mengarah pada benda, orang, tempat, atau konsep abstrak?</li>\n</ul>\n<h2>Tips &amp; Trik TTS untuk Pemain Pemula Hingga Mahir</h2>\n<p>Berikut beberapa <strong>tips dan trik tts</strong> yang bisa Anda terapkan untuk meningkatkan kemampuan Anda.</p>\n<h3>Tips untuk TTS Tebak Kata Level Sulit</h3>\n<ul>\n<li><strong>Fokus pada Jumlah Huruf dan Posisi Huruf yang Sudah Terisi:</strong> Jumlah huruf yang dibutuhkan dan posisi huruf yang sudah terisi adalah informasi berharga. Gunakan informasi ini untuk mempersempit kemungkinan jawaban.  Saat mengerjakan <strong>tts tebak kata level sulit</strong>, ini sangat penting.</li>\n<li><strong>Gunakan Fitur Petunjuk (Jika Tersedia) dengan Bijak:</strong> Banyak aplikasi TTS menawarkan fitur petunjuk, seperti mengungkapkan satu huruf atau menghilangkan beberapa opsi yang salah. Gunakan fitur ini dengan bijak, jangan terlalu sering bergantung padanya.</li>\n</ul>\n<h3>Cara Cepat Menyelesaikan TTS</h3>\n<ul>\n<li><strong>Melatih Kecepatan Berpikir dan Menghafal Pola Umum:</strong> Semakin sering Anda bermain TTS, semakin cepat Anda akan mengenali pola-pola umum dan menebak jawaban dengan lebih akurat.</li>\n<li><strong>Menggunakan Teknik “Brainstorming” Singkat:</strong>  Luangkan beberapa detik untuk melakukan <em>brainstorming</em> singkat tentang kata-kata yang mungkin relevan dengan clue. Tuliskan semua ide yang terlintas di benak Anda, meskipun terdengar tidak masuk akal.</li>\n</ul>\n\n<h2>Mencari Jawaban TTS: Kapan dan Di Mana?</h2>\n<p>Terkadang, kita membutuhkan sedikit bantuan untuk menemukan <strong>jawaban tts hari ini</strong>.</p>\n<h3>Memahami Kebutuhan Akan Jawaban TTS</h3>\n<ul>\n<li><strong>Belajar dari Kesalahan:</strong> Mencari jawaban setelah mencoba sendiri bisa membantu Anda memahami mengapa jawaban Anda salah dan belajar dari kesalahan tersebut.</li>\n<li><strong>Menghemat Waktu &amp; Mengatasi Kebuntuan:</strong> Jika Anda benar-benar terjebak dan tidak bisa melanjutkan, mencari jawaban bisa menjadi cara untuk mengatasi kebuntuan dan tetap menikmati permainan.</li>\n</ul>\n<h3>Sumber Jawaban TTS Hari Ini: Gratis vs. Berbayar</h3>\n<ul>\n<li><strong>Website/Blog Penyedia Jawaban TTS (Gratis):</strong>  Banyak website dan blog yang menyediakan jawaban TTS secara gratis. Namun, pastikan sumbernya terpercaya dan jawabannya akurat.</li>\n<li><strong>Grup/Forum Diskusi TTS (Gratis):</strong> Bergabunglah dengan grup atau forum diskusi TTS untuk bertukar informasi dan meminta bantuan dari pemain lain.</li>\n</ul>\n<h3>Membeli Kunci Jawaban TTS (Kommersial)</h3>\n<ul>\n<li><strong>Keuntungan dan Kerugian Membeli Kunci Jawaban:</strong> Membeli <strong>kunci jawaban tts</strong> bisa menghemat waktu, tetapi bisa juga mengurangi tantangan dan kepuasan dalam bermain.</li>\n<li><strong>Platform yang Menawarkan Jasa Pembelian Kunci Jawaban:</strong>  Beberapa platform menawarkan jasa penjualan kunci jawaban TTS. Pastikan platform tersebut terpercaya dan menawarkan jawaban yang akurat.  Namun perlu diingat, <strong>beli kunci jawaban tts</strong> sebaiknya menjadi opsi terakhir.</li>\n</ul>\n<h2>Kesimpulan: Jadilah Master TTS!</h2>\n<p><strong>Menaklukkan tts level sulit</strong> membutuhkan kombinasi strategi, pengetahuan, dan latihan. Jangan menyerah saat menghadapi tantangan. Ingatlah bahwa setiap soal memiliki solusi, dan setiap kesalahan adalah kesempatan untuk belajar.</p>\n<h3>Pentingnya Latihan dan Konsistensi</h3>\n<p>Semakin sering Anda bermain TTS, semakin terasah kemampuan Anda. Luangkan waktu setiap hari untuk bermain TTS dan teruslah belajar kosakata baru.</p>\n<h3>Mengubah Tantangan TTS Menjadi Peluang Belajar</h3>\n<p>TTS bukan hanya sekadar permainan, tetapi juga sarana untuk meningkatkan pengetahuan umum, kosakata, dan kemampuan berpikir Anda. Jadikan setiap tantangan TTS sebagai peluang untuk belajar dan berkembang.</p>\n<p>Jadi, tunggu apa lagi? Mulailah petualangan Anda dan jadilah master TTS! Apakah Anda siap untuk tantangan berikutnya?</p>', 'TTS level sulit memang dirancang untuk menguji kemampuan berpikir, pengetahuan umum, dan kosakata Anda. Namun, ada beberapa faktor spesifik yang membuatnya terasa begitu menantang.', '', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 'published', '2025-05-14 06:57:27', '2025-05-14 06:59:14'),
	('5aa68719-6660-4571-ab5d-c9bb17fdf91b', 'Manfaat Teka teki Silang untuk Mengasah Otak', 'manfaat-teka-teki-silang-untuk-mengasah-otak', '<article>\n  <h1>Manfaat Teka Teki Silang: Asah Otak Sambil Bersenang-senang</h1>\n\n  <p><strong>Teka teki silang</strong> (TTS) merupakan salah satu permainan kata yang populer di berbagai kalangan usia. Selain menyenangkan, permainan ini juga memiliki banyak manfaat bagi kesehatan otak dan kemampuan berpikir. Dalam artikel ini, kita akan membahas manfaat bermain teka teki silang, sejarah singkatnya, serta tips agar lebih mahir dalam mengisi TTS.</p>\n\n  <h2>Apa Itu Teka Teki Silang?</h2>\n  <p>Teka teki silang adalah permainan kata dalam bentuk kotak-kotak kosong yang diisi berdasarkan petunjuk mendatar dan menurun. Permainan ini biasanya ditemukan di majalah, koran, atau aplikasi digital. Setiap kotak diisi huruf untuk membentuk kata tertentu sesuai petunjuk.</p>\n\n  <h2>Sejarah Singkat Teka Teki Silang</h2>\n  <p>Teka teki silang pertama kali diperkenalkan pada tahun 1913 oleh <strong>Arthur Wynne</strong>, seorang jurnalis dari Inggris. Permainan ini diterbitkan di surat kabar New York World dan dengan cepat mendapatkan popularitas. Sejak itu, TTS menjadi permainan rutin di berbagai media cetak di seluruh dunia, termasuk di Indonesia.</p>\n\n  <h2>Manfaat Bermain Teka Teki Silang</h2>\n  <p>Bermain teka teki silang bukan hanya sekadar hiburan. Berikut beberapa manfaatnya:</p>\n  <ul>\n    <li><strong>Melatih Daya Ingat</strong> – Teka teki silang membantu mengasah ingatan, terutama dalam mengingat kosakata, istilah, dan informasi umum.</li>\n    <li><strong>Meningkatkan Kosakata</strong> – TTS membantu memperkaya perbendaharaan kata dari berbagai bidang.</li>\n    <li><strong>Mengurangi Risiko Pikun</strong> – Aktivitas otak yang aktif dapat membantu mencegah penurunan fungsi kognitif.</li>\n    <li><strong>Meningkatkan Fokus dan Konsentrasi</strong> – Menyelesaikan TTS membutuhkan perhatian dan ketelitian.</li>\n    <li><strong>Mengisi Waktu dengan Produktif</strong> – TTS menjadi pilihan aktivitas ringan yang tetap bermanfaat.</li>\n  </ul>\n\n  <h2>Teka Teki Silang Digital: Lebih Praktis dan Modern</h2>\n  <p>Kini, <strong>teka teki silang online</strong> atau dalam bentuk aplikasi semakin populer. Dengan teknologi, TTS dapat diakses dari smartphone maupun komputer kapan saja. Banyak aplikasi juga menawarkan tingkat kesulitan yang bisa disesuaikan, cocok untuk pemula hingga pemain berpengalaman.</p>\n\n  <h2>Tips Jitu Mengisi Teka Teki Silang</h2>\n  <ol>\n    <li><strong>Mulai dari yang mudah</strong> – Isi kata yang paling Anda yakini dulu.</li>\n    <li><strong>Gunakan konteks</strong> – Coba tebak kata dari huruf yang sudah terisi sebagian.</li>\n    <li><strong>Jangan takut menghapus</strong> – Salah dalam TTS itu biasa, jangan ragu untuk mencoba lagi.</li>\n    <li><strong>Perbanyak membaca</strong> – Semakin banyak kosakata yang Anda ketahui, semakin mudah menyelesaikan TTS.</li>\n    <li><strong>Latihan rutin</strong> – Seperti otot, otak juga butuh dilatih secara teratur.</li>\n  </ol>\n\n  <h2>Kesimpulan</h2>\n  <p><strong>Teka teki silang</strong> adalah permainan yang tidak hanya menghibur, tetapi juga bermanfaat untuk kesehatan mental dan kecerdasan. Baik dalam bentuk cetak maupun digital, TTS tetap menjadi favorit banyak orang. Jadi, jangan ragu untuk meluangkan waktu setiap hari mengisi teka teki silang dan rasakan sendiri manfaatnya.</p>\n</article>\n', 'Teka teki silang (TTS) merupakan salah satu permainan kata yang populer di berbagai kalangan usia. Selain menyenangkan, permainan ini juga memiliki banyak manfaat bagi kesehatan otak dan kemampuan berpikir.', '', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 'published', '2025-05-14 05:44:09', '2025-05-14 05:45:46'),
	('91a9ee16-e640-4028-9513-a951741bce08', 'Rahasia Menjadi Jago TTS: Tips dan Trik Ampuh', 'rahasia-menjadi-jago-tts-tips-trik-ampuh', 'Pernahkah Anda merasa tertantang, bahkan frustrasi, saat menghadapi teka-teki silang (TTS)? Atau justru merasa ketagihan dan selalu ingin mengasah otak dengan permainan ini? Jika ya, Anda berada di tempat yang tepat! Artikel ini akan menjadi panduan lengkap Anda untuk menaklukkan TTS, mulai dari dasar-dasar hingga level yang paling sulit. Mari kita mulai petualangan seru ini!\n<h2>Apa Itu TTS dan Mengapa Populer di Indonesia? (Pengantar)</h2>\nTeka-teki silang, atau TTS, adalah permainan kata yang sangat populer di Indonesia. Permainan ini melatih otak, meningkatkan kosakata, dan memberikan hiburan yang menantang. Tapi, apa sebenarnya TTS itu dan mengapa begitu digemari di negeri kita?\n<h3>Sejarah Singkat TTS di Indonesia (TTS Indonesia)</h3>\nTTS pertama kali muncul di Amerika Serikat pada tahun 1913, diciptakan oleh Arthur Wynne. Kemudian, permainan ini masuk ke Indonesia melalui surat kabar dan majalah pada tahun 1930-an. Sejak saat itu, TTS terus berkembang dan menjadi bagian dari budaya populer Indonesia. Kini, kita bisa menemukan TTS dalam berbagai format, mulai dari koran, majalah, buku, hingga aplikasi digital. Popularitas <strong>TTS Indonesia</strong> tak lekang oleh waktu, terbukti dengan banyaknya penggemar dari berbagai kalangan usia.\n<h3>Manfaat Bermain TTS: Melatih Otak &amp; Meningkatkan Kosakata</h3>\nBermain TTS bukan hanya sekadar mengisi kotak-kotak dengan huruf. Ada banyak manfaat tersembunyi di dalamnya! TTS melatih kemampuan berpikir logis, meningkatkan daya ingat, dan memperluas wawasan pengetahuan umum. Selain itu, secara signifikan membantu <strong>belajar TTS</strong> dan memperkaya kosakata. Semakin banyak Anda bermain, semakin banyak kata baru yang Anda temukan.\n<h3>Mengenal Berbagai Jenis TTS (Tebak Kata, TTS Klasik, dll.)</h3>\nTTS memiliki beberapa variasi. Ada TTS klasik dengan petunjuk berupa definisi, sinonim, atau antonim. Kemudian ada <strong>TTS tebak kata</strong> yang lebih fokus pada menebak kata berdasarkan gambar atau petunjuk visual. Selain itu, ada pula TTS tematik yang berfokus pada topik tertentu, seperti sejarah, sains, atau olahraga. Memahami jenis-jenis TTS ini akan membantu Anda memilih permainan yang sesuai dengan minat dan kemampuan Anda.\n<h2>Memulai Petualangan TTS: Dasar-Dasar yang Perlu Diketahui (Belajar TTS)</h2>\nSebelum terjun ke level yang lebih tinggi, penting untuk memahami dasar-dasar permainan TTS. Ini adalah fondasi yang akan membantu Anda menjadi pemain TTS yang handal.\n<h3>Memahami Aturan Dasar TTS</h3>\nAturan dasar TTS cukup sederhana. Anda diberikan kotak-kotak yang saling berpotongan, dan setiap kotak mewakili satu huruf. Tugas Anda adalah mengisi kotak-kotak tersebut dengan huruf yang tepat berdasarkan petunjuk yang diberikan. Petunjuk biasanya berupa definisi, sinonim, antonim, atau informasi lain yang relevan dengan kata yang dicari.\n<h3>Mengenal Jenis Clue yang Umum (Definisi, Sinonim, Antonim, dll.)</h3>\nPetunjuk atau <em>clue</em> dalam TTS sangat bervariasi. Beberapa jenis <em>clue</em> yang paling umum meliputi:\n<ul>\n 	<li><strong>Definisi:</strong> Penjelasan langsung tentang kata yang dicari.</li>\n 	<li><strong>Sinonim:</strong> Kata lain yang memiliki arti yang sama.</li>\n 	<li><strong>Antonim:</strong> Kata yang memiliki arti yang berlawanan.</li>\n 	<li><strong>Petunjuk Silang:</strong> Petunjuk yang mengacu pada kata lain dalam TTS.</li>\n 	<li><strong>Petunjuk Sandi:</strong> Petunjuk yang disamarkan atau menggunakan kode.</li>\n</ul>\n<h3>Cara Membaca Clue dengan Efektif</h3>\nMembaca <em>clue</em> dengan efektif adalah kunci utama dalam menyelesaikan TTS. Perhatikan kata kunci dalam <em>clue</em>. Misalnya, jika <em>clue</em>-nya adalah "Ibukota Prancis", kata kunci yang paling penting adalah "Ibukota" dan "Prancis". Jangan terpaku pada kalimat secara keseluruhan, tapi fokuslah pada inti dari <em>clue</em> tersebut.\n<h3>Membangun Kosakata: Pentingnya Memperkaya Perbendaharaan Kata</h3>\nSemakin kaya kosakata Anda, semakin mudah Anda menyelesaikan TTS. Luangkan waktu untuk membaca buku, artikel, atau kamus untuk memperluas perbendaharaan kata Anda. Jangan ragu untuk mencari arti kata-kata yang tidak Anda ketahui. Ini merupakan bagian penting dari proses <strong>belajar TTS</strong>.\n<h2>Strategi Jitu Menjawab TTS (Strategi Bermain TTS, Tips dan Trik TTS)</h2>\nSetelah memahami dasar-dasarnya, saatnya mempelajari strategi jitu untuk menjawab TTS. Berikut beberapa <strong>tips dan trik TTS</strong> yang bisa Anda coba:\n<h3>Mulai dari yang Mudah: Tentukan Prioritas Soal</h3>\nJangan langsung mencoba mengerjakan soal yang paling sulit. Mulailah dengan soal-soal yang menurut Anda paling mudah. Ini akan memberikan Anda momentum dan membantu Anda mengisi beberapa huruf, yang bisa menjadi petunjuk untuk soal-soal lain.\n<h3>Menggunakan Clue Lain untuk Membantu: Hubungan Antar Soal</h3>\nPerhatikan bahwa soal-soal dalam TTS seringkali saling berhubungan. Jika Anda sudah berhasil mengisi beberapa huruf pada satu soal, coba gunakan huruf-huruf tersebut untuk membantu Anda memecahkan soal lain yang berpotongan.\n<h3>Menebak dengan Logika: Memanfaatkan Pola dan Informasi yang Ada (Pelajari Pola TTS)</h3>\nTTS seringkali memiliki pola tertentu. Misalnya, jika Anda tahu bahwa sebuah kata memiliki lima huruf dan huruf pertamanya adalah "S", Anda bisa mulai menebak kata-kata yang dimulai dengan "S" dan memiliki lima huruf. <strong>Pelajari pola TTS</strong> ini akan sangat membantu.\n<h3>Teknik "Hit &amp; Trial": Kapan dan Bagaimana Menggunakannya</h3>\nJika Anda benar-benar buntu, jangan ragu untuk mencoba teknik <em>hit &amp; trial</em>. Coba isi kotak-kotak dengan huruf-huruf yang menurut Anda mungkin benar. Jika salah, hapus dan coba huruf lain.\n<h3>Memanfaatkan Huruf yang Sudah Terisi</h3>\nHuruf yang sudah terisi adalah petunjuk berharga. Perhatikan huruf-huruf tersebut dan coba pikirkan kata-kata yang mengandung huruf-huruf tersebut.\n<h2>Mengatasi TTS Level Sulit (TTS Level Sulit)</h2>\nTantangan semakin meningkat saat Anda memasuki level TTS yang sulit. Berikut adalah beberapa cara untuk mengatasinya:\n<h3>Mengenali Tipe Soal Sulit (Bahasa Kiasan, Istilah Khusus, dll.)</h3>\nSoal-soal sulit seringkali menggunakan bahasa kiasan, istilah khusus, atau kata-kata asing. Penting untuk bisa mengenali jenis soal-soal ini dan mencari tahu artinya.\n<h3>Memecahkan Soal dengan Clue Abstrak</h3>\nBeberapa soal memiliki <em>clue</em> yang sangat abstrak dan sulit dipahami. Cobalah untuk menguraikan <em>clue</em> tersebut menjadi bagian-bagian yang lebih kecil dan mencari hubungan antara bagian-bagian tersebut.\n<h3>Mencari Bantuan dari Sumber Eksternal (Kamus TTS)</h3>\nJika Anda benar-benar kesulitan, jangan ragu untuk mencari bantuan dari sumber eksternal, seperti <strong>kamus TTS</strong>. Kamus TTS berisi daftar kata-kata yang sering muncul dalam TTS beserta definisinya.\n<h2>Sumber Daya TTS Online dan Offline (Aplikasi TTS, Download TTS, Kamus TTS)</h2>\nAda banyak sumber daya TTS yang tersedia, baik online maupun offline.\n<h3>Aplikasi TTS Terbaik untuk Android &amp; iOS</h3>\nBanyak <strong>aplikasi TTS</strong> yang bisa Anda unduh di smartphone Anda. Beberapa aplikasi yang populer antara lain: Tsel TTS, Teka-Teki Silang Indonesia, dan Crossword Puzzle.\n<h3>Website dan Platform TTS Online Populer</h3>\nSelain aplikasi, ada juga banyak website dan platform TTS online yang bisa Anda akses, seperti Kompas.com, Detik.com, dan KapanLagi.com.\n<h3>Cara Mendapatkan Soal TTS Offline (Download TTS)</h3>\nJika Anda ingin bermain TTS tanpa koneksi internet, Anda bisa <strong>download TTS</strong> dalam format PDF atau gambar. Banyak website yang menyediakan soal TTS gratis untuk diunduh.\n<h3>Memanfaatkan Kamus TTS Online dan Buku Kamus</h3>\n<strong>Kamus TTS</strong> online dan buku kamus adalah alat yang sangat berguna untuk membantu Anda memecahkan soal TTS. Gunakan kamus ini untuk mencari arti kata-kata yang tidak Anda ketahui.\n<h3>Sumber Daya Tambahan: Blog, Forum, dan Grup Diskusi TTS</h3>\nBergabunglah dengan blog, forum, atau grup diskusi TTS untuk bertukar ilmu dan pengalaman dengan sesama penggemar TTS.\n<h2>Meningkatkan Kemampuan TTS Anda: Latihan &amp; Komunitas (Latihan Soal TTS, Komunitas TTS Indonesia)</h2>\nKonsistensi adalah kunci untuk meningkatkan kemampuan TTS Anda.\n<h3>Situs Latihan Soal TTS Online</h3>\nAda banyak situs web yang menyediakan <strong>latihan soal TTS</strong> online gratis. Manfaatkan situs-situs ini untuk mengasah kemampuan Anda.\n<h3>Bergabung dengan Komunitas TTS Indonesia: Bertukar Ilmu &amp; Pengalaman</h3>\nBergabung dengan <strong>komunitas TTS Indonesia</strong> adalah cara yang bagus untuk belajar dari pemain lain, bertukar tips dan trik, dan mendapatkan inspirasi.\n<h3>Memecahkan TTS Bersama Teman (Game TTS)</h3>\nBermain TTS bersama teman bisa menjadi pengalaman yang menyenangkan dan bermanfaat.\n<h3>Manfaat dari Diskusi dan Sharing Solusi (Rahasia TTS - dari pemain lain)</h3>\nBerdiskusi dan berbagi solusi dengan pemain lain dapat membuka wawasan baru dan membantu Anda memahami pola-pola dalam TTS. Inilah <strong>rahasia TTS</strong> yang seringkali dibagikan dalam komunitas.\n<h2>Disclaimer: Kunci Jawaban TTS (Kunci Jawaban TTS)</h2>\nMeskipun tergoda, gunakan <strong>kunci jawaban TTS</strong> dengan bijak.\n<h3>Kapan Sebaiknya Menggunakan Kunci Jawaban? (Sebagai Pembelajaran, Bukan Jalan Pintas)</h3>\nGunakan kunci jawaban hanya sebagai alat pembelajaran, bukan sebagai jalan pintas. Setelah melihat kunci jawaban, cobalah untuk memahami mengapa jawaban tersebut benar.\n<h3>Sumber Kunci Jawaban TTS yang Terpercaya (dengan Peringatan)</h3>\nAda banyak website yang menyediakan kunci jawaban TTS, tetapi tidak semua sumber terpercaya. Berhati-hatilah dalam memilih sumber kunci jawaban.\n<h3>Hindari Ketergantungan pada Kunci Jawaban: Fokus pada Proses Belajar.</h3>\nKetergantungan pada kunci jawaban akan menghambat proses belajar Anda. Fokuslah pada proses memecahkan soal TTS itu sendiri.\n<h2>Kesimpulan: Menjadi Master TTS!</h2>\nTTS adalah permainan yang menyenangkan dan bermanfaat. Dengan latihan yang konsisten dan strategi yang tepat, Anda bisa menjadi master TTS!\n<h3>TTS sebagai Hobi yang Menyenangkan dan Bermanfaat</h3>\nTTS bukan hanya sekadar permainan, tetapi juga hobi yang menyenangkan dan bermanfaat.\n<h3>Teruslah Berlatih dan Jangan Menyerah!</h3>\nJangan menyerah jika Anda mengalami kesulitan. Teruslah berlatih dan Anda pasti akan menjadi pemain TTS yang handal. Jadi, siap untuk menaklukkan TTS selanjutnya?', 'Teka-teki silang, atau TTS, adalah permainan kata yang sangat populer di Indonesia. Permainan ini melatih otak, meningkatkan kosakata, dan memberikan hiburan yang menantang', '', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 'published', '2025-05-14 06:19:29', '2025-05-14 07:00:56');

-- Dumping structure for table crosswords_db.categories
CREATE TABLE IF NOT EXISTS `categories` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.categories: ~15 rows (approximately)
REPLACE INTO `categories` (`id`, `name`, `slug`, `description`, `image_url`, `created_at`) VALUES
	('c001a2b4-6d7e-11ee-8c99-0242ac120002', 'Pengetahuan Umum', 'pengetahuan-umum', 'Kumpulan teka-teki silang dengan tema pengetahuan umum seperti geografi, sejarah, dan ilmu pengetahuan dasar.', 'https://example.com/images/categories/pengetahuan-umum.jpg', '2025-05-14 10:00:00'),
	('c001a580-6d7e-11ee-8c99-0242ac120002', 'Olahraga', 'olahraga', 'Teka-teki silang tentang berbagai cabang olahraga, atlet terkenal, turnamen dunia, dan terminologi olahraga.', 'https://example.com/images/categories/olahraga.jpg', '2025-05-14 10:15:00'),
	('c001a710-6d7e-11ee-8c99-0242ac120002', 'Sains & Teknologi', 'sains-dan-teknologi', 'Kumpulan teka-teki silang mengenai berbagai bidang ilmu sains, penemuan ilmiah, teknologi modern, dan inovasi terkini.', 'https://example.com/images/categories/sains-teknologi.jpg', '2025-05-14 10:30:00'),
	('c001a85a-6d7e-11ee-8c99-0242ac120002', 'Hiburan', 'hiburan', 'Teka-teki silang seputar dunia hiburan termasuk film, musik, acara TV, selebritis, dan industri hiburan.', 'https://example.com/images/categories/hiburan.jpg', '2025-05-14 10:45:00'),
	('c001a980-6d7e-11ee-8c99-0242ac120002', 'Budaya Indonesia', 'budaya-indonesia', 'Kumpulan teka-teki silang tentang budaya Indonesia, seperti adat istiadat, pakaian tradisional, seni, dan bahasa daerah.', 'https://picsum.photos/seed/olahraga/300/200', '2025-05-14 11:00:00'),
	('c001aab6-6d7e-11ee-8c99-0242ac120002', 'Kuliner', 'kuliner', 'Teka-teki silang tentang makanan, resep, bahan masakan, koki terkenal, dan kuliner dari berbagai belahan dunia.', 'https://example.com/images/categories/kuliner.jpg', '2025-05-14 11:15:00'),
	('c001ac0a-6d7e-11ee-8c99-0242ac120002', 'Sastra & Bahasa', 'sastra-dan-bahasa', 'Kumpulan teka-teki silang yang berfokus pada karya sastra, penulis terkenal, istilah bahasa, dan tata bahasa.', 'https://example.com/images/categories/sastra-bahasa.jpg', '2025-05-14 11:30:00'),
	('c001ad68-6d7e-11ee-8c99-0242ac120002', 'Geografi Dunia', 'geografi-dunia', 'Teka-teki silang seputar lokasi geografis, negara, ibukota, sungai, gunung, dan landmark dunia terkenal.', 'https://example.com/images/categories/geografi-dunia.jpg', '2025-05-14 11:45:00'),
	('c001aed0-6d7e-11ee-8c99-0242ac120002', 'Sejarah & Tokoh', 'sejarah-dan-tokoh', 'Kumpulan teka-teki silang tentang peristiwa sejarah penting, tokoh berpengaruh, dan momen bersejarah dunia.', 'https://example.com/images/categories/sejarah-tokoh.jpg', '2025-05-14 12:00:00'),
	('c001b00c-6d7e-11ee-8c99-0242ac120002', 'Dunia Anak', 'dunia-anak', 'Teka-teki silang dengan tingkat kesulitan mudah untuk anak-anak, berisi tema kartun, hewan, fairy tales, dan pengetahuan dasar.', 'https://example.com/images/categories/dunia-anak.jpg', '2025-05-14 12:15:00'),
	('c001b156-6d7e-11ee-8c99-0242ac120002', 'Sains Populer', 'sains-populer', 'Teka-teki silang tentang topik sains yang populer, penemuan modern, dan fenomena alam yang menarik.', 'https://example.com/images/categories/sains-populer.jpg', '2025-05-14 12:30:00'),
	('c001b2a0-6d7e-11ee-8c99-0242ac120002', 'Flora & Fauna', 'flora-dan-fauna', 'Kumpulan teka-teki silang tentang dunia tumbuhan dan hewan, keanekaragaman hayati, dan ekosistem.', 'https://example.com/images/categories/flora-fauna.jpg', '2025-05-14 12:45:00'),
	('c001b3ea-6d7e-11ee-8c99-0242ac120002', 'Mitologi & Legenda', 'mitologi-dan-legenda', 'Teka-teki silang seputar mitologi berbagai budaya, cerita rakyat, legenda, dan kisah-kisah kuno.', 'https://example.com/images/categories/mitologi-legenda.jpg', '2025-05-14 13:00:00'),
	('c001b53e-6d7e-11ee-8c99-0242ac120002', 'Musik & Lagu', 'musik-dan-lagu', 'Kumpulan teka-teki silang khusus tentang musik dari berbagai genre, penyanyi, band, alat musik, dan istilah musik.', 'https://example.com/images/categories/musik-lagu.jpg', '2025-05-14 13:15:00'),
	('c001b688-6d7e-11ee-8c99-0242ac120002', 'Peribahasa & Kata Mutiara', 'peribahasa-dan-kata-mutiara', 'Teka-teki silang tentang peribahasa Indonesia dan internasional, kata-kata bijak, dan ungkapan populer.', 'https://example.com/images/categories/peribahasa.jpg', '2025-05-14 13:30:00');

-- Dumping structure for table crosswords_db.crosswords
CREATE TABLE IF NOT EXISTS `crosswords` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `grid_size` int NOT NULL DEFAULT '15',
  `grid_data` json NOT NULL,
  `words` json NOT NULL,
  `clues` json NOT NULL,
  `word_positions` json NOT NULL,
  `difficulty` enum('mudah','sedang','sulit') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sedang',
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '1',
  `plays` int NOT NULL DEFAULT '0',
  `rating` decimal(3,1) DEFAULT NULL,
  `category_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_crosswords_category` (`category_id`),
  KEY `idx_crosswords_user` (`user_id`),
  KEY `idx_crosswords_public` (`is_public`),
  KEY `idx_crosswords_difficulty` (`difficulty`),
  CONSTRAINT `fk_crosswords_category_id` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_crosswords_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.crosswords: ~5 rows (approximately)
REPLACE INTO `crosswords` (`id`, `title`, `slug`, `description`, `grid_size`, `grid_data`, `words`, `clues`, `word_positions`, `difficulty`, `user_id`, `is_public`, `plays`, `rating`, `category_id`, `created_at`, `updated_at`) VALUES
	('25ed640a-d93f-4921-8b1d-fc18febf97e9', 'Pengetahuan Umum 1', 'pengetahuan-umum-1', '', 15, '[[{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "M", "wordIds": [10]}, {"char": "A", "wordIds": [9, 10]}, {"char": "S", "wordIds": [10]}, {"char": "S", "wordIds": [10]}, {"char": "A", "wordIds": [10]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "M", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [1, 2]}, {"char": "S", "wordIds": [1]}, {"char": "T", "wordIds": [1, 4]}, {"char": "E", "wordIds": [1]}, {"char": "R", "wordIds": [1, 9]}, {"char": "O", "wordIds": [1]}, {"char": "I", "wordIds": [1, 3]}, {"char": "D", "wordIds": [1]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "F", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [3, 5]}, {"char": "O", "wordIds": [5]}, {"char": "M", "wordIds": [5]}, {"char": "E", "wordIds": [5, 6]}, {"char": "T", "wordIds": [5]}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "I", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "Y", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "S", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "W", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": "K", "wordIds": [7]}, {"char": "I", "wordIds": [7, 8]}, {"char": "M", "wordIds": [7]}, {"char": "I", "wordIds": [2, 7]}, {"char": "A", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "K", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "Y", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "A", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]]', '["ASTEROID", "GALAKSI", "MILKYWAY", "SATELIT", "KOMET", "GEOLOGI", "KIMIA", "FISIKA", "ANTROPOLOGI", "MASSA"]', '{"down": {"2": "Kumpulan bintang, gas, dan debu di alam semesta", "3": "Galaksi tempat tata surya berada", "4": "Benda langit yang mengorbit planet", "6": "Ilmu tentang struktur bumi", "8": "Ilmu tentang materi, energi, dan gerak", "9": "Ilmu tentang manusia dan budayanya"}, "across": {"1": "Benda langit kecil yang mengorbit matahari", "5": "Benda langit dengan ekor bercahaya", "7": "Ilmu tentang zat dan reaksinya", "10": "Jumlah materi dalam suatu benda"}}', '[{"col": 3, "row": 4, "number": 1, "direction": "across"}, {"col": 3, "row": 3, "number": 2, "direction": "down"}, {"col": 9, "row": 3, "number": 3, "direction": "down"}, {"col": 5, "row": 2, "number": 4, "direction": "down"}, {"col": 9, "row": 6, "number": 5, "direction": "across"}, {"col": 12, "row": 5, "number": 6, "direction": "down"}, {"col": 0, "row": 9, "number": 7, "direction": "across"}, {"col": 1, "row": 6, "number": 8, "direction": "down"}, {"col": 7, "row": 1, "number": 9, "direction": "down"}, {"col": 6, "row": 1, "number": 10, "direction": "across"}]', 'mudah', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 1, 194, NULL, 'c001a2b4-6d7e-11ee-8c99-0242ac120002', '2025-05-13 17:59:21', '2025-05-13 17:59:21'),
	('29c8f261-9294-4855-86d1-eb93673b21d8', 'Budaya Jawa', 'budaya-jawa', 'Uji ketajaman pikiran Anda dengan Budaya Jawa, teka-teki silang mudah yang menantang dengan tema Budaya Indonesia. Terdiri dari 97 kotak dan 18 soal yang mencakup bidang Budaya Indonesia, budaya, dan hiburan.\n\nBudaya Jawa dirancang oleh Admin, kreator puzzle berpengalaman dengan 5 tahun dalam menciptakan teka-teki yang mengasah otak. Setiap pertanyaan telah disusun secara cermat untuk memberikan tantangan kognitif yang menyenangkan namun tetap menantang.\n\nSelesaikan dalam waktu 12 menit untuk menguji kecepatan berpikir Anda. Tersedia dalam digital dengan petunjuk interaktif yang memudahkan pengerjaan. Cocok untuk semua kalangan yang ingin meningkatkan kosakata dan daya ingat dan memperluas pengetahuan umum.', 20, '[[{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [16]}, {"char": "E", "wordIds": [16]}, {"char": "R", "wordIds": [16]}, {"char": "I", "wordIds": [14, 16]}, {"char": "M", "wordIds": [16]}, {"char": "P", "wordIds": [16]}, {"char": "I", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "R", "wordIds": [14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [15]}, {"char": "E", "wordIds": [15]}, {"char": "D", "wordIds": [15]}, {"char": "H", "wordIds": [15]}, {"char": "A", "wordIds": [14, 15]}, {"char": "Y", "wordIds": [15]}, {"char": "A", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [18]}, {"char": "U", "wordIds": [18]}, {"char": "S", "wordIds": [13, 18]}, {"char": "A", "wordIds": [18]}, {"char": "K", "wordIds": [18]}, {"char": "A", "wordIds": [18]}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [9, 14]}, {"char": "E", "wordIds": [9]}, {"char": "M", "wordIds": [9]}, {"char": "B", "wordIds": [9]}, {"char": "A", "wordIds": [7, 9]}, {"char": "N", "wordIds": [9]}, {"char": "G", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "R", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "J", "wordIds": [17]}, {"char": "A", "wordIds": [17]}, {"char": "M", "wordIds": [13, 17]}, {"char": "A", "wordIds": [17]}, {"char": "S", "wordIds": [17]}, {"char": "A", "wordIds": [17]}, {"char": "N", "wordIds": [17]}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "D", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "K", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [1]}, {"char": "A", "wordIds": [1, 2]}, {"char": "T", "wordIds": [1]}, {"char": "I", "wordIds": [1, 7]}, {"char": "K", "wordIds": [1, 3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "E", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "M", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "R", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": "D", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [6]}, {"char": "R", "wordIds": [3, 6]}, {"char": "A", "wordIds": [6]}, {"char": "T", "wordIds": [6]}, {"char": "O", "wordIds": [6]}, {"char": "N", "wordIds": [6, 13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": "M", "wordIds": [10]}, {"char": "A", "wordIds": [10, 11]}, {"char": "C", "wordIds": [10]}, {"char": "A", "wordIds": [10, 12]}, {"char": "P", "wordIds": [10]}, {"char": "A", "wordIds": [5, 10]}, {"char": "T", "wordIds": [10]}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "T", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [3, 8]}, {"char": "E", "wordIds": [8]}, {"char": "K", "wordIds": [8]}, {"char": "A", "wordIds": [8]}, {"char": "R", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "O", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "W", "wordIds": [4]}, {"char": "A", "wordIds": [4, 5]}, {"char": "Y", "wordIds": [4]}, {"char": "A", "wordIds": [4]}, {"char": "N", "wordIds": [2, 4]}, {"char": "G", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "N", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]]', '["BATIK", "GAMELAN", "KERIS", "WAYANG", "DALANG", "KRATON", "ABDI", "SEKAR", "TEMBANG", "MACAPAT", "KERATON", "PRANATA", "SLAMETAN", "TIRAKAT", "BEDHAYA", "SERIMPI", "JAMASAN", "PUSAKA"]', '{"down": {"2": "Alat musik tradisional Jawa yang dimainkan secara ensambel", "3": "Senjata tradisional Jawa yang dianggap memiliki kekuatan spiritual", "5": "Orang yang memainkan wayang kulit", "7": "Pelayan atau pengikut setia raja di kraton", "11": "Tempat tinggal Sultan di Yogyakarta", "12": "Aturan atau tata cara dalam budaya Jawa", "13": "Tradisi selamatan atau doa bersama dalam budaya Jawa", "14": "Puasa atau laku spiritual dalam budaya Jawa"}, "across": {"1": "Pakaian khas Jawa yang memiliki motif unik dan diwariskan secara turun temurun", "4": "Pertunjukan seni bayangan khas Jawa", "6": "Istana raja di budaya Jawa", "8": "Lagu atau tembang Jawa", "9": "Nyanyian dalam budaya Jawa", "10": "Jenis puisi tradisional Jawa", "15": "Tarian sakral yang biasa dipentaskan di lingkungan keraton", "16": "Tarian klasik Jawa yang ditarikan oleh empat penari", "17": "Prosesi pembersihan benda pusaka", "18": "Benda keramat yang diwariskan turun temurun"}}', '[{"col": 7, "row": 10, "number": 1, "direction": "across"}, {"col": 8, "row": 9, "number": 2, "direction": "down"}, {"col": 11, "row": 10, "number": 3, "direction": "down"}, {"col": 4, "row": 15, "number": 4, "direction": "across"}, {"col": 5, "row": 12, "number": 5, "direction": "down"}, {"col": 10, "row": 12, "number": 6, "direction": "across"}, {"col": 10, "row": 7, "number": 7, "direction": "down"}, {"col": 11, "row": 14, "number": 8, "direction": "across"}, {"col": 6, "row": 7, "number": 9, "direction": "across"}, {"col": 0, "row": 13, "number": 10, "direction": "across"}, {"col": 1, "row": 10, "number": 11, "direction": "down"}, {"col": 3, "row": 7, "number": 12, "direction": "down"}, {"col": 15, "row": 5, "number": 13, "direction": "down"}, {"col": 6, "row": 1, "number": 14, "direction": "down"}, {"col": 2, "row": 4, "number": 15, "direction": "across"}, {"col": 3, "row": 2, "number": 16, "direction": "across"}, {"col": 13, "row": 8, "number": 17, "direction": "across"}, {"col": 13, "row": 5, "number": 18, "direction": "across"}]', 'mudah', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 1, 82, NULL, 'c001a980-6d7e-11ee-8c99-0242ac120002', '2025-05-14 13:29:04', '2025-05-14 13:29:04'),
	('3da57db1-96ef-4ff4-a6fb-69878f5ad034', 'Budaya Jawa 2', 'budaya-jawa-2', 'Uji ketajaman pikiran Anda dengan Budaya Jawa #2, teka-teki silang sedang yang menantang dengan tema Budaya Indonesia. Terdiri dari 95 kotak dan 17 soal yang mencakup bidang Budaya Indonesia, budaya, dan hiburan.\n\nBudaya Jawa #2 dirancang oleh Admin, kreator puzzle berpengalaman dengan 5 tahun dalam menciptakan teka-teki yang mengasah otak. Setiap pertanyaan telah disusun secara cermat untuk memberikan tantangan kognitif yang menyenangkan namun tetap menantang.\n\nSelesaikan dalam waktu 17 menit untuk menguji kecepatan berpikir Anda. Tersedia dalam digital dengan petunjuk interaktif yang memudahkan pengerjaan. Cocok untuk semua kalangan yang ingin meningkatkan kosakata dan daya ingat dan memperluas pengetahuan umum.', 17, '[[{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [17]}, {"char": "A", "wordIds": [17]}, {"char": "T", "wordIds": [17]}, {"char": "I", "wordIds": [17]}, {"char": "K", "wordIds": [16, 17]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "R", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "J", "wordIds": [7, 13]}, {"char": "A", "wordIds": [13]}, {"char": "M", "wordIds": [13]}, {"char": "P", "wordIds": [13]}, {"char": "I", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [6, 16]}, {"char": "E", "wordIds": [6]}, {"char": "R", "wordIds": [4, 6]}, {"char": "I", "wordIds": [6]}, {"char": "M", "wordIds": [6, 7]}, {"char": "P", "wordIds": [6]}, {"char": "I", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "D", "wordIds": [14]}, {"char": "U", "wordIds": [14, 15]}, {"char": "K", "wordIds": [14]}, {"char": "U", "wordIds": [8, 14]}, {"char": "N", "wordIds": [14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "L", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [7, 10]}, {"char": "I", "wordIds": [10]}, {"char": "N", "wordIds": [10]}, {"char": "D", "wordIds": [10]}, {"char": "E", "wordIds": [10, 11]}, {"char": "N", "wordIds": [10]}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "U", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [4]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [1, 8]}, {"char": "E", "wordIds": [1]}, {"char": "R", "wordIds": [1, 2]}, {"char": "A", "wordIds": [1]}, {"char": "T", "wordIds": [1, 4]}, {"char": "O", "wordIds": [1]}, {"char": "N", "wordIds": [1, 7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [11]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [15]}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [3]}, {"char": "L", "wordIds": [3]}, {"char": "A", "wordIds": [2, 3]}, {"char": "M", "wordIds": [3]}, {"char": "E", "wordIds": [3, 5]}, {"char": "T", "wordIds": [3]}, {"char": "A", "wordIds": [3]}, {"char": "N", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "D", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "H", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "D", "wordIds": [12]}, {"char": "A", "wordIds": [5, 12]}, {"char": "N", "wordIds": [12]}, {"char": "Y", "wordIds": [12]}, {"char": "A", "wordIds": [12]}, {"char": "N", "wordIds": [12]}, {"char": "G", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "Y", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [9]}, {"char": "E", "wordIds": [9]}, {"char": "L", "wordIds": [9]}, {"char": "A", "wordIds": [5, 9]}, {"char": "M", "wordIds": [9]}, {"char": "E", "wordIds": [9]}, {"char": "T", "wordIds": [9]}, {"char": "A", "wordIds": [9]}, {"char": "N", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]]', '["KERATON", "PRANATA", "SLAMETAN", "TIRAKAT", "BEDHAYA", "SERIMPI", "JAMASAN", "PUSAKA", "SELAMETAN", "SINDEN", "KLENIK", "DANYANG", "JAMPI", "DUKUN", "GUNUNGAN", "KERIS", "BATIK"]', '{"down": {"2": "Aturan atau tata cara dalam budaya Jawa", "4": "Puasa atau laku spiritual dalam budaya Jawa", "5": "Tarian sakral yang biasa dipentaskan di lingkungan keraton", "7": "Prosesi pembersihan benda pusaka", "8": "Benda keramat yang diwariskan turun temurun", "11": "Kepercayaan terhadap hal-hal gaib atau mistis", "15": "Hasil bumi yang disusun mengerucut dan diarak dalam tradisi grebeg", "16": "Senjata tradisional yang dianggap sakral oleh masyarakat Jawa"}, "across": {"1": "Tempat tinggal Sultan di Yogyakarta", "3": "Tradisi selamatan atau doa bersama dalam budaya Jawa", "6": "Tarian klasik Jawa yang ditarikan oleh empat penari", "9": "Upacara adat sebagai ungkapan syukur", "10": "Penyanyi perempuan dalam pertunjukan gamelan", "12": "Penjaga spiritual suatu tempat menurut kepercayaan Jawa", "13": "Mantra dalam bahasa Jawa", "14": "Orang yang melakukan praktik pengobatan tradisional atau spiritual", "17": "Teknik pewarnaan kain menggunakan malam"}}', '[{"col": 4, "row": 8, "number": 1, "direction": "across"}, {"col": 6, "row": 7, "number": 2, "direction": "down"}, {"col": 4, "row": 11, "number": 3, "direction": "across"}, {"col": 8, "row": 2, "number": 4, "direction": "down"}, {"col": 8, "row": 10, "number": 5, "direction": "down"}, {"col": 6, "row": 4, "number": 6, "direction": "across"}, {"col": 10, "row": 2, "number": 7, "direction": "down"}, {"col": 4, "row": 4, "number": 8, "direction": "down"}, {"col": 5, "row": 16, "number": 9, "direction": "across"}, {"col": 10, "row": 6, "number": 10, "direction": "across"}, {"col": 14, "row": 4, "number": 11, "direction": "down"}, {"col": 7, "row": 14, "number": 12, "direction": "across"}, {"col": 10, "row": 2, "number": 13, "direction": "across"}, {"col": 1, "row": 5, "number": 14, "direction": "across"}, {"col": 2, "row": 4, "number": 15, "direction": "down"}, {"col": 6, "row": 0, "number": 16, "direction": "down"}, {"col": 2, "row": 0, "number": 17, "direction": "across"}]', 'sedang', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 1, 26, NULL, 'c001a980-6d7e-11ee-8c99-0242ac120002', '2025-05-14 14:08:10', '2025-05-14 14:08:10'),
	('80fe76fa-389e-4277-a73a-99d86c423c23', 'TEST', 'test', '', 20, '[[{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [1]}, {"char": "E", "wordIds": [1]}, {"char": "S", "wordIds": [1]}, {"char": "T", "wordIds": [1]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]]', '["TEST"]', '{"down": [], "across": {"1": ""}}', '[{"col": 8, "row": 10, "number": 1, "direction": "across"}]', 'sedang', 'd6dc9b13-e313-455d-aacd-40bdf6f3638b', 1, 2, NULL, 'c001b00c-6d7e-11ee-8c99-0242ac120002', '2025-05-15 13:13:11', '2025-05-15 13:13:11'),
	('d92b6c26-b418-43d5-8720-64152c906778', 'Budaya Jawa 3', 'budaya-jawa-3', 'Uji ketajaman pikiran Anda dengan Budaya Jawa #3, teka-teki silang sedang yang menantang dengan tema Budaya Indonesia. Terdiri dari 81 kotak dan 16 soal yang mencakup bidang Budaya Indonesia, budaya, dan hiburan.\nAsah Kemampuan Berpikir dengan Teka-Teki Silang Budaya Jawa #3\n\nBudaya Jawa #3 adalah media pembelajaran yang menyenangkan untuk memperluas pengetahuan tentang budaya Indonesia. Teka-teki silang ini terdiri dari 81 kotak dan 16 soal yang mencakup topik budaya nasional, kearifan lokal, hingga unsur hiburan.\n\nDisusun oleh Admin, seorang kreator berpengalaman, setiap pertanyaan dirancang untuk melatih kemampuan berpikir kritis, memperkaya kosakata, serta meningkatkan daya ingat.\n\nDengan format digital yang interaktif, pengguna dapat mengerjakannya secara mudah dan fleksibel. Tantang diri Anda untuk menyelesaikan dalam 16 menit, dan rasakan manfaatnya bagi pengembangan kognitif serta pemahaman budaya.\nBudaya Jawa #3 dirancang oleh Admin, kreator puzzle berpengalaman dengan 5 tahun dalam menciptakan teka-teki yang mengasah otak. Setiap pertanyaan telah disusun secara cermat untuk memberikan tantangan kognitif yang menyenangkan namun tetap menantang.\n\nSelesaikan dalam waktu 16 menit untuk menguji kecepatan berpikir Anda. Tersedia dalam digital dengan petunjuk interaktif yang memudahkan pengerjaan. Cocok untuk semua kalangan yang ingin meningkatkan kosakata dan daya ingat dan memperluas pengetahuan umum.', 17, '[[{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [8]}, {"char": "A", "wordIds": [8]}, {"char": "N", "wordIds": [8]}, {"char": "G", "wordIds": [7, 8]}, {"char": "K", "wordIds": [8]}, {"char": "O", "wordIds": [8]}, {"char": "N", "wordIds": [8]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [3, 6]}, {"char": "E", "wordIds": [6]}, {"char": "M", "wordIds": [6]}, {"char": "A", "wordIds": [6]}, {"char": "N", "wordIds": [6, 7]}, {"char": "T", "wordIds": [6]}, {"char": "E", "wordIds": [6]}, {"char": "N", "wordIds": [6]}, {"char": " ", "wordIds": []}, {"char": "M", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "U", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "D", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [10]}, {"char": "E", "wordIds": [10]}, {"char": "M", "wordIds": [3, 10]}, {"char": "A", "wordIds": [10]}, {"char": "R", "wordIds": [10]}, {"char": " ", "wordIds": []}, {"char": "I", "wordIds": [7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [7, 15]}, {"char": "G", "wordIds": [15]}, {"char": "A", "wordIds": [15]}, {"char": "B", "wordIds": [15]}, {"char": "E", "wordIds": [15]}, {"char": "N", "wordIds": [15, 16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "W", "wordIds": [4]}, {"char": "E", "wordIds": [3, 4]}, {"char": "D", "wordIds": [4]}, {"char": "A", "wordIds": [4]}, {"char": "N", "wordIds": [4]}, {"char": "G", "wordIds": [4, 7]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [16]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [3]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "L", "wordIds": [1]}, {"char": "A", "wordIds": [1, 2]}, {"char": "N", "wordIds": [1]}, {"char": "G", "wordIds": [1, 3]}, {"char": "G", "wordIds": [1]}, {"char": "A", "wordIds": [1]}, {"char": "R", "wordIds": [1, 9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "E", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": "J", "wordIds": [5]}, {"char": "A", "wordIds": [2, 5]}, {"char": "M", "wordIds": [5]}, {"char": "U", "wordIds": [5]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "P", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": "A", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "R", "wordIds": [2]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "G", "wordIds": [11]}, {"char": "A", "wordIds": [9, 11]}, {"char": "R", "wordIds": [11]}, {"char": "E", "wordIds": [11, 12]}, {"char": "N", "wordIds": [11]}, {"char": "G", "wordIds": [11, 13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "B", "wordIds": [9]}, {"char": " ", "wordIds": []}, {"char": "T", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": "O", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "R", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": "N", "wordIds": [13]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "S", "wordIds": [14]}, {"char": "A", "wordIds": [14]}, {"char": "R", "wordIds": [14]}, {"char": "U", "wordIds": [12, 14]}, {"char": "N", "wordIds": [14]}, {"char": "G", "wordIds": [13, 14]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": "K", "wordIds": [12]}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}], [{"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}, {"char": " ", "wordIds": []}]]', '["LANGGAR", "PASAR", "TUMPENG", "WEDANG", "JAMU", "TEMANTEN", "GENDING", "PANGKON", "REBAB", "SEMAR", "GARENG", "PETRUK", "BAGONG", "SARUNG", "NGABEN", "TEMBANG"]', '{"down": {"2": "Tempat jual beli tradisional masyarakat Jawa", "3": "Nasi berbentuk kerucut yang disajikan dalam acara syukuran", "7": "Komposisi musik Jawa", "9": "Alat musik gesek dalam gamelan", "12": "Punakawan bertubuh tinggi dengan hidung panjang", "13": "Punakawan bertubuh besar", "16": "Nyanyian dalam budaya Jawa"}, "across": {"1": "Mushola kecil di lingkungan kampung Jawa", "4": "Minuman tradisional hangat khas Jawa", "5": "Minuman herbal tradisional Jawa", "6": "Pengantin dalam bahasa Jawa", "8": "Tempat duduk dalam gamelan", "10": "Tokoh punakawan bijak dalam pewayangan", "11": "Salah satu punakawan yang bertubuh kecil", "14": "Kain panjang yang biasa dipakai masyarakat Jawa", "15": "Upacara kremasi dalam budaya Bali (bukan Jawa, tapi sering dibandingkan)"}}', '[{"col": 1, "row": 8, "number": 1, "direction": "across"}, {"col": 2, "row": 7, "number": 2, "direction": "down"}, {"col": 4, "row": 2, "number": 3, "direction": "down"}, {"col": 3, "row": 6, "number": 4, "direction": "across"}, {"col": 1, "row": 10, "number": 5, "direction": "across"}, {"col": 4, "row": 2, "number": 6, "direction": "across"}, {"col": 8, "row": 0, "number": 7, "direction": "down"}, {"col": 5, "row": 0, "number": 8, "direction": "across"}, {"col": 7, "row": 8, "number": 9, "direction": "down"}, {"col": 2, "row": 4, "number": 10, "direction": "across"}, {"col": 6, "row": 11, "number": 11, "direction": "across"}, {"col": 9, "row": 10, "number": 12, "direction": "down"}, {"col": 11, "row": 9, "number": 13, "direction": "down"}, {"col": 6, "row": 14, "number": 14, "direction": "across"}, {"col": 8, "row": 5, "number": 15, "direction": "across"}, {"col": 13, "row": 0, "number": 16, "direction": "down"}]', 'sedang', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 1, 52, NULL, 'c001a980-6d7e-11ee-8c99-0242ac120002', '2025-05-14 14:16:08', '2025-05-14 14:16:08');

-- Dumping structure for table crosswords_db.rate_limits
CREATE TABLE IF NOT EXISTS `rate_limits` (
  `id` varchar(36) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `endpoint` varchar(255) NOT NULL,
  `request_count` int NOT NULL DEFAULT '1',
  `first_request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_blocked` tinyint(1) NOT NULL DEFAULT '0',
  `block_expiry` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ip_address` (`ip_address`,`endpoint`),
  KEY `idx_rate_limits_last_request_time` (`last_request_time`),
  KEY `idx_rate_limits_ip_endpoint` (`ip_address`,`endpoint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table crosswords_db.rate_limits: ~7 rows (approximately)
REPLACE INTO `rate_limits` (`id`, `ip_address`, `endpoint`, `request_count`, `first_request_time`, `last_request_time`, `is_blocked`, `block_expiry`) VALUES
	('207ebcd9-f00c-44df-bf14-f74757e2edfd', '::1', '/api/categories/c001a980-6d7e-11ee-8c99-0242ac120002', 3, '2025-05-15 05:51:43', '2025-05-15 05:52:15', 0, NULL),
	('7402682b-d16a-429b-8167-688deba9b1e3', '::1', '/api/crosswords/d92b6c26-b418-43d5-8720-64152c906778/play', 3, '2025-05-15 05:51:43', '2025-05-15 05:52:15', 0, NULL),
	('9a6b4983-357d-4493-a31d-d0420ec25627', '::1', '/api/crosswords/budaya-jawa-3', 3, '2025-05-15 05:51:43', '2025-05-15 05:52:15', 0, NULL),
	('b2bd823d-ac84-46d1-bb29-ab9e09ac93d9', '::1', '/api/progress/d92b6c26-b418-43d5-8720-64152c906778', 3, '2025-05-15 05:51:43', '2025-05-15 05:51:45', 0, NULL),
	('cfe8c247-c793-4d63-9c70-0eae5db47796', '::1', '/api/featured', 1, '2025-05-15 05:52:12', '2025-05-15 05:52:12', 0, NULL),
	('cffaead5-249e-4e9f-bbfa-06f3c7c4118d', '::1', '/api/users/me', 6, '2025-05-15 05:51:43', '2025-05-15 05:52:15', 0, NULL),
	('d903e71b-bafd-404d-9168-2b6f72caa43b', '::1', '/api/categories', 1, '2025-05-15 05:52:12', '2025-05-15 05:52:12', 0, NULL);

-- Dumping structure for table crosswords_db.settings
CREATE TABLE IF NOT EXISTS `settings` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `setting_key` (`setting_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.settings: ~9 rows (approximately)
REPLACE INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
	('02c59b48-3145-11f0-add3-d85ed372885b', 'enable_rate_limiting', 'true', '2025-05-15 11:28:11', '2025-05-15 11:28:11'),
	('02c5ad31-3145-11f0-add3-d85ed372885b', 'rate_limit_window', '60', '2025-05-15 11:28:11', '2025-05-15 11:28:11'),
	('02c5ae78-3145-11f0-add3-d85ed372885b', 'rate_limit_max_requests', '60', '2025-05-15 11:28:11', '2025-05-15 11:28:11'),
	('02c5af51-3145-11f0-add3-d85ed372885b', 'enable_bot_protection', 'true', '2025-05-15 11:28:11', '2025-05-15 11:28:11'),
	('63be1b5d-307e-11f0-add3-d85ed372885b', 'site_title', 'Teka-Teki Silang Online', '2025-05-14 11:46:24', '2025-05-15 04:24:15'),
	('63be208a-307e-11f0-add3-d85ed372885b', 'site_description', 'Platform teka-teki silang online terbaik di Indonesia', '2025-05-14 11:46:24', '2025-05-15 04:24:15'),
	('63be2256-307e-11f0-add3-d85ed372885b', 'max_hints', '5', '2025-05-14 11:46:24', '2025-05-15 04:24:15'),
	('63be303f-307e-11f0-add3-d85ed372885b', 'enable_google_login', 'true', '2025-05-14 11:46:24', '2025-05-15 04:24:15'),
	('63be3164-307e-11f0-add3-d85ed372885b', 'default_difficulty', 'mudah', '2025-05-14 11:46:24', '2025-05-15 04:24:15');

-- Dumping structure for table crosswords_db.user_profiles
CREATE TABLE IF NOT EXISTS `user_profiles` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bio` text COLLATE utf8mb4_unicode_ci,
  `role` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `google_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `auth_provider` enum('email','google') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'email',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.user_profiles: ~1 rows (approximately)
REPLACE INTO `user_profiles` (`id`, `username`, `email`, `password_hash`, `display_name`, `avatar_url`, `bio`, `role`, `google_id`, `auth_provider`, `created_at`) VALUES
	('5b4dc128-8ba5-4bd3-b990-6de4ddd11522', '<EMAIL>', '<EMAIL>', '$2y$10$18l9vqNQsKKMfmOGTQa.IeE3wMcq8CHpJ2sl/gqYpdaBHBw/EPPB6', 'widi yanata', NULL, NULL, 'admin', 'eyJhbGciOiJSUzI1NiIsImtpZCI6ImUxNGMzN2Q2ZTVjNzU2ZThiNzJmZGI1MDA0YzBjYzM1NjMzNzkyNGUiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HPlkpvDD9zD5Hqit9p_Lby8Yeho3PGJpmGYq3leZk2flTKCrcCxUWyuIGYTVV0ofD0vbhC5qiqvrNbYbZdUj1XULj1KG_mQJm-L1pw48W5aumMUsqmeP_JLPmQ1VMq0Yduj0mGm-zDGSweIvlTozfhpI5Ap5C17e_Q1lzNYdYmjT6nUpOfYz8aLXe8DDAkQnVMfKRH0cOW8VSRTlCeKgGhMyhlNzOc-7M3e1xcia1gIbc_Wt12am6eJpxJjtP1xbTHJtl1gXc-rC8mF99WBaROhG89OFCLKtDPlkSQdnJxzBeZvLdNd_Qrg5wA2GCpKhLd75-F4JABAXqMq-Ea_oKw', 'google', '2025-05-13 17:56:45'),
	('d6dc9b13-e313-455d-aacd-40bdf6f3638b', 'widiyanatadotcom', '<EMAIL>', NULL, 'info sehat', 'https://lh3.googleusercontent.com/a/ACg8ocLS9ZDNUOCt34BxD_1OmKgUPccuxvVZxFsbSRYB7TF6uwNh2Q=s96-c', NULL, 'user', 'eyJhbGciOiJSUzI1NiIsImtpZCI6ImUxNGMzN2Q2ZTVjNzU2ZThiNzJmZGI1MDA0YzBjYzM1NjMzNzkyNGUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FJoMzJzrjF-2eCLxVnErP8sMIih5_xTAjeflDvKs5Aj2EK19FjuDGekMEV5Z4YEtrosn_8p7B-oHwNIaAc8aJafyuqvxKfxGdwhciIwQPZUrwQuVHWxihFz_Lcd4xK15nVCr-FYXrH9UnOTRJAduMtF57E05CYJb2Ib6FEJhQgtdSu3jA39vTWVGMyX4YwhS-9oYJPKtf_GZLhZ2AE_oP12xPqncljq-g-6qLzQT1rbkmYfj2Qwo0EmLetiZ8VgMyY64ZHodhEt7kfeROpa5izI4E93YRu3bFDya80H_gf5rw3eKUXKBU2HQTQCNtnYPSPrxuzjGMQi_SQ6AvVKp2A', 'google', '2025-05-15 13:09:46');

-- Dumping structure for table crosswords_db.user_progress
CREATE TABLE IF NOT EXISTS `user_progress` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `crossword_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `progress_data` json NOT NULL,
  `is_completed` tinyint(1) NOT NULL DEFAULT '0',
  `time_spent` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_progress_user_crossword` (`user_id`,`crossword_id`),
  KEY `idx_user_progress_user` (`user_id`),
  KEY `idx_user_progress_crossword` (`crossword_id`),
  CONSTRAINT `fk_user_progress_crossword_id` FOREIGN KEY (`crossword_id`) REFERENCES `crosswords` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_progress_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table crosswords_db.user_progress: ~7 rows (approximately)
REPLACE INTO `user_progress` (`id`, `user_id`, `crossword_id`, `progress_data`, `is_completed`, `time_spent`, `created_at`, `updated_at`) VALUES
	('2dd99f64-be31-4c65-98d9-a8411be5dbd4', 'd6dc9b13-e313-455d-aacd-40bdf6f3638b', '29c8f261-9294-4855-86d1-eb93673b21d8', '{"progress": 7, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "T", "E", "M", "B", "A", "N", "G", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-15 13:09:59', '2025-05-15 14:31:52'),
	('4de35ed4-b10a-4041-b121-2e192baf8a46', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', '29c8f261-9294-4855-86d1-eb93673b21d8', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-14 13:29:08', '2025-05-15 13:41:51'),
	('524847fb-0799-485c-9037-34eba9b62359', 'd6dc9b13-e313-455d-aacd-40bdf6f3638b', '25ed640a-d93f-4921-8b1d-fc18febf97e9', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-15 14:03:26', '2025-05-15 14:03:26'),
	('7a64fc31-883e-4e70-8c8e-afd2aa1d0284', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', 'd92b6c26-b418-43d5-8720-64152c906778', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-14 14:16:12', '2025-05-15 13:21:51'),
	('92536fe4-4241-467f-9722-2eb8d49b2648', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', '25ed640a-d93f-4921-8b1d-fc18febf97e9', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-14 08:46:39', '2025-05-15 12:49:42'),
	('a4d2f940-7d6d-4d2a-916b-b957517f0ce3', 'd6dc9b13-e313-455d-aacd-40bdf6f3638b', '80fe76fa-389e-4277-a73a-99d86c423c23', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-15 13:13:14', '2025-05-15 14:02:36'),
	('a612bf41-b65b-426e-b986-4a996930981e', 'd6dc9b13-e313-455d-aacd-40bdf6f3638b', 'd92b6c26-b418-43d5-8720-64152c906778', '{"progress": 0, "userAnswers": [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-15 13:17:24', '2025-05-15 13:17:24'),
	('a7f36a0a-2beb-4b51-a728-1467203eff1c', '5b4dc128-8ba5-4bd3-b990-6de4ddd11522', '3da57db1-96ef-4ff4-a6fb-69878f5ad034', '{"progress": 23, "userAnswers": [["", "", "B", "A", "T", "I", "K", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "E", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "R", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "I", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "S", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "S", "I", "N", "D", "E", "N", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "K", "E", "R", "A", "T", "O", "N", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "revealedCells": []}', 0, NULL, '2025-05-14 14:08:13', '2025-05-15 14:01:35');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
