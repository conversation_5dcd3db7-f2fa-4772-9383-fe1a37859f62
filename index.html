<!doctype html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/images/TTS.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Tekateki.io - Teka Teki Silang Online. Mainkan ribuan teka-teki silang atau buat sendiri dan bagikan ke dunia." />
    <meta name="keywords" content="teka teki silang, TTS, crossword puzzle, game teka-teki, puzzle Indonesia, Tekateki.io" />
    <meta name="author" content="Tekateki.io" />
    <meta name="robots" content="index, follow" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="google-site-verification" content="REPLACE_WITH_YOUR_VERIFICATION_CODE" />

    <!-- Content Security Policy -->
    <!-- <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https://source.unsplash.com https://www.google-analytics.com;
      font-src 'self';
      connect-src 'self' https://openrouter.ai https://tts-api.widiyanata.com http://localhost:1111 https://www.google-analytics.com;
      frame-src 'self';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      frame-ancestors 'self';
      upgrade-insecure-requests;
    " /> -->
    <title>Tekateki.io - Teka Teki Silang Online</title>
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Tekateki.io - Teka Teki Silang Online" />
    <meta property="og:description" content="Mainkan ribuan teka-teki silang atau buat sendiri dan bagikan ke dunia." />
    <meta property="og:site_name" content="Tekateki.io" />
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Tekateki.io - Teka Teki Silang Online" />
    <meta name="twitter:description" content="Mainkan ribuan teka-teki silang atau buat sendiri dan bagikan ke dunia." />

    <!-- Google Analytics - Optimized deferred loading for better performance -->
    <script>
      // Create a placeholder for gtag function
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      // Only load analytics in production
      if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        // Get the measurement ID from environment variable
        const measurementId = 'G-MEASUREMENT_ID';

        // Configure analytics
        gtag('config', measurementId, {
          'anonymize_ip': true,
          'cookie_expires': 365 * 24 * 60 * 60, // 1 year in seconds
          'send_page_view': false // We'll send page views manually for better control
        });

        // Use requestIdleCallback to load analytics when browser is idle
        const loadAnalytics = () => {
          // Defer loading the analytics script
          const analyticsScript = document.createElement('script');
          analyticsScript.async = true;
          analyticsScript.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
          document.head.appendChild(analyticsScript);
        };

        // Use requestIdleCallback if available, otherwise use setTimeout
        if ('requestIdleCallback' in window) {
          window.requestIdleCallback(loadAnalytics, { timeout: 5000 });
        } else {
          // Fallback for browsers that don't support requestIdleCallback
          setTimeout(loadAnalytics, 3000); // 3 second delay
        }
      }
    </script>

    <!-- Preload critical resources -->
    <link rel="preload" href="/images/TTS.png" as="image" />
    <link rel="preconnect" href="https://openrouter.ai" />
    <link rel="preconnect" href="http://localhost:1111" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
