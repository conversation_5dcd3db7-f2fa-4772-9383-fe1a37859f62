# System Patterns: Crossword Generator

## 1. Overall Architecture
The project appears to follow a client-server architecture:
- **Frontend (Client-Side):** A Single Page Application (SPA) built with React (likely using TypeScript, given `.tsx` files and `tsconfig.json`). It handles user interaction, rendering the crossword grid, and communicating with the backend API.
- **Backend (Server-Side):** A PHP-based API that manages data persistence (puzzles, categories, etc.) and potentially business logic not suitable for the client. The `api/` directory structure suggests a custom MVC-like pattern or a micro-framework.
- **Database:** A relational database is implied by `api/schema.sql` and PHP database configuration files (`api/config/database.php`). This likely stores crossword data, categories, and user information.
- **Serverless Functions:** Initially, Supabase functions were considered due to the `supabase/` directory, but it has been confirmed that Supabase is not used.

## 2. Frontend Patterns
- **Component-Based Architecture:** Standard React pattern, with UI broken down into reusable components (e.g., `CrosswordGrid.tsx`, `CategoryList.tsx`).
- **State Management:**
    - `src/context/CrosswordContext.tsx`: Manages shared crossword-related state (grid, words, clues, mode, etc.). Key actions include `ADD_WORD`, `AUTO_PLACE_WORD`, `REMOVE_WORD`, `SELECT_WORD`, `SELECT_CELL`, `SET_GRID_SIZE`, `OPTIMIZE_GRID`, `RESET`, `SET_MODE`, `SET_STATE`, `UPDATE_USER_ANSWER`, `LOAD_PUZZLE`, and `RESET_USER_ANSWERS`.
    - `src/context/AuthContext.tsx` (New): Manages user authentication state (`isAuthenticated`, `user`, `isLoading`, `error`). Provides `login`, `register`, and `logout` functions that now call the respective API services (`apiLogin`, `apiRegister`, `apiLogout` from `../services/api.ts`). The `register` function also attempts to log the user in after successful registration. Includes a reducer for auth state transitions (e.g., `LOGIN_REQUEST`, `LOGIN_SUCCESS`, `LOAD_USER_FAILURE`, `REGISTER_SUCCESS`). An effect hook calls `apiFetchCurrentUser` on mount to check for an active session and updates the auth state accordingly.
    - Local component state (`useState`, `useReducer`) is used for component-specific data.
- **Routing (`App.tsx`):**
    - Uses `react-router-dom` for client-side routing.
    - The entire application is wrapped with `AuthProvider` (from `AuthContext.tsx`) and then `CrosswordProvider` (from `CrosswordContext.tsx`).
    - Defines top-level routes for:
        - Authentication: `/login` (renders `LoginPage.tsx`) and `/register` (renders `RegisterPage.tsx`). These are outside the main application layout.
        - Protected Admin Routes: Routes like `/admin` and `/admin/categories` are wrapped with `ProtectedRoute.tsx` and passed `allowedRoles={['admin']}` to ensure only authenticated admin users can access them. These routes render their respective pages without the main `Header`/`Footer`.
    - A catch-all route (`path="*"`) handles all other public routes, rendering them within a main layout that includes `<Header />` and `<Footer />`.
    - Within this main layout, specific routes like `/create` are also wrapped with `ProtectedRoute.tsx` (without specific roles, meaning any authenticated user can access).
- **Component (`ProtectedRoute.tsx` - New):**
    - A higher-order component that uses `useAuth()` from `AuthContext`.
    - Accepts an optional `allowedRoles?: string[]` prop.
    - Checks `auth.isLoading` to show a loading indicator.
    - If not loading and `auth.isAuthenticated` is false, it redirects to `/login` (passing current `location` in state).
    - If authenticated and `allowedRoles` are provided, it checks if `auth.user?.role` is included in `allowedRoles`. If not, it redirects to `/` (home).
    - If authenticated and authorized (either no roles specified or user has an allowed role), it renders `<Outlet />`.
- **Core UI Component (`CrosswordGrid.tsx`):**
    - Directly uses `useCrossword()` from `CrosswordContext` for state (`state.grid`, `state.gridSize`, `state.selectedWordId`, `state.mode`, `state.userAnswers`, `state.wordPositions`) and dispatching actions (`SELECT_CELL`, `UPDATE_USER_ANSWER`).
    - Renders the grid dynamically using CSS Grid. Cell appearance changes based on state (empty, selected, regular).
    - Displays word numbers in appropriate cells.
    - In 'edit' mode, displays cell characters.
    - In 'play' mode, renders individual `<input>` elements for each active cell, allowing single character input.
    - Implements auto-advance to the next cell within the current word upon input in 'play' mode.
    - Handles cell click to select a cell/word.
- **Page Component (`CreatePage.tsx`):**
    - Manages local state for form inputs (title, description, difficulty, categoryId).
    - Fetches categories using `getCategories` from `src/services/api.ts` on mount.
    - Uses `CrosswordContext` to dispatch `SET_MODE` ('edit') and `RESET` actions on mount, preparing the editor.
    - Uses `AuthContext` to retrieve the authenticated user's ID.
    - Renders a form for crossword metadata.
    - Embeds `CrosswordApp` component (likely the main editor interface).
    - `handleSave` function:
        - Validates title and checks if the crossword has at least one word (using `state.words`, `state.wordPositions`, `state.clues` from context).
        - Constructs payload including metadata, the authenticated `user_id` (if available), and the entire `state` from `CrosswordContext`.
        - Calls `createCrossword` service from `src/services/api.ts`.
        - Handles loading states, error messages, and success messages.
        - Navigates to the play page of the newly created crossword on success.
- **API Interaction (`src/services/api.ts`):**
    - Uses the native `fetch` API for all backend communication.
    - Defines a hardcoded `API_BASE_URL = 'http://localhost:1111'`.
    - Expects a common `ApiResponse<T>` structure (`{ status, data?, message?, pagination? }`).
    - Provides typed functions for various endpoints:
        - **Crosswords:**
            - `getCrosswords(filters, pagination)`: GET `/api/crosswords`
            - `getCrosswordById(id)`: GET `/api/crosswords/{id}`
            - `createCrossword(payload)`: POST `/api/crosswords` (sends full frontend `CrosswordState`)
            - `updateCrossword(id, payload)`: PUT `/api/crosswords/{id}`
            - `recordCrosswordPlay(id)`: POST `/api/crosswords/{id}/play`
            - `rateCrossword(id, rating)`: POST `/api/crosswords/{id}/rate`
        - **Categories:**
            - `getCategories()`: GET `/api/categories`
            - `getCategoryById(id)`: GET `/api/categories/{id}`
            - `createCategory(payload)`: POST `/api/categories`
            - `updateCategory(id, payload)`: PUT `/api/categories/{id}`
            - `deleteCategory(id)`: DELETE `/api/categories/{id}`
        - **Users (New):**
            - `registerUser(userData)`: POST `/api/users/register`
            - `loginUser(credentials)`: POST `/api/users/login`
            - `logoutUser()`: POST `/api/users/logout`
            - `fetchCurrentUser()`: GET `/api/users/me`
    - Defines TypeScript interfaces for API data structures (e.g., `CrosswordData`, `Category`, `User`, `LoginCredentials`, `RegisterUserData`).
- **Utility Functions:** `src/utils/` directory contains helper functions and business logic.
    - **`crosswordLogic.ts`:** Handles significant client-side crossword generation and manipulation.
        - Key algorithms include:
            - `findPossiblePlacements(word, state)`: Identifies and scores valid placements for a new word by checking intersections with existing words on the grid.
            - `tryPlacement(word, direction, startRow, startCol, intersectionIdx, state)`: Validates if a word can be placed at a specific position without conflicts or going out of bounds.
            - `calculatePlacementScore(word, direction, startRow, startCol, state)`: Assigns a score to a potential placement based on factors like number of intersections, proximity to center, compactness, and use of uncommon letters.
            - `optimizeWordPlacement(words, maxIterations)`: A high-level function that attempts to generate an optimal crossword grid by iteratively trying different word orders and placements, using `findPossiblePlacements` and `calculateGridScore` to evaluate outcomes.
            - `calculateGridScore(state)`: Evaluates the overall quality of a generated grid based on density and intersection ratio.
            - `optimizeGrid(state)`: Resizes the grid to tightly fit the existing words, with some padding.
            - `getWordNumbers(state)`: Generates a map of cell coordinates to word numbers for display.
        - The generation logic appears to be primarily client-side, with the backend mainly used for storing and retrieving the puzzle state.
    - **`wordAnalysis.ts`:**
        - Provides `analyzeWordCompatibility(words: string[])` function.
        - This function calculates a compatibility score (0-100) for a given list of words based on the frequency of common predefined letters (A, E, I, N, R, S, T).
        - It also generates an array of string recommendations to improve the word list, considering:
            - Word length distribution (penalizing too many short or long words).
            - Frequency of predefined rare letters (Q, X, Z, J, K, V, W, Y).
            - Overall low compatibility score.
        - If no issues are found, it provides a positive feedback message.
        - This utility serves as an advisory tool for users, helping them select words that are more suitable for crossword puzzle generation.
- **Crossword Editor Components (`src/components/` related to `CrosswordApp.tsx`):**
    - **`CrosswordApp.tsx`**:
        - Orchestrates the main UI for creating and editing crosswords, typically embedded within `CreatePage.tsx`.
        - Features a two-column layout. The main column has tabs for "Grid" (displaying `CrosswordGrid.tsx`) and "Words & Clues" (displaying `CrosswordClues.tsx`), with a `Controls.tsx` component below. The secondary column displays `WordInputForm.tsx`.
    - **`WordInputForm.tsx`**:
        - Central component for adding words to the puzzle.
        - Supports multiple input methods:
            - Single word input with manual placement (direction, row, col) via `ADD_WORD` dispatch.
            - Single word input with automatic placement via `AUTO_PLACE_WORD` dispatch.
            - Batch input (multiple "word:clue" lines) processed into `AUTO_PLACE_WORD` dispatches.
            - AI-powered word generation using OpenRouter.ai (Gemini model), requiring an API key. Generated words are analyzed for compatibility using an internal `analyzeWordCompatibility` function before being dispatched via `AUTO_PLACE_WORD`.
            - Excel file import/export (using `xlsx` library) for words, clues, and optional placement data. Import dispatches `ADD_WORD` or `AUTO_PLACE_WORD`.
        - Provides an Excel template for download.
    - **`CrosswordClues.tsx`**:
        - Displays sorted lists of "Across" and "Down" clues derived from `CrosswordContext state.clues` and `state.wordPositions`.
        - Allows users to select a word in the grid by clicking its clue, which dispatches a `SELECT_WORD` action.
        - Highlights the currently selected clue.
    - **`Controls.tsx`**:
        - Offers various actions for managing the crossword editor state:
            - "Optimize Grid": Dispatches `OPTIMIZE_GRID` (likely triggers `optimizeGrid` from `crosswordLogic.ts` via reducer).
            - "Grid Size": Allows users to change the grid dimensions via `SET_GRID_SIZE` dispatch.
            - "Save": Performs a client-side save of the entire `CrosswordContext state` to a `crossword.json` file.
            - "Toggle Mode": Switches between 'edit' and 'play' modes via `SET_MODE` dispatch.
            - "Reset": Clears the entire crossword (words, grid, clues) after user confirmation, via `RESET` dispatch.
            - "Optimize Layout": Directly calls `optimizeWordPlacement(state.words)` from `crosswordLogic.ts` and dispatches `SET_STATE` with the newly generated layout.
- **Admin Panel Components (`src/pages/admin/`, `src/components/admin/`):**
    - **`AdminCategoriesPage.tsx`**:
        - Provides a CRUD interface for managing categories, using `AdminLayout`.
        - Fetches categories using `getCategories` service from `api.ts`.
        - Displays categories in a table, showing name, description, crossword count, and creation date.
        - Uses `CategoryForm.tsx` (modal/inline form) for adding new categories and editing existing ones.
        - Uses `DeleteConfirmationModal.tsx` before deleting a category.
        - Delete functionality is disabled if a category has associated crosswords (`crossword_count > 0`).
    - **`CategoryForm.tsx`**:
        - A reusable form component for both creating and updating categories.
        - Manages state for form fields (name, description, image URL) and validation errors.
        - Validates that the category name is not empty and the image URL (if provided) is a valid URL.
        - Provides an image preview for valid image URLs.
        - Calls `createCategory` or `updateCategory` API services upon submission.
        - Communicates success or cancellation via `onSuccess` and `onCancel` props.
    - **`DeleteConfirmationModal.tsx`**:
        - A modal dialog to confirm category deletion.
        - Calls the `deleteCategory` API service.
        - Uses `onConfirm` and `onCancel` props to signal the outcome to the parent component (`AdminCategoriesPage.tsx`).
    - **`AdminDashboardPage.tsx`**:
        - Uses `AdminLayout`.
        - Acts as a central navigation page for the admin area.
        - Displays a grid of static "dashboard cards", each linking to a different admin section:
            - "Kelola Kategori" (Manage Categories) -> `/admin/categories`
            - "Kelola Pengguna" (Manage Users) -> `/admin/users`
            - "Kelola TTS" (Manage Crosswords) -> `/admin/crosswords`
            - "Pengaturan" (Settings) -> `/admin/settings`
        - Includes a placeholder section for "Aktivitas Terbaru" (Recent Activity), currently static.
        - Does not fetch dynamic data itself but links to other admin pages that might.
- **Page Component (`PlayPage.tsx`):**
    - Fetches a specific crossword by ID using `getCrosswordById` service and records a play via `recordCrosswordPlay`.
    - Dispatches a `LOAD_PUZZLE` action to `CrosswordContext` with the processed data from the API to initialize the shared puzzle state (grid, words, clues, etc.) and set the mode to 'play'.
    - Manages local state primarily for puzzle metadata (title, creator, difficulty, category) not stored in the central context, and for UI states like `isLoading`.
    - Renders the `<CrosswordGrid />` component for grid display and interaction, and `<CrosswordClues />` for clue display, leveraging the `CrosswordContext`.
    - Provides "Check Answers" (compares context `userAnswers` with context `grid`) and "Reset Jawaban" (dispatches `RESET_USER_ANSWERS`) functionalities.
    - Displays puzzle metadata and uses `<CrosswordClues />` which correctly shows word numbers.
    - Includes a cleanup effect to dispatch `RESET` to the context when the component unmounts or `puzzleId` changes.
- **Page Component (`RegisterPage.tsx` - New):**
    - Provides a UI form for new user registration with fields for username, email, password, and an optional display name.
    - Manages local state for form inputs and displays form-level validation errors (e.g., required fields, password length).
    - On form submission, it calls a (currently placeholder) `register` function, intended to eventually use `AuthContext` to interact with the `registerUser` API service.
    - Navigates to the login page upon simulated successful registration.
    - Includes a link to the login page for existing users.
- **Page Component (`LoginPage.tsx` - New):**
    - Provides a UI form for user login with fields for email and password.
    - Manages local state for form inputs and displays form-level validation errors.
    - On form submission, it calls a (currently placeholder) `login` function, intended to eventually use `AuthContext` to interact with the `loginUser` API service.
    - Navigates to the homepage upon simulated successful login.
    - Includes a link to the registration page for new users.
- **Component (`Header.tsx`):**
    - Displays the application logo and primary navigation links (Beranda, Main, Buat).
    - Dynamically renders user authentication status using `useAuth()` from `AuthContext`:
        - If authenticated: Shows a user dropdown menu (desktop) or section (mobile) with the user's display name/username, a link to the "Admin Panel", and a "Logout" button (with `LogOutIcon`) that calls `auth.logout()`.
        - If not authenticated: Shows "Login" and "Register" links (with `LogInIcon`, `UserPlusIcon`).
    - Includes a toggleable mobile menu for navigation on smaller screens.
    - Uses `lucide-react` for icons.
- **Styling:** `tailwind.config.js` and `index.css` indicate the use of Tailwind CSS for utility-first styling.

## 3. Backend Patterns (PHP API)
- **MVC-like Structure:**
    - `api/models/` (e.g., `CategoryModel.php`, `CrosswordModel.php`): Likely handle database interactions and data representation.
    - `api/controllers/` (e.g., `CategoryController.php`, `CrosswordController.php`): Handle incoming API requests, interact with models, and formulate responses.
    - `api/index.php`: Acts as the entry point or front controller.
- **Configuration:** `api/config/config.php` centralizes key settings:
    - `APP_ENV`: Environment (development/production).
    - Error reporting levels based on `APP_ENV`.
    - Database connection constants (`DB_HOST`, `DB_NAME`, `DB_USER`, `DB_PASS`).
    - `API_VERSION`, `API_BASE_URL`.
    - Default timezone (`UTC`).
    - Includes `database.php` (which sets up a PDO connection via a Singleton `Database` class) and `helpers.php`.
- **Database Connection Management:** The `api/config/database.php` file defines a `Database` class that uses the Singleton pattern to provide a single PDO connection instance. It configures PDO for error exceptions (`PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION`) and associative array fetching (`PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC`).
- **Routing (Confirmed from `api/index.php`):**
    - **Session Management:** `session_start()` is called at the beginning of `api/index.php` to enable session-based authentication.
    - **Entry Point:** `api/index.php` is the sole entry point for API requests.
    - **CORS Handling:** Sets `Access-Control-Allow-Origin: *` and handles `OPTIONS` preflight requests.
    - **URI Parsing:** Parses `$_SERVER['REQUEST_URI']` to determine the resource (e.g., 'crosswords', 'categories', 'users') and an optional ID/action. It expects the API base path to be 'api' (e.g., `example.com/api/resource/id_or_action`).
    - **Request Method Handling:** Uses `$_SERVER['REQUEST_METHOD']` (GET, POST, PUT, DELETE).
    - **Request Body Parsing:** Decodes JSON from `php://input` for POST/PUT requests.
    - **Controller Dispatch:** A `switch` statement based on the resource dynamically requires and instantiates controller classes (e.g., `CrosswordController`, `CategoryController`).
        - `CrosswordController.php`:
            - Handles GET all (with filtering for `category_id`, `difficulty`, `is_public`, `user_id` and pagination `limit`, `page`), GET by ID.
            - Handles POST for create (validates required fields, sanitizes input).
            - Handles PUT for update (validates, sanitizes).
            - Handles DELETE by ID.
            - Special POST actions: `/crosswords/{id}/play` (calls `recordPlay`), `/crosswords/{id}/rate` (calls `rate`, validates rating).
            - Instantiates `CrosswordModel` for database operations.
            - Uses helper functions like `validateRequiredFields()` and `sanitizeInput()` (from `api/utils/helpers.php`).
        - `CategoryController.php`:
            - Handles GET all (enhances with `crossword_count` from `CategoryModel`), GET by ID (enhances with `crossword_count`).
            - Handles POST for create (validates `name`, sanitizes `name`, `description`, `image_url`).
            - Handles PUT for update (validates `name` if provided, sanitizes inputs).
            - Handles DELETE by ID (checks `crossword_count` from `CategoryModel` to prevent deletion if crosswords are associated).
            - Instantiates `CategoryModel` for database operations.
            - Uses `sanitizeInput()` (from `api/utils/helpers.php`).
        - `UserController.php`:
            - `register($data)`: Handles user registration. Validates `username`, `email`, `password`. Checks for existing username/email via `UserModel`. Calls `UserModel->create()` to save the new user (password is hashed). Returns new user ID on success.
            - `login($data)`: Handles user login. Validates `email`, `password`. Finds user by email. Verifies password using `password_verify()`. If successful, regenerates session ID, stores `user_id` and `username` in `$_SESSION`, and returns user data (excluding password hash).
            - `logout()`: Clears `$_SESSION` array, destroys the session cookie (if used), and calls `session_destroy()`. Returns success message.
            - `getCurrentUser()`: Checks for `$_SESSION['user_id']`. If set, fetches user details (excluding password hash) using `UserModel->findById()`. Returns user data or throws 401 if not authenticated/user not found.
            - Instantiates `UserModel`.
            - Uses `validateRequiredFields()` and `sanitizeInput()` (from `api/utils/helpers.php`).
- **Model Layer (`CrosswordModel.php`):**
    - Obtains PDO connection from `Database::getInstance()`.
    - `getAll()`: Constructs dynamic SQL for fetching crosswords with filters (category, difficulty, public status, user ID) and pagination. Joins `user_profiles` for creator username.
    - `getById()`: Fetches a single crossword, joins `user_profiles`. Decodes JSON fields (`grid_data`, `words`, `clues`, `word_positions`) into PHP arrays. Formats a `state` object for potential frontend compatibility.
    - `create()`: Handles insertion. Can take direct data or a `state` object (similar to frontend context). Encodes data to JSON for storage. Generates UUIDs.
    - `update()`: Dynamically builds `SET` clause for updates. Can also process a `state` object.
    - `delete()`: Deletes by ID.
    - `incrementPlays()`, `updateRating()`: Specific update operations.
    - `generateUuid()`: Private helper for UUID v4 generation.
- **Model Layer (`CategoryModel.php`):**
    - Similar structure to `CrosswordModel`, uses `Database::getInstance()`.
    - `getAll()`: Fetches all categories, ordered by name.
    - `getById()`: Fetches a single category.
    - `create()`: Creates a new category. Uses a global `generateUuid()` function (from `helpers.php`).
    - `update()`: Updates category details.
    - `delete()`: Deletes a category.
    - `countCrosswords()`: Counts crosswords associated with a category ID.
- **Model Layer (`UserModel.php` - New):**
    - Obtains PDO connection from `Database::getInstance()`.
    - `create($username, $email, $password, $displayName)`: Creates a new user. Hashes the password using `password_hash()`. Uses global `generateUuid()`.
    - `findByEmail($email)`: Retrieves a user by their email address.
    - `findByUsername($username)`: Retrieves a user by their username.
    - `findById($id)`: Retrieves a user by their ID (omits password hash).
    - **Action Dispatch:** Within each resource case, it further routes to controller methods based on the HTTP method and presence of an ID or specific action segments in the URI (e.g., `/crosswords/{id}/play`, `/crosswords/{id}/rate`, `/users/register`).
    - **Error Handling:** `api/index.php` uses a global `try-catch` block. On exception, it calls `jsonResponse()` (from `helpers.php`) to return a JSON error message with an appropriate HTTP status code (e.g., from `$e->getCode()` or a default). Controllers also throw exceptions for specific errors (e.g., "Crossword not found", validation failures). Model methods also throw exceptions for database errors.
- **Helper Utilities (`api/utils/helpers.php`):** This file, included via `config.php`, provides common functions:
    - `sanitizeInput($data)`: Recursively trims, strips slashes, and applies `htmlspecialchars` to input data. Used by controllers.
    - `jsonResponse($data, $statusCode)`: Sets HTTP status code, `Content-Type: application/json` header, echoes JSON encoded data, and then calls `exit`. Used by `api/index.php` for success and error responses.
    - `generateRandomString($length)`: Generates a random alphanumeric string (current usage in the project TBD).
    - `validateRequiredFields($data, $requiredFields)`: Checks if all specified fields are present and not empty in the `$data` array. Throws an `Exception` with code 400 if a field is missing. Used by `CrosswordController`.
    - `generateUuid()`: Generates a v4 UUID. Used by `CategoryModel`. Note: `CrosswordModel` implements its own private `generateUuid` method, which is an inconsistency.

## 4. Database Patterns
- **Relational Database:** Confirmed by `schema.sql` and connection details in `config.php` and `database.php` (MySQL via PDO). The schema uses `VARCHAR(36)` for IDs (confirmed to be UUIDs, e.g., generated by `CrosswordModel.php`'s private `generateUuid()` method and `CategoryModel.php`'s use of a global `generateUuid()`). Timestamps `created_at` and `updated_at` are common.
- **Key Entities (Confirmed from `schema.sql`):**
    - `crosswords`: Stores puzzle metadata (ID, title, description, grid_size), actual puzzle data (grid_data, words, clues, word_positions as JSON), difficulty, user ID (creator), publicity status, play count, rating, and category ID.
    - `categories`: Stores category information (ID, name, description, image_url).
    - `user_profiles`: Stores user information (ID, username, email, password_hash, display_name, avatar_url, bio).
    - `user_progress`: Tracks user progress on specific crosswords (ID, user_id, crossword_id, progress_data as JSON, completion status, time_spent).
- **Relationships (Confirmed from `schema.sql`):**
    - `crosswords` to `categories`: Many-to-one (a crossword belongs to one category, via `category_id`).
    - `crosswords` to `user_profiles`: Many-to-one (a crossword can be created by one user, via `user_id`).
    - `user_progress` to `user_profiles`: Many-to-one (progress belongs to one user).
    - `user_progress` to `crosswords`: Many-to-one (progress is for one crossword).
- **Data Types:** Extensive use of `JSON` for storing complex data like grid layouts, word lists, and clues. `ENUM` is used for difficulty.
- **Indexes:** Defined for common query paths (category, user, public status, difficulty on crosswords; user and crossword on progress).

## 5. Supabase Functions (Not Used)
- The `supabase/` directory exists, but it has been confirmed that Supabase functions are not actively used in this project.

## 6. Build and Development
- **Vite:** `vite.config.ts` indicates Vite is used as the frontend build tool and development server.
- **TypeScript:** Used for frontend development, providing static typing.
- **ESLint:** `eslint.config.js` for code linting and maintaining code quality.
- **NPM:** `package.json` and `package-lock.json` for managing frontend dependencies.

## 7. Key Technical Decisions (Inferred)
- **Frontend Framework:** React with TypeScript.
- **Backend Language:** PHP.
- **Styling:** Tailwind CSS.
- **Build Tool:** Vite.

## 8. Areas for Further Investigation
- Authentication and authorization mechanisms, if any (still no clear signs).
- How `WordInputForm.tsx` and `Controls.tsx` invoke `crosswordLogic.ts` functions (either directly or via context dispatches) is now much clearer. The primary remaining UI integration question is how `CrosswordApp.tsx` itself might coordinate these if not solely through its children.
- Comprehensive error handling and logging strategies across the full stack (beyond basic controller/model exceptions).
- Specifics of admin panel functionalities for Users, Crosswords (TTS), and Settings, as linked from `AdminDashboardPage.tsx`.
