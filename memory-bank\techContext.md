# Tech Context: Crossword Generator

## 1. Frontend Technologies
- **Core Framework:** React (`react`, `react-dom`)
- **Language:** TypeScript (`typescript`, as seen in `.tsx` files and `tsconfig.json`)
- **Build Tool / Dev Server:** Vite (`vite`, `vite.config.ts`)
- **Package Manager:** NPM (implied by `package.json`, `package-lock.json`)
- **Styling:**
    - Tailwind CSS (`tailwindcss`, `tailwind.config.js`)
    - PostCSS (`postcss`, `postcss.config.js`) for processing Tailwind.
- **Linting:** ESLint (`eslint`, `eslint.config.js`) for code quality and consistency.
- **Routing:** React Router DOM (`react-router-dom`), as confirmed in `src/App.tsx`. Defines public routes (e.g., `/`, `/create`, `/category/:categoryId`, `/play/:puzzleId`) and admin routes (e.g., `/admin`, `/admin/categories`). Uses nested routes.
- **State Management:** React Context API (`src/context/CrosswordContext.tsx`), with `CrosswordProvider` wrapping the application in `src/App.tsx`.
- **Animation:** Framer Motion (`framer-motion`)
- **Icons:** Lucide React (`lucide-react`)
- **Excel File Handling:** XLSX (`xlsx`) - This is an interesting dependency, perhaps for importing/exporting puzzles.
- **HTTP Client:** Native `fetch` API (confirmed from `src/services/api.ts`).
    - Frontend API Base URL: `http://localhost:1111` (hardcoded in `src/services/api.ts`).

## 2. Backend Technologies (PHP API)
- **Language:** PHP (Version 8.3 as per comments in `config.php` and `index.php`)
- **Web Server Configuration:** Apache (implied by `.htaccess` file in `api/`) for URL rewriting, likely directing all API traffic to `api/index.php`.
- **API Entry Point:** `api/index.php` handles all incoming API requests.
- **API Routing:** A simple custom router in `api/index.php` parses the URI (expecting `/api/{resource}/{id?}/{action?}`) and dispatches to controller methods based on the resource, ID, action, and HTTP method.
- **CORS:** `api/index.php` sets `Access-Control-Allow-Origin: *` and handles `OPTIONS` preflight requests.
- **Request Handling:**
    - Reads JSON body from `php://input` for `POST`/`PUT`.
    - Returns JSON responses.
- **API Base URL (Backend Perspective):** `/api` (defined in `config.php` and used in `index.php` routing logic). Note: Frontend service `api.ts` uses `http://localhost:1111` as its target.
- **Environment:** Configured for 'development' mode (error reporting enabled in `config.php`, though `index.php` sets `display_errors` to 0 but `error_reporting` to `E_ALL`).
- **Timezone:** UTC.
- **Database Interaction:** Custom PHP scripts. `api/config/config.php` defines DB connection constants:
    - Host: `localhost`
    - DB Name: `crosswords_db`
    - User: `root`
    - Password: (empty)
    It also includes `api/config/database.php` (which establishes the PDO connection using a Singleton pattern) and `api/utils/helpers.php`.
- **Database Abstraction:** PHP Data Objects (PDO) is used for database communication, as seen in `api/config/database.php`. The connection uses `utf8mb4` charset.
- **Dependency Management (PHP):** No `composer.json` visible at the root or `api/` directory, suggesting either no Composer dependencies or it's managed elsewhere/manually.

## 3. Database
- **Type:** MySQL/MariaDB (confirmed by DSN `mysql:` in `database.php` and schema syntax).
- **Connection Details (from `config.php` & `database.php`):**
    - Driver: MySQL (PDO DSN: `mysql:host=...;dbname=...;charset=utf8mb4`)
    - Host: `localhost`
    - Database Name: `crosswords_db`
    - User: `root`
    - Password: (empty)
- **Schema Definition:** `api/schema.sql` defines the following tables:
    - `crosswords`: Stores puzzle details, including grid data, words, clues, and positions as JSON. Uses `VARCHAR(36)` for IDs (likely UUIDs).
    - `categories`: For categorizing crosswords.
    - `user_profiles`: For user information.
    - `user_progress`: Tracks user attempts and completion of crosswords.
- **Key Data Types:** `VARCHAR(36)` for IDs, `JSON` for complex structured data, `DATETIME` for timestamps, `ENUM` for difficulty levels.
- **Foreign Keys:** Established between `user_progress` and `user_profiles`/`crosswords`. `crosswords` also links to `categories` and `user_profiles`.
- **Indexes:** Implemented on foreign keys and frequently queried columns to optimize performance.
- **Data Population (Example):** `api/scripts/populate_categories.php` suggests scripts for initial data setup.

## 4. Serverless Functions (Supabase - Not Used)
- The `supabase/` directory and files within it (e.g., `supabase/functions/generate-words/index.ts`, `supabase/functions/get-puzzles-by-category/index.ts`) exist, but it has been confirmed that Supabase is not actively used in this project.

## 5. Development Environment & Tools
- **Node.js:** Required for the frontend toolchain (NPM, Vite).
- **PHP Interpreter:** Required for running the backend API.
- **Web Server (for PHP):** Apache (recommended due to `.htaccess`) or another server capable of running PHP (e.g., Nginx with PHP-FPM, built-in PHP dev server).
- **Database Server:** An instance of the chosen RDBMS (e.g., MySQL, PostgreSQL).
- **Code Editor:** VS Code (implied by user's environment).
- **Version Control:** Git (implied by `.gitignore`).

## 6. Technical Constraints & Considerations
- **PHP Backend Modernity:** The PHP backend appears to be custom-built without a prominent modern framework. This could impact maintainability, security, and scalability depending on its implementation quality.
- **API Design:** The API is handled entirely by the PHP backend.
- **Database Management:** Migrations and schema changes need a defined process.

## 7. Dependencies (Key Frontend - from `package.json` if available, otherwise inferred)
- `react`, `react-dom`
- `typescript`
- `vite`
- `@vitejs/plugin-react`
- `tailwindcss`, `postcss`, `autoprefixer`
- `eslint` and related plugins
- `react-router-dom`
- `framer-motion`
- `lucide-react`
- `xlsx`

**Dev Dependencies:**
- `@eslint/js`
- `@types/react`, `@types/react-dom`
- `@vitejs/plugin-react`
- `autoprefixer`
- `eslint`
- `eslint-plugin-react-hooks`
- `eslint-plugin-react-refresh`
- `globals`
- `postcss`
- `tailwindcss`
- `typescript`
- `typescript-eslint`
- `vite`
