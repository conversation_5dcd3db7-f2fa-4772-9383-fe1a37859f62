# Tekateki.io Mobile Application Implementation Plan

## Framework Analysis: React Native vs Flutter

### **React Native Advantages for Tekateki.io:**

1. **Code Reusability (85-90%)**
   - Existing React components can be adapted with minimal changes
   - Business logic, API services, and state management can be largely reused
   - TypeScript interfaces and types are directly transferable
   - Authentication flow and API integration patterns are identical

2. **Developer Experience**
   - Team already familiar with React ecosystem
   - Same debugging tools and development patterns
   - Existing knowledge of React hooks, context, and component lifecycle

3. **API Integration**
   - Fetch API works identically
   - Existing API service layer (`src/services/api.ts`) can be reused with minimal modifications
   - Authentication headers and session management patterns are the same

4. **Performance for Crossword Grid**
   - React Native's FlatList and VirtualizedList are excellent for grid rendering
   - Touch handling is native and responsive
   - Good performance for the crossword grid interactions

### **Flutter Advantages:**

1. **Superior Performance**
   - Better rendering performance for complex grids
   - More consistent 60fps animations
   - Better memory management for large crossword grids

2. **UI Consistency**
   - Pixel-perfect UI across platforms
   - Better control over custom crossword grid rendering
   - More sophisticated gesture handling

3. **Platform Integration**
   - Better native feature integration
   - More robust offline capabilities
   - Superior keyboard handling

### **Recommendation: React Native**

**Justification:**
1. **85-90% code reuse** from existing React codebase
2. **Faster development time** (estimated 3-4 months vs 6-8 months for Flutter)
3. **Lower learning curve** for the development team
4. **Proven performance** for similar grid-based applications
5. **Existing API integration** can be reused almost entirely

## Detailed Implementation Plan

### **Phase 1: Project Setup & Core Architecture (Week 1-2)**

#### 1.1 Project Initialization
```bash
npx react-native@latest init TekatekiMobile --template react-native-template-typescript
cd TekatekiMobile
```

#### 1.2 Essential Dependencies
```json
{
  "dependencies": {
    "@react-native-async-storage/async-storage": "^1.19.0",
    "@react-native-community/netinfo": "^9.4.0",
    "@react-navigation/native": "^6.1.0",
    "@react-navigation/stack": "^6.3.0",
    "@react-navigation/bottom-tabs": "^6.5.0",
    "react-native-gesture-handler": "^2.12.0",
    "react-native-reanimated": "^3.5.0",
    "react-native-safe-area-context": "^4.7.0",
    "react-native-screens": "^3.25.0",
    "react-native-vector-icons": "^10.0.0",
    "react-native-toast-message": "^2.1.0",
    "react-native-keyboard-aware-scroll-view": "^0.9.5",
    "@react-native-google-signin/google-signin": "^10.0.0"
  }
}
```

#### 1.3 Project Structure
```
src/
├── components/
│   ├── crossword/
│   │   ├── CrosswordGrid.tsx
│   │   ├── CrosswordClues.tsx
│   │   ├── CrosswordCell.tsx
│   │   └── PlayControls.tsx
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── LoadingSpinner.tsx
│   │   └── Toast.tsx
│   └── common/
│       ├── Header.tsx
│       └── ErrorBoundary.tsx
├── screens/
│   ├── HomeScreen.tsx
│   ├── PlayScreen.tsx
│   ├── CategoryScreen.tsx
│   ├── ProfileScreen.tsx
│   └── auth/
│       ├── LoginScreen.tsx
│       └── RegisterScreen.tsx
├── services/
│   ├── api.ts
│   ├── storage.ts
│   └── auth.ts
├── context/
│   ├── AuthContext.tsx
│   └── CrosswordContext.tsx
├── navigation/
│   ├── AppNavigator.tsx
│   ├── AuthNavigator.tsx
│   └── TabNavigator.tsx
├── types/
│   └── index.ts
└── utils/
    ├── constants.ts
    └── helpers.ts
```

### **Phase 2: API Integration & Authentication (Week 3-4)**

#### 2.1 API Service Layer (Adapted from Web)
```typescript
// src/services/api.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://localhost:1111'; // Will be configurable

interface ApiResponse<T> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const getHeaders = async (contentType = 'application/json'): Promise<HeadersInit> => {
  const token = await AsyncStorage.getItem('auth_token');
  return {
    'Content-Type': contentType,
    'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

const fetchAPI = async <T>(
  endpoint: string,
  options: RequestInit = {},
  cacheTTL: number | null = null
): Promise<ApiResponse<T>> => {
  try {
    const headers = await getHeaders();
    const response = await fetch(`${API_BASE_URL}/v1/api${endpoint}`, {
      ...options,
      headers: { ...headers, ...options.headers },
    });

    const data: ApiResponse<T> = await response.json();
    return data;
  } catch (error) {
    console.error('API error:', error);
    return {
      status: 'error',
      message: 'Network error occurred',
    };
  }
};

// Crossword API
export const crosswordAPI = {
  getAll: async (params: {
    category_id?: string;
    difficulty?: string;
    limit?: number;
    page?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return fetchAPI<Crossword[]>(`/crosswords${query}`);
  },

  getOne: async (idOrSlug: string) => {
    return fetchAPI<Crossword>(`/crosswords/${idOrSlug}`);
  },

  recordPlay: async (id: string) => {
    return fetchAPI<void>(`/crosswords/${id}/play`, { method: 'POST' });
  },
};

// User API
export const userAPI = {
  login: async (credentials: { email: string; password: string }) => {
    return fetchAPI<{ user: User; token: string }>('/users/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  googleLogin: async (data: {
    token: string;
    email: string;
    display_name: string;
    avatar_url: string;
  }) => {
    return fetchAPI<{ user: User; token: string }>('/users/google-login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  getCurrentUser: async () => {
    return fetchAPI<{ user: User }>('/users/me');
  },
};

// Progress API
export const progressAPI = {
  getOne: async (crosswordId: string) => {
    return fetchAPI<UserProgress>(`/progress/${crosswordId}`);
  },

  save: async (crosswordId: string, progressData: {
    progress_data: {
      userAnswers: any[][];
      revealedCells: number[][];
      progress: number;
    };
    is_completed: number;
    time_spent: number;
  }) => {
    return fetchAPI<{ id: string }>(`/progress/${crosswordId}`, {
      method: 'POST',
      body: JSON.stringify(progressData),
    });
  },
};
```

#### 2.2 Authentication Context
```typescript
// src/context/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userAPI } from '../services/api';

interface User {
  id: string;
  username: string;
  email: string;
  display_name: string;
  avatar_url?: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  googleLogin: (token: string, email: string, displayName: string, avatarUrl: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        const response = await userAPI.getCurrentUser();
        if (response.status === 'success' && response.data?.user) {
          setUser(response.data.user);
        } else {
          await AsyncStorage.removeItem('auth_token');
        }
      }
    } catch (err) {
      console.error('Auth check error:', err);
      await AsyncStorage.removeItem('auth_token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.login({ email, password });
      if (response.status === 'success' && response.data) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        setUser(response.data.user);
      } else {
        setError(response.message || 'Login failed');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const googleLogin = async (token: string, email: string, displayName: string, avatarUrl: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.googleLogin({
        token,
        email,
        display_name: displayName,
        avatar_url: avatarUrl,
      });
      if (response.status === 'success' && response.data) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        setUser(response.data.user);
      } else {
        setError(response.message || 'Google login failed');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem('auth_token');
    setUser(null);
  };

  const clearError = () => setError(null);

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      error,
      login,
      googleLogin,
      logout,
      clearError,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### **Phase 3: Core Crossword Components (Week 5-8)**

#### 3.1 Crossword Grid Component
```typescript
// src/components/crossword/CrosswordGrid.tsx
import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PanResponder,
} from 'react-native';
import { useCrossword } from '../../context/CrosswordContext';

interface CrosswordGridProps {
  crossword: Crossword;
  userAnswers: string[][];
  selectedCell: [number, number] | null;
  selectedWordId: string | null;
  revealedCells: number[][];
  incorrectCells: number[][];
  onSelectCell: (row: number, col: number) => void;
  showResults?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');
const GRID_PADDING = 20;
const MAX_GRID_SIZE = screenWidth - GRID_PADDING * 2;

const CrosswordCell: React.FC<{
  row: number;
  col: number;
  isBlack: boolean;
  isSelected: boolean;
  isPartOfWord: boolean;
  wordNumber: number | null;
  userAnswer: string;
  revealed: boolean;
  incorrect: boolean;
  correctChar: string;
  showResults: boolean;
  cellSize: number;
  onPress: () => void;
}> = React.memo(({
  row,
  col,
  isBlack,
  isSelected,
  isPartOfWord,
  wordNumber,
  userAnswer,
  revealed,
  incorrect,
  correctChar,
  showResults,
  cellSize,
  onPress,
}) => {
  const getCellStyle = () => {
    if (isBlack) return [styles.cell, styles.blackCell, { width: cellSize, height: cellSize }];

    let backgroundColor = '#FFFFFF';
    if (isSelected) backgroundColor = '#3B82F6';
    else if (isPartOfWord) backgroundColor = '#DBEAFE';
    else if (revealed) backgroundColor = '#FEF3C7';
    else if (showResults && incorrect) backgroundColor = '#FEE2E2';
    else if (showResults && userAnswer === correctChar) backgroundColor = '#D1FAE5';

    return [
      styles.cell,
      styles.whiteCell,
      { backgroundColor, width: cellSize, height: cellSize }
    ];
  };

  return (
    <TouchableOpacity
      style={getCellStyle()}
      onPress={onPress}
      disabled={isBlack}
      activeOpacity={0.7}
    >
      {!isBlack && (
        <>
          {wordNumber && (
            <Text style={[styles.cellNumber, { fontSize: cellSize * 0.25 }]}>
              {wordNumber}
            </Text>
          )}
          <Text style={[styles.cellText, { fontSize: cellSize * 0.5 }]}>
            {userAnswer || (showResults ? correctChar : '')}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
});

export const CrosswordGrid: React.FC<CrosswordGridProps> = ({
  crossword,
  userAnswers,
  selectedCell,
  selectedWordId,
  revealedCells,
  incorrectCells,
  onSelectCell,
  showResults = false,
}) => {
  const { grid_size, state } = crossword;

  const cellSize = useMemo(() => {
    return Math.floor(MAX_GRID_SIZE / grid_size) - 1;
  }, [grid_size]);

  const wordNumbersMap = useMemo(() => {
    const map = new Map<string, number>();
    state.wordPositions.forEach(wordPosition => {
      const key = `${wordPosition.row}-${wordPosition.col}`;
      map.set(key, wordPosition.number);
    });
    return map;
  }, [state.wordPositions]);

  const isPartOfSelectedWord = useCallback((row: number, col: number) => {
    if (!selectedWordId) return false;
    const cell = state.grid[row][col];
    return cell && cell.wordIds && cell.wordIds.includes(parseInt(selectedWordId));
  }, [selectedWordId, state.grid]);

  const isRevealed = useCallback((row: number, col: number) => {
    return revealedCells.some(([r, c]) => r === row && c === col);
  }, [revealedCells]);

  const isIncorrect = useCallback((row: number, col: number) => {
    return incorrectCells.some(([r, c]) => r === row && c === col);
  }, [incorrectCells]);

  const renderGrid = () => {
    const cells = [];
    for (let i = 0; i < grid_size; i++) {
      for (let j = 0; j < grid_size; j++) {
        const cell = state.grid[i][j];
        const isBlack = !cell || cell.char === ' ';
        const isSelected = selectedCell && selectedCell[0] === i && selectedCell[1] === j;
        const isPartOfWord = isPartOfSelectedWord(i, j);
        const wordNumber = wordNumbersMap.get(`${i}-${j}`) || null;
        const userAnswer = userAnswers[i]?.[j] || '';
        const revealed = isRevealed(i, j);
        const incorrect = isIncorrect(i, j);
        const correctChar = cell?.char || '';

        cells.push(
          <CrosswordCell
            key={`${i}-${j}`}
            row={i}
            col={j}
            isBlack={isBlack}
            isSelected={isSelected}
            isPartOfWord={isPartOfWord}
            wordNumber={wordNumber}
            userAnswer={userAnswer}
            revealed={revealed}
            incorrect={incorrect}
            correctChar={correctChar}
            showResults={showResults}
            cellSize={cellSize}
            onPress={() => onSelectCell(i, j)}
          />
        );
      }
    }
    return cells;
  };

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.grid,
          {
            width: MAX_GRID_SIZE,
            height: MAX_GRID_SIZE,
          }
        ]}
      >
        {renderGrid()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: GRID_PADDING,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  cell: {
    borderWidth: 0.5,
    borderColor: '#D1D5DB',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  blackCell: {
    backgroundColor: '#000000',
  },
  whiteCell: {
    backgroundColor: '#FFFFFF',
  },
  cellNumber: {
    position: 'absolute',
    top: 1,
    left: 1,
    fontWeight: 'bold',
    color: '#374151',
  },
  cellText: {
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
  },
});
```

#### 3.2 Crossword Clues Component
```typescript
// src/components/crossword/CrosswordClues.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

interface CrosswordCluesProps {
  crossword: Crossword;
  selectedWordId: string | null;
  onSelectWord: (wordId: string) => void;
}

export const CrosswordClues: React.FC<CrosswordCluesProps> = ({
  crossword,
  selectedWordId,
  onSelectWord,
}) => {
  const [activeTab, setActiveTab] = useState<'across' | 'down'>('across');

  const acrossClues = Object.entries(crossword.state.clues.across)
    .map(([number, clue]) => ({ number: parseInt(number), clue }))
    .sort((a, b) => a.number - b.number);

  const downClues = Object.entries(crossword.state.clues.down)
    .map(([number, clue]) => ({ number: parseInt(number), clue }))
    .sort((a, b) => a.number - b.number);

  const renderClue = (clueData: { number: number; clue: string }, direction: 'across' | 'down') => {
    const wordPosition = crossword.state.wordPositions.find(
      pos => pos.number === clueData.number && pos.direction === direction
    );
    const isSelected = selectedWordId === wordPosition?.id?.toString();

    return (
      <TouchableOpacity
        key={`${direction}-${clueData.number}`}
        style={[styles.clueItem, isSelected && styles.selectedClue]}
        onPress={() => wordPosition && onSelectWord(wordPosition.id.toString())}
      >
        <Text style={[styles.clueNumber, isSelected && styles.selectedClueText]}>
          {clueData.number}.
        </Text>
        <Text style={[styles.clueText, isSelected && styles.selectedClueText]}>
          {clueData.clue}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Tab Headers */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'across' && styles.activeTab]}
          onPress={() => setActiveTab('across')}
        >
          <Text style={[styles.tabText, activeTab === 'across' && styles.activeTabText]}>
            Mendatar ({acrossClues.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'down' && styles.activeTab]}
          onPress={() => setActiveTab('down')}
        >
          <Text style={[styles.tabText, activeTab === 'down' && styles.activeTabText]}>
            Menurun ({downClues.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Clues List */}
      <ScrollView style={styles.cluesList} showsVerticalScrollIndicator={false}>
        {activeTab === 'across'
          ? acrossClues.map(clue => renderClue(clue, 'across'))
          : downClues.map(clue => renderClue(clue, 'down'))
        }
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  activeTab: {
    backgroundColor: '#3B82F6',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  cluesList: {
    flex: 1,
    padding: 16,
  },
  clueItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedClue: {
    backgroundColor: '#3B82F6',
    borderColor: '#2563EB',
  },
  clueNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#374151',
    marginRight: 8,
    minWidth: 30,
  },
  clueText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
    lineHeight: 20,
  },
  selectedClueText: {
    color: '#FFFFFF',
  },
});
```

#### 3.3 Play Controls Component
```typescript
// src/components/crossword/PlayControls.tsx
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';

interface PlayControlsProps {
  onToggleDirection: () => void;
  onRevealCell: () => void;
  onRevealWord: () => void;
  onCheckAnswers: () => void;
  onSaveProgress: () => void;
  onResetCrossword: () => void;
  onSurrender: () => void;
  isCompleted: boolean;
  showResults: boolean;
  hintsRemaining: number;
}

export const PlayControls: React.FC<PlayControlsProps> = ({
  onToggleDirection,
  onRevealCell,
  onRevealWord,
  onCheckAnswers,
  onSaveProgress,
  onResetCrossword,
  onSurrender,
  isCompleted,
  showResults,
  hintsRemaining,
}) => {
  const handleRevealCell = () => {
    if (hintsRemaining <= 0) {
      Alert.alert('Petunjuk Habis', 'Anda sudah menggunakan semua petunjuk yang tersedia.');
      return;
    }
    Alert.alert(
      'Buka Sel',
      'Apakah Anda yakin ingin membuka sel ini? Ini akan menggunakan 1 petunjuk.',
      [
        { text: 'Batal', style: 'cancel' },
        { text: 'Ya', onPress: onRevealCell },
      ]
    );
  };

  const handleRevealWord = () => {
    if (hintsRemaining <= 0) {
      Alert.alert('Petunjuk Habis', 'Anda sudah menggunakan semua petunjuk yang tersedia.');
      return;
    }
    Alert.alert(
      'Buka Kata',
      'Apakah Anda yakin ingin membuka seluruh kata ini? Ini akan menggunakan 1 petunjuk.',
      [
        { text: 'Batal', style: 'cancel' },
        { text: 'Ya', onPress: onRevealWord },
      ]
    );
  };

  const handleReset = () => {
    Alert.alert(
      'Reset Teka-Teki',
      'Apakah Anda yakin ingin menghapus semua jawaban?',
      [
        { text: 'Batal', style: 'cancel' },
        { text: 'Ya', onPress: onResetCrossword },
      ]
    );
  };

  const handleSurrender = () => {
    Alert.alert(
      'Menyerah',
      'Apakah Anda yakin ingin menyerah dan melihat semua jawaban?',
      [
        { text: 'Batal', style: 'cancel' },
        { text: 'Ya', onPress: onSurrender },
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Primary Actions */}
      <View style={styles.primaryActions}>
        <TouchableOpacity style={styles.primaryButton} onPress={onToggleDirection}>
          <Text style={styles.primaryButtonText}>Ubah Arah</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.primaryButton, styles.checkButton]}
          onPress={onCheckAnswers}
          disabled={isCompleted}
        >
          <Text style={styles.primaryButtonText}>
            {showResults ? 'Memeriksa...' : 'Periksa Jawaban'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Hint Actions */}
      <View style={styles.hintActions}>
        <Text style={styles.hintText}>Petunjuk tersisa: {hintsRemaining}</Text>

        <View style={styles.hintButtons}>
          <TouchableOpacity
            style={[styles.hintButton, hintsRemaining <= 0 && styles.disabledButton]}
            onPress={handleRevealCell}
            disabled={hintsRemaining <= 0}
          >
            <Text style={[styles.hintButtonText, hintsRemaining <= 0 && styles.disabledText]}>
              Buka Sel
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.hintButton, hintsRemaining <= 0 && styles.disabledButton]}
            onPress={handleRevealWord}
            disabled={hintsRemaining <= 0}
          >
            <Text style={[styles.hintButtonText, hintsRemaining <= 0 && styles.disabledText]}>
              Buka Kata
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Secondary Actions */}
      <View style={styles.secondaryActions}>
        <TouchableOpacity style={styles.secondaryButton} onPress={onSaveProgress}>
          <Text style={styles.secondaryButtonText}>Simpan Progress</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.secondaryButton} onPress={handleReset}>
          <Text style={styles.secondaryButtonText}>Reset</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.secondaryButton, styles.surrenderButton]} onPress={handleSurrender}>
          <Text style={[styles.secondaryButtonText, styles.surrenderText]}>Menyerah</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  primaryActions: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  checkButton: {
    backgroundColor: '#10B981',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  hintActions: {
    marginBottom: 16,
  },
  hintText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  hintButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  hintButton: {
    flex: 1,
    backgroundColor: '#F59E0B',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  hintButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#D1D5DB',
  },
  disabledText: {
    color: '#9CA3AF',
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: 8,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  secondaryButtonText: {
    color: '#374151',
    fontSize: 12,
    fontWeight: '500',
  },
  surrenderButton: {
    borderColor: '#EF4444',
  },
  surrenderText: {
    color: '#EF4444',
  },
});
```

### **Phase 4: Navigation & Screens (Week 9-10)**

#### 4.1 Navigation Structure
```typescript
// src/navigation/AppNavigator.tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useAuth } from '../context/AuthContext';

// Screens
import HomeScreen from '../screens/HomeScreen';
import PlayScreen from '../screens/PlayScreen';
import CategoryScreen from '../screens/CategoryScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const TabNavigator = () => (
  <Tab.Navigator
    screenOptions={{
      tabBarActiveTintColor: '#3B82F6',
      tabBarInactiveTintColor: '#6B7280',
      headerShown: false,
    }}
  >
    <Tab.Screen
      name="Home"
      component={HomeScreen}
      options={{
        tabBarLabel: 'Beranda',
        tabBarIcon: ({ color, size }) => (
          <Icon name="home" size={size} color={color} />
        ),
      }}
    />
    <Tab.Screen
      name="Categories"
      component={CategoryScreen}
      options={{
        tabBarLabel: 'Kategori',
        tabBarIcon: ({ color, size }) => (
          <Icon name="grid" size={size} color={color} />
        ),
      }}
    />
    <Tab.Screen
      name="Profile"
      component={ProfileScreen}
      options={{
        tabBarLabel: 'Profil',
        tabBarIcon: ({ color, size }) => (
          <Icon name="user" size={size} color={color} />
        ),
      }}
    />
  </Tab.Navigator>
);

const AuthNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Register" component={RegisterScreen} />
  </Stack.Navigator>
);

const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user ? (
          <>
            <Stack.Screen name="Main" component={TabNavigator} />
            <Stack.Screen
              name="Play"
              component={PlayScreen}
              options={{
                headerShown: true,
                title: 'Teka-Teki Silang',
                headerBackTitle: 'Kembali'
              }}
            />
          </>
        ) : (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
```

#### 4.2 Play Screen (Main Game Screen)
```typescript
// src/screens/PlayScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { CrosswordGrid } from '../components/crossword/CrosswordGrid';
import { CrosswordClues } from '../components/crossword/CrosswordClues';
import { PlayControls } from '../components/crossword/PlayControls';
import { useCrossword } from '../context/CrosswordContext';
import { useAuth } from '../context/AuthContext';

type RootStackParamList = {
  Play: { crosswordId: string };
};

type PlayScreenRouteProp = RouteProp<RootStackParamList, 'Play'>;
type PlayScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Play'>;

interface Props {
  route: PlayScreenRouteProp;
  navigation: PlayScreenNavigationProp;
}

const PlayScreen: React.FC<Props> = ({ route, navigation }) => {
  const { crosswordId } = route.params;
  const { user } = useAuth();
  const {
    currentCrossword,
    userAnswers,
    selectedCell,
    selectedWordId,
    selectedDirection,
    isCompleted,
    timeSpent,
    hintsUsed,
    hintsRemaining,
    maxHints,
    loading,
    error,
    loadCrossword,
    selectCell,
    toggleDirection,
    updateAnswer,
    revealCell,
    revealWord,
    checkAnswers,
    saveProgress,
    resetCrossword,
    surrender,
  } = useCrossword();

  const [showResults, setShowResults] = useState(false);
  const [checkResult, setCheckResult] = useState<{
    correct: boolean;
    incorrectCells: number[][];
  } | null>(null);

  useEffect(() => {
    loadCrossword(crosswordId);
  }, [crosswordId, loadCrossword]);

  useEffect(() => {
    if (isCompleted) {
      Alert.alert(
        'Selamat!',
        'Anda telah menyelesaikan teka-teki silang ini!',
        [{ text: 'OK' }]
      );
    }
  }, [isCompleted]);

  const handleCellSelect = useCallback((row: number, col: number) => {
    selectCell(row, col);
  }, [selectCell]);

  const handleCheckAnswers = () => {
    if (!currentCrossword) return;

    const result = checkAnswers();
    setCheckResult(result);
    setShowResults(true);

    if (result.correct) {
      Alert.alert('Sempurna!', 'Semua jawaban Anda benar!');
    } else {
      Alert.alert('Hampir!', `${result.incorrectCells.length} jawaban masih salah.`);
    }

    if (user) {
      saveProgress();
    }

    setTimeout(() => {
      setCheckResult(null);
      setShowResults(false);
    }, 3000);
  };

  const handleRevealCell = () => {
    if (selectedCell) {
      revealCell(selectedCell[0], selectedCell[1]);
    }
  };

  const handleSaveProgress = () => {
    if (user) {
      saveProgress();
      Alert.alert('Tersimpan', 'Progress Anda telah disimpan.');
    } else {
      Alert.alert('Login Diperlukan', 'Silakan login untuk menyimpan progress.');
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (error || !currentCrossword) {
    return <ErrorScreen message={error || 'Teka-teki tidak ditemukan'} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.gameContainer}>
          {/* Crossword Grid */}
          <View style={styles.gridContainer}>
            <CrosswordGrid
              crossword={currentCrossword}
              userAnswers={userAnswers}
              selectedCell={selectedCell}
              selectedWordId={selectedWordId}
              revealedCells={[]}
              incorrectCells={checkResult?.incorrectCells || []}
              onSelectCell={handleCellSelect}
              showResults={showResults}
            />
          </View>

          {/* Clues */}
          <View style={styles.cluesContainer}>
            <CrosswordClues
              crossword={currentCrossword}
              selectedWordId={selectedWordId}
              onSelectWord={(wordId) => {
                const word = currentCrossword.state.wordPositions.find((w) => w.id.toString() === wordId);
                if (word) {
                  selectCell(word.row, word.col);
                }
              }}
            />
          </View>
        </View>

        {/* Controls */}
        <PlayControls
          onToggleDirection={toggleDirection}
          onRevealCell={handleRevealCell}
          onRevealWord={revealWord}
          onCheckAnswers={handleCheckAnswers}
          onSaveProgress={handleSaveProgress}
          onResetCrossword={resetCrossword}
          onSurrender={surrender}
          isCompleted={isCompleted}
          showResults={showResults}
          hintsRemaining={hintsRemaining}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  gameContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  gridContainer: {
    flex: 1,
    minHeight: 300,
  },
  cluesContainer: {
    flex: 1,
    minHeight: 200,
  },
});

export default PlayScreen;
```

### **Phase 5: Mobile-Specific Features & Optimization (Week 11-12)**

#### 5.1 Touch Input & Keyboard Handling
```typescript
// src/components/crossword/MobileKeyboard.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Dimensions,
} from 'react-native';

interface MobileKeyboardProps {
  visible: boolean;
  onLetterPress: (letter: string) => void;
  onBackspace: () => void;
  onClose: () => void;
}

const { width: screenWidth } = Dimensions.get('window');
const KEYBOARD_LETTERS = [
  ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
  ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
  ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
];

export const MobileKeyboard: React.FC<MobileKeyboardProps> = ({
  visible,
  onLetterPress,
  onBackspace,
  onClose,
}) => {
  const keyWidth = (screenWidth - 60) / 10; // 10 keys per row max

  const renderKey = (letter: string, index: number) => (
    <TouchableOpacity
      key={`${letter}-${index}`}
      style={[styles.key, { width: keyWidth }]}
      onPress={() => onLetterPress(letter)}
      activeOpacity={0.7}
    >
      <Text style={styles.keyText}>{letter}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.keyboard}>
          <View style={styles.header}>
            <Text style={styles.title}>Keyboard</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeText}>✕</Text>
            </TouchableOpacity>
          </View>

          {KEYBOARD_LETTERS.map((row, rowIndex) => (
            <View key={rowIndex} style={styles.row}>
              {row.map((letter, index) => renderKey(letter, index))}
            </View>
          ))}

          <View style={styles.actionRow}>
            <TouchableOpacity
              style={[styles.actionKey, styles.backspaceKey]}
              onPress={onBackspace}
            >
              <Text style={styles.actionKeyText}>⌫</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  keyboard: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
  },
  closeText: {
    fontSize: 18,
    color: '#6B7280',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
  },
  key: {
    height: 44,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginHorizontal: 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  keyText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  actionKey: {
    height: 44,
    paddingHorizontal: 24,
    backgroundColor: '#EF4444',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backspaceKey: {
    backgroundColor: '#F59E0B',
  },
  actionKeyText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
```

#### 5.2 Offline Support & Caching
```typescript
// src/services/storage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

interface CachedCrossword {
  id: string;
  data: Crossword;
  timestamp: number;
  progress?: UserProgress;
}

const CACHE_KEYS = {
  CROSSWORDS: 'cached_crosswords',
  USER_PROGRESS: 'user_progress',
  OFFLINE_ACTIONS: 'offline_actions',
};

const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

export class StorageService {
  // Cache crossword data
  static async cacheCrossword(crossword: Crossword): Promise<void> {
    try {
      const cached: CachedCrossword = {
        id: crossword.id,
        data: crossword,
        timestamp: Date.now(),
      };

      const existingCache = await this.getCachedCrosswords();
      const updatedCache = {
        ...existingCache,
        [crossword.id]: cached,
      };

      await AsyncStorage.setItem(CACHE_KEYS.CROSSWORDS, JSON.stringify(updatedCache));
    } catch (error) {
      console.error('Error caching crossword:', error);
    }
  }

  // Get cached crossword
  static async getCachedCrossword(id: string): Promise<Crossword | null> {
    try {
      const cache = await this.getCachedCrosswords();
      const cached = cache[id];

      if (!cached) return null;

      // Check if cache is expired
      if (Date.now() - cached.timestamp > CACHE_EXPIRY) {
        await this.removeCachedCrossword(id);
        return null;
      }

      return cached.data;
    } catch (error) {
      console.error('Error getting cached crossword:', error);
      return null;
    }
  }

  // Get all cached crosswords
  static async getCachedCrosswords(): Promise<Record<string, CachedCrossword>> {
    try {
      const cached = await AsyncStorage.getItem(CACHE_KEYS.CROSSWORDS);
      return cached ? JSON.parse(cached) : {};
    } catch (error) {
      console.error('Error getting cached crosswords:', error);
      return {};
    }
  }

  // Save user progress locally
  static async saveProgressLocally(crosswordId: string, progress: UserProgress): Promise<void> {
    try {
      const existingProgress = await this.getLocalProgress();
      const updatedProgress = {
        ...existingProgress,
        [crosswordId]: {
          ...progress,
          lastSaved: Date.now(),
        },
      };

      await AsyncStorage.setItem(CACHE_KEYS.USER_PROGRESS, JSON.stringify(updatedProgress));
    } catch (error) {
      console.error('Error saving progress locally:', error);
    }
  }

  // Get local progress
  static async getLocalProgress(): Promise<Record<string, UserProgress & { lastSaved: number }>> {
    try {
      const progress = await AsyncStorage.getItem(CACHE_KEYS.USER_PROGRESS);
      return progress ? JSON.parse(progress) : {};
    } catch (error) {
      console.error('Error getting local progress:', error);
      return {};
    }
  }

  // Queue offline actions
  static async queueOfflineAction(action: {
    type: 'SAVE_PROGRESS' | 'RECORD_PLAY';
    crosswordId: string;
    data: any;
    timestamp: number;
  }): Promise<void> {
    try {
      const existingActions = await this.getOfflineActions();
      const updatedActions = [...existingActions, action];

      await AsyncStorage.setItem(CACHE_KEYS.OFFLINE_ACTIONS, JSON.stringify(updatedActions));
    } catch (error) {
      console.error('Error queuing offline action:', error);
    }
  }

  // Get offline actions
  static async getOfflineActions(): Promise<any[]> {
    try {
      const actions = await AsyncStorage.getItem(CACHE_KEYS.OFFLINE_ACTIONS);
      return actions ? JSON.parse(actions) : [];
    } catch (error) {
      console.error('Error getting offline actions:', error);
      return [];
    }
  }

  // Sync offline actions when online
  static async syncOfflineActions(): Promise<void> {
    try {
      const isConnected = await NetInfo.fetch().then(state => state.isConnected);
      if (!isConnected) return;

      const actions = await this.getOfflineActions();

      for (const action of actions) {
        try {
          // Process each action based on type
          switch (action.type) {
            case 'SAVE_PROGRESS':
              await progressAPI.save(action.crosswordId, action.data);
              break;
            case 'RECORD_PLAY':
              await crosswordAPI.recordPlay(action.crosswordId);
              break;
          }
        } catch (error) {
          console.error('Error syncing action:', action, error);
        }
      }

      // Clear synced actions
      await AsyncStorage.removeItem(CACHE_KEYS.OFFLINE_ACTIONS);
    } catch (error) {
      console.error('Error syncing offline actions:', error);
    }
  }

  // Remove cached crossword
  static async removeCachedCrossword(id: string): Promise<void> {
    try {
      const cache = await this.getCachedCrosswords();
      delete cache[id];
      await AsyncStorage.setItem(CACHE_KEYS.CROSSWORDS, JSON.stringify(cache));
    } catch (error) {
      console.error('Error removing cached crossword:', error);
    }
  }

  // Clear all cache
  static async clearCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        CACHE_KEYS.CROSSWORDS,
        CACHE_KEYS.USER_PROGRESS,
        CACHE_KEYS.OFFLINE_ACTIONS,
      ]);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }
}
```

### **Phase 6: Testing & Deployment (Week 13-14)**

#### 6.1 Testing Strategy
```typescript
// __tests__/components/CrosswordGrid.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { CrosswordGrid } from '../src/components/crossword/CrosswordGrid';

const mockCrossword = {
  id: '1',
  grid_size: 5,
  state: {
    grid: [
      [{ char: 'H', wordIds: [1] }, { char: 'E', wordIds: [1] }, { char: 'L', wordIds: [1] }, { char: 'L', wordIds: [1] }, { char: 'O', wordIds: [1] }],
      [{ char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }],
      [{ char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }],
      [{ char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }],
      [{ char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }, { char: ' ' }],
    ],
    wordPositions: [
      { id: 1, row: 0, col: 0, number: 1, direction: 'across' }
    ],
    clues: { across: { 1: 'Greeting' }, down: {} }
  }
};

describe('CrosswordGrid', () => {
  it('renders correctly', () => {
    const { getByTestId } = render(
      <CrosswordGrid
        crossword={mockCrossword}
        userAnswers={Array(5).fill(null).map(() => Array(5).fill(''))}
        selectedCell={null}
        selectedWordId={null}
        revealedCells={[]}
        incorrectCells={[]}
        onSelectCell={jest.fn()}
      />
    );

    expect(getByTestId('crossword-grid')).toBeTruthy();
  });

  it('handles cell selection', () => {
    const onSelectCell = jest.fn();
    const { getByTestId } = render(
      <CrosswordGrid
        crossword={mockCrossword}
        userAnswers={Array(5).fill(null).map(() => Array(5).fill(''))}
        selectedCell={null}
        selectedWordId={null}
        revealedCells={[]}
        incorrectCells={[]}
        onSelectCell={onSelectCell}
      />
    );

    const cell = getByTestId('cell-0-0');
    fireEvent.press(cell);

    expect(onSelectCell).toHaveBeenCalledWith(0, 0);
  });
});
```

#### 6.2 Performance Optimization
```typescript
// src/utils/performance.ts
import { InteractionManager } from 'react-native';

export class PerformanceUtils {
  // Debounce function for frequent operations
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  // Throttle function for scroll events
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  // Run after interactions for better performance
  static runAfterInteractions(callback: () => void): void {
    InteractionManager.runAfterInteractions(callback);
  }

  // Batch state updates
  static batchUpdates(updates: (() => void)[]): void {
    InteractionManager.runAfterInteractions(() => {
      updates.forEach(update => update());
    });
  }
}

// Grid optimization for large crosswords
export const optimizeGridRendering = (gridSize: number) => {
  const CELL_SIZE_BREAKPOINTS = {
    small: { maxSize: 10, cellSize: 35 },
    medium: { maxSize: 15, cellSize: 25 },
    large: { maxSize: 20, cellSize: 20 },
    xlarge: { maxSize: Infinity, cellSize: 15 }
  };

  for (const [key, config] of Object.entries(CELL_SIZE_BREAKPOINTS)) {
    if (gridSize <= config.maxSize) {
      return {
        cellSize: config.cellSize,
        shouldVirtualize: gridSize > 15,
        batchSize: Math.min(gridSize * 2, 50)
      };
    }
  }

  return {
    cellSize: 15,
    shouldVirtualize: true,
    batchSize: 50
  };
};
```

### **Phase 7: Deployment & Distribution**

#### 7.1 Build Configuration
```json
// android/app/build.gradle additions
android {
    ...
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }
    }

    splits {
        abi {
            reset()
            enable true
            universalApk false
            include "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
        }
    }
}
```

#### 7.2 App Store Optimization
```typescript
// App metadata for stores
export const APP_METADATA = {
  name: 'Tekateki.io - Teka-Teki Silang',
  description: 'Aplikasi teka-teki silang Indonesia terbaik dengan ribuan puzzle menarik',
  keywords: [
    'teka-teki silang',
    'crossword',
    'puzzle',
    'indonesia',
    'brain game',
    'word game',
    'educational',
    'offline'
  ],
  features: [
    'Ribuan teka-teki silang berkualitas',
    'Mode offline - main tanpa internet',
    'Sistem petunjuk yang membantu',
    'Sinkronisasi progress antar perangkat',
    'Berbagai kategori menarik',
    'Interface yang mudah digunakan'
  ],
  screenshots: [
    'home_screen.png',
    'crossword_grid.png',
    'clues_view.png',
    'categories.png',
    'profile.png'
  ]
};
```

## **Mobile-Specific UX Considerations**

### **1. Touch Interactions**
- **Large touch targets** (minimum 44px) for crossword cells
- **Haptic feedback** for cell selection and word completion
- **Gesture support** for zooming and panning large grids
- **Swipe gestures** for navigating between clues

### **2. Responsive Design**
- **Adaptive grid sizing** based on screen dimensions
- **Portrait/landscape orientation** support
- **Tablet optimization** with larger grid and side-by-side layout
- **Safe area handling** for devices with notches

### **3. Performance Optimizations**
- **Virtual scrolling** for large crossword grids
- **Lazy loading** of crossword data
- **Image optimization** and caching
- **Memory management** for background/foreground transitions

### **4. Accessibility**
- **Screen reader support** for visually impaired users
- **High contrast mode** support
- **Font scaling** support
- **Voice input** for answers (future enhancement)

## **Development Timeline Summary**

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| **Phase 1** | Week 1-2 | Project setup, dependencies, basic structure |
| **Phase 2** | Week 3-4 | API integration, authentication, data flow |
| **Phase 3** | Week 5-8 | Core crossword components, game logic |
| **Phase 4** | Week 9-10 | Navigation, screens, user flows |
| **Phase 5** | Week 11-12 | Mobile optimizations, offline support |
| **Phase 6** | Week 13-14 | Testing, performance tuning |
| **Phase 7** | Week 15-16 | Deployment, store submission |

## **Estimated Costs & Resources**

### **Development Team**
- **1 Senior React Native Developer** (16 weeks)
- **1 UI/UX Designer** (4 weeks)
- **1 QA Tester** (4 weeks)

### **Tools & Services**
- **React Native development environment**
- **iOS Developer Account** ($99/year)
- **Google Play Developer Account** ($25 one-time)
- **Testing devices** (iOS/Android)
- **App Store optimization tools**

### **Total Estimated Timeline: 3-4 months**

This comprehensive plan leverages the existing React codebase to create a high-quality mobile crossword application with excellent performance and user experience. The React Native approach will allow for rapid development while maintaining code quality and reusability.