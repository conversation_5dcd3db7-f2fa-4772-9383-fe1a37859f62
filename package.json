{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "analyze": "vite build --mode production && open dist/stats.html", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@react-oauth/google": "^0.12.2", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.1", "terser": "^5.39.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}