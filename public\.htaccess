# Enable URL rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Redirect to HTTPS if not already (in production)
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Exclude the blog directory from React routing
    RewriteRule ^blog(/.*)?$ - [L]

    # If the requested file or directory exists, serve it directly
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d

    # Otherwise, route all requests to index.html for SPA routing
    RewriteRule ^ index.html [QSA,L]
</IfModule>

# Set security headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options "nosniff"

    # Enable XSS protection in browsers
    Header set X-XSS-Protection "1; mode=block"

    # Prevent clickjacking
    Header set X-Frame-Options "SAMEORIGIN"

    # Enable HSTS (HTTP Strict Transport Security)
    Header set Strict-Transport-Security "max-age=********; includeSubDomains"

    # Set Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://source.unsplash.com; font-src 'self'; connect-src 'self' https://openrouter.ai https://tts-api.widiyanata.com http://localhost:1111;"

    # Set referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"

    # Set permissions policy
    Header set Permissions-Policy "camera=(), microphone=(), geolocation=()"
</IfModule>

# Enable GZIP compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/json "access plus 0 seconds"
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Prevent directory listing
Options -Indexes
