# Instalasi WordPress di Subfolder /blog

Folder ini diperuntukkan untuk instalasi WordPress yang akan menangani bagian blog dari aplikasi.

## Langkah-langkah Instalasi WordPress

1. **Download WordPress**
   - Unduh versi terbaru WordPress dari [wordpress.org](https://wordpress.org/download/)
   - Ekstrak file WordPress ke folder ini (public/blog)

2. **Buat Database**
   - Buat database MySQL baru untuk WordPress
   - Catat nama database, username, dan password

3. **Konfigurasi WordPress**
   - Rename file `wp-config-sample.php` menjadi `wp-config.php`
   - Edit file `wp-config.php` dan masukkan informasi database Anda
   - Tambahkan kode berikut di file `wp-config.php` untuk mengatur URL WordPress:

   ```php
   define('WP_HOME', 'http://localhost:1111/blog');
   define('WP_SITEURL', 'http://localhost:1111/blog');
   ```

4. **<PERSON><PERSON><PERSON><PERSON> Instalasi**
   - <PERSON>uka http://localhost:1111/blog di browser
   - I<PERSON>ti langkah-langkah instalasi WordPress

5. **Konfigurasi Permalink**
   - Masuk ke dashboard WordPress
   - Buka Settings > Permalinks
   - Pilih struktur permalink yang diinginkan (disarankan: Post name)
   - Simpan perubahan

## Integrasi dengan Aplikasi React

WordPress di subfolder /blog akan menangani semua URL yang dimulai dengan `/blog`. Aplikasi React sudah dikonfigurasi untuk mengarahkan semua permintaan ke subfolder `/blog` ke instalasi WordPress.

## Catatan Penting

- Pastikan file `.htaccess` di root aplikasi React sudah dikonfigurasi dengan benar untuk mengarahkan permintaan ke subfolder `/blog` ke WordPress
- Jika menggunakan server web selain Apache, pastikan konfigurasi server sudah diatur dengan benar
- Untuk deployment ke cPanel, pastikan WordPress diinstal di subfolder `/blog` dan konfigurasi URL sudah diatur dengan benar
