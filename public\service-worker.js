// Service Worker untuk Tekateki.io App
// Mendukung offline play dan caching resource

// Versi service worker - ubah ini ketika ada perubahan pada service worker
const VERSION = '1.0.0';
const CACHE_NAME = `tekateki-cache-v${VERSION}`;
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/favicon.ico',
  // Vite menghasilkan file dengan hash di nama file, jadi kita tidak bisa menyimpannya secara statis
  // Kita akan menangkap file-file ini saat runtime dengan strategi cache-first
];

// API URL yang akan di-cache
const API_CACHE_NAME = `tekateki-api-cache-v${VERSION}`;
const API_URL_PATTERN = /\/api\//;

// Simpan versi di self untuk bisa diakses dari luar
self.VERSION = VERSION;

// Install event - cache static assets with better error handling
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets');

        // Use a more resilient approach instead of cache.addAll
        // cache.addAll fails if any single request fails
        const cachePromises = STATIC_ASSETS.map(url => {
          // Try to fetch and cache each resource individually
          return fetch(new Request(url, { cache: 'reload' }))
            .then(response => {
              // Only cache successful responses
              if (response.ok) {
                return cache.put(url, response);
              }
              console.warn(`Failed to fetch ${url} for caching`);
              return Promise.resolve(); // Continue despite failure
            })
            .catch(error => {
              console.error(`Error caching ${url}:`, error);
              return Promise.resolve(); // Continue despite failure
            });
        });

        return Promise.all(cachePromises);
      })
      .then(() => {
        console.log('Static assets cached successfully (or with some recoverable failures)');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service worker installation failed:', error);
        // Still skip waiting to activate the service worker even if caching fails
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME, API_CACHE_NAME];

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!cacheWhitelist.includes(cacheName)) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache or network with improved error handling
self.addEventListener('fetch', (event) => {
  // Don't handle non-GET requests or those that should be excluded from caching
  if (event.request.method !== 'GET') {
    return;
  }

  try {
    const url = new URL(event.request.url);

    // Skip caching for some browser-specific URLs that might cause issues
    if (url.pathname.startsWith('/browser-sync/') ||
        url.pathname.startsWith('/sockjs-node/') ||
        url.pathname.startsWith('/chrome-extension/')) {
      return;
    }

    // Handle API requests
    if (API_URL_PATTERN.test(url.pathname)) {
      event.respondWith(handleApiRequest(event.request));
      return;
    }

    // Handle static assets and navigation requests
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          // Return cached response if found
          if (response) {
            return response;
          }

          // Clone the request because it's a one-time use stream
          const fetchRequest = event.request.clone();

          return fetch(fetchRequest)
            .then((response) => {
              // Check if valid response
              if (!response || !response.ok) {
                console.warn(`Non-OK response for ${url.pathname}: ${response.status}`);
                return response;
              }

              // Only cache same-origin responses
              if (url.origin === self.location.origin) {
                try {
                  // Clone the response because it's a one-time use stream
                  const responseToCache = response.clone();

                  caches.open(CACHE_NAME)
                    .then((cache) => {
                      cache.put(event.request, responseToCache)
                        .catch(err => {
                          console.warn(`Failed to cache ${url.pathname}:`, err);
                        });
                    })
                    .catch(err => {
                      console.warn(`Failed to open cache for ${url.pathname}:`, err);
                    });
                } catch (err) {
                  console.warn(`Error during caching ${url.pathname}:`, err);
                }
              }

              return response;
            })
            .catch((error) => {
              console.warn(`Fetch failed for ${url.pathname}:`, error);

              // If fetch fails (offline), try to serve the index.html for navigation requests
              if (event.request.mode === 'navigate') {
                return caches.match('/index.html')
                  .catch(err => {
                    console.error('Failed to serve index.html as fallback:', err);
                    return new Response('Offline and no cached content available', {
                      status: 503,
                      headers: { 'Content-Type': 'text/plain' }
                    });
                  });
              }

              return new Response('Network error, and no cached version available', {
                status: 408,
                headers: { 'Content-Type': 'text/plain' }
              });
            });
        })
        .catch(error => {
          console.error(`Cache match error for ${url.pathname}:`, error);
          return fetch(event.request)
            .catch(() => new Response('Service worker error', {
              status: 500,
              headers: { 'Content-Type': 'text/plain' }
            }));
        })
    );
  } catch (error) {
    console.error('Service worker fetch handler error:', error);
    // Don't break the web if our service worker has bugs
    return;
  }
});

// Handle API requests with a different caching strategy and better error handling
async function handleApiRequest(request) {
  // Try network first
  try {
    const response = await fetch(request);

    // Cache successful GET responses
    if (request.method === 'GET' && response.ok) {
      try {
        const responseToCache = response.clone();
        const cache = await caches.open(API_CACHE_NAME);
        await cache.put(request, responseToCache)
          .catch(err => {
            console.warn(`Failed to cache API response for ${request.url}:`, err);
          });
      } catch (cacheError) {
        console.warn(`Error during API response caching for ${request.url}:`, cacheError);
        // Continue despite caching failure
      }
    }

    return response;
  } catch (networkError) {
    console.warn(`Network error for API request ${request.url}:`, networkError);

    try {
      // If network fails, try cache
      const cachedResponse = await caches.match(request);

      if (cachedResponse) {
        console.log(`Serving cached API response for ${request.url}`);
        return cachedResponse;
      }
    } catch (cacheError) {
      console.error(`Cache match error for API request ${request.url}:`, cacheError);
    }

    // If no cache or cache error, return error response
    return new Response(JSON.stringify({
      status: 'error',
      message: 'Anda sedang offline dan data ini belum tersimpan di cache.',
      url: request.url
    }), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  }
}

// Listen for messages from the client
self.addEventListener('message', (event) => {
  // Periksa apakah ada port untuk mengirim respons
  const hasPort = event.ports && event.ports[0];

  // Handle skip waiting message
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  // Handle version check message
  if (event.data && event.data.type === 'GET_VERSION') {
    if (hasPort) {
      // Kirim versi service worker ke client
      event.ports[0].postMessage({
        type: 'VERSION',
        version: VERSION
      });
    }
  }
});
