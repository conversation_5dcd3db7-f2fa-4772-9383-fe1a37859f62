# Rencana Pengembangan Aplikasi Mobile Tekateki.io dengan React Native

## 1. Analisis Arsitektur Teknis

### 1.1 Perbandingan Framework: React Native vs Flutter

#### React Native - **REKOMENDASI UTAMA**

**Keunggulan untuk Tekateki.io:**
- **Code Reusability Tinggi**: 85-90% kode dari React/Vite frontend dapat diadaptasi
- **Shared Business Logic**: Context API, state management, dan utility functions dapat digunakan kembali
- **Developer Experience**: Tim sudah familiar dengan React ecosystem
- **Community Support**: Library crossword-specific lebih banyak tersedia
- **Hot Reload**: Development cycle lebih cepat
- **Native Performance**: Optimal untuk UI interaktif seperti crossword grid

**Pertimbangan:**
- Bundle size sedikit lebih besar dari Flutter
- Memerlukan platform-specific code untuk beberapa fitur advanced

#### Flutter

**Keunggulan:**
- Performance rendering yang sangat baik untuk grid kompleks
- Single codebase untuk semua platform
- Dart language yang powerful
- UI consistency yang lebih baik

**Kekurangan untuk Tekateki.io:**
- **Zero Code Reusability**: Harus menulis ulang semua komponen dari TypeScript/React ke Dart
- **Learning Curve**: Tim harus mempelajari Dart dan Flutter ecosystem
- **Development Time**: 3-4x lebih lama karena rewrite complete
- **Maintenance Overhead**: Dua codebase terpisah (web + mobile)

### 1.2 Strategi Adaptasi Codebase Existing

#### Komponen yang Dapat Digunakan Kembali (85-95% compatibility):

1. **Business Logic & State Management**
   ```typescript
   // src/context/CrosswordContext.tsx - 95% reusable
   // src/context/AuthContext.tsx - 90% reusable
   // src/services/api.ts - 85% reusable
   // src/utils/crosswordLogic.ts - 100% reusable
   ```

2. **Type Definitions**
   ```typescript
   // src/types/crossword.ts - 100% reusable
   // src/types/api.ts - 100% reusable
   ```

3. **Utility Functions**
   ```typescript
   // src/utils/errorHandler.ts - 90% reusable
   // src/services/apiUtils.ts - 80% reusable (perlu adaptasi untuk React Native)
   ```

#### Komponen yang Perlu Adaptasi (50-70% compatibility):

1. **UI Components**
   ```typescript
   // CrosswordGrid.tsx - Perlu adaptasi untuk touch interactions
   // CrosswordClues.tsx - Perlu redesign untuk mobile layout
   // PlayControls.tsx - Adaptasi untuk mobile gestures
   ```

#### Komponen yang Perlu Rewrite Complete:

1. **Navigation System** - React Router → React Navigation
2. **Styling System** - Tailwind CSS → StyleSheet/Styled Components
3. **Web-specific Features** - Local Storage → AsyncStorage

### 1.3 Integrasi dengan Backend PHP API

#### Konfigurasi API yang Sudah Ada:
- **Base URL**: `http://localhost:1111/api/`
- **Authentication**: X-API-Key header (`45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5`)
- **JWT Token Support**: Sudah terimplementasi untuk user sessions
- **CORS Configuration**: Perlu penyesuaian untuk mobile app

#### Adaptasi untuk Mobile:
```typescript
// services/api.mobile.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';

const API_BASE_URL = __DEV__
  ? 'http://********:1111/api'  // Android emulator
  : 'https://widiyanata.com/tts-api/api';   // Production

const getHeaders = async (): Promise<HeadersInit> => {
  const token = await AsyncStorage.getItem('auth_token');
  return {
    'Content-Type': 'application/json',
    'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};
```

### 1.4 Cross-Platform Compatibility

#### iOS Considerations:
- **App Store Guidelines**: Compliance dengan content policies
- **Performance**: Optimasi untuk berbagai device (iPhone SE hingga Pro Max)
- **Accessibility**: VoiceOver support untuk screen readers
- **Push Notifications**: APNs integration

#### Android Considerations:
- **Material Design**: Adaptasi UI sesuai platform guidelines
- **Performance**: Optimasi untuk berbagai spesifikasi device
- **Accessibility**: TalkBack support
- **Push Notifications**: FCM integration
- **File Storage**: Handling external storage permissions

## 2. Perencanaan Implementasi Fitur

### 2.1 Mobile-Optimized Crossword Grid Interface

#### Touch Interactions:
```typescript
// components/mobile/CrosswordGridMobile.tsx
import { PanGestureHandler, TapGestureHandler } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle } from 'react-native-reanimated';

interface MobileCrosswordGridProps {
  crossword: CrosswordData;
  onCellSelect: (row: number, col: number) => void;
  onCellInput: (row: number, col: number, value: string) => void;
}

const CrosswordGridMobile: React.FC<MobileCrosswordGridProps> = ({
  crossword,
  onCellSelect,
  onCellInput
}) => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  // Pinch-to-zoom gesture handling
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateX: translateX.value },
      { translateY: translateY.value }
    ]
  }));

  return (
    <PanGestureHandler>
      <Animated.View style={[styles.gridContainer, animatedStyle]}>
        {/* Grid cells implementation */}
      </Animated.View>
    </PanGestureHandler>
  );
};
```

#### Responsive Grid Sizing:
```typescript
// utils/gridSizing.ts
import { Dimensions } from 'react-native';

export const calculateGridSize = (gridDimension: number) => {
  const { width, height } = Dimensions.get('window');
  const availableSpace = Math.min(width * 0.9, height * 0.6);
  const cellSize = Math.floor(availableSpace / gridDimension);

  return {
    cellSize: Math.max(cellSize, 24), // Minimum 24px untuk touch target
    gridWidth: cellSize * gridDimension,
    fontSize: Math.max(cellSize * 0.6, 12)
  };
};
```

### 2.2 Floating Clues Panel untuk Mobile

#### Design Pattern:
```typescript
// components/mobile/FloatingCluesPanel.tsx
import { BottomSheet } from '@gorhom/bottom-sheet';

const FloatingCluesPanel: React.FC<CluesPanelProps> = ({
  clues,
  selectedWordId,
  onClueSelect
}) => {
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      enablePanDownToClose={false}
    >
      <View style={styles.cluesContainer}>
        <CluesSection
          title="Mendatar"
          clues={clues.across}
          selectedWordId={selectedWordId}
          onClueSelect={onClueSelect}
        />
        <CluesSection
          title="Menurun"
          clues={clues.down}
          selectedWordId={selectedWordId}
          onClueSelect={onClueSelect}
        />
      </View>
    </BottomSheet>
  );
};
```

### 2.3 Offline Storage dan Sinkronisasi

#### Implementasi Offline Storage:
```typescript
// services/storage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

export class OfflineStorageService {
  private static KEYS = {
    CROSSWORDS: 'cached_crosswords',
    USER_PROGRESS: 'user_progress',
    OFFLINE_ACTIONS: 'offline_actions'
  };

  static async cacheCrossword(crossword: CrosswordData): Promise<void> {
    try {
      const cached = {
        id: crossword.id,
        data: crossword,
        timestamp: Date.now(),
        expires: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };

      await AsyncStorage.setItem(
        `${this.KEYS.CROSSWORDS}_${crossword.id}`,
        JSON.stringify(cached)
      );
    } catch (error) {
      console.error('Error caching crossword:', error);
    }
  }

  static async getCachedCrossword(id: string): Promise<CrosswordData | null> {
    try {
      const cached = await AsyncStorage.getItem(`${this.KEYS.CROSSWORDS}_${id}`);
      if (!cached) return null;

      const parsedCache = JSON.parse(cached);
      if (Date.now() > parsedCache.expires) {
        await AsyncStorage.removeItem(`${this.KEYS.CROSSWORDS}_${id}`);
        return null;
      }

      return parsedCache.data;
    } catch (error) {
      console.error('Error retrieving cached crossword:', error);
      return null;
    }
  }
}
```

### 2.4 User Authentication Integration

#### Google Login untuk Mobile:
```typescript
// services/auth.mobile.ts
import { GoogleSignin } from '@react-native-google-signin/google-signin';

export class MobileAuthService {
  static async initializeGoogleSignIn() {
    GoogleSignin.configure({
      webClientId: 'YOUR_WEB_CLIENT_ID',
      offlineAccess: true,
    });
  }

  static async signInWithGoogle(): Promise<User> {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();

      // Send to backend API
      const response = await fetch(`${API_BASE_URL}/users/google-login`, {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify({
          token: userInfo.idToken,
          email: userInfo.user.email,
          displayName: userInfo.user.name,
          avatarUrl: userInfo.user.photo
        })
      });

      const result = await response.json();
      if (result.status === 'success') {
        await AsyncStorage.setItem('auth_token', result.data.token);
        return result.data.user;
      }

      throw new Error(result.message);
    } catch (error) {
      throw new Error(`Google login failed: ${error.message}`);
    }
  }
}
```

## 3. Adaptasi UI/UX untuk Mobile

### 3.1 Monochrome Black/White Crossword Theme

#### Mobile-First Design Principles:
```typescript
// styles/monochromeTheme.ts
import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const monochromeTheme = {
  colors: {
    paper: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717'
    },
    ink: {
      50: '#f8f8f8',
      100: '#f0f0f0',
      200: '#e4e4e4',
      300: '#d1d1d1',
      400: '#b4b4b4',
      500: '#9a9a9a',
      600: '#818181',
      700: '#6a6a6a',
      800: '#5a5a5a',
      900: '#4a4a4a'
    }
  },
  typography: {
    serif: {
      fontFamily: 'Georgia',
      fontWeight: '400'
    },
    mono: {
      fontFamily: 'Courier New',
      fontWeight: '400'
    }
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  }
};

export const styles = StyleSheet.create({
  crosswordCell: {
    backgroundColor: monochromeTheme.colors.paper[50],
    borderWidth: 1,
    borderColor: monochromeTheme.colors.ink[300],
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 32,
    minWidth: 32
  },
  selectedCell: {
    backgroundColor: monochromeTheme.colors.ink[200],
    borderColor: monochromeTheme.colors.ink[900],
    borderWidth: 2
  },
  cellText: {
    fontFamily: monochromeTheme.typography.mono.fontFamily,
    fontSize: 16,
    color: monochromeTheme.colors.ink[900],
    textAlign: 'center'
  }
});
```

### 3.2 Newspaper-Style Typography untuk Mobile

#### Responsive Typography System:
```typescript
// styles/typography.ts
import { PixelRatio, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');
const scale = width / 375; // Base width (iPhone X)

export const normalize = (size: number) => {
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

export const typography = {
  headline: {
    fontSize: normalize(24),
    fontFamily: 'Georgia-Bold',
    lineHeight: normalize(32),
    color: monochromeTheme.colors.ink[900]
  },
  title: {
    fontSize: normalize(20),
    fontFamily: 'Georgia',
    lineHeight: normalize(28),
    color: monochromeTheme.colors.ink[800]
  },
  body: {
    fontSize: normalize(16),
    fontFamily: 'Georgia',
    lineHeight: normalize(24),
    color: monochromeTheme.colors.ink[700]
  },
  caption: {
    fontSize: normalize(12),
    fontFamily: 'Georgia',
    lineHeight: normalize(16),
    color: monochromeTheme.colors.ink[600]
  },
  crosswordNumber: {
    fontSize: normalize(10),
    fontFamily: 'Courier-Bold',
    color: monochromeTheme.colors.ink[900]
  }
};
```

### 3.3 Touch-Friendly Navigation dan Keyboard Input

#### Keyboard Navigation untuk Mobile:
```typescript
// components/mobile/CrosswordKeyboard.tsx
import { VirtualKeyboard } from 'react-native-virtual-keyboard';

const CrosswordKeyboard: React.FC<KeyboardProps> = ({
  onKeyPress,
  onBackspace,
  onDirectionToggle
}) => {
  const keys = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
  ];

  return (
    <View style={styles.keyboardContainer}>
      {keys.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.keyboardRow}>
          {row.map((key) => (
            <TouchableOpacity
              key={key}
              style={styles.key}
              onPress={() => onKeyPress(key)}
            >
              <Text style={styles.keyText}>{key}</Text>
            </TouchableOpacity>
          ))}
        </View>
      ))}

      <View style={styles.actionRow}>
        <TouchableOpacity style={styles.actionKey} onPress={onBackspace}>
          <Text style={styles.actionKeyText}>⌫</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionKey} onPress={onDirectionToggle}>
          <Text style={styles.actionKeyText}>↔</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
```

### 3.4 Accessibility Features untuk Screen Readers

#### Screen Reader Support:
```typescript
// components/mobile/AccessibleCrosswordCell.tsx
const AccessibleCrosswordCell: React.FC<CellProps> = ({
  row,
  col,
  char,
  number,
  isSelected,
  onPress
}) => {
  const accessibilityLabel = useMemo(() => {
    let label = `Sel ${row + 1}, ${col + 1}`;
    if (number) label += `, nomor ${number}`;
    if (char) label += `, berisi huruf ${char}`;
    if (isSelected) label += `, dipilih`;
    return label;
  }, [row, col, char, number, isSelected]);

  return (
    <TouchableOpacity
      style={[styles.cell, isSelected && styles.selectedCell]}
      onPress={() => onPress(row, col)}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
      accessibilityHint="Ketuk untuk memilih sel ini"
    >
      {number && (
        <Text style={styles.cellNumber} accessibilityElementsHidden={true}>
          {number}
        </Text>
      )}
      <Text style={styles.cellChar} accessibilityElementsHidden={true}>
        {char}
      </Text>
    </TouchableOpacity>
  );
};
```

## 4. Roadmap Pengembangan

### 4.1 Phase 1: Setup & Core Infrastructure (Minggu 1-2)

#### Deliverables:
- ✅ Project initialization dengan React Native CLI
- ✅ Setup navigation structure (React Navigation)
- ✅ Implementasi basic authentication flow
- ✅ API service layer adaptation
- ✅ Offline storage setup (AsyncStorage)

#### Tasks:
```bash
# 1. Project Setup
npx react-native@latest init TekatekiMobile --template react-native-template-typescript

# 2. Install dependencies
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install @react-native-async-storage/async-storage @react-native-community/netinfo
npm install react-native-gesture-handler react-native-reanimated
npm install @react-native-google-signin/google-signin

# 3. iOS setup
cd ios && pod install

# 4. Android setup - update android/app/build.gradle
```

### 4.2 Phase 2: Core Crossword Components (Minggu 3-4)

#### Deliverables:
- ✅ Mobile-optimized crossword grid component
- ✅ Touch interaction handling (tap, pinch-to-zoom)
- ✅ Basic clues display
- ✅ Cell selection and input functionality

#### Key Components:
```typescript
// Priority implementation order:
1. CrosswordGridMobile.tsx
2. CrosswordCellMobile.tsx
3. CluesListMobile.tsx
4. FloatingCluesPanel.tsx
5. CrosswordKeyboard.tsx
```

### 4.3 Phase 3: Game Logic & State Management (Minggu 5-6)

#### Deliverables:
- ✅ Adaptasi CrosswordContext untuk mobile
- ✅ Game state persistence
- ✅ Progress tracking
- ✅ Timer functionality
- ✅ Hint system implementation

#### State Management Adaptation:
```typescript
// context/CrosswordContextMobile.tsx
// Adaptasi dari src/context/CrosswordContext.tsx dengan:
// - AsyncStorage untuk persistence
// - Mobile-specific gesture handling
// - Optimized re-rendering untuk performance
```

### 4.4 Phase 4: UI/UX Polish & Advanced Features (Minggu 7-8)

#### Deliverables:
- ✅ Monochrome theme implementation
- ✅ Floating clues panel dengan gesture support
- ✅ Keyboard shortcuts dan accessibility
- ✅ Loading states dan error handling
- ✅ Push notifications setup

### 4.5 Phase 5: Offline Functionality (Minggu 9-10)

#### Deliverables:
- ✅ Offline crossword caching
- ✅ Progress synchronization
- ✅ Network state handling
- ✅ Background sync implementation

#### Offline Strategy:
```typescript
// services/syncService.ts
export class SyncService {
  static async syncWhenOnline() {
    const isConnected = await NetInfo.fetch().then(state => state.isConnected);
    if (!isConnected) return;

    // Sync pending progress
    await this.syncUserProgress();

    // Sync completed puzzles
    await this.syncCompletedPuzzles();

    // Download new puzzles
    await this.downloadNewPuzzles();
  }
}
```

### 4.6 Phase 6: Testing & Optimization (Minggu 11-12)

#### Deliverables:
- ✅ Unit tests untuk core components
- ✅ Integration tests untuk API calls
- ✅ Performance optimization
- ✅ Memory leak detection dan fixes
- ✅ Accessibility testing

#### Testing Strategy:
```typescript
// __tests__/CrosswordGrid.test.tsx
import { render, fireEvent } from '@testing-library/react-native';
import CrosswordGridMobile from '../components/CrosswordGridMobile';

describe('CrosswordGridMobile', () => {
  it('should handle cell selection correctly', () => {
    const mockOnCellSelect = jest.fn();
    const { getByTestId } = render(
      <CrosswordGridMobile
        crossword={mockCrossword}
        onCellSelect={mockOnCellSelect}
      />
    );

    fireEvent.press(getByTestId('cell-0-0'));
    expect(mockOnCellSelect).toHaveBeenCalledWith(0, 0);
  });
});
```

## 5. Spesifikasi Teknis

### 5.1 React Native Libraries yang Diperlukan

#### Core Dependencies:
```json
{
  "dependencies": {
    "@react-native-async-storage/async-storage": "^1.19.0",
    "@react-native-community/netinfo": "^9.4.0",
    "@react-navigation/native": "^6.1.0",
    "@react-navigation/stack": "^6.3.0",
    "@react-navigation/bottom-tabs": "^6.5.0",
    "react-native-gesture-handler": "^2.12.0",
    "react-native-reanimated": "^3.5.0",
    "react-native-safe-area-context": "^4.7.0",
    "react-native-screens": "^3.25.0",
    "react-native-vector-icons": "^10.0.0",
    "react-native-toast-message": "^2.1.0",
    "react-native-keyboard-aware-scroll-view": "^0.9.5",
    "@react-native-google-signin/google-signin": "^10.0.0",
    "@gorhom/bottom-sheet": "^4.4.0",
    "react-native-haptic-feedback": "^2.0.0"
  }
}
```

#### Development Dependencies:
```json
{
  "devDependencies": {
    "@testing-library/react-native": "^12.0.0",
    "@testing-library/jest-native": "^5.4.0",
    "jest": "^29.0.0",
    "react-test-renderer": "^18.2.0",
    "flipper-plugin-react-native-performance": "^0.2.0"
  }
}
```

### 5.2 API Integration Patterns untuk Mobile

#### Network Layer dengan Retry Logic:
```typescript
// services/networkService.ts
import NetInfo from '@react-native-netinfo/netinfo';

export class NetworkService {
  private static maxRetries = 3;
  private static retryDelay = 1000;

  static async fetchWithRetry(
    url: string,
    options: RequestInit,
    retries = this.maxRetries
  ): Promise<Response> {
    try {
      const response = await fetch(url, options);
      if (!response.ok && retries > 0) {
        await this.delay(this.retryDelay);
        return this.fetchWithRetry(url, options, retries - 1);
      }
      return response;
    } catch (error) {
      if (retries > 0) {
        await this.delay(this.retryDelay);
        return this.fetchWithRetry(url, options, retries - 1);
      }
      throw error;
    }
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 5.3 Local Storage Solutions

#### Structured Storage dengan Encryption:
```typescript
// services/secureStorage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import CryptoJS from 'crypto-js';

export class SecureStorage {
  private static encryptionKey = 'tekateki-mobile-key';

  static async setSecureItem(key: string, value: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      const encrypted = CryptoJS.AES.encrypt(jsonValue, this.encryptionKey).toString();
      await AsyncStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Error storing secure item:', error);
    }
  }

  static async getSecureItem<T>(key: string): Promise<T | null> {
    try {
      const encrypted = await AsyncStorage.getItem(key);
      if (!encrypted) return null;

      const decrypted = CryptoJS.AES.decrypt(encrypted, this.encryptionKey);
      const jsonValue = decrypted.toString(CryptoJS.enc.Utf8);
      return JSON.parse(jsonValue);
    } catch (error) {
      console.error('Error retrieving secure item:', error);
      return null;
    }
  }
}
```

### 5.4 State Management dengan Zustand (Alternative)

#### Lightweight State Management:
```typescript
// store/crosswordStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CrosswordStore {
  currentCrossword: CrosswordData | null;
  userAnswers: string[][];
  selectedCell: [number, number] | null;
  gameState: 'not-started' | 'playing' | 'paused' | 'completed';
  timeSpent: number;

  // Actions
  loadCrossword: (crossword: CrosswordData) => void;
  updateAnswer: (row: number, col: number, value: string) => void;
  selectCell: (row: number, col: number) => void;
  startGame: () => void;
  pauseGame: () => void;
  completeGame: () => void;
}

export const useCrosswordStore = create<CrosswordStore>()(
  persist(
    (set, get) => ({
      currentCrossword: null,
      userAnswers: [],
      selectedCell: null,
      gameState: 'not-started',
      timeSpent: 0,

      loadCrossword: (crossword) => set({
        currentCrossword: crossword,
        userAnswers: Array(crossword.grid_size).fill(null).map(() =>
          Array(crossword.grid_size).fill('')
        )
      }),

      updateAnswer: (row, col, value) => set((state) => {
        const newAnswers = [...state.userAnswers];
        newAnswers[row][col] = value.toUpperCase();
        return { userAnswers: newAnswers };
      }),

      selectCell: (row, col) => set({ selectedCell: [row, col] }),

      startGame: () => set({ gameState: 'playing' }),
      pauseGame: () => set({ gameState: 'paused' }),
      completeGame: () => set({ gameState: 'completed' })
    }),
    {
      name: 'crossword-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        userAnswers: state.userAnswers,
        timeSpent: state.timeSpent,
        gameState: state.gameState
      })
    }
  )
);
```

## 6. Deployment Considerations

### 6.1 App Store Deployment

#### iOS App Store:
- **Bundle ID**: `io.tekateki.mobile`
- **App Category**: Games > Word Games
- **Age Rating**: 4+ (All Ages)
- **Required Permissions**: None (offline-first approach)
- **App Store Screenshots**: 6.5", 5.5", iPad Pro sizes

#### Google Play Store:
- **Package Name**: `io.tekateki.mobile`
- **Target SDK**: API 33 (Android 13)
- **Minimum SDK**: API 21 (Android 5.0)
- **App Bundle**: Required untuk optimized delivery
- **Content Rating**: Everyone

### 6.2 Performance Optimization

#### Bundle Size Optimization:
```javascript
// metro.config.js
module.exports = {
  transformer: {
    minifierConfig: {
      keep_fnames: true,
      mangle: {
        keep_fnames: true,
      },
    },
  },
  resolver: {
    alias: {
      '@': './src',
    },
  },
};
```

#### Memory Management:
```typescript
// utils/memoryOptimization.ts
export class MemoryOptimizer {
  static optimizeGridRendering(gridSize: number) {
    // Use FlatList for large grids (>20x20)
    if (gridSize > 20) {
      return {
        renderingStrategy: 'virtualized',
        windowSize: 10,
        initialNumToRender: 50
      };
    }

    return {
      renderingStrategy: 'standard',
      windowSize: gridSize,
      initialNumToRender: gridSize * gridSize
    };
  }
}
```

## 7. Kesimpulan dan Rekomendasi

### 7.1 Keunggulan Pendekatan React Native

1. **Time to Market**: 3-4 bulan vs 6-8 bulan untuk Flutter
2. **Code Reusability**: 85-90% dari existing React codebase
3. **Maintenance**: Single team dapat handle web + mobile
4. **Performance**: Adequate untuk crossword grid interactions
5. **Community**: Mature ecosystem dengan library support

### 7.2 Estimasi Timeline dan Resource

#### Timeline: 12 Minggu (3 Bulan)
- **Phase 1-2**: Setup & Core Components (4 minggu)
- **Phase 3-4**: Game Logic & UI Polish (4 minggu)
- **Phase 5-6**: Offline & Testing (4 minggu)

#### Resource Requirements:
- **1 Senior React Native Developer** (full-time)
- **1 UI/UX Designer** (part-time, 2 hari/minggu)
- **1 QA Tester** (part-time, minggu 8-12)

#### Budget Estimation:
- **Development**: 3 bulan × Rp 25,000,000 = Rp 75,000,000
- **Design**: 24 hari × Rp 1,500,000 = Rp 36,000,000
- **Testing**: 5 minggu × Rp 8,000,000 = Rp 40,000,000
- **Total**: ~Rp 151,000,000

### 7.3 Next Steps

1. **Immediate Actions**:
   - Setup development environment
   - Create React Native project structure
   - Implement basic navigation

2. **Week 1 Priorities**:
   - API service layer adaptation
   - Authentication flow implementation
   - Basic crossword grid component

3. **Success Metrics**:
   - App Store approval rate: >95%
   - User retention (Day 7): >40%
   - Performance: 60fps grid interactions
   - Crash rate: <1%
