import { BrowserRouter as Router, Routes, Route, useLocation, useNavigationType } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { CrosswordProvider } from './context/CrosswordContext';
import { AuthProvider } from './context/AuthContext';
import { useEffect, lazy, Suspense } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import ProtectedRoute from './components/ProtectedRoute';
import { ToastProvider } from './components/ui/ToastProvider';
import ErrorBoundary from './components/ErrorBoundary';
import NotFound from './components/NotFound';
import ConnectionStatus from './components/ConnectionStatus';
import OfflineBanner from './components/OfflineBanner';

// Lazy load page components with prefetch and chunk naming
const LandingPage = lazy(() => import(/* webpackChunkName: "landing" */ './pages/LandingPage'));
const LoginPage = lazy(() => import(/* webpackChunkName: "auth" */ './pages/LoginPage'));
const RegisterPage = lazy(() => import(/* webpackChunkName: "auth" */ './pages/RegisterPage'));
const CategoryPage = lazy(() => import(/* webpackChunkName: "category" */ './pages/CategoryPage'));
const PlayPage = lazy(() => import(/* webpackChunkName: "play" */ './pages/PlayPage'));
const CreatePage = lazy(() => import(/* webpackChunkName: "create" */ './pages/CreatePage'));
const ProfilePage = lazy(() => import(/* webpackChunkName: "profile" */ './pages/ProfilePage'));

// SEO-enhancing pages
const FAQPage = lazy(() => import(/* webpackChunkName: "seo" */ './pages/FAQPage'));
const HowToPlayPage = lazy(() => import(/* webpackChunkName: "seo" */ './pages/HowToPlayPage'));
const GlossaryPage = lazy(() => import(/* webpackChunkName: "seo" */ './pages/GlossaryPage'));
const BenefitsPage = lazy(() => import(/* webpackChunkName: "seo" */ './pages/BenefitsPage'));
const TipsStrategiesPage = lazy(() => import(/* webpackChunkName: "seo" */ './pages/TipsStrategiesPage'));

// Legal and informational pages
const AboutPage = lazy(() => import(/* webpackChunkName: "info" */ './pages/AboutPage'));
const PrivacyPolicyPage = lazy(() => import(/* webpackChunkName: "legal" */ './pages/PrivacyPolicyPage'));
const TermsPage = lazy(() => import(/* webpackChunkName: "legal" */ './pages/TermsPage'));
const ContactPage = lazy(() => import(/* webpackChunkName: "info" */ './pages/ContactPage'));
const ShareRedirect = lazy(() => import(/* webpackChunkName: "share" */ './pages/ShareRedirect'));

// Lazy load landing pages for different user segments
const StudentLandingPage = lazy(() => import(/* webpackChunkName: "student-landing" */ './pages/StudentLandingPage'));
const TeacherLandingPage = lazy(() => import(/* webpackChunkName: "teacher-landing" */ './pages/TeacherLandingPage'));
const CasualLandingPage = lazy(() => import(/* webpackChunkName: "casual-landing" */ './pages/CasualLandingPage'));
// Blog pages removed - now handled by WordPress

// Lazy load admin pages - grouped in a single chunk
const AdminDashboardPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminDashboardPage'));
const AdminCategoriesPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminCategoriesPage'));
const AdminUsersPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminUsersPage'));
const AdminBlogsPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminBlogsPage'));
const AdminSettingsPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminSettingsPage'));
const AdminCrosswordsPage = lazy(() => import(/* webpackChunkName: "admin" */ './pages/admin/AdminCrosswordsPage'));

// Demo pages
const MonochromeDemo = lazy(() => import(/* webpackChunkName: "demo" */ './pages/MonochromeDemo'));

// Analytics tracker component - only loaded in production with performance optimizations
const AnalyticsTracker = () => {
  // Only include this component's logic in production builds
  if (!import.meta.env.PROD) return null;

  const location = useLocation();
  const navigationType = useNavigationType();

  useEffect(() => {
    // Use requestIdleCallback to defer non-critical analytics work
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        // Dynamically import analytics only when needed
        import('./utils/analytics').then(({ trackPageView }) => {
          trackPageView(
            location.pathname + location.search,
            document.title
          );
        });
      }, { timeout: 2000 }); // 2 second timeout as fallback
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      setTimeout(() => {
        import('./utils/analytics').then(({ trackPageView }) => {
          trackPageView(
            location.pathname + location.search,
            document.title
          );
        });
      }, 0);
    }
  }, [location, navigationType]);

  return null;
}

// Loading component for Suspense fallback
const LoadingSpinner = () => (
  <div className="flex items-center justify-center w-full h-full min-h-[200px]">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-ink spinner"></div>
  </div>
);

function App() {
  return (
    <HelmetProvider>
      <ToastProvider>
        <AuthProvider>
          <CrosswordProvider>
            <ErrorBoundary>
              <Router>
                <AnalyticsTracker />
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Auth Routes - Typically no standard Header/Footer */}
                    <Route path="/login" element={<LoginPage />} />
                    <Route path="/register" element={<RegisterPage />} />

                    {/* Protected Admin Routes - No standard Header/Footer from main layout */}
                    <Route element={<ProtectedRoute allowedRoles={['admin']} />}>
                      <Route path="/admin" element={<AdminDashboardPage />} />
                      <Route path="/admin/categories" element={<AdminCategoriesPage />} />
                      <Route path="/admin/users" element={<AdminUsersPage />} />
                      <Route path="/admin/blogs" element={<AdminBlogsPage />} />
                      <Route path="/admin/settings" element={<AdminSettingsPage />} />
                      <Route path="/admin/crosswords" element={<AdminCrosswordsPage />} />
                    </Route>

                    {/* Public and Protected Routes with Header/Footer */}
                    <Route path="*" element={
                      <div className="min-h-screen bg-paper-main flex flex-col">
                        <OfflineBanner />
                        <Header />
                        <main className="flex-grow">
                          <Suspense fallback={<LoadingSpinner />}>
                            <Routes>
                              {/* Public Routes */}
                              <Route path="/" element={<LandingPage />} />
                              {/* Landing pages for different user segments */}
                              <Route path="/untuk-pelajar" element={<StudentLandingPage />} />
                              <Route path="/untuk-guru" element={<TeacherLandingPage />} />
                              <Route path="/untuk-hiburan" element={<CasualLandingPage />} />
                              {/* SEO-friendly category routes */}
                              <Route path="/teka-teki-silang/:categorySlug" element={<CategoryPage />} />
                              <Route path="/category/:categoryId" element={<CategoryPage />} /> {/* Keep old route for backward compatibility */}
                              {/* SEO-friendly puzzle routes */}
                              <Route path="/teka-teki-silang/:categorySlug/:puzzleSlug" element={<PlayPage />} />
                              <Route path="/play/:puzzleId" element={<PlayPage />} /> {/* Keep old route for backward compatibility */}
                              <Route path="/play" element={<CategoryPage />} />
                              <Route path="/s/:code" element={<ShareRedirect />} /> {/* Share redirect route */}
                              {/* Blog routes removed - now handled by WordPress in /blog subfolder */}

                              {/* SEO-enhancing pages */}
                              <Route path="/bantuan/faq" element={<FAQPage />} />
                              <Route path="/cara-bermain" element={<HowToPlayPage />} />
                              <Route path="/kamus-istilah-tts" element={<GlossaryPage />} />
                              <Route path="/manfaat-tts" element={<BenefitsPage />} />
                              <Route path="/tips-strategi-tts" element={<TipsStrategiesPage />} />

                              {/* Legal and informational pages */}
                              <Route path="/tentang-kami" element={<AboutPage />} />
                              <Route path="/kebijakan-privasi" element={<PrivacyPolicyPage />} />
                              <Route path="/syarat-ketentuan" element={<TermsPage />} />
                              <Route path="/kontak" element={<ContactPage />} />

                              {/* Demo pages */}
                              <Route path="/demo/monochrome" element={<MonochromeDemo />} />

                              {/* Protected Routes with Main Layout */}
                              <Route element={<ProtectedRoute />}>
                                <Route path="/create" element={<CreatePage />} />
                                <Route path="/profile" element={<ProfilePage />} />
                                {/* Add other protected routes needing main layout here */}
                              </Route>

                              {/* 404 Not Found Route - Must be last */}
                              <Route path="*" element={<NotFound />} />
                            </Routes>
                          </Suspense>
                        </main>
                        <Footer />
                        <ConnectionStatus />
                      </div>
                    } />
                  </Routes>
                </Suspense>
              </Router>
            </ErrorBoundary>
          </CrosswordProvider>
        </AuthProvider>
      </ToastProvider>
    </HelmetProvider>
  );
}

export default App;
