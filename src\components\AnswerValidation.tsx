import React, { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, AlertCircleIcon } from 'lucide-react';

interface AnswerValidationProps {
  isCorrect: boolean | null; // null = not validated yet, true = correct, false = incorrect
  isComplete: boolean;
  showFeedback: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

const AnswerValidation: React.FC<AnswerValidationProps> = ({
  isCorrect,
  isComplete,
  showFeedback,
  position = 'top',
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [animationClass, setAnimationClass] = useState('');

  useEffect(() => {
    if (showFeedback && isCorrect !== null) {
      setIsVisible(true);
      setAnimationClass('animate-fadeIn');
      
      // Auto-hide after 2 seconds for correct answers
      if (isCorrect) {
        const timer = setTimeout(() => {
          setAnimationClass('animate-fadeOut');
          setTimeout(() => setIsVisible(false), 300);
        }, 2000);
        
        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
    }
  }, [showFeedback, isCorrect]);

  if (!isVisible || isCorrect === null) return null;

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'absolute -top-12 left-1/2 transform -translate-x-1/2';
      case 'bottom':
        return 'absolute -bottom-12 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'absolute -left-12 top-1/2 transform -translate-y-1/2';
      case 'right':
        return 'absolute -right-12 top-1/2 transform -translate-y-1/2';
      default:
        return 'absolute -top-12 left-1/2 transform -translate-x-1/2';
    }
  };

  const getValidationContent = () => {
    if (isComplete) {
      return {
        icon: <CheckCircleIcon className="w-5 h-5" />,
        text: 'Kata lengkap!',
        bgColor: 'bg-green-500',
        textColor: 'text-white',
        borderColor: 'border-green-600'
      };
    } else if (isCorrect) {
      return {
        icon: <CheckCircleIcon className="w-4 h-4" />,
        text: 'Benar',
        bgColor: 'bg-green-500',
        textColor: 'text-white',
        borderColor: 'border-green-600'
      };
    } else {
      return {
        icon: <XCircleIcon className="w-4 h-4" />,
        text: 'Salah',
        bgColor: 'bg-red-500',
        textColor: 'text-white',
        borderColor: 'border-red-600'
      };
    }
  };

  const validation = getValidationContent();

  return (
    <div className={`${getPositionClasses()} z-50 ${animationClass} ${className}`}>
      <div className={`
        flex items-center gap-2 px-3 py-1.5 rounded-sm shadow-paper-lg border-2
        ${validation.bgColor} ${validation.textColor} ${validation.borderColor}
        font-serif font-bold text-sm
      `}>
        {validation.icon}
        <span>{validation.text}</span>
      </div>
      
      {/* Arrow pointer */}
      <div className={`
        absolute w-0 h-0 
        ${position === 'top' ? 'top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-transparent border-t-green-600' : ''}
        ${position === 'bottom' ? 'bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-transparent border-b-green-600' : ''}
        ${position === 'left' ? 'left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-transparent border-l-green-600' : ''}
        ${position === 'right' ? 'right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-transparent border-r-green-600' : ''}
        ${!isCorrect && !isComplete ? 'border-t-red-600 border-b-red-600 border-l-red-600 border-r-red-600' : ''}
      `} />
    </div>
  );
};

// Hook for managing answer validation state
export const useAnswerValidation = () => {
  const [validationState, setValidationState] = useState<{
    [key: string]: {
      isCorrect: boolean | null;
      isComplete: boolean;
      showFeedback: boolean;
    };
  }>({});

  const validateAnswer = (
    wordId: string, 
    userAnswer: string, 
    correctAnswer: string,
    isWordComplete: boolean = false
  ) => {
    const isCorrect = userAnswer.toLowerCase() === correctAnswer.toLowerCase();
    
    setValidationState(prev => ({
      ...prev,
      [wordId]: {
        isCorrect,
        isComplete: isWordComplete && isCorrect,
        showFeedback: true
      }
    }));

    // Auto-clear feedback after delay
    setTimeout(() => {
      setValidationState(prev => ({
        ...prev,
        [wordId]: {
          ...prev[wordId],
          showFeedback: false
        }
      }));
    }, 3000);
  };

  const clearValidation = (wordId: string) => {
    setValidationState(prev => {
      const newState = { ...prev };
      delete newState[wordId];
      return newState;
    });
  };

  const clearAllValidations = () => {
    setValidationState({});
  };

  return {
    validationState,
    validateAnswer,
    clearValidation,
    clearAllValidations
  };
};

export default AnswerValidation;
