import React, { useState, useEffect, memo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { getCategories, Category } from '../services/api';
import { fallbackCategories } from '../data/fallbackData';

// Moved category icons and colors to a separate file to reduce bundle size
import { getCategoryIcon, getCategoryColor } from '../utils/categoryUtils';

// Skeleton loader for categories
const CategorySkeleton = memo(() => (
  <div className="card-paper overflow-hidden animate-pulse">
    <div className="h-24 bg-primary-300"></div>
    <div className="p-4 text-center">
      <div className="h-5 bg-primary-300 rounded w-3/4 mx-auto mb-2"></div>
      <div className="h-4 bg-primary-200 rounded w-1/3 mx-auto"></div>
    </div>
  </div>
));

// Error message component
const ErrorMessage = memo(({ message }: { message: string }) => (
  <div className="text-center p-8 text-primary-800">
    <p>{message}</p>
    <p className="mt-2 text-primary-600">Please try again later.</p>
  </div>
));

// Category card component
const CategoryCard = memo(({ category }: { category: Category }) => {
  const url = category.slug ? `/teka-teki-silang/${category.slug}` : `/category/${category.id}`;
  // Pass category name and description for better keyword matching
  const colorClass = getCategoryColor(category.id, category.name, category.description);
  const icon = getCategoryIcon(category.id, category.name, category.description);

  return (
    <Link
      key={category.id}
      to={url}
      className="card-paper overflow-hidden hover:shadow-paper-lg transition group"
    >
      <div className={`${colorClass} p-6 flex justify-center items-center text-newsprint group-hover:scale-110 transition-transform`}>
        {icon}
      </div>
      <div className="p-4 text-center">
        <h3 className="font-bold text-lg text-ink-dark">{category.name}</h3>
        {category.crossword_count > 0 && (
          <p className="text-sm text-primary-600 mt-1">{category.crossword_count} TTS</p>
        )}
      </div>
    </Link>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return prevProps.category.id === nextProps.category.id;
});

// API data fetching with caching
const CACHE_KEY = 'categories';
const CACHE_EXPIRY = 10 * 60 * 1000; // 10 minutes in milliseconds

const fetchCategories = async (): Promise<Category[]> => {
  try {
    // Check if we have cached data
    const cachedData = sessionStorage.getItem(CACHE_KEY);
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      // Check if cache is still valid
      if (Date.now() - timestamp < CACHE_EXPIRY) {
        return data;
      }
    }

    // If no valid cache, fetch from API
    const categories = await getCategories();

    // Cache the result
    sessionStorage.setItem(CACHE_KEY, JSON.stringify({
      data: categories,
      timestamp: Date.now()
    }));

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    // Return fallback data on error
    return fallbackCategories;
  }
};

const CategoryList: React.FC = () => {
  const [state, setState] = useState<{
    categories: Category[];
    isLoading: boolean;
    error: string | null;
  }>({
    categories: [],
    isLoading: true,
    error: null
  });

  // Memoized fetch function
  const loadCategories = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      const data = await fetchCategories();
      setState({
        categories: data,
        isLoading: false,
        error: null
      });
    } catch (err) {
      setState({
        categories: [],
        isLoading: false,
        error: 'Failed to load categories'
      });
    }
  }, []);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  const { categories, isLoading, error } = state;

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {[...Array(8)].map((_, index) => (
          <CategorySkeleton key={index} />
        ))}
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {categories.map((category) => (
        <CategoryCard key={category.id} category={category} />
      ))}
    </div>
  );
};

export default CategoryList;


