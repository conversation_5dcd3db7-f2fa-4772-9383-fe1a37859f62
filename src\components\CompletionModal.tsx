import React from 'react';
import { TrophyIcon, ClockIcon, LightbulbIcon, CheckCircleIcon } from 'lucide-react';

interface CompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  completionTime: number;
  hintsUsed: number;
  totalHints: number;
  progress: number;
  onPlayAgain?: () => void;
  onBackToMenu?: () => void;
}

const CompletionModal: React.FC<CompletionModalProps> = ({
  isOpen,
  onClose,
  completionTime,
  hintsUsed,
  totalHints,
  progress,
  onPlayAgain,
  onBackToMenu
}) => {
  if (!isOpen) return null;

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPerformanceMessage = () => {
    const efficiency = ((totalHints - hintsUsed) / totalHints) * 100;
    
    if (completionTime < 300 && efficiency > 80) { // Under 5 minutes, used less than 20% hints
      return { message: "Luar biasa! Anda adalah master teka-teki silang!", emoji: "🏆", color: "text-yellow-600" };
    } else if (completionTime < 600 && efficiency > 60) { // Under 10 minutes, used less than 40% hints
      return { message: "Hebat! Performa yang sangat baik!", emoji: "🌟", color: "text-blue-600" };
    } else if (efficiency > 50) { // Used less than 50% hints
      return { message: "Bagus! Anda menyelesaikan dengan baik!", emoji: "👏", color: "text-green-600" };
    } else {
      return { message: "Selamat! Anda berhasil menyelesaikan teka-teki!", emoji: "🎉", color: "text-purple-600" };
    }
  };

  const performance = getPerformanceMessage();

  return (
    <div className="fixed inset-0 bg-ink-900 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-newsprint border-4 border-ink-900 rounded-sm shadow-paper-xl max-w-md w-full overflow-hidden animate-bounce-in">
        {/* Header */}
        <div className="bg-ink-900 text-newsprint p-6 text-center">
          <div className="text-6xl mb-2">{performance.emoji}</div>
          <h2 className="text-2xl font-bold font-serif mb-2">Teka-Teki Selesai!</h2>
          <p className={`text-lg font-serif ${performance.color.replace('text-', 'text-newsprint')}`}>
            {performance.message}
          </p>
        </div>

        {/* Stats */}
        <div className="p-6 bg-paper-texture">
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* Completion Time */}
            <div className="bg-newsprint border-2 border-ink-900 rounded-sm p-4 text-center">
              <ClockIcon className="w-8 h-8 mx-auto mb-2 text-ink-700" />
              <div className="text-2xl font-bold font-mono text-ink-900">
                {formatTime(completionTime)}
              </div>
              <div className="text-sm font-serif text-ink-600">Waktu Selesai</div>
            </div>

            {/* Progress */}
            <div className="bg-newsprint border-2 border-ink-900 rounded-sm p-4 text-center">
              <CheckCircleIcon className="w-8 h-8 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold font-mono text-ink-900">
                {progress}%
              </div>
              <div className="text-sm font-serif text-ink-600">Kemajuan</div>
            </div>

            {/* Hints Used */}
            <div className="bg-newsprint border-2 border-ink-900 rounded-sm p-4 text-center">
              <LightbulbIcon className="w-8 h-8 mx-auto mb-2 text-yellow-600" />
              <div className="text-2xl font-bold font-mono text-ink-900">
                {hintsUsed}/{totalHints}
              </div>
              <div className="text-sm font-serif text-ink-600">Bantuan Digunakan</div>
            </div>

            {/* Performance Score */}
            <div className="bg-newsprint border-2 border-ink-900 rounded-sm p-4 text-center">
              <TrophyIcon className="w-8 h-8 mx-auto mb-2 text-yellow-600" />
              <div className="text-2xl font-bold font-mono text-ink-900">
                {Math.round(((totalHints - hintsUsed) / totalHints) * 100)}%
              </div>
              <div className="text-sm font-serif text-ink-600">Efisiensi</div>
            </div>
          </div>

          {/* Achievement Message */}
          <div className="bg-ink-100 border-l-4 border-ink-900 p-4 rounded-sm mb-6">
            <p className={`font-serif text-center ${performance.color}`}>
              <strong>{performance.message}</strong>
            </p>
            <p className="text-ink-700 text-sm text-center mt-2">
              Anda telah menyelesaikan semua petunjuk dengan benar!
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            {onPlayAgain && (
              <button
                onClick={onPlayAgain}
                className="flex-1 bg-ink-900 text-newsprint px-4 py-3 rounded-sm font-serif font-semibold hover:bg-ink-800 transition-all duration-200 shadow-paper hover:shadow-paper-lg flex items-center justify-center"
              >
                🔄 Main Lagi
              </button>
            )}
            
            {onBackToMenu && (
              <button
                onClick={onBackToMenu}
                className="flex-1 bg-newsprint text-ink-900 border-2 border-ink-900 px-4 py-3 rounded-sm font-serif font-semibold hover:bg-ink-100 transition-all duration-200 shadow-paper hover:shadow-paper-lg flex items-center justify-center"
              >
                🏠 Menu Utama
              </button>
            )}
            
            <button
              onClick={onClose}
              className="flex-1 bg-ink-600 text-newsprint px-4 py-3 rounded-sm font-serif font-semibold hover:bg-ink-700 transition-all duration-200 shadow-paper hover:shadow-paper-lg flex items-center justify-center"
            >
              ✕ Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletionModal;
