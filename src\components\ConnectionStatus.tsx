import React, { useState, useEffect } from 'react';
import { isOnline, addConnectionStatusListeners, removeConnectionStatusListeners } from '../utils/serviceWorkerRegistration';
import { WifiIcon, WifiOffIcon } from 'lucide-react';

/**
 * Komponen untuk menampilkan status koneksi
 * Menampilkan indikator online/offline dan pesan status
 */
const ConnectionStatus: React.FC = () => {
  const [online, setOnline] = useState(isOnline());
  const [showStatus, setShowStatus] = useState(false);
  const [fadeOut, setFadeOut] = useState(false);

  // Handler untuk perubahan status koneksi
  const handleOnline = () => {
    setOnline(true);
    showStatusMessage();
  };

  const handleOffline = () => {
    setOnline(false);
    showStatusMessage();
  };

  // Menampilkan pesan status selama beberapa detik
  const showStatusMessage = () => {
    setFadeOut(false);
    setShowStatus(true);
    
    // <PERSON><PERSON> fade out setelah 3 detik
    const fadeTimer = setTimeout(() => {
      setFadeOut(true);
    }, 3000);
    
    // Sembunyikan pesan setelah animasi fade out selesai
    const hideTimer = setTimeout(() => {
      setShowStatus(false);
    }, 3500);
    
    return () => {
      clearTimeout(fadeTimer);
      clearTimeout(hideTimer);
    };
  };

  // Tambahkan listener untuk perubahan status koneksi
  useEffect(() => {
    addConnectionStatusListeners(handleOnline, handleOffline);
    
    // Tampilkan status awal jika offline
    if (!isOnline()) {
      showStatusMessage();
    }
    
    return () => {
      removeConnectionStatusListeners(handleOnline, handleOffline);
    };
  }, []);

  return (
    <>
      {/* Indikator status koneksi (selalu ditampilkan) */}
      <div className="fixed bottom-4 right-4 z-50">
        <div 
          className={`flex items-center justify-center w-10 h-10 rounded-full shadow-lg ${
            online ? 'bg-green-100' : 'bg-red-100'
          }`}
          onClick={showStatusMessage}
          title={online ? 'Online - Klik untuk detail' : 'Offline - Klik untuk detail'}
        >
          {online ? (
            <WifiIcon className="w-5 h-5 text-green-600" />
          ) : (
            <WifiOffIcon className="w-5 h-5 text-red-600" />
          )}
        </div>
      </div>
      
      {/* Pesan status (ditampilkan sementara) */}
      {showStatus && (
        <div 
          className={`fixed bottom-16 right-4 z-50 p-3 rounded-lg shadow-lg max-w-xs transition-opacity duration-500 ${
            online ? 'bg-green-100' : 'bg-red-100'
          } ${fadeOut ? 'opacity-0' : 'opacity-100'}`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              {online ? (
                <WifiIcon className="w-5 h-5 text-green-600" />
              ) : (
                <WifiOffIcon className="w-5 h-5 text-red-600" />
              )}
            </div>
            <div>
              <p className="font-medium">
                {online ? 'Anda online' : 'Anda offline'}
              </p>
              <p className="text-sm mt-1">
                {online 
                  ? 'Semua fitur tersedia dan data akan disinkronkan.' 
                  : 'Beberapa fitur mungkin tidak tersedia. Data yang di-cache masih dapat diakses.'}
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ConnectionStatus;
