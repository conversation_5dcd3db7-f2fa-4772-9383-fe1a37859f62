import React, { useState } from 'react';
import { useCrossword } from '../context/CrosswordContext';
import { SaveIcon, DownloadIcon, RefreshIcon, AdjustmentsIcon, RefreshCwIcon, LoadingIcon } from './Icons';
import { optimizeWordPlacement } from '../utils/crosswordLogic';

const Controls: React.FC = () => {
  const { state, dispatch } = useCrossword();
  const [gridSize, setGridSize] = useState(state.gridSize.toString());
  const [showSizeControls, setShowSizeControls] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizeMessage, setOptimizeMessage] = useState<string | null>(null);

  // Handle grid resize
  const handleResizeGrid = () => {
    const size = parseInt(gridSize);
    if (size >= 5 && size <= 30) {
      dispatch({ type: 'SET_GRID_SIZE', size });
      setShowSizeControls(false);
    }
  };

  // Handle grid optimization
  const handleOptimizeGrid = () => {
    dispatch({ type: 'OPTIMIZE_GRID' });
  };

  // Handle crossword reset
  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset the crossword? This will remove all words.')) {
      dispatch({ type: 'RESET' });
    }
  };

  // Save crossword to file
  const handleSave = () => {
    try {
      const data = JSON.stringify(state, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = 'crossword.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error saving crossword:', error);
      alert('Failed to save crossword');
    }
  };

  // Switch between edit and play modes
  const toggleMode = () => {
    dispatch({ type: 'SET_MODE', mode: state.mode === 'edit' ? 'play' : 'edit' });
  };

  const handleOptimizeLayout = () => {
    if (state.words.length < 2) {
      setOptimizeMessage('Tambahkan minimal dua kata sebelum mengoptimalkan tata letak');
      setTimeout(() => setOptimizeMessage(null), 3000);
      return;
    }

    // Show loading state
    setIsOptimizing(true);
    setOptimizeMessage('Mengoptimalkan tata letak...');

    // Use setTimeout to allow the UI to update before starting the optimization
    setTimeout(() => {
      try {
        // Pastikan state memiliki semua properti yang diperlukan
        const stateToOptimize = {
          ...state,
          // Pastikan wordPositions memiliki id yang benar
          wordPositions: state.wordPositions.map((pos, idx) => ({
            ...pos,
            id: pos.id !== undefined ? pos.id : idx + 1
          })),
          // Gunakan ukuran grid saat ini
          gridSize: parseInt(gridSize) || state.gridSize
        };

        // Log state asli untuk debugging
        console.log('State asli sebelum optimasi:', stateToOptimize);

        // Jalankan algoritma optimasi dengan state lengkap
        const optimizedState = optimizeWordPlacement(stateToOptimize, 150);

        // Log state hasil optimasi untuk debugging
        console.log('State setelah optimasi:', optimizedState);

        // Periksa apakah semua kata berhasil ditempatkan
        if (optimizedState.words.length < state.words.length) {
          setOptimizeMessage(`Berhasil menempatkan ${optimizedState.words.length} dari ${state.words.length} kata`);
        } else {
          setOptimizeMessage('Tata letak berhasil dioptimalkan!');
        }

        // Update the state with the optimized layout
        dispatch({ type: 'SET_STATE', state: optimizedState });

        // Clear the message after a delay
        setTimeout(() => setOptimizeMessage(null), 3000);
      } catch (error) {
        console.error('Error optimizing layout:', error);
        setOptimizeMessage('Gagal mengoptimalkan tata letak. Silakan coba lagi.');
        setTimeout(() => setOptimizeMessage(null), 3000);
      } finally {
        setIsOptimizing(false);
      }
    }, 100);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleOptimizeGrid}
          className="flex items-center px-3 py-2 bg-slate-600 text-white rounded hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2"
          title="Optimize Grid"
        >
          <AdjustmentsIcon className="w-5 h-5 mr-1" />
          <span>Optimize Grid</span>
        </button>

        <button
          onClick={() => setShowSizeControls(!showSizeControls)}
          className="flex items-center px-3 py-2 bg-slate-100 text-slate-700 rounded hover:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2"
          title="Change Grid Size"
        >
          <span>Grid Size: {state.gridSize}×{state.gridSize}</span>
        </button>

        <button
          onClick={handleSave}
          className="flex items-center px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          title="Save Crossword"
        >
          <SaveIcon className="w-5 h-5 mr-1" />
          <span>Save</span>
        </button>

        <button
          onClick={toggleMode}
          className={`flex items-center px-3 py-2 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            state.mode === 'play'
              ? 'bg-slate-600 text-white hover:bg-slate-700 focus:ring-slate-500'
              : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
          }`}
          title={state.mode === 'edit' ? 'Switch to Play Mode' : 'Switch to Edit Mode'}
        >
          <span>{state.mode === 'edit' ? 'Play Mode' : 'Edit Mode'}</span>
        </button>

        <button
          onClick={handleReset}
          className="flex items-center px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ml-auto"
          title="Reset Crossword"
        >
          <RefreshIcon className="w-5 h-5 mr-1" />
          <span>Reset</span>
        </button>
        <button
          onClick={handleOptimizeLayout}
          className="flex items-center px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:bg-purple-400 disabled:cursor-not-allowed"
          title="Optimize Layout"
          disabled={isOptimizing}
        >
          {isOptimizing ? (
            <>
              <LoadingIcon className="w-5 h-5 mr-1" />
              <span>Mengoptimalkan...</span>
            </>
          ) : (
            <>
              <RefreshIcon className="w-5 h-5 mr-1" />
              <span>Optimize Layout</span>
            </>
          )}
        </button>
      </div>

      {optimizeMessage && (
        <div className={`mt-2 p-2 rounded text-sm ${
          optimizeMessage.includes('Gagal')
            ? 'bg-red-100 text-red-700'
            : optimizeMessage.includes('Berhasil menempatkan')
              ? 'bg-yellow-100 text-yellow-700'
              : 'bg-green-100 text-green-700'
        }`}>
          {optimizeMessage}
          {isOptimizing && (
            <div className="mt-1 text-xs text-gray-600">
              Mengoptimalkan dengan ukuran grid {gridSize}×{gridSize}
            </div>
          )}
        </div>
      )}

      {showSizeControls && (
        <div className="mt-4 flex items-center gap-2">
          <input
            type="number"
            value={gridSize}
            onChange={(e) => setGridSize(e.target.value)}
            min="5"
            max="30"
            className="w-20 px-2 py-1 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleResizeGrid}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Resize
          </button>
          <span className="text-sm text-slate-500">Min: 5, Max: 30</span>
        </div>
      )}
    </div>
  );
};

export default Controls;
