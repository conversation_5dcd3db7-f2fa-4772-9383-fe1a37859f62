import React, { useState } from 'react';
import { useCrossword } from '../context/CrosswordContext';
import CrosswordGrid from './CrosswordGrid';
import CrosswordClues from './CrosswordClues';
import WordInputForm from './WordInputForm';
import Controls from './Controls';

const CrosswordApp: React.FC = () => {
  const { state } = useCrossword();
  const [activeTab, setActiveTab] = useState<'grid' | 'words'>('grid');

  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-3/5">
          <div className="bg-white rounded-lg shadow-md p-4 mb-6">
            <div className="flex border-b mb-4">
              <button
                className={`py-2 px-4 ${
                  activeTab === 'grid' 
                    ? 'border-b-2 border-blue-500 text-blue-600 font-medium' 
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                onClick={() => setActiveTab('grid')}
              >
                Grid
              </button>
              <button
                className={`py-2 px-4 ${
                  activeTab === 'words' 
                    ? 'border-b-2 border-blue-500 text-blue-600 font-medium' 
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                onClick={() => setActiveTab('words')}
              >
                Words & Clues
              </button>
            </div>
            
            {activeTab === 'grid' ? (
              <div className="flex justify-center items-center">
                <CrosswordGrid />
              </div>
            ) : (
              <CrosswordClues />
            )}
          </div>
          <Controls />
        </div>
        
        <div className="w-full md:w-2/5">
          <WordInputForm />
        </div>
      </div>
    </div>
  );
};

export default CrosswordApp;