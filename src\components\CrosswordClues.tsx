import React, { useState, useEffect } from 'react';
import { useCrossword } from '../context/CrosswordContext';

interface CrosswordCluesProps {
  className?: string;
}

const CrosswordClues: React.FC<CrosswordCluesProps> = ({ className = '' }) => {
  const { state, dispatch } = useCrossword();
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState<'across' | 'down'>('across');

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Mendapatkan petunjuk mendatar
  const acrossClues = Object.entries(state.clues.across)
    .map(([number, clue]) => ({ number: parseInt(number), clue }))
    .sort((a, b) => a.number - b.number);

  // Mendapatkan petunjuk menurun
  const downClues = Object.entries(state.clues.down)
    .map(([number, clue]) => ({ number: parseInt(number), clue }))
    .sort((a, b) => a.number - b.number);

  // Mendapatkan ID kata berdasarkan nomor dan arah
  const getWordId = (number: number, direction: 'across' | 'down') => {
    const index = state.wordPositions.findIndex(
      pos => pos.number === number && pos.direction === direction
    );
    return index !== -1 ? index + 1 : null;
  };

  // Menangani klik pada petunjuk
  const handleClueClick = (number: number, direction: 'across' | 'down') => {
    const wordId = getWordId(number, direction);
    if (wordId !== null) {
      dispatch({ type: 'SELECT_WORD', wordId });
    }
  };

  // Memeriksa apakah petunjuk dipilih
  const isClueSelected = (number: number, direction: 'across' | 'down') => {
    if (!state.selectedWordId) return false;
    const selectedPosition = state.wordPositions[state.selectedWordId - 1];
    return selectedPosition && selectedPosition.number === number && selectedPosition.direction === direction;
  };

  return (
    <div className={`${className}`}>
      {/* Enhanced Mobile Tabs with newspaper styling */}
      {isMobile && (
        <div className="flex border-b-2 border-ink mb-4 bg-primary-50 rounded-t-lg overflow-hidden">
          <button
            className={`flex-1 py-3 px-4 font-serif font-semibold transition-all duration-200 ${
              activeTab === 'across'
                ? 'bg-ink text-newsprint border-b-2 border-ink shadow-sm'
                : 'text-ink-secondary hover:text-ink hover:bg-primary-100'
            }`}
            onClick={() => setActiveTab('across')}
          >
            MENDATAR
          </button>
          <button
            className={`flex-1 py-3 px-4 font-serif font-semibold transition-all duration-200 ${
              activeTab === 'down'
                ? 'bg-ink text-newsprint border-b-2 border-ink shadow-sm'
                : 'text-ink-secondary hover:text-ink hover:bg-primary-100'
            }`}
            onClick={() => setActiveTab('down')}
          >
            MENURUN
          </button>
        </div>
      )}

      <div className={`${isMobile ? '' : 'grid grid-cols-1 md:grid-cols-1 gap-6'}`}>
        {/* Enhanced Petunjuk Mendatar */}
        <div className={isMobile && activeTab !== 'across' ? 'hidden' : ''}>
          {!isMobile && (
            <h3 className="text-lg font-bold mb-4 pb-3 border-b-2 border-ink text-ink-dark font-serif">
              MENDATAR
            </h3>
          )}
          {acrossClues.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-ink-muted italic font-serif">Belum ada petunjuk mendatar</p>
            </div>
          ) : (
            <ul className="space-y-1 max-h-[50vh] md:max-h-[60vh] overflow-y-auto pr-2">
              {acrossClues.map(({ number, clue }) => (
                <li
                  key={`across-${number}`}
                  className={`cursor-pointer rounded-lg p-1 text-sm transition-all duration-200 border ${
                    isClueSelected(number, 'across')
                      ? 'bg-black text-white border-black shadow-md'
                      : 'bg-primary-25 hover:bg-primary-50 border-primary-200 hover:border-ink-secondary text-black'
                  }`}
                  onClick={() => handleClueClick(number, 'across')}
                >
                  <div className="flex items-start gap-3">
                    <span className={`font-bold font-mono text-base flex-shrink-0 ${
                      isClueSelected(number, 'across') ? 'text-white' : 'text-ink-dark'
                    }`}>
                      {number}.
                    </span>
                    <span className={`font-serif leading-relaxed ${
                      isClueSelected(number, 'across') ? 'text-white' : 'text-ink'
                    }`}>
                      {clue || <span className="italic text-ink-muted">Tidak ada petunjuk</span>}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Enhanced Petunjuk Menurun */}
        <div className={isMobile && activeTab !== 'down' ? 'hidden' : ''}>
          {!isMobile && (
            <h3 className="text-lg font-bold mb-4 pb-3 border-b-2 border-ink text-ink-dark font-serif mt-6">
              MENURUN
            </h3>
          )}
          {downClues.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-ink-muted italic font-serif">Belum ada petunjuk menurun</p>
            </div>
          ) : (
            <ul className="space-y-1 max-h-[50vh] md:max-h-[60vh] overflow-y-auto pr-2">
              {downClues.map(({ number, clue }) => (
                <li
                  key={`down-${number}`}
                  className={`cursor-pointer rounded-lg p-1 text-sm transition-all duration-200 border ${
                    isClueSelected(number, 'down')
                      ? 'bg-black text-white border-black shadow-md'
                      : 'bg-primary-25 hover:bg-primary-50 border-primary-200 hover:border-ink-secondary text-black'
                  }`}
                  onClick={() => handleClueClick(number, 'down')}
                >
                  <div className="flex items-start gap-3">
                    <span className={`font-bold font-mono text-base flex-shrink-0 ${
                      isClueSelected(number, 'down') ? 'text-white' : 'text-ink-dark'
                    }`}>
                      {number}.
                    </span>
                    <span className={`font-serif leading-relaxed ${
                      isClueSelected(number, 'down') ? 'text-white' : 'text-ink'
                    }`}>
                      {clue || <span className="italic text-ink-muted">Tidak ada petunjuk</span>}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default CrosswordClues;

