import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useCrossword } from '../context/CrosswordContext';
import VirtualKeyboard from './VirtualKeyboard';
import AnswerValidation, { useAnswerValidation } from './AnswerValidation';

interface CrosswordGridProps {
  showResults?: boolean;
}

// Memoized Cell component to prevent unnecessary re-renders
const Cell = memo(({
  row,
  col,
  isEmpty,
  numbers,
  isSelected,
  isCorrect,
  isIncorrect,
  isRevealed,
  showResults,
  char,
  userAnswer,
  isSurrendered,
  onCellClick,
  onCellInput,
  onInputFocus,
  onInputBlur
}: {
  row: number;
  col: number;
  isEmpty: boolean;
  numbers: number[] | null;
  isSelected: boolean;
  isCorrect: boolean;
  isIncorrect: boolean;
  isRevealed: boolean;
  showResults: boolean;
  char: string;
  userAnswer: string;
  isSurrendered: boolean;
  onCellClick: (row: number, col: number) => void;
  onCellInput: (row: number, col: number, e: React.ChangeEvent<HTMLInputElement>) => void;
  onInputFocus?: () => void;
  onInputBlur?: () => void;
}) => {
  // Enhanced cell styling with newspaper theme
  let cellClasses = 'crossword-cell transition-all duration-200 hover:shadow-sm';

  if (isEmpty) {
    // Fix: Ensure empty cells have solid black background for newspaper theme
    cellClasses += ' bg-black border-2 border-black cursor-default';
  } else if (isSelected) {
    cellClasses += ' bg-primary-200 border-2 border-ink shadow-md ring-2 ring-ink ring-opacity-50';
  } else if (showResults) {
    if (isCorrect) {
      cellClasses += ' bg-green-100 border-2 border-green-500 text-green-800';
    } else if (isIncorrect) {
      cellClasses += ' bg-red-100 border-2 border-red-500 text-red-800';
    } else {
      cellClasses += ' bg-newsprint border border-ink-secondary hover:bg-primary-50';
    }
  } else if (isRevealed) {
    cellClasses += ' bg-blue-100 border-2 border-blue-500 text-blue-800';
  } else {
    cellClasses += ' bg-newsprint border border-ink-secondary hover:bg-primary-50 hover:border-ink';
  }

  // Memoize the click handler to prevent recreating it on every render
  const handleClick = useCallback(() => {
    if (!isEmpty) {
      onCellClick(row, col);
    }
  }, [row, col, isEmpty, onCellClick]);

  return (
    <div
      key={`${row}-${col}`}
      className={`relative flex items-center justify-center ${cellClasses}`}
      onClick={handleClick}
    >
      {numbers && numbers.length > 0 && !isEmpty && (
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          {/* Enhanced number display to fix intersection bug */}
          {numbers.length === 1 ? (
            <span className="absolute text-xs top-0.5 left-1 text-ink-dark font-bold leading-none bg-newsprint px-1 rounded-sm shadow-sm">
              {numbers[0]}
            </span>
          ) : (
            // Multiple numbers for intersecting clues - display all numbers
            <div className="absolute top-0 left-0 w-full">
              {numbers.map((number, index) => (
                <span
                  key={number}
                  className={`absolute text-xs top-0 text-ink-dark font-bold leading-none bg-newsprint px-1 rounded-sm shadow-sm ${
                    index === 0 ? 'left-0.5' : 'right-0.5'
                  }`}
                  style={{
                    top: index > 0 ? `${index * 12}px` : '2px'
                  }}
                >
                  {number}
                </span>
              ))}
            </div>
          )}
        </div>
      )}

      {!isEmpty && (
        <>
          {isSurrendered ? (
            // If surrendered, show the correct answer
            <span className="text-lg font-medium text-primary-600">
              {char}
            </span>
          ) : (
            // Otherwise show the input field
            <input
              type="text"
              maxLength={1}
              value={userAnswer || ''}
              onChange={(e) => onCellInput(row, col, e)}
              onFocus={onInputFocus}
              onBlur={onInputBlur}
              className={`w-full h-full text-center text-lg font-bold bg-transparent focus:outline-none focus:ring-0 font-mono uppercase ${
                showResults && isIncorrect
                  ? 'text-red-800'
                  : isRevealed
                    ? 'text-blue-800'
                    : isCorrect && showResults
                      ? 'text-green-800'
                      : 'text-ink-dark'
              } ${isSurrendered ? 'cursor-default' : 'cursor-text'}`}
              data-row={row}
              data-col={col}
              disabled={isSurrendered}
              autoComplete="off"
              spellCheck="false"
              aria-label={`Cell ${row + 1}, ${col + 1}`}
            />
          )}

          {/* Show correct answer for incorrect cells when checking answers */}
          {showResults && isIncorrect && !isSurrendered && (
            <span className="absolute -bottom-1 right-1 text-xs font-bold text-primary-600">
              {char}
            </span>
          )}
        </>
      )}
    </div>
  );
});

const CrosswordGrid: React.FC<CrosswordGridProps> = ({ showResults = false }) => {
  const { state, dispatch } = useCrossword();
  const [floatingClue, setFloatingClue] = useState<{
    text: string;
    number: number;
    direction: 'across' | 'down';
  } | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [showVirtualKeyboard, setShowVirtualKeyboard] = useState(false);

  // Answer validation system
  const { validationState, validateAnswer, clearValidation } = useAnswerValidation();

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Handle cell click - memoized to prevent recreating on every render
  const handleCellClick = useCallback((row: number, col: number) => {
    if (state.grid[row][col].char !== ' ') {
      dispatch({ type: 'SELECT_CELL', row, col });

      // Update floating clue for mobile
      if (isMobile && state.mode === 'play') {
        const cell = state.grid[row][col];
        const wordIds = cell.wordIds;

        if (wordIds.length > 0) {
          // If there's a selected word ID, use that, otherwise use the first word ID
          const wordId = state.selectedWordId && wordIds.includes(state.selectedWordId)
            ? state.selectedWordId
            : wordIds[0];

          const wordPosition = state.wordPositions[wordId - 1];
          if (wordPosition) {
            const { direction, number } = wordPosition;
            const clueText = state.clues[direction][number] || '';

            setFloatingClue({
              text: clueText,
              number,
              direction
            });
          }
        }
      }
    }
  }, [state.grid, state.mode, state.selectedWordId, state.wordPositions, state.clues, isMobile, dispatch]);

  // Handle user input in play mode - memoized to prevent recreating on every render
  const handleCellInput = useCallback((row: number, col: number, e: React.ChangeEvent<HTMLInputElement>) => {
    if (state.mode === 'play') {
      const value = e.target.value.slice(-1); // Get only the last character
      dispatch({ type: 'UPDATE_USER_ANSWER', row, col, value });

      // Auto-advance to next cell
      if (value && state.selectedWordId) {
        const wordPosition = state.wordPositions[state.selectedWordId - 1];
        if (wordPosition) {
          const { direction } = wordPosition;
          const nextRow = direction === 'across' ? row : row + 1;
          const nextCol = direction === 'across' ? col + 1 : col;

          // Check if next cell is within bounds and part of the same word
          if (
            nextRow < state.gridSize &&
            nextCol < state.gridSize &&
            state.grid[nextRow][nextCol].wordIds.includes(state.selectedWordId)
          ) {
            // Focus next input
            const nextInput = document.querySelector(
              `input[data-row="${nextRow}"][data-col="${nextCol}"]`
            ) as HTMLInputElement;
            if (nextInput) {
              nextInput.focus();
            }
          }
        }
      }
    }
  }, [state.mode, state.selectedWordId, state.wordPositions, state.gridSize, state.grid, dispatch]);

  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (state.mode !== 'play' || !state.selectedWordId) return;

      // Get the selected word position
      const wordPosition = state.wordPositions[state.selectedWordId - 1];
      if (!wordPosition) return;

      // Find the currently selected cell
      let selectedRow = -1;
      let selectedCol = -1;

      if (state.selectedCell) {
        [selectedRow, selectedCol] = state.selectedCell;
      } else {
        // If no cell is selected, use the first cell of the selected word
        selectedRow = wordPosition.row;
        selectedCol = wordPosition.col;
      }

      let nextRow = selectedRow;
      let nextCol = selectedCol;

      switch (e.key) {
        case 'ArrowUp':
          nextRow = Math.max(0, selectedRow - 1);
          break;
        case 'ArrowDown':
          nextRow = Math.min(state.gridSize - 1, selectedRow + 1);
          break;
        case 'ArrowLeft':
          nextCol = Math.max(0, selectedCol - 1);
          break;
        case 'ArrowRight':
          nextCol = Math.min(state.gridSize - 1, selectedCol + 1);
          break;
        case 'Tab':
          // Toggle direction on Tab
          e.preventDefault();
          dispatch({ type: 'TOGGLE_DIRECTION' });
          return;
        case 'Escape':
          // Close floating clue on Escape
          if (floatingClue) {
            setFloatingClue(null);
            return;
          }
          break;
        default:
          return; // Don't handle other keys
      }

      // Only move if the target cell is not empty
      if (state.grid[nextRow][nextCol].char !== ' ') {
        handleCellClick(nextRow, nextCol);

        // Focus the input in the new cell
        setTimeout(() => {
          const input = document.querySelector(
            `input[data-row="${nextRow}"][data-col="${nextCol}"]`
          ) as HTMLInputElement;
          if (input) {
            input.focus();
          }
        }, 0);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.mode, state.selectedWordId, state.wordPositions, state.selectedCell, state.gridSize, state.grid, floatingClue, dispatch, handleCellClick]);

  // Virtual keyboard handlers
  const handleVirtualKeyPress = useCallback((key: string) => {
    if (state.selectedCell) {
      const [row, col] = state.selectedCell;
      dispatch({ type: 'UPDATE_USER_ANSWER', row, col, value: key });

      // Auto-advance to next cell
      if (state.selectedWordId) {
        const wordPosition = state.wordPositions[state.selectedWordId - 1];
        if (wordPosition) {
          const { direction } = wordPosition;
          const nextRow = direction === 'across' ? row : row + 1;
          const nextCol = direction === 'across' ? col + 1 : col;

          // Check if next cell is within bounds and part of the same word
          if (
            nextRow < state.gridSize &&
            nextCol < state.gridSize &&
            state.grid[nextRow][nextCol].wordIds.includes(state.selectedWordId)
          ) {
            handleCellClick(nextRow, nextCol);
          }
        }
      }
    }
  }, [state.selectedCell, state.selectedWordId, state.wordPositions, state.gridSize, state.grid, dispatch, handleCellClick]);

  const handleVirtualBackspace = useCallback(() => {
    if (state.selectedCell) {
      const [row, col] = state.selectedCell;
      dispatch({ type: 'UPDATE_USER_ANSWER', row, col, value: '' });
    }
  }, [state.selectedCell, dispatch]);

  const handleVirtualKeyboardClose = useCallback(() => {
    setShowVirtualKeyboard(false);
  }, []);

  // Show virtual keyboard when a cell is selected on mobile
  useEffect(() => {
    if (isMobile && state.selectedCell) {
      setShowVirtualKeyboard(true);
    } else if (!isMobile || !state.selectedCell) {
      setShowVirtualKeyboard(false);
    }
  }, [isMobile, state.selectedCell]);

  // Hide virtual keyboard when clicking outside crossword area
  useEffect(() => {
    if (!isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      // Check if click is outside crossword grid and virtual keyboard
      if (!target.closest('.crossword-cell') &&
          !target.closest('[data-virtual-keyboard]') &&
          !target.closest('input[data-row]')) {
        setShowVirtualKeyboard(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile]);

  // Handle input focus for virtual keyboard
  const handleInputFocus = useCallback(() => {
    if (isMobile) {
      setShowVirtualKeyboard(true);
    }
  }, [isMobile]);

  const handleInputBlur = useCallback(() => {
    // Don't hide immediately - let the cell selection logic handle it
    // This prevents flickering when switching between cells
  }, []);

  // Get word numbers for a cell - memoized to prevent recalculation on every render
  // Returns an array of numbers to handle cells that are the start of both across and down clues
  const getCellNumbers = useCallback((row: number, col: number) => {
    const numbers: number[] = [];

    for (const { row: r, col: c, number } of state.wordPositions) {
      if (r === row && c === col) {
        numbers.push(number);
      }
    }

    return numbers.length > 0 ? numbers : null;
  }, [state.wordPositions]);

  // Check if a cell is part of the selected word - memoized
  const isSelectedCell = useCallback((row: number, col: number) => {
    if (!state.selectedWordId) return false;
    return state.grid[row][col].wordIds.includes(state.selectedWordId);
  }, [state.selectedWordId, state.grid]);

  // Check if a cell's answer is correct - memoized
  const isCellCorrect = useCallback((row: number, col: number) => {
    if (!state.userAnswers) return false;
    return state.userAnswers[row][col] === state.grid[row][col].char;
  }, [state.userAnswers, state.grid]);

  // Check if a cell's answer is incorrect - memoized
  const isCellIncorrect = useCallback((row: number, col: number) => {
    if (!state.userAnswers || state.userAnswers[row][col] === '') return false;
    return state.userAnswers[row][col] !== state.grid[row][col].char;
  }, [state.userAnswers, state.grid]);

  // Check if a cell has been revealed as a hint - memoized
  const isCellRevealed = useCallback((row: number, col: number) => {
    return state.revealedCells?.some(([r, c]) => r === row && c === col) || false;
  }, [state.revealedCells]);

  // Memoize the grid content to prevent unnecessary re-renders
  const gridContent = useMemo(() => {
    const cells = [];

    for (let row = 0; row < state.gridSize; row++) {
      for (let col = 0; col < state.gridSize; col++) {
        const cell = state.grid[row][col];
        const isEmpty = cell.char === ' ';
        const numbers = getCellNumbers(row, col);
        const isSelected = isSelectedCell(row, col);
        const isCorrect = isCellCorrect(row, col);
        const isIncorrect = isCellIncorrect(row, col);
        const isRevealed = isCellRevealed(row, col);

        cells.push(
          <Cell
            key={`${row}-${col}`}
            row={row}
            col={col}
            isEmpty={isEmpty}
            numbers={numbers}
            isSelected={isSelected}
            isCorrect={isCorrect}
            isIncorrect={isIncorrect}
            isRevealed={isRevealed}
            showResults={showResults}
            char={cell.char}
            userAnswer={state.userAnswers?.[row][col] || ''}
            isSurrendered={state.isSurrendered || false}
            onCellClick={handleCellClick}
            onCellInput={handleCellInput}
            onInputFocus={handleInputFocus}
            onInputBlur={handleInputBlur}
          />
        );
      }
    }

    return (
      <div
        className="grid gap-[2px] p-2 bg-ink rounded-lg shadow-paper-lg"
        style={{
          gridTemplateColumns: `repeat(${state.gridSize}, minmax(32px, 1fr))`,
          gridTemplateRows: `repeat(${state.gridSize}, minmax(32px, 1fr))`,
          aspectRatio: '1 / 1',
        }}
      >
        {cells}
      </div>
    );
  }, [
    state.gridSize,
    state.grid,
    state.userAnswers,
    state.isSurrendered,
    getCellNumbers,
    isSelectedCell,
    isCellCorrect,
    isCellIncorrect,
    isCellRevealed,
    showResults,
    handleCellClick,
    handleCellInput,
    handleInputFocus,
    handleInputBlur
  ]);

  return (
    <div className="relative overflow-auto">
      {gridContent}

      {/* Enhanced floating clue for mobile with fixed positioning */}
      {isMobile && floatingClue && (
        <div
          className="fixed top-4 left-4 right-4 bg-newsprint rounded-lg shadow-paper-lg border-2 border-ink z-[9999] animate-fadeIn"
          style={{
            position: 'fixed',
            top: '1rem',
            left: '1rem',
            right: '1rem',
            maxWidth: '28rem',
            margin: '0 auto',
            zIndex: 9999
          }}
        >
          <div className="p-2">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 bg-black text-newsprint rounded-full w-12 h-12 flex items-center justify-center border-2 border-ink">
                <span className="font-bold text-lg font-mono">{floatingClue.number}</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center mb-1">
                  <span className="text-xs font-bold text-ink bg-primary-100 px-2 py-1 rounded-full border border-ink-secondary">
                    {floatingClue.direction === 'across' ? 'MENDATAR' : 'MENURUN'}
                  </span>
                </div>
                <p className="text-sm font-medium text-ink-dark leading-relaxed font-serif">
                  {floatingClue.text || 'Tidak ada petunjuk tersedia'}
                </p>
              </div>
              <button
                className="flex-shrink-0 text-ink-secondary hover:text-ink bg-primary-100 hover:bg-primary-200 rounded-full p-2 transition-all duration-200 border border-ink-secondary hover:border-ink"
                onClick={() => setFloatingClue(null)}
                aria-label="Tutup petunjuk"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            {/* <div className="mt-3 pt-3 border-t border-ink-secondary text-xs text-ink-muted text-center font-mono">
              Tekan ESC untuk menutup • Ketuk sel lain untuk ganti petunjuk
            </div> */}
          </div>
        </div>
      )}

      {/* Virtual Keyboard for Mobile */}
      <VirtualKeyboard
        isVisible={showVirtualKeyboard}
        onKeyPress={handleVirtualKeyPress}
        onBackspace={handleVirtualBackspace}
        onClose={handleVirtualKeyboardClose}
      />
    </div>
  );
};

// Export as memoized component to prevent unnecessary re-renders
export default memo(CrosswordGrid);
