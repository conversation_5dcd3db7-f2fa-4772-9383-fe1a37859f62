import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { AlertCircle, ArrowLeftIcon, RefreshCw } from 'lucide-react';
import { ErrorType } from '../utils/errorHandler';

interface ErrorPageProps {
  type?: ErrorType;
  title?: string;
  message?: string;
  backLink?: string;
  backText?: string;
  onRetry?: () => void;
  retryText?: string;
}

/**
 * Standardized error page component for different error types
 */
const ErrorPage: React.FC<ErrorPageProps> = ({
  type = ErrorType.UNKNOWN,
  title,
  message,
  backLink = '/',
  backText = 'Kembali ke Beranda',
  onRetry,
  retryText = 'Coba Lagi'
}) => {
  // Default titles and messages based on error type
  const defaultTitles = {
    [ErrorType.NETWORK]: 'Kesalahan Koneksi',
    [ErrorType.SERVER]: 'Kesalahan Server',
    [ErrorType.VALIDATION]: 'Data Tidak Valid',
    [ErrorType.AUTHENTICATION]: '<PERSON><PERSON>',
    [ErrorType.AUTHORIZATION]: '<PERSON><PERSON><PERSON>',
    [ErrorType.NOT_FOUND]: '404 - Halaman Tidak Ditemukan',
    [ErrorType.TIMEOUT]: 'Permintaan Timeout',
    [ErrorType.UNKNOWN]: 'Terjadi Kesalahan'
  };

  const defaultMessages = {
    [ErrorType.NETWORK]: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda dan coba lagi.',
    [ErrorType.SERVER]: 'Terjadi kesalahan pada server. Silakan coba lagi nanti.',
    [ErrorType.VALIDATION]: 'Data yang Anda masukkan tidak valid. Silakan periksa kembali dan coba lagi.',
    [ErrorType.AUTHENTICATION]: 'Sesi Anda telah berakhir. Silakan login kembali.',
    [ErrorType.AUTHORIZATION]: 'Anda tidak memiliki izin untuk mengakses halaman ini.',
    [ErrorType.NOT_FOUND]: 'Maaf, halaman yang Anda cari tidak ditemukan. Silakan kembali ke beranda atau coba tautan lain.',
    [ErrorType.TIMEOUT]: 'Permintaan memakan waktu terlalu lama. Silakan coba lagi.',
    [ErrorType.UNKNOWN]: 'Terjadi kesalahan yang tidak diketahui. Silakan coba lagi nanti.'
  };

  // Use provided title/message or default based on error type
  const displayTitle = title || defaultTitles[type];
  const displayMessage = message || defaultMessages[type];

  // Color schemes based on error type
  const colorSchemes = {
    [ErrorType.NETWORK]: 'text-orange-600 bg-orange-100 border-orange-300',
    [ErrorType.SERVER]: 'text-red-600 bg-red-100 border-red-300',
    [ErrorType.VALIDATION]: 'text-yellow-600 bg-yellow-100 border-yellow-300',
    [ErrorType.AUTHENTICATION]: 'text-blue-600 bg-blue-100 border-blue-300',
    [ErrorType.AUTHORIZATION]: 'text-red-600 bg-red-100 border-red-300',
    [ErrorType.NOT_FOUND]: 'text-blue-600 bg-blue-100 border-blue-300',
    [ErrorType.TIMEOUT]: 'text-orange-600 bg-orange-100 border-orange-300',
    [ErrorType.UNKNOWN]: 'text-gray-600 bg-gray-100 border-gray-300'
  };

  const colorScheme = colorSchemes[type];

  return (
    <div className="container mx-auto px-4 py-12">
      <div className={`max-w-md mx-auto text-center p-6 rounded-lg border ${colorScheme}`}>
        <div className="flex justify-center mb-4">
          <AlertCircle className={`w-16 h-16 ${colorScheme.split(' ')[0]}`} />
        </div>
        <h1 className={`text-2xl font-bold mb-4 ${colorScheme.split(' ')[0]}`}>{displayTitle}</h1>
        <p className="text-gray-600 mb-6">{displayMessage}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              {retryText}
            </button>
          )}
          <Link
            to={backLink}
            className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            {backText}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
