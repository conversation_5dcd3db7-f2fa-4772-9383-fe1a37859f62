import React, { useState, useEffect, memo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { StarIcon, UsersIcon } from 'lucide-react';
import { getFeaturedCrosswords } from '../services/api';
import { useAsyncOperation } from '../hooks/useAsyncOperation';

// Optimized image component with better performance
const OptimizedImage = memo(({ src, alt, className }: { src: string; alt: string; className: string }) => {
  // Use a single state for tracking both source and loading state
  const [state, setState] = useState({
    imgSrc: src,
    isLoaded: false
  });

  // Combined error and load handlers to reduce re-renders
  const imageHandlers = {
    onError: () => setState(prev => ({ ...prev, imgSrc: '/images/default-img.png' })),
    onLoad: () => setState(prev => ({ ...prev, isLoaded: true }))
  };

  return (
    // <div className={`${className} relative bg-gray-200`}>
    //   {!state.isLoaded && (
    //     <div className="absolute inset-0 bg-gray-200 animate-pulse" aria-hidden="true" />
    //   )}
    //   <img
    //     src={state.imgSrc}
    //     alt={alt}
    //     className={`w-full h-full object-cover transition-opacity duration-300 ${state.isLoaded ? 'opacity-100' : 'opacity-0'}`}
    //     loading="lazy"
    //     decoding="async"
    //     {...imageHandlers}
    //     width="300"
    //     height="200"
    //   />
    // </div>
    ""
  );
});

// Fallback data for when API fails - moved to a separate file to reduce bundle size
import { fallbackFeaturedCrosswords } from '../data/fallbackData';

// Optimized URL generator with proper typing
const getCrosswordUrl = (crossword: Crossword): string => {
  if (crossword.category_slug && crossword.slug) {
    return `/teka-teki-silang/${crossword.category_slug}/${crossword.slug}`;
  }
  return `/play/${crossword.id}`;
};

// API data fetching with caching
// Using a simple in-memory cache with expiration
const CACHE_KEY = 'featured_crosswords';
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes in milliseconds

const fetchFeaturedCrosswords = async (): Promise<CrosswordListItem[]> => {
  try {
    // Check if we have cached data
    const cachedData = sessionStorage.getItem(CACHE_KEY);
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      // Check if cache is still valid
      if (Date.now() - timestamp < CACHE_EXPIRY) {
        return data;
      }
    }

    // If no valid cache, fetch from API
    const featuredCrosswords = await getFeaturedCrosswords();

    // Cache the result
    sessionStorage.setItem(CACHE_KEY, JSON.stringify({
      data: featuredCrosswords,
      timestamp: Date.now()
    }));

    return featuredCrosswords;
  } catch (error) {
    console.error('Error fetching featured crosswords:', error);
    // Return fallback data on error
    return fallbackFeaturedCrosswords;
  }
};

// Define types for better type safety
interface Crossword {
  id: string;
  title: string;
  creator: string;
  difficulty: 'mudah' | 'sedang' | 'sulit';
  plays: number;
  rating: number;
  category: string;
  category_slug?: string;
  slug?: string;
  image: string;
}

// Get difficulty color class - extracted to reduce inline conditionals
const getDifficultyColorClass = (difficulty: 'mudah' | 'sedang' | 'sulit'): string => {
  switch (difficulty) {
    case 'mudah': return 'bg-primary-400';
    case 'sedang': return 'bg-primary-600';
    case 'sulit': return 'bg-primary-800';
    default: return 'bg-primary-500';
  }
};

// Optimized crossword card component with better performance
const CrosswordCard = memo(({ crossword }: { crossword: Crossword }) => {
  // Pre-compute values to avoid recalculations in render
  const url = getCrosswordUrl(crossword);
  const difficultyClass = getDifficultyColorClass(crossword.difficulty);
  const imageAlt = `TTS: ${crossword.title} - ${crossword.difficulty}`;

  return (
    <Link
      to={url}
      className="card-paper overflow-hidden hover:shadow-paper-lg transition-shadow"
    >
      <OptimizedImage
        src={crossword.image}
        alt={imageAlt}
        className="h-20 md:h-40 overflow-hidden"
      />
      <div className="p-4">
        <h3 className="font-bold text-lg mb-1 line-clamp-1 text-ink-dark">{crossword.title}</h3>
        <p className="text-sm text-primary-600 mb-2">Oleh: {crossword.creator}</p>

        <div className="flex justify-between items-center text-sm">
          <span className={`px-2 py-1 rounded-full text-newsprint ${difficultyClass}`}>
            {crossword.difficulty}
          </span>

          <div className="flex items-center space-x-3">
            <span className="flex items-center text-primary-700">
              <StarIcon className="w-4 h-4 text-primary-600 mr-1" />
              {crossword.rating}
            </span>
            <span className="flex items-center text-primary-700">
              <UsersIcon className="w-4 h-4 text-primary-600 mr-1" />
              {crossword.plays}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return prevProps.crossword.id === nextProps.crossword.id &&
         prevProps.crossword.title === nextProps.crossword.title;
});

// Import standardized UI components
import { CardSkeleton, FeedbackMessage } from './ui/Feedback';

const FeaturedCrosswords: React.FC = () => {
  const [crosswords, setCrosswords] = useState<Crossword[]>([]);

  // Use the standardized async operation hook
  const { loading: isLoading, error, execute: fetchData } = useAsyncOperation(
    async () => {
      const data = await fetchFeaturedCrosswords();

      // Process data with optimized image paths
      const crosswordsWithImages = data.map((crossword: any) => ({
        ...crossword,
        image: `/images/default-img.png`
      })) as Crossword[];

      setCrosswords(crosswordsWithImages);
      return crosswordsWithImages;
    },
    {
      showLoadingToast: false,
      showErrorToast: true,
      errorMessage: 'Gagal memuat teka-teki silang unggulan'
    }
  );

  // Effect hook with proper cleanup
  useEffect(() => {
    const controller = new AbortController();
    fetchData();

    return () => {
      controller.abort();
    };
  }, [fetchData]);

  // Show loading state with skeleton
  if (isLoading && crosswords.length === 0) {
    return <CardSkeleton count={4} className="grid-cols-2 md:grid-cols-2 lg:grid-cols-4" />;
  }

  // Show error state
  if (error && crosswords.length === 0) {
    return <FeedbackMessage type="error" message={error} />;
  }

  // Render crossword cards
  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {crosswords.map((crossword) => (
        <CrosswordCard key={crossword.id} crossword={crossword} />
      ))}
    </div>
  );
};

export default FeaturedCrosswords;




