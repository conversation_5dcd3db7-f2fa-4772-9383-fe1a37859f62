import React from 'react';
import { Link } from 'react-router-dom';
import { GridIcon, GithubIcon, TwitterIcon, InstagramIcon } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-ink text-newsprint border-t border-primary-700">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Logo da<PERSON> */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <GridIcon className="w-8 h-8 text-primary-400" />
              <span className="font-bold text-xl font-serif">Tekateki.io</span>
            </div>
            <p className="text-primary-300 mb-4">
              Platform untuk membuat, berbagi, dan memainkan teka-teki silang. Buat TTS kustom Anda sendiri atau jelajahi ribuan teka-teki yang dibuat oleh komunitas kami.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-primary-300 hover:text-newsprint transition-colors">
                <GithubIcon className="w-5 h-5" />
              </a>
              <a href="#" className="text-primary-300 hover:text-newsprint transition-colors">
                <TwitterIcon className="w-5 h-5" />
              </a>
              <a href="#" className="text-primary-300 hover:text-newsprint transition-colors">
                <InstagramIcon className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Tautan */}
          <div>
            <h3 className="font-bold text-lg mb-4 text-newsprint font-serif">Jelajahi</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-primary-300 hover:text-newsprint transition-colors">Beranda</Link>
              </li>
              <li>
                <Link to="/play" className="text-primary-300 hover:text-newsprint transition-colors">Main</Link>
              </li>
              <li>
                <Link to="/create" className="text-primary-300 hover:text-newsprint transition-colors">Buat</Link>
              </li>
              <li>
                <Link to="/category/umum" className="text-primary-300 hover:text-newsprint transition-colors">Kategori</Link>
              </li>
              <li>
                <a href="/blog" className="text-primary-300 hover:text-newsprint transition-colors" target="_self">Blog</a>
              </li>
            </ul>
          </div>

          {/* Bantuan */}
          <div>
            <h3 className="font-bold text-lg mb-4 text-newsprint font-serif">Bantuan</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/cara-bermain" className="text-primary-300 hover:text-newsprint transition-colors">Cara Bermain</Link>
              </li>
              <li>
                <Link to="/tips-strategi-tts" className="text-primary-300 hover:text-newsprint transition-colors">Tips & Strategi</Link>
              </li>
              <li>
                <Link to="/kamus-istilah-tts" className="text-primary-300 hover:text-newsprint transition-colors">Kamus Istilah</Link>
              </li>
              <li>
                <Link to="/manfaat-tts" className="text-primary-300 hover:text-newsprint transition-colors">Manfaat TTS</Link>
              </li>
              <li>
                <Link to="/bantuan/faq" className="text-primary-300 hover:text-newsprint transition-colors">FAQ</Link>
              </li>
            </ul>
          </div>

          {/* Tautan Lainnya */}
          <div>
            <h3 className="font-bold text-lg mb-4 text-newsprint font-serif">Lainnya</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/tentang-kami" className="text-primary-300 hover:text-newsprint transition-colors">Tentang Kami</Link>
              </li>
              <li>
                <Link to="/kebijakan-privasi" className="text-primary-300 hover:text-newsprint transition-colors">Kebijakan Privasi</Link>
              </li>
              <li>
                <Link to="/syarat-ketentuan" className="text-primary-300 hover:text-newsprint transition-colors">Syarat dan Ketentuan</Link>
              </li>
              <li>
                <Link to="/kontak" className="text-primary-300 hover:text-newsprint transition-colors">Kontak</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-primary-700 mt-8 pt-8 text-center text-primary-400">
          <p>&copy; {new Date().getFullYear()} Tekateki.io. Semua hak dilindungi.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;



