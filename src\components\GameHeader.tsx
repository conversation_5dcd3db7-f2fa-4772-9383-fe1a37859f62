import React from 'react';
import { TagIcon, Maximize2Icon } from 'lucide-react';
import { Category } from '../services/api';
import ShareButton from './ShareButton';

interface GameHeaderProps {
  puzzleTitle: string;
  puzzleCreator: string;
  puzzleDifficulty: string;
  puzzleDescription: string;
  category: Category | null;
  progress: number;
  gridSize: number;
  isDescriptionOpen: boolean;
  onToggleDescription: () => void;
  onToggleFocusMode: () => void;
  calculateScore: () => number;
  timeSpent: number;
}

const GameHeader: React.FC<GameHeaderProps> = ({
  puzzleTitle,
  puzzleCreator,
  puzzleDifficulty,
  puzzleDescription,
  category,
  progress,
  gridSize,
  isDescriptionOpen,
  onToggleDescription,
  onToggleFocusMode,
  calculateScore,
  timeSpent,
}) => {
  return (
    <div className="bg-newsprint border-2 border-ink-900 p-4 md:p-6 mb-6 shadow-paper-lg bg-paper-lines">
      {/* Main title with newspaper headline styling */}
      <div className="border-b-4 border-ink-900 pb-4 mb-4">
        <h1 className="text-3xl md:text-4xl font-bold text-ink-900 font-serif leading-tight tracking-tight uppercase">
          {puzzleTitle}
        </h1>

        {/* Subtitle with metadata */}
        <div className="flex flex-wrap items-center gap-4 mt-3 text-sm text-ink-700">
          {category && (
            <div className="flex items-center bg-ink-900 text-newsprint px-3 py-1 rounded-sm">
              <TagIcon className="w-4 h-4 mr-1" />
              <span className="font-medium font-serif">{category.name}</span>
            </div>
          )}
          <span className="font-medium font-serif">Oleh: {puzzleCreator}</span>
          <span className="font-medium font-serif">Kesulitan: {puzzleDifficulty}</span>
          <span className="font-medium font-mono">Ukuran: {gridSize}×{gridSize}</span>
        </div>
      </div>

      {/* Action buttons row */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          <button
            onClick={onToggleDescription}
            className="bg-ink-100 text-ink-900 border-2 border-ink-900 text-sm px-3 py-1.5 hover:bg-ink-900 hover:text-newsprint transition-all duration-200 font-serif font-semibold rounded-sm"
          >
            {isDescriptionOpen ? 'Sembunyikan' : 'Tampilkan'} Deskripsi
          </button>

          {/* Enhanced Progress indicator */}
          <div className="flex items-center gap-2 bg-ink-900 text-newsprint px-3 py-1.5 rounded-sm border-2 border-ink-900">
            <div className={`w-3 h-3 rounded-full ${
              progress === 100 ? 'bg-green-400' :
              progress > 75 ? 'bg-blue-400' :
              progress > 50 ? 'bg-yellow-400' :
              progress > 25 ? 'bg-orange-400' : 'bg-red-400'
            }`}></div>
            <span className="text-sm font-bold font-mono">
              Progress: {progress}%
            </span>
          </div>
        </div>

        {/* Focus Mode and Share Buttons */}
        <div className="flex items-center gap-2">
          <button
            onClick={onToggleFocusMode}
            className="p-2.5 rounded-sm bg-ink-100 text-ink-900 border-2 border-ink-900 hover:bg-ink-900 hover:text-newsprint transition-all duration-200 hover:scale-105"
            title="Mode Fokus - Sembunyikan elemen lain"
            aria-label="Aktifkan Mode Fokus"
          >
            <Maximize2Icon className="w-5 h-5" />
          </button>
          <ShareButton
            title={puzzleTitle}
            isCompleted={progress === 100}
            score={calculateScore()}
            timeSpent={timeSpent}
            variant="outline"
            size="sm"
          />
        </div>
      </div>

      {/* Enhanced Expandable description section */}
      {puzzleDescription && isDescriptionOpen && (
        <div className="mt-6 pt-4 border-t-4 border-ink-900 animate-fadeIn">
          <div className="bg-ink-100 p-4 rounded-sm border-l-4 border-ink-900">
            <h3 className="text-lg font-bold mb-3 text-ink-900 font-serif uppercase">
              Deskripsi Teka-Teki
            </h3>
            <div className="text-ink-800 font-serif leading-relaxed">
              {puzzleDescription}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameHeader;
