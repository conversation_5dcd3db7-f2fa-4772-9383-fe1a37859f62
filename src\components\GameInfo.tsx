import React from 'react';
import { TagIcon } from 'lucide-react';
import { Category } from '../services/api';
import GameTimer from './GameTimer';
import { GameState } from '../types/crossword';

interface GameInfoProps {
  puzzleCreator: string;
  puzzleDifficulty: string;
  gridSize: number;
  hintsRemaining: number;
  progress: number;
  timeSpent: number;
  gameState: GameState;
  category?: Category | null;
  onTogglePauseResume: () => void;
}

const GameInfo: React.FC<GameInfoProps> = ({
  puzzleCreator,
  puzzleDifficulty,
  gridSize,
  hintsRemaining,
  progress,
  timeSpent,
  gameState,
  category,
  onTogglePauseResume,
}) => {
  return (
    <div className="bg-newsprint border-4 border-ink-900 p-4 shadow-paper-lg bg-paper-lines rounded-sm">
      <h3 className="text-lg font-bold text-ink-900 font-serif mb-3 uppercase border-b-2 border-ink-900 pb-2">
        Informasi Permainan
      </h3>

      {/* Timer and Controls */}
      <div className="mb-4">
        <GameTimer
          timeSpent={timeSpent}
          gameState={gameState}
          onTogglePauseResume={onTogglePauseResume}
          compact
          className="justify-between bg-ink-100 p-3 rounded-sm border-2 border-ink-900"
        />
      </div>

      <div className="space-y-3 text-sm">
        <div className="flex justify-between items-center">
          <span className="text-ink-700 font-serif">Pembuat:</span>
          <span className="font-bold text-ink-900 font-serif">{puzzleCreator}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-ink-700 font-serif">Tingkat Kesulitan:</span>
          <span className="font-bold text-ink-900 capitalize font-serif">{puzzleDifficulty}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-ink-700 font-serif">Ukuran Grid:</span>
          <span className="font-bold text-ink-900 font-mono">{gridSize}×{gridSize}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-ink-700 font-serif">Bantuan Tersisa:</span>
          <span className="font-bold text-ink-900 font-mono">{hintsRemaining}</span>
        </div>
        {category && (
          <div className="flex justify-between items-center">
            <span className="text-ink-700 font-serif">Kategori:</span>
            <div className="flex items-center bg-ink-900 text-newsprint px-2 py-1 rounded-sm">
              <TagIcon className="w-3 h-3 mr-1" />
              <span className="text-xs font-medium font-serif">{category.name}</span>
            </div>
          </div>
        )}
        <div className="pt-2 border-t-2 border-ink-900">
          <div className="flex justify-between items-center mb-2">
            <span className="text-ink-700 font-serif">Progress:</span>
            <span className="font-bold text-ink-900 font-mono">{progress}%</span>
          </div>
          <div className="w-full bg-ink-200 rounded-sm h-3 border border-ink-900">
            <div
              className="bg-ink-900 h-full rounded-sm transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameInfo;
