import React from 'react';
import { PlayIcon, PauseIcon, ClockIcon } from 'lucide-react';
import { GameState } from '../types/crossword';

interface GameTimerProps {
  timeSpent: number;
  gameState: GameState;
  onTogglePauseResume: () => void;
  compact?: boolean;
  className?: string;
}

// Helper function to format time in MM:SS format
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const GameTimer: React.FC<GameTimerProps> = ({
  timeSpent,
  gameState,
  onTogglePauseResume,
  compact = false,
  className = '',
}) => {
  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="bg-ink-100 px-3 py-1.5 rounded-sm border-2 border-ink-900">
          <div className="flex items-center gap-2">
            <ClockIcon className="w-4 h-4 text-ink-900" />
            <span className="font-mono font-bold text-ink-900">
              {formatTime(timeSpent)}
            </span>
          </div>
        </div>
        <button
          onClick={onTogglePauseResume}
          className="bg-ink-900 text-newsprint p-1.5 rounded-sm hover:bg-ink-800 transition-all duration-200 shadow-paper"
          title={gameState === 'playing' ? 'Jeda' : 'Lanjutkan'}
          aria-label={gameState === 'playing' ? 'Jeda permainan' : 'Lanjutkan permainan'}
        >
          {gameState === 'playing' ? (
            <PauseIcon className="w-4 h-4" />
          ) : (
            <PlayIcon className="w-4 h-4" />
          )}
        </button>
      </div>
    );
  }

  return (
    <div className={`bg-ink-900 text-newsprint px-6 py-4 rounded-sm border-4 border-ink-900 shadow-paper ${className}`}>
      <div className="flex items-center gap-3">
        <ClockIcon className="w-6 h-6" />
        <span className="text-2xl font-mono font-bold">
          {formatTime(timeSpent)}
        </span>
        {gameState !== 'not-started' && (
          <button
            onClick={onTogglePauseResume}
            className="ml-4 bg-newsprint text-ink-900 p-2 rounded-sm hover:bg-ink-100 transition-all duration-200 shadow-paper hover:shadow-paper-lg"
            title={gameState === 'playing' ? 'Jeda' : 'Lanjutkan'}
            aria-label={gameState === 'playing' ? 'Jeda permainan' : 'Lanjutkan permainan'}
          >
            {gameState === 'playing' ? (
              <PauseIcon className="w-5 h-5" />
            ) : (
              <PlayIcon className="w-5 h-5" />
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default GameTimer;
