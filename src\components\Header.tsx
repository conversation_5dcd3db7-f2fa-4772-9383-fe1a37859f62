import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { GridIcon, MenuIcon, XIcon, UserIcon, LogInIcon, UserPlusIcon, LogOutIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const auth = useAuth(); // Use the actual hook
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className="bg-newsprint shadow-paper border-b border-primary-200">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <img src="/images/TTS.png" alt="Logo Tekateki.io" className="w-12 h-12 border border-primary-400 rounded text-ink" />

            <span className="font-bold text-xl text-ink font-serif">Tekateki.io</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link
              to="/"
              className={`nav-link ${isActive('/') ? 'active' : ''}`}
            >
              Beranda
            </Link>
            <Link
              to="/teka-teki-silang/semua"
              className={`nav-link ${isActive('/play') || location.pathname.startsWith('/teka-teki-silang/') ? 'active' : ''}`}
            >
              Main
            </Link>
            <Link
              to="/create"
              className={`nav-link ${isActive('/create') ? 'active' : ''}`}
            >
              Buat
            </Link>
            <a
              href="/blog"
              className="nav-link"
              target="_self"
            >
              Blog
            </a>
            <div className="relative group">
              <button
                className={`nav-link ${
                  location.pathname.startsWith('/bantuan') ||
                  location.pathname === '/cara-bermain' ||
                  location.pathname === '/kamus-istilah-tts' ||
                  location.pathname === '/manfaat-tts' ||
                  location.pathname === '/tips-strategi-tts'
                    ? 'active'
                    : ''
                }`}
              >
                Bantuan
              </button>
              <div className="absolute left-0 w-48 bg-newsprint rounded-md shadow-paper-lg py-1 z-10 hidden group-hover:block border border-primary-200">
                <Link to="/cara-bermain" className="block px-4 py-2 text-primary-700 hover:bg-primary-50 hover:text-ink">
                  Cara Bermain
                </Link>
                <Link to="/tips-strategi-tts" className="block px-4 py-2 text-primary-700 hover:bg-primary-50 hover:text-ink">
                  Tips & Strategi
                </Link>
                <Link to="/kamus-istilah-tts" className="block px-4 py-2 text-primary-700 hover:bg-primary-50 hover:text-ink">
                  Kamus Istilah
                </Link>
                <Link to="/manfaat-tts" className="block px-4 py-2 text-primary-700 hover:bg-primary-50 hover:text-ink">
                  Manfaat TTS
                </Link>
                <Link to="/bantuan/faq" className="block px-4 py-2 text-primary-700 hover:bg-primary-50 hover:text-ink">
                  FAQ
                </Link>
              </div>
            </div>
          </nav>

          {/* User Menu & Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            {auth.isAuthenticated && auth.user ? (
              <div className="relative group">
                <button className="flex items-center space-x-2 p-2 rounded-full bg-primary-100 text-primary-700 hover:bg-primary-200">
                  <UserIcon className="w-5 h-5" />
                  <span className="hidden md:inline text-sm font-medium">{auth.user.displayName || auth.user.username}</span>
                </button>
                <div className="absolute right-0 w-48 bg-newsprint rounded-md shadow-paper-lg py-1 z-10 hidden group-hover:block border border-primary-200">
                  {auth.user?.role === 'admin' && (
                    <Link
                      to="/admin"
                      className="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-ink"
                    >
                      Admin Panel
                    </Link>
                  )}
                  <Link
                    to="/profile"
                    className="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-ink"
                  >
                    Profile
                  </Link>
                  <button
                    onClick={async () => {
                      await auth.logout();
                      navigate('/'); // Redirect to home after logout
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-ink flex items-center"
                  >
                    <LogOutIcon className="w-4 h-4 mr-2" />
                    Logout
                  </button>
                </div>
              </div>
            ) : (
              <div className="hidden md:flex items-center space-x-2">
                <Link
                  to="/login"
                  className="btn-outline flex items-center text-sm"
                >
                  <LogInIcon className="w-4 h-4 mr-1" />
                  Login
                </Link>
                <Link
                  to="/register"
                  className="btn-primary flex items-center text-sm"
                >
                  <UserPlusIcon className="w-4 h-4 mr-1" />
                  Register
                </Link>
              </div>
            )}

            <button
              className="md:hidden p-2 text-primary-700 hover:text-ink"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <XIcon className="w-6 h-6" /> : <MenuIcon className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-newsprint border-t border-primary-200">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex flex-col space-y-3">
              <Link
                to="/"
                className={`py-2 px-4 rounded-md font-medium ${isActive('/') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                onClick={() => setIsMenuOpen(false)}
              >
                Beranda
              </Link>
              <Link
                to="/teka-teki-silang/semua"
                className={`py-2 px-4 rounded-md font-medium ${isActive('/play') || location.pathname.startsWith('/teka-teki-silang/') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                onClick={() => setIsMenuOpen(false)}
              >
                Main
              </Link>
              <Link
                to="/create"
                className={`py-2 px-4 rounded-md font-medium ${isActive('/create') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                onClick={() => setIsMenuOpen(false)}
              >
                Buat
              </Link>
              <a
                href="/blog"
                className="py-2 px-4 rounded-md font-medium text-primary-700 hover:bg-primary-50 hover:text-ink"
                target="_self"
                onClick={() => setIsMenuOpen(false)}
              >
                Blog
              </a>

              {/* Help Pages */}
              <div className="py-2 px-4">
                <p className="font-medium text-ink mb-2">Bantuan</p>
                <div className="pl-2 flex flex-col space-y-2">
                  <Link
                    to="/cara-bermain"
                    className={`py-1 px-2 rounded-md text-sm ${location.pathname === '/cara-bermain' ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Cara Bermain
                  </Link>
                  <Link
                    to="/tips-strategi-tts"
                    className={`py-1 px-2 rounded-md text-sm ${location.pathname === '/tips-strategi-tts' ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Tips & Strategi
                  </Link>
                  <Link
                    to="/kamus-istilah-tts"
                    className={`py-1 px-2 rounded-md text-sm ${location.pathname === '/kamus-istilah-tts' ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Kamus Istilah
                  </Link>
                  <Link
                    to="/manfaat-tts"
                    className={`py-1 px-2 rounded-md text-sm ${location.pathname === '/manfaat-tts' ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Manfaat TTS
                  </Link>
                  <Link
                    to="/bantuan/faq"
                    className={`py-1 px-2 rounded-md text-sm ${location.pathname === '/bantuan/faq' ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    FAQ
                  </Link>
                </div>
              </div>

              <div className="border-t border-primary-200 my-2"></div>
              {auth.isAuthenticated && auth.user ? (
                <>
                  <span className="px-4 py-2 text-sm text-primary-600">
                    Hi, {auth.user.displayName || auth.user.username}
                  </span>
                  {auth.user?.role === 'admin' && (
                    <Link
                      to="/admin"
                      className={`block py-2 px-4 rounded-md font-medium ${isActive('/admin') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Admin Panel
                    </Link>
                  )}
                  <Link
                    to="/profile"
                    className={`block py-2 px-4 rounded-md font-medium ${isActive('/profile') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <button
                    onClick={async () => {
                      await auth.logout();
                      setIsMenuOpen(false);
                      navigate('/');
                    }}
                    className="w-full text-left py-2 px-4 rounded-md font-medium text-primary-800 hover:bg-primary-100 hover:text-ink flex items-center"
                  >
                    <LogOutIcon className="w-4 h-4 mr-2" />
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className={`py-2 px-4 rounded-md font-medium ${isActive('/login') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className={`py-2 px-4 rounded-md font-medium ${isActive('/register') ? 'bg-primary-100 text-ink' : 'text-primary-700 hover:bg-primary-50 hover:text-ink'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Register
                  </Link>
                </>
              )}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
