import React from 'react';
import { LightBulbIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

interface HintSuggestion {
  type: 'letter' | 'word' | 'intersection';
  wordNumber: number;
  direction: 'across' | 'down';
  position?: { row: number; col: number };
  reason: string;
  priority: number;
}

interface HintSuggestionsProps {
  suggestions: HintSuggestion[];
  onApplyHint: (suggestion: HintSuggestion) => void;
  hintsRemaining: number;
  isVisible: boolean;
  onClose: () => void;
}

const HintSuggestions: React.FC<HintSuggestionsProps> = ({
  suggestions,
  onApplyHint,
  hintsRemaining,
  isVisible,
  onClose
}) => {
  if (!isVisible || suggestions.length === 0) return null;

  // Sort suggestions by priority (highest first)
  const sortedSuggestions = [...suggestions].sort((a, b) => b.priority - a.priority);
  
  // Show top 3 suggestions
  const topSuggestions = sortedSuggestions.slice(0, 3);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'intersection':
        return '⚡';
      case 'letter':
        return '💡';
      case 'word':
        return '🔍';
      default:
        return '💡';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'intersection':
        return 'Persilangan';
      case 'letter':
        return 'Huruf';
      case 'word':
        return 'Kata';
      default:
        return 'Bantuan';
    }
  };

  return (
    <div className="fixed inset-0 bg-ink-900 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-newsprint border-4 border-ink-900 rounded-sm shadow-paper-xl max-w-md w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="bg-ink-900 text-newsprint p-4 border-b-4 border-ink-900">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <LightBulbIcon className="w-6 h-6 mr-2" />
              <h3 className="text-lg font-bold font-serif">Saran Bantuan</h3>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm font-mono bg-newsprint text-ink-900 px-2 py-1 rounded-sm">
                {hintsRemaining} tersisa
              </span>
              <button
                onClick={onClose}
                className="text-newsprint hover:text-ink-200 transition-colors"
                aria-label="Tutup saran bantuan"
              >
                ✕
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 bg-paper-texture max-h-96 overflow-y-auto">
          <p className="text-ink-700 text-sm font-serif mb-4">
            Berikut adalah saran bantuan terbaik berdasarkan kemajuan Anda saat ini:
          </p>

          <div className="space-y-3">
            {topSuggestions.map((suggestion, index) => (
              <div
                key={`${suggestion.wordNumber}-${suggestion.direction}-${index}`}
                className="bg-newsprint border-2 border-ink-900 rounded-sm p-3 hover:shadow-paper transition-all duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="text-lg mr-2">{getTypeIcon(suggestion.type)}</span>
                      <span className="text-xs font-mono bg-ink-900 text-newsprint px-2 py-1 rounded-sm mr-2">
                        {getTypeLabel(suggestion.type)}
                      </span>
                      <span className="text-xs font-mono bg-ink-100 text-ink-900 px-2 py-1 rounded-sm">
                        {suggestion.wordNumber} {suggestion.direction === 'across' ? 'Mendatar' : 'Menurun'}
                      </span>
                    </div>
                    <p className="text-ink-800 text-sm font-serif leading-relaxed">
                      {suggestion.reason}
                    </p>
                    {suggestion.position && (
                      <p className="text-ink-600 text-xs font-mono mt-1">
                        Posisi: Baris {suggestion.position.row + 1}, Kolom {suggestion.position.col + 1}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => onApplyHint(suggestion)}
                    disabled={hintsRemaining <= 0}
                    className={`ml-3 px-3 py-2 rounded-sm font-serif text-sm font-semibold transition-all duration-200 flex items-center ${
                      hintsRemaining > 0
                        ? 'bg-ink-900 text-newsprint hover:bg-ink-800 shadow-paper hover:shadow-paper-lg'
                        : 'bg-ink-300 text-ink-500 cursor-not-allowed'
                    }`}
                    title={hintsRemaining > 0 ? 'Gunakan bantuan ini' : 'Tidak ada bantuan tersisa'}
                  >
                    Gunakan
                    <ArrowRightIcon className="w-4 h-4 ml-1" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {hintsRemaining <= 0 && (
            <div className="mt-4 bg-ink-100 border-l-4 border-ink-900 p-3 rounded-sm">
              <p className="text-ink-800 text-sm font-serif">
                ⚠️ <strong>Bantuan habis!</strong> Anda telah menggunakan semua bantuan yang tersedia.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-ink-100 p-3 border-t-2 border-ink-900">
          <p className="text-ink-700 text-xs font-serif text-center">
            💡 Tip: Bantuan dengan prioritas tinggi akan membantu Anda menyelesaikan teka-teki lebih cepat
          </p>
        </div>
      </div>
    </div>
  );
};

export default HintSuggestions;
