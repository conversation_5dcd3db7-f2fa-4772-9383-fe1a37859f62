import React from 'react';
import { KeyboardIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface KeyboardShortcut {
  keys: string[];
  description: string;
  category: 'navigation' | 'game' | 'accessibility';
}

interface KeyboardShortcutsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const KeyboardShortcutsModal: React.FC<KeyboardShortcutsModalProps> = ({
  isOpen,
  onClose
}) => {
  if (!isOpen) return null;

  const shortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    { keys: ['↑', '↓', '←', '→'], description: 'Navigasi sel dengan panah', category: 'navigation' },
    { keys: ['Tab'], description: 'Pindah ke kata berikutnya', category: 'navigation' },
    { keys: ['Shift', 'Tab'], description: 'Pindah ke kata sebelumnya', category: 'navigation' },
    { keys: ['Space'], description: 'Ubah arah (mendatar/menurun)', category: 'navigation' },
    { keys: ['Enter'], description: '<PERSON>n<PERSON><PERSON><PERSON> pilihan', category: 'navigation' },
    
    // Game shortcuts
    { keys: ['Ctrl', 'F'], description: 'Toggle mode fokus', category: 'game' },
    { keys: ['Ctrl', 'H'], description: 'Tampilkan bantuan', category: 'game' },
    { keys: ['Ctrl', 'P'], description: 'Jeda/Lanjutkan permainan', category: 'game' },
    { keys: ['Ctrl', 'R'], description: 'Reset jawaban', category: 'game' },
    { keys: ['Ctrl', 'Enter'], description: 'Periksa jawaban', category: 'game' },
    { keys: ['Backspace'], description: 'Hapus huruf', category: 'game' },
    { keys: ['Delete'], description: 'Hapus huruf', category: 'game' },
    
    // Accessibility shortcuts
    { keys: ['Ctrl', 'Shift', 'C'], description: 'Toggle kontras tinggi', category: 'accessibility' },
    { keys: ['F1'], description: 'Bantuan keyboard (modal ini)', category: 'accessibility' },
    { keys: ['Escape'], description: 'Tutup modal/keluar mode', category: 'accessibility' },
  ];

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'navigation':
        return 'Navigasi';
      case 'game':
        return 'Permainan';
      case 'accessibility':
        return 'Aksesibilitas';
      default:
        return 'Lainnya';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'navigation':
        return '🧭';
      case 'game':
        return '🎮';
      case 'accessibility':
        return '♿';
      default:
        return '⌨️';
    }
  };

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  const renderKey = (key: string) => (
    <kbd
      key={key}
      className="inline-block bg-ink-900 text-newsprint px-2 py-1 rounded-sm text-xs font-mono font-semibold shadow-paper border border-ink-700"
    >
      {key}
    </kbd>
  );

  return (
    <div className="fixed inset-0 bg-ink-900 bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-newsprint border-4 border-ink-900 rounded-sm shadow-paper-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-ink-900 text-newsprint p-4 border-b-4 border-ink-900">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <KeyboardIcon className="w-6 h-6 mr-2" />
              <h2 className="text-xl font-bold font-serif">Pintasan Keyboard</h2>
            </div>
            <button
              onClick={onClose}
              className="text-newsprint hover:text-ink-200 transition-colors p-1"
              aria-label="Tutup bantuan keyboard"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 bg-paper-texture max-h-[calc(90vh-120px)] overflow-y-auto">
          <p className="text-ink-700 text-sm font-serif mb-6 leading-relaxed">
            Gunakan pintasan keyboard berikut untuk navigasi yang lebih cepat dan efisien saat bermain teka-teki silang:
          </p>

          <div className="space-y-6">
            {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
              <div key={category} className="bg-newsprint border-2 border-ink-900 rounded-sm p-4">
                <h3 className="text-lg font-bold font-serif text-ink-900 mb-4 flex items-center">
                  <span className="text-xl mr-2">{getCategoryIcon(category)}</span>
                  {getCategoryTitle(category)}
                </h3>
                
                <div className="space-y-3">
                  {categoryShortcuts.map((shortcut, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2 border-b border-ink-200 last:border-b-0"
                    >
                      <span className="text-ink-800 font-serif text-sm flex-1">
                        {shortcut.description}
                      </span>
                      <div className="flex items-center gap-1 ml-4">
                        {shortcut.keys.map((key, keyIndex) => (
                          <React.Fragment key={keyIndex}>
                            {keyIndex > 0 && (
                              <span className="text-ink-600 text-xs mx-1">+</span>
                            )}
                            {renderKey(key)}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Tips section */}
          <div className="mt-6 bg-ink-100 border-l-4 border-ink-900 p-4 rounded-sm">
            <h4 className="text-ink-900 font-serif font-semibold mb-2 flex items-center">
              💡 Tips Penggunaan
            </h4>
            <ul className="text-ink-700 text-sm font-serif space-y-1 list-disc list-inside">
              <li>Gunakan panah untuk navigasi cepat antar sel</li>
              <li>Tab untuk berpindah antar kata secara berurutan</li>
              <li>Mode fokus menyembunyikan elemen yang mengganggu</li>
              <li>Bantuan akan memberikan saran berdasarkan kemajuan Anda</li>
              <li>Kontras tinggi membantu visibilitas yang lebih baik</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-ink-100 p-4 border-t-2 border-ink-900">
          <div className="flex items-center justify-between">
            <p className="text-ink-700 text-xs font-serif">
              Tekan <kbd className="bg-ink-900 text-newsprint px-1 py-0.5 rounded text-xs">F1</kbd> kapan saja untuk membuka bantuan ini
            </p>
            <button
              onClick={onClose}
              className="bg-ink-900 text-newsprint px-4 py-2 rounded-sm font-serif text-sm font-semibold hover:bg-ink-800 transition-all duration-200 shadow-paper hover:shadow-paper-lg"
            >
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcutsModal;
