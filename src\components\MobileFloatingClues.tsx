import React, { useState, useRef, useEffect } from 'react';
import { InfoIcon, ChevronUpIcon, ChevronDownIcon } from 'lucide-react';
import CrosswordClues from './CrosswordClues';
import { useCrossword } from '../context/CrosswordContext';

interface MobileFloatingCluesProps {
  isExpanded: boolean;
  onToggle: () => void;
}

const MobileFloatingClues: React.FC<MobileFloatingCluesProps> = ({
  isExpanded,
  onToggle,
}) => {
  const { state } = useCrossword();
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentHeight, setCurrentHeight] = useState(isExpanded ? 60 : 16);
  const panelRef = useRef<HTMLDivElement>(null);

  // Handle touch start for drag gesture
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
  };

  // Handle touch move for drag gesture
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const currentY = e.touches[0].clientY;
    const deltaY = startY - currentY;
    const newHeight = Math.max(16, Math.min(80, currentHeight + (deltaY / window.innerHeight) * 100));
    
    if (panelRef.current) {
      panelRef.current.style.height = `${newHeight}vh`;
    }
  };

  // Handle touch end for drag gesture
  const handleTouchEnd = () => {
    if (!isDragging) return;
    setIsDragging(false);
    
    if (panelRef.current) {
      const height = parseFloat(panelRef.current.style.height);
      if (height > 30) {
        setCurrentHeight(60);
        if (!isExpanded) onToggle();
      } else {
        setCurrentHeight(16);
        if (isExpanded) onToggle();
      }
    }
  };

  // Update height when expanded state changes
  useEffect(() => {
    setCurrentHeight(isExpanded ? 60 : 16);
  }, [isExpanded]);

  // Get current clue information
  const getCurrentClue = () => {
    if (!state.selectedWordId || !state.wordPositions) return null;
    
    const selectedPosition = state.wordPositions[state.selectedWordId - 1];
    if (!selectedPosition) return null;

    const clue = selectedPosition.direction === 'across'
      ? state.clues.across[selectedPosition.number]
      : state.clues.down[selectedPosition.number];

    return {
      number: selectedPosition.number,
      direction: selectedPosition.direction,
      clue: clue || 'Tidak ada petunjuk'
    };
  };

  const currentClue = getCurrentClue();

  return (
    <div 
      ref={panelRef}
      className={`fixed bottom-0 left-0 right-0 z-40 bg-newsprint border-t-4 border-ink-900 shadow-paper-xl transition-all duration-300 ${
        isExpanded ? 'h-[60vh]' : 'h-16'
      }`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Drag Handle */}
      <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-ink-400 rounded-full"></div>
      
      {/* Panel Header */}
      <div className="flex items-center justify-between p-4 border-b-2 border-ink-900 bg-paper-lines">
        <div className="flex items-center gap-3">
          <InfoIcon className="w-5 h-5 text-ink-900" />
          <h3 className="font-serif font-bold text-ink-900 text-lg">Petunjuk</h3>
          {state.selectedWordId && (
            <span className="bg-ink-900 text-newsprint px-2 py-1 rounded-sm text-xs font-mono">
              #{state.selectedWordId}
            </span>
          )}
        </div>
        <button
          onClick={onToggle}
          className="p-2 bg-ink-900 text-newsprint rounded-sm hover:bg-ink-800 transition-all duration-200 touch-manipulation"
          aria-label={isExpanded ? "Tutup petunjuk" : "Buka petunjuk"}
          style={{ minHeight: '44px', minWidth: '44px' }} // Better touch target
        >
          {isExpanded ? <ChevronDownIcon className="w-5 h-5" /> : <ChevronUpIcon className="w-5 h-5" />}
        </button>
      </div>

      {/* Panel Content */}
      {isExpanded && (
        <div className="p-4 overflow-auto h-full bg-paper-texture">
          <CrosswordClues />
        </div>
      )}

      {/* Compact Current Clue Display (when collapsed) */}
      {!isExpanded && currentClue && (
        <div className="px-4 py-2 bg-ink-100">
          <div className="flex items-start gap-2">
            <span className="font-bold font-mono text-ink-900 text-sm flex-shrink-0">
              {currentClue.number}. {currentClue.direction === 'across' ? 'MENDATAR' : 'MENURUN'}
            </span>
            <span className="font-serif text-ink-800 text-sm leading-relaxed truncate">
              {currentClue.clue}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileFloatingClues;
