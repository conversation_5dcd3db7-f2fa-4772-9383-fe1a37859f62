import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeftIcon } from 'lucide-react';

interface NotFoundProps {
  title?: string;
  message?: string;
  backLink?: string;
  backText?: string;
  secondaryLink?: string;
  secondaryText?: string;
}

/**
 * Standardized 404 Not Found component
 */
const NotFound: React.FC<NotFoundProps> = ({
  title = '404 - Halaman Tidak Ditemukan',
  message = 'Maaf, halaman yang Anda cari tidak ditemukan. Silakan kembali ke beranda atau coba tautan lain.',
  backLink = '/',
  backText = 'Kembali ke Beranda',
  secondaryLink = '/play',
  secondaryText = 'Jela<PERSON><PERSON>-<PERSON>'
}) => {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-md mx-auto text-center">
        <h1 className="text-3xl font-bold text-blue-600 mb-4">{title}</h1>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to={backLink}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            {backText}
          </Link>
          {secondaryLink && (
            <Link
              to={secondaryLink}
              className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              {secondaryText}
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotFound;
