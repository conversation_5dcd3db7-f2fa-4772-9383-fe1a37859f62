import React, { useState, useEffect } from 'react';
import { isOnline, addConnectionStatusListeners, removeConnectionStatusListeners } from '../utils/serviceWorkerRegistration';
import { WifiOffIcon, XIcon } from 'lucide-react';

/**
 * Komponen banner untuk menampilkan pesan offline
 * Ditampilkan di bagian atas halaman ketika pengguna offline
 */
const OfflineBanner: React.FC = () => {
  const [offline, setOffline] = useState(!isOnline());
  const [dismissed, setDismissed] = useState(false);

  // Handler untuk perubahan status koneksi
  const handleOnline = () => {
    setOffline(false);
    setDismissed(false); // Reset dismissed state when going online
  };

  const handleOffline = () => {
    setOffline(true);
  };

  // Dismiss banner
  const handleDismiss = () => {
    setDismissed(true);
  };

  // Tambahkan listener untuk perubahan status koneksi
  useEffect(() => {
    addConnectionStatusListeners(handleOnline, handleOffline);
    
    return () => {
      removeConnectionStatusListeners(handleOnline, handleOffline);
    };
  }, []);

  // Jika online atau banner telah di-dismiss, jangan tampilkan
  if (!offline || dismissed) {
    return null;
  }

  return (
    <div className="bg-yellow-100 border-b border-yellow-200 px-4 py-3 animate-slideIn">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <WifiOffIcon className="w-5 h-5 text-yellow-600 mr-2" />
          <div>
            <p className="font-medium text-yellow-800">
              Anda sedang offline
            </p>
            <p className="text-sm text-yellow-700">
              Anda masih dapat bermain teka-teki silang yang telah di-cache sebelumnya.
            </p>
          </div>
        </div>
        <button 
          onClick={handleDismiss}
          className="text-yellow-600 hover:text-yellow-800"
          aria-label="Tutup pesan"
        >
          <XIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

export default OfflineBanner;
