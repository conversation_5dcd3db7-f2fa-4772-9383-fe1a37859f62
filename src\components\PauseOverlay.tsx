import React from 'react';
import { PlayIcon } from 'lucide-react';
import GameTimer from './GameTimer';

interface PauseOverlayProps {
  timeSpent: number;
  onResumeGame: () => void;
}

const PauseOverlay: React.FC<PauseOverlayProps> = ({
  timeSpent,
  onResumeGame,
}) => {
  return (
    <div className="fixed inset-0 bg-ink-900 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-newsprint p-8 rounded-sm border-4 border-ink-900 shadow-paper-xl text-center max-w-md">
        <h2 className="text-2xl font-bold text-ink-900 font-serif mb-4 uppercase">Permainan Dijeda</h2>
        <div className="mb-6">
          <div className="flex items-center justify-center mb-4">
            <GameTimer
              timeSpent={timeSpent}
              gameState="paused"
              onTogglePauseResume={() => {}}
              compact
            />
          </div>
          <p className="text-ink-700 font-serif">Klik tombol di bawah untuk melanjutkan permainan.</p>
        </div>
        <button
          onClick={onResumeGame}
          className="bg-ink-900 text-newsprint px-6 py-3 rounded-sm font-serif font-bold hover:bg-ink-800 transition-all duration-200 shadow-paper border-2 border-ink-900 hover:shadow-paper-lg flex items-center gap-2 mx-auto"
        >
          <PlayIcon className="w-5 h-5" />
          Lanjutkan
        </button>
      </div>
    </div>
  );
};

export default PauseOverlay;
