import React, { useState } from 'react';
import { useCrossword } from '../context/CrosswordContext';
import { CheckIcon, RefreshCwIcon, HelpCircleIcon, FlagIcon } from 'lucide-react';

interface PlayControlsProps {
  onCheckAnswers: () => void;
  onResetAnswers: () => void;
}

const PlayControls: React.FC<PlayControlsProps> = ({
  onCheckAnswers,
  onResetAnswers
}) => {
  const { state, dispatch } = useCrossword();
  const [showHintConfirm, setShowHintConfirm] = useState(false);
  const [showSurrenderConfirm, setShowSurrenderConfirm] = useState(false);
  const [showAnswerResults, setShowAnswerResults] = useState(false);
  const [answerResults, setAnswerResults] = useState<{
    correct: number;
    incorrect: number;
    empty: number;
    total: number;
  }>({ correct: 0, incorrect: 0, empty: 0, total: 0 });

  // Function to use a hint on the currently selected word - improved with smart hint system
  const useHint = () => {
    if (!state.selectedWordId || state.hintsRemaining === 0) {
      return;
    }

    // Get the selected word position
    const wordPosition = state.wordPositions[state.selectedWordId - 1];
    if (!wordPosition) return;

    const { direction, row, col } = wordPosition;
    const word = state.words[state.selectedWordId - 1];

    if (!word) return;

    // Find empty or incorrect cells in the selected word
    // Now with priority scoring for smarter hints
    const candidateCells: Array<{
      row: number;
      col: number;
      priority: number;
    }> = [];

    // Common letters in Indonesian (higher priority = less likely to be revealed)
    const letterFrequency: Record<string, number> = {
      'A': 1, 'I': 1, 'E': 1, 'N': 2, 'T': 2, 'R': 2, 'O': 2,
      'S': 3, 'L': 3, 'U': 3, 'K': 3, 'D': 3, 'M': 3,
      'P': 4, 'B': 4, 'G': 4, 'H': 4, 'Y': 5, 'J': 5,
      'C': 5, 'W': 6, 'F': 6, 'V': 7, 'Z': 8, 'X': 9, 'Q': 10
    };

    for (let i = 0; i < word.length; i++) {
      const r = direction === 'across' ? row : row + i;
      const c = direction === 'across' ? col + i : col;
      const correctChar = state.grid[r][c].char;

      // Skip cells that are already revealed or correctly filled
      if (
        !state.revealedCells?.some(([rr, cc]) => rr === r && cc === c) &&
        state.userAnswers?.[r]?.[c] !== correctChar
      ) {
        // Calculate priority based on several factors
        let priority = 0;

        // 1. Letter frequency (rarer letters get higher priority)
        priority += letterFrequency[correctChar] || 5;

        // 2. Intersection points (cells that are part of multiple words get higher priority)
        priority += state.grid[r][c].wordIds.length > 1 ? 3 : 0;

        // 3. Position in word (middle letters often harder to guess)
        const positionFactor = Math.abs(i - word.length / 2) / (word.length / 2);
        priority += Math.round(3 * (1 - positionFactor)); // Higher priority for middle letters

        // 4. If user has attempted this cell but got it wrong, give it higher priority
        if (state.userAnswers?.[r]?.[c] && state.userAnswers[r][c] !== correctChar) {
          priority += 2;
        }

        candidateCells.push({ row: r, col: c, priority });
      }
    }

    // If there are candidate cells, reveal one with weighted probability based on priority
    if (candidateCells.length > 0) {
      // Sort by priority (highest first)
      candidateCells.sort((a, b) => b.priority - a.priority);

      // Take the top 3 highest priority cells (or all if less than 3)
      const topCandidates = candidateCells.slice(0, Math.min(3, candidateCells.length));

      // Select one of the top candidates
      const selectedCandidate = topCandidates[Math.floor(Math.random() * topCandidates.length)];

      dispatch({ type: 'USE_HINT', row: selectedCandidate.row, col: selectedCandidate.col });
      setShowHintConfirm(false);

      // Show a toast or notification about the hint
      // This would require adding a toast component, but we'll leave that for future implementation
    } else {
      // All cells are either revealed or correct already
      setShowHintConfirm(false);
      // Could show a message that no hints are needed for this word
    }
  };

  // Function to check answers and show results
  const handleCheckAnswers = () => {
    if (!state.userAnswers || !state.grid) return;

    let correct = 0;
    let incorrect = 0;
    let empty = 0;
    let total = 0;

    for (let r = 0; r < state.gridSize; r++) {
      for (let c = 0; c < state.gridSize; c++) {
        if (state.grid[r][c].char !== ' ') {
          total++;
          if (state.userAnswers[r][c] === '') {
            empty++;
          } else if (state.userAnswers[r][c] === state.grid[r][c].char) {
            correct++;
          } else {
            incorrect++;
          }
        }
      }
    }

    setAnswerResults({ correct, incorrect, empty, total });
    setShowAnswerResults(true);
    dispatch({ type: 'CHECK_ANSWERS', showResults: true });
    onCheckAnswers();
  };

  // Function to surrender and show all correct answers
  const handleSurrender = () => {
    dispatch({ type: 'SURRENDER' });
    setShowSurrenderConfirm(false);

    // Show results after surrendering
    const total = state.grid.reduce((count, row) =>
      count + row.filter(cell => cell.char !== ' ').length, 0);

    setAnswerResults({
      correct: total,
      incorrect: 0,
      empty: 0,
      total
    });
    setShowAnswerResults(true);
  };

  return (
    <div className="space-y-4">
      {/* Progress Bar - Enhanced with animation and better visual feedback */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">Progress</h3>
          <div className="flex items-center">
            <span className={`text-lg font-bold ${
              state.progress === 100 ? 'text-green-600' :
              state.progress > 75 ? 'text-blue-600' :
              state.progress > 50 ? 'text-yellow-600' :
              state.progress > 25 ? 'text-orange-600' : 'text-gray-600'
            }`}>{state.progress}%</span>
            {state.progress === 100 && (
              <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Selesai!
              </span>
            )}
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
          <div
            className={`h-3 rounded-full transition-all duration-500 ease-out ${
              state.progress === 100 ? 'bg-green-500' :
              state.progress > 75 ? 'bg-blue-600' :
              state.progress > 50 ? 'bg-yellow-500' :
              state.progress > 25 ? 'bg-orange-500' : 'bg-gray-500'
            }`}
            style={{ width: `${state.progress}%` }}
          ></div>
        </div>
      </div>

      {/* Controls - Enhanced with better visual hierarchy and tooltips */}
      <div className="flex flex-wrap gap-3 justify-center">
        <button
          onClick={handleCheckAnswers}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
          title="Periksa jawaban Anda"
        >
          <CheckIcon className="w-4 h-4 mr-2" />
          Periksa Jawaban
        </button>

        <button
          onClick={onResetAnswers}
          className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          title="Reset semua jawaban"
        >
          <RefreshCwIcon className="w-4 h-4 mr-2" />
          Reset Jawaban
        </button>

        <button
          onClick={() => setShowHintConfirm(true)}
          disabled={state.hintsRemaining === 0 || !state.selectedWordId}
          className={`flex items-center px-4 py-2 rounded-md transition-colors shadow-sm hover:shadow focus:outline-none ${
            state.hintsRemaining === 0 || !state.selectedWordId
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50'
          }`}
          title={state.hintsRemaining === 0
            ? "Tidak ada bantuan tersisa"
            : !state.selectedWordId
              ? "Pilih kata terlebih dahulu"
              : "Gunakan bantuan untuk kata yang dipilih"}
        >
          <HelpCircleIcon className="w-4 h-4 mr-2" />
          Bantuan ({state.hintsRemaining})
        </button>

        <button
          onClick={() => setShowSurrenderConfirm(true)}
          disabled={state.isSurrendered}
          className={`flex items-center px-4 py-2 rounded-md transition-colors shadow-sm hover:shadow focus:outline-none ${
            state.isSurrendered
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50'
          }`}
          title={state.isSurrendered ? "Anda telah menyerah" : "Tampilkan semua jawaban dan akhiri permainan"}
        >
          <FlagIcon className="w-4 h-4 mr-2" />
          Menyerah
        </button>
      </div>

      {/* Hint Confirmation Modal - Enhanced with better UI and more information */}
      {showHintConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full animate-slideIn">
            <div className="flex items-center mb-4">
              <div className="bg-yellow-100 p-2 rounded-full mr-3">
                <HelpCircleIcon className="w-6 h-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold">Gunakan Bantuan?</h3>
            </div>

            <div className="mb-6">
              <p className="mb-3">
                Anda akan menggunakan 1 bantuan untuk mengungkapkan satu huruf pada kata yang dipilih.
              </p>

              <div className="bg-blue-50 border-l-4 border-blue-500 p-3 mb-3">
                <p className="text-sm text-blue-700">
                  <strong>Kata yang dipilih:</strong> {state.selectedWordId && state.wordPositions[state.selectedWordId - 1] ?
                    `#${state.wordPositions[state.selectedWordId - 1].number} ${state.wordPositions[state.selectedWordId - 1].direction === 'across' ? 'Mendatar' : 'Menurun'}` :
                    'Tidak ada kata yang dipilih'}
                </p>
              </div>

              <div className="flex items-center justify-between bg-yellow-50 p-3 rounded-md">
                <span className="font-medium">Bantuan tersisa:</span>
                <span className="font-bold text-yellow-700">{state.hintsRemaining} / {state.hintsRemaining + (state.hintsUsed || 0)}</span>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowHintConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                Batal
              </button>
              <button
                onClick={useHint}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                Gunakan Bantuan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Surrender Confirmation Modal - Enhanced with better UI */}
      {showSurrenderConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full animate-slideIn">
            <div className="flex items-center mb-4">
              <div className="bg-purple-100 p-2 rounded-full mr-3">
                <FlagIcon className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold">Menyerah?</h3>
            </div>

            <div className="mb-6">
              <p className="mb-3">
                Apakah Anda yakin ingin menyerah? Semua jawaban yang benar akan ditampilkan dan permainan akan berakhir.
              </p>

              <div className="bg-red-50 border-l-4 border-red-500 p-3">
                <p className="text-sm text-red-700">
                  <strong>Perhatian:</strong> Tindakan ini tidak dapat dibatalkan dan akan memengaruhi statistik permainan Anda.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSurrenderConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                Batal
              </button>
              <button
                onClick={handleSurrender}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                Ya, Saya Menyerah
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Answer Results Modal - Enhanced with better UI and animations */}
      {showAnswerResults && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full animate-slideIn">
            <div className="flex items-center mb-4">
              <div className={`p-2 rounded-full mr-3 ${
                answerResults.correct === answerResults.total
                  ? 'bg-green-100'
                  : answerResults.correct > answerResults.total / 2
                    ? 'bg-yellow-100'
                    : 'bg-red-100'
              }`}>
                <CheckIcon className={`w-6 h-6 ${
                  answerResults.correct === answerResults.total
                    ? 'text-green-600'
                    : answerResults.correct > answerResults.total / 2
                      ? 'text-yellow-600'
                      : 'text-red-600'
                }`} />
              </div>
              <h3 className="text-xl font-bold">Hasil Pemeriksaan</h3>
            </div>

            <div className="space-y-3 mb-6">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-green-50 p-3 rounded-md">
                  <div className="text-sm text-gray-600 mb-1">Benar</div>
                  <div className="text-xl font-bold text-green-600 flex items-center">
                    {answerResults.correct}
                    <span className="text-sm text-gray-500 ml-1">/ {answerResults.total}</span>
                    <span className="ml-auto text-sm">
                      {Math.round((answerResults.correct / answerResults.total) * 100)}%
                    </span>
                  </div>
                </div>

                <div className="bg-red-50 p-3 rounded-md">
                  <div className="text-sm text-gray-600 mb-1">Salah</div>
                  <div className="text-xl font-bold text-red-600 flex items-center">
                    {answerResults.incorrect}
                    <span className="text-sm text-gray-500 ml-1">/ {answerResults.total}</span>
                    <span className="ml-auto text-sm">
                      {Math.round((answerResults.incorrect / answerResults.total) * 100)}%
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <div className="text-sm text-gray-600 mb-1">Kosong</div>
                <div className="text-xl font-bold text-gray-600 flex items-center">
                  {answerResults.empty}
                  <span className="text-sm text-gray-500 ml-1">/ {answerResults.total}</span>
                  <span className="ml-auto text-sm">
                    {Math.round((answerResults.empty / answerResults.total) * 100)}%
                  </span>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Progress Total</span>
                  <span className="font-bold">{state.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full transition-all duration-500 ease-out ${
                      state.progress === 100 ? 'bg-green-500' :
                      state.progress > 75 ? 'bg-blue-600' :
                      state.progress > 50 ? 'bg-yellow-500' :
                      state.progress > 25 ? 'bg-orange-500' : 'bg-gray-500'
                    }`}
                    style={{ width: `${state.progress}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {answerResults.correct === answerResults.total ? (
              <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4">
                <p className="font-bold">Selamat! 🎉</p>
                <p>Anda telah menyelesaikan teka-teki silang dengan benar.</p>
              </div>
            ) : (
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                <p className="font-bold">Masih ada yang salah atau kosong.</p>
                <p>Teruslah mencoba! Jawaban yang salah ditandai dengan warna merah.</p>
              </div>
            )}

            <div className="flex justify-end">
              <button
                onClick={() => setShowAnswerResults(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlayControls;
