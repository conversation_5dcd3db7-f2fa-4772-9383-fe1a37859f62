import React from 'react';
import { PlayIcon } from 'lucide-react';
import GameTimer from './GameTimer';

interface PreGameInterfaceProps {
  timeSpent: number;
  gridSize: number;
  hintsRemaining: number;
  puzzleDifficulty: string;
  progress: number;
  gameState?: 'not-started' | 'playing' | 'paused' | 'completed';
  onStartGame: () => void;
  onPlayAgain?: () => void;
}

const PreGameInterface: React.FC<PreGameInterfaceProps> = ({
  timeSpent,
  gridSize,
  hintsRemaining,
  puzzleDifficulty,
  progress,
  gameState,
  onStartGame,
  onPlayAgain,
}) => {
  // Check if this is a completed puzzle based on game state and progress
  const isCompletedPuzzle = gameState === 'completed' || progress === 100;
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-newsprint border-4 border-ink-900 p-8 shadow-paper-xl bg-paper-texture rounded-sm">
        {/* Pre-game Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-ink-900 font-serif mb-4 uppercase tracking-wide">
            {isCompletedPuzzle ? 'Teka-Teki Selesai!' : 'Siap Memulai?'}
          </h2>
          <p className="text-ink-700 font-serif text-lg leading-relaxed">
            {isCompletedPuzzle
              ? 'Anda telah menyelesaikan teka-teki ini. Ingin mencoba lagi dari awal atau melanjutkan permainan?'
              : 'Klik tombol "Mulai" untuk memulai permainan dan mengaktifkan timer.'
            }
          </p>
          {isCompletedPuzzle && (
            <div className="mt-4 bg-green-100 border-l-4 border-green-500 p-4 rounded-sm">
              <div className="flex items-center">
                <div className="text-2xl mr-3">🎉</div>
                <div>
                  <p className="text-green-800 font-serif font-semibold">
                    Selamat! Progress: {progress}%
                  </p>
                  <p className="text-green-700 text-sm font-serif">
                    Waktu penyelesaian: {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Timer Display */}
        <div className="flex items-center justify-center mb-8">
          <GameTimer
            timeSpent={timeSpent}
            gameState="not-started"
            onTogglePauseResume={() => {}}
          />
        </div>

        {/* Game Info Preview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-ink-100 p-4 rounded-sm border-2 border-ink-900 text-center">
            <div className="text-ink-900 font-serif font-bold text-lg">Ukuran Grid</div>
            <div className="text-ink-700 font-mono text-xl">{gridSize}×{gridSize}</div>
          </div>
          <div className="bg-ink-100 p-4 rounded-sm border-2 border-ink-900 text-center">
            <div className="text-ink-900 font-serif font-bold text-lg">Bantuan</div>
            <div className="text-ink-700 font-mono text-xl">{hintsRemaining}</div>
          </div>
          <div className="bg-ink-100 p-4 rounded-sm border-2 border-ink-900 text-center">
            <div className="text-ink-900 font-serif font-bold text-lg">Kesulitan</div>
            <div className="text-ink-700 font-serif text-xl capitalize">{puzzleDifficulty}</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <div className={`flex ${isCompletedPuzzle ? 'flex-col sm:flex-row gap-4 justify-center' : 'justify-center'}`}>
            {isCompletedPuzzle && onPlayAgain && (
              <button
                onClick={onPlayAgain}
                className="bg-green-600 text-newsprint px-8 py-4 rounded-sm font-serif font-bold text-xl hover:bg-green-700 transition-all duration-200 shadow-paper border-4 border-green-600 hover:shadow-paper-lg flex items-center gap-3 mx-auto"
              >
                🔄 Main Lagi
              </button>
            )}

            <button
              onClick={onStartGame}
              className={`${isCompletedPuzzle ? 'bg-ink-600 hover:bg-ink-700 border-ink-600' : 'bg-ink-900 hover:bg-ink-800 border-ink-900'} text-newsprint px-8 py-4 rounded-sm font-serif font-bold text-xl transition-all duration-200 shadow-paper border-4 hover:shadow-paper-lg flex items-center gap-3 mx-auto`}
            >
              <PlayIcon className="w-6 h-6" />
              {isCompletedPuzzle ? 'Lanjutkan' : 'Mulai'}
            </button>
          </div>

          <p className="text-ink-600 text-sm font-serif mt-3">
            {isCompletedPuzzle
              ? '"Main Lagi" akan menghapus semua progress dan memulai dari awal'
              : 'Mode fokus akan diaktifkan secara otomatis'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default PreGameInterface;
