import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface ProtectedRouteProps {
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedRoles }) => {
  const auth = useAuth();
  const location = useLocation();

  if (auth.isLoading) {
    // You might want to render a loading spinner here
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!auth.isAuthenticated) {
    // Redirect them to the /login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check for role authorization if allowedRoles are specified
  if (allowedRoles && auth.user?.role && !allowedRoles.includes(auth.user.role)) {
    // User is authenticated but does not have the required role
    // Redirect to home page or an "Unauthorized" page
    // For simplicity, redirecting to home.
    // You could also pass a message via location state if desired.
    return <Navigate to="/" replace />;
  }

  return <Outlet />; // Render child routes
};

export default ProtectedRoute;
