import React, { useState, useEffect } from 'react';
import { Share2, Twitter, Facebook, Copy, MessageCircle } from 'lucide-react';
import { API_URL } from '../config';

interface ShareButtonProps {
  title: string;
  url?: string;
  message?: string;
  showText?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  isCompleted?: boolean;
  score?: number;
  timeSpent?: number;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  title,
  url = '',
  message = '',
  showText = true,
  variant = 'outline',
  size = 'default',
  className = '',
  isCompleted = false,
  score,
  timeSpent,
}) => {
  const [copied, setCopied] = useState(false);

  // Format time spent in minutes and seconds
  const formatTime = (seconds: number) => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // State for the share URL
  const [shareUrl, setShareUrl] = useState<string>(url || window.location.href);
  const [isGeneratingUrl, setIsGeneratingUrl] = useState<boolean>(false);

  // Function to generate a short share URL
  const generateShareUrl = async () => {
    if (isGeneratingUrl) return;

    try {
      setIsGeneratingUrl(true);

      // Extract the crossword ID from the URL
      const currentUrl = url || window.location.href;
      const match = currentUrl.match(/\/play\/([^\/]+)/);

      if (match && match[1]) {
        const crosswordId = match[1];

        // Call the API to generate a short URL
        const response = await fetch(`${API_URL}/api/share/${crosswordId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5'
          },
        });

        const data = await response.json();

        if (data.status === 'success' && data.data?.share_url) {
          // Use the short URL for sharing
          const baseUrl = `${window.location.protocol}//${window.location.host}`;
          setShareUrl(`${baseUrl}/s/${data.data.short_code}`);
        }
      }
    } catch (error) {
      console.error('Error generating share URL:', error);
    } finally {
      setIsGeneratingUrl(false);
    }
  };

  // Generate share URL when component mounts
  useEffect(() => {
    generateShareUrl();
  }, []);

  // Generate the sharing message based on whether the puzzle is completed
  const generateMessage = () => {
    if (message) return message;

    if (isCompleted && score !== undefined && timeSpent !== undefined) {
      return `Saya baru saja menyelesaikan teka-teki silang "${title}" dengan skor ${score} dalam waktu ${formatTime(timeSpent)}! #TekaTeki #TTS`;
    } else {
      return `Coba selesaikan teka-teki silang "${title}" ini! #TekaTeki #TTS`;
    }
  };

  const shareMessage = generateMessage();

  // Share handlers
  const shareToTwitter = () => {
    window.open(
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessage)}&url=${encodeURIComponent(shareUrl)}`,
      '_blank'
    );
  };

  const shareToFacebook = () => {
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      '_blank'
    );
  };

  const shareToWhatsApp = () => {
    window.open(
      `https://wa.me/?text=${encodeURIComponent(`${shareMessage} ${shareUrl}`)}`,
      '_blank'
    );
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(`${shareMessage} ${shareUrl}`).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Button styles based on variant
  const getButtonStyles = () => {
    switch (variant) {
      case 'outline':
        return 'border border-gray-300 hover:bg-gray-100';
      case 'secondary':
        return 'bg-gray-200 hover:bg-gray-300';
      case 'ghost':
        return 'hover:bg-gray-100';
      case 'link':
        return 'text-blue-600 hover:underline';
      default:
        return 'bg-blue-600 text-white hover:bg-blue-700';
    }
  };

  // Button size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      case 'icon':
        return 'p-2';
      default:
        return 'px-4 py-2';
    }
  };

  return (
    <div className="relative inline-block">
      <button
        onClick={() => {
          const dropdown = document.getElementById('share-dropdown');
          if (dropdown) {
            dropdown.classList.toggle('hidden');
          }
        }}
        className={`${getButtonStyles()} ${getSizeStyles()} rounded-md flex items-center justify-center ${className}`}
      >
        <Share2 className="h-4 w-4 mr-2" />
        {showText && "Bagikan"}
      </button>

      <div id="share-dropdown" className="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
        <div className="py-1">
          <button onClick={shareToTwitter} className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
            <Twitter className="h-4 w-4 mr-2" />
            Twitter
          </button>
          <button onClick={shareToFacebook} className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
            <Facebook className="h-4 w-4 mr-2" />
            Facebook
          </button>
          <button onClick={shareToWhatsApp} className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
            <MessageCircle className="h-4 w-4 mr-2" />
            WhatsApp
          </button>
          <button onClick={copyToClipboard} className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
            <Copy className="h-4 w-4 mr-2" />
            {copied ? "Tersalin!" : "Salin Tautan"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShareButton;
