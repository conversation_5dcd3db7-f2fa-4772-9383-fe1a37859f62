import React, { useCallback } from 'react';

interface VirtualKeyboardProps {
  isVisible: boolean;
  onKeyPress: (key: string) => void;
  onBackspace: () => void;
  onClose: () => void;
}

const VirtualKeyboard: React.FC<VirtualKeyboardProps> = ({
  isVisible,
  onKeyPress,
  onBackspace,
  onClose
}) => {
  // Indonesian alphabet layout optimized for crosswords
  const keyboardRows = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
  ];

  const handleKeyPress = useCallback((key: string) => {
    onKeyPress(key);
  }, [onKeyPress]);

  const handleBackspace = useCallback(() => {
    onBackspace();
  }, [onBackspace]);

  if (!isVisible) return null;

  return (
    <div
      key="virtual-keyboard"
      id="virtual-keyboard"
      data-virtual-keyboard="true"
      className="fixed bottom-0 left-0 right-0 bg-newsprint border-t-2 border-ink shadow-paper-lg z-[9998] animate-slideInUp"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 9998
      }}
    >
      {/* Header with close button */}
      <div className="flex items-center justify-between p-3 border-b border-ink-secondary">
        <h3 className="text-sm font-bold text-ink-dark font-serif">Papan Ketik Virtual</h3>
        <button
          onClick={onClose}
          className="p-2 text-ink-secondary hover:text-ink bg-primary-100 hover:bg-primary-200 rounded-full transition-all duration-200 border border-ink-secondary hover:border-ink"
          aria-label="Tutup papan ketik"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Keyboard layout */}
      <div className="p-4 pb-6">
        <div className="space-y-2">
          {keyboardRows.map((row, rowIndex) => (
            <div key={rowIndex} className="flex justify-center gap-1">
              {row.map((key) => (
                <button
                  key={key}
                  onClick={() => handleKeyPress(key)}
                  className="flex-1 max-w-[2.5rem] h-12 bg-primary-50 hover:bg-primary-100 border-2 border-ink-secondary hover:border-ink text-ink-dark font-bold font-mono text-lg rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
                  aria-label={`Ketik huruf ${key}`}
                >
                  {key}
                </button>
              ))}
            </div>
          ))}

          {/* Bottom row with backspace */}
          <div className="flex justify-center gap-1 mt-3">
            <button
              onClick={handleBackspace}
              className="flex items-center justify-center px-6 h-12 bg-red-50 hover:bg-red-100 border-2 border-red-300 hover:border-red-500 text-red-700 font-bold rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
              aria-label="Hapus huruf"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
              </svg>
              <span className="text-sm">HAPUS</span>
            </button>

            <button
              onClick={onClose}
              className="flex items-center justify-center px-6 h-12 bg-primary-100 hover:bg-primary-200 border-2 border-ink-secondary hover:border-ink text-ink-dark font-bold rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
              aria-label="Tutup papan ketik"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
              <span className="text-sm">TUTUP</span>
            </button>
          </div>
        </div>

        {/* Instructions */}
        {/* <div className="mt-4 text-center">
          <p className="text-xs text-ink-muted font-mono">
            Ketuk huruf untuk mengisi sel • Gunakan HAPUS untuk menghapus
          </p>
        </div> */}
      </div>
    </div>
  );
};

export default VirtualKeyboard;
