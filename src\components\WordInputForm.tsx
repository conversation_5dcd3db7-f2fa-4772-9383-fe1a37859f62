import React, { useState, useRef } from 'react';
import { useCrossword } from '../context/CrosswordContext';
import { PlusIcon, ListIcon, WandIcon, SettingsIcon, FileUpIcon, FileDownIcon } from 'lucide-react';

// Lazy load XLSX only when needed
// const loadXLSX = async () => {
//   return import('xlsx').then(module => module.default || module);
// };

// // Add import/export functions
// const exportToExcel = async (words) => {
//   try {
//     // Show loading indicator
//     const loadingEl = document.createElement('div');
//     loadingEl.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-1 text-sm';
//     loadingEl.textContent = 'Loading Excel module...';
//     document.body.appendChild(loadingEl);

//     // Dynamically import XLSX
//     const XLSX = await loadXLSX();

//     // Format data for Excel
//     const data = words.map(word => ({
//       Word: word.word,
//       Clue: word.clue || '',
//       Direction: word.direction || '',
//       Row: word.row !== undefined ? word.row : '',
//       Column: word.col !== undefined ? word.col : ''
//     }));

//     // Create worksheet
//     const ws = XLSX.utils.json_to_sheet(data);

//     // Create workbook
//     const wb = XLSX.utils.book_new();
//     XLSX.utils.book_append_sheet(wb, ws, "Crossword Words");

//     // Generate Excel file and trigger download
//     XLSX.writeFile(wb, "crossword_words.xlsx");

//     // Remove loading indicator
//     document.body.removeChild(loadingEl);
//   } catch (error) {
//     console.error('Error exporting to Excel:', error);
//     alert('Failed to export data. Please try again.');
//   }
// };

// const importFromExcel = async (file, dispatch) => {
//   return new Promise(async (resolve, reject) => {
//     try {
//       // Dynamically import XLSX
//       const XLSX = await loadXLSX();

//       const reader = new FileReader();

//       reader.onload = (e) => {
//         try {
//           const data = new Uint8Array(e.target.result);
//           const workbook = XLSX.read(data, { type: 'array' });

//           // Get first worksheet
//           const worksheetName = workbook.SheetNames[0];
//           const worksheet = workbook.Sheets[worksheetName];

//           // Convert to JSON
//           const words = XLSX.utils.sheet_to_json(worksheet);

//           // Process and add words
//           words.forEach(row => {
//             const word = row.Word?.toString().toUpperCase();
//             const clue = row.Clue?.toString();

//             if (word) {
//               if (row.Direction && (row.Row !== undefined && row.Row !== '') &&
//                   (row.Column !== undefined && row.Column !== '')) {
//                 // Add with specific position
//                 dispatch({
//                   type: 'ADD_WORD',
//                   word,
//                   direction: row.Direction.toLowerCase(),
//                   row: parseInt(row.Row),
//                   col: parseInt(row.Column),
//                   clue: clue || undefined,
//                 });
//               } else {
//                 // Auto-place
//                 dispatch({
//                   type: 'AUTO_PLACE_WORD',
//                   word,
//                   clue: clue || undefined,
//                 });
//               }
//             }
//           });

//           resolve(words.length);
//         } catch (error) {
//           console.error('Error importing Excel file:', error);
//           reject(error);
//         }
//       };

//       reader.onerror = (error) => reject(error);
//       reader.readAsArrayBuffer(file);
//     } catch (error) {
//       console.error('Error loading XLSX module:', error);
//       reject(new Error('Failed to load Excel module. Please try again.'));
//     }
//   });
// };

const WordInputForm: React.FC = () => {
  const { state, dispatch } = useCrossword();
  const [word, setWord] = useState('');
  const [clue, setClue] = useState('');
  const [direction, setDirection] = useState<'across' | 'down'>('across');
  const [row, setRow] = useState(0);
  const [col, setCol] = useState(0);
  const [showManualControls, setShowManualControls] = useState(false);
  const [batchWords, setBatchWords] = useState('');
  const [showBatchInput, setShowBatchInput] = useState(false);
  const [theme, setTheme] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [wordCount, setWordCount] = useState(10);
  const [openRouterKey, setOpenRouterKey] = useState(localStorage.getItem('openRouterKey') || '');
  const [showSettings, setShowSettings] = useState(false);
  const [compatibilityScore, setCompatibilityScore] = useState<number | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [language, setLanguage] = useState<'en' | 'id'>('en');
  // const [isImporting, setIsImporting] = useState(false);
  // const fileInputRef = useRef<HTMLInputElement>(null);

  // // Handle file import
  // const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (!file) return;

  //   setIsImporting(true);
  //   try {
  //     const wordCount = await importFromExcel(file, dispatch);
  //     alert(`Successfully imported ${wordCount} words`);
  //   } catch (error) {
  //     console.error('Import failed:', error);
  //     alert('Failed to import words. Please check the file format.');
  //   } finally {
  //     setIsImporting(false);
  //     if (fileInputRef.current) fileInputRef.current.value = '';
  //   }
  // };

  // // Handle export
  // const handleExport = () => {
  //   if (state.words.length === 0) {
  //     alert('No words to export');
  //     return;
  //   }

  //   exportToExcel(state.words);
  // };

  // // Download sample Excel template
  // const downloadSampleTemplate = async () => {
  //   try {
  //     // Show loading indicator
  //     const loadingEl = document.createElement('div');
  //     loadingEl.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-1 text-sm';
  //     loadingEl.textContent = 'Loading Excel module...';
  //     document.body.appendChild(loadingEl);

  //     // Dynamically import XLSX
  //     const XLSX = await loadXLSX();

  //     const sampleData = [
  //       { Word: 'REACT', Clue: 'JavaScript library for building user interfaces', Direction: 'across', Row: 0, Column: 0 },
  //       { Word: 'REDUX', Clue: 'State management library', Direction: 'down', Row: 0, Column: 0 },
  //       { Word: 'TYPESCRIPT', Clue: 'Typed superset of JavaScript', Direction: '', Row: '', Column: '' }
  //     ];

  //     const ws = XLSX.utils.json_to_sheet(sampleData);
  //     const wb = XLSX.utils.book_new();
  //     XLSX.utils.book_append_sheet(wb, ws, "Sample");
  //     XLSX.writeFile(wb, "crossword_template.xlsx");

  //     // Remove loading indicator
  //     document.body.removeChild(loadingEl);
  //   } catch (error) {
  //     console.error('Error creating template:', error);
  //     alert('Failed to create template. Please try again.');
  //   }
  // };

  // Menangani penempatan kata manual
  const handleAddWord = (e: React.FormEvent) => {
    e.preventDefault();
    if (!word.trim()) return;

    dispatch({
      type: 'ADD_WORD',
      word,
      direction,
      row,
      col,
      clue: clue.trim() || undefined,
    });

    // Bersihkan form
    setWord('');
    setClue('');
  };

  // Menangani penempatan kata otomatis
  const handleAutoPlaceWord = () => {
    if (!word.trim()) return;

    dispatch({
      type: 'AUTO_PLACE_WORD',
      word,
      clue: clue.trim() || undefined,
    });

    // Bersihkan form
    setWord('');
    setClue('');
  };

  // Menangani input kata batch
  const handleAddBatchWords = () => {
    if (!batchWords.trim()) return;

    const lines = batchWords
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    lines.forEach(line => {
      // Periksa apakah baris memiliki format "kata:petunjuk"
      const parts = line.split(':');
      const w = parts[0].trim();
      const c = parts.length > 1 ? parts[1].trim() : undefined;

      if (w) {
        dispatch({
          type: 'AUTO_PLACE_WORD',
          word: w,
          clue: c,
        });
      }
    });

    setBatchWords('');
    setShowBatchInput(false);
  };

  // Simpan kunci API OpenRouter
  const handleSaveKey = () => {
    localStorage.setItem('openRouterKey', openRouterKey);
    setShowSettings(false);
  };

  // Menangani generasi kata AI
  const handleGenerateWords = async () => {
    if (!theme.trim() || isGenerating || !openRouterKey) return;

    setIsGenerating(true);
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Pembuat TTS',
        },
        body: JSON.stringify({
          model: 'google/gemini-2.0-flash-exp:free',
          messages: [
            {
              role: 'system',
              content: 'You are a crossword puzzle creator. Generate themed words and clues that work well together in a crossword grid.',
            },
            {
              role: 'user',
              content: `Generate ${wordCount} crossword puzzle words and clues related to "${theme}" in ${language === 'en' ? 'English' : 'Indonesian'} language.
                Follow these rules for better crossword compatibility:
                1. Include words of varying lengths (3-15 letters)
                2. Include words with common crossword letters (E, A, R, I, O, T, N, S, L)
                3. Include some words with uncommon letters (J, K, Q, X, Z) but not too many
                4. Ensure words have some common letters that can intersect with other words
                5. Each word should only contain letters A-Z

                Format the response as a JSON array of objects with "word" and "clue" properties.
                Make the clues challenging but solvable.
                IMPORTANT: Return ONLY valid JSON without markdown formatting, code blocks, or quotes.`,
            },
          ],
        }),
      });

      if (!response.ok) throw new Error('Gagal menghasilkan kata-kata');

      const data = await response.json();
      const content = data.choices[0]?.message?.content || '[]';

      // Bersihkan konten dengan menghapus indikator blok kode markdown
      const cleanedContent = content
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .trim();

      let words;
      try {
        words = JSON.parse(cleanedContent);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error('Gagal mengurai respons AI sebagai JSON');
      }

      // Validasi dan format kata-kata
      const validWords = words
        .filter(({ word }) => /^[A-Za-z]{3,15}$/i.test(word))
        .map(({ word, clue }) => ({
          word: word.toUpperCase(),
          clue: clue.charAt(0).toUpperCase() + clue.slice(1),
        }));

      // Analisis kompatibilitas
      const { score, recommendations } = analyzeWordCompatibility(validWords.map(w => w.word));
      setCompatibilityScore(score);
      setRecommendations(recommendations);

      validWords.forEach(({ word, clue }) => {
        dispatch({
          type: 'AUTO_PLACE_WORD',
          word,
          clue,
        });
      });

      setTheme('');
    } catch (error) {
      console.error('Error generating words:', error);
      alert('Gagal menghasilkan kata-kata: ' + (error.message || 'Kesalahan tidak diketahui'));
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-slate-800 mb-4">Tambah Kata</h2>

      {/* Form input kata tunggal */}
      <div className="mb-6">
        <div className="flex mb-4">
          <input
            type="text"
            value={word}
            onChange={(e) => setWord(e.target.value.toUpperCase())}
            placeholder="Masukkan kata"
            className="flex-grow px-3 py-2 border border-slate-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleAutoPlaceWord}
            disabled={!word.trim()}
            className={`px-4 py-2 rounded-r-md text-white font-medium
              ${!word.trim() ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            <PlusIcon className="w-5 h-5" />
          </button>
        </div>

        <input
          type="text"
          value={clue}
          onChange={(e) => setClue(e.target.value)}
          placeholder="Petunjuk (opsional)"
          className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
        />

        {/* Improved control buttons layout */}
        <div className="mb-4">
          {/* Primary controls - organized in a clean row */}
          <div className="flex flex-wrap items-center gap-3 mb-3 pb-3 border-b border-gray-100">
            <button
              onClick={() => setShowManualControls(!showManualControls)}
              className="flex items-center px-3 py-1.5 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 transition"
            >
              {showManualControls ? 'Hide Manual Controls' : 'Show Manual Controls'}
            </button>

            <button
              onClick={() => setShowBatchInput(!showBatchInput)}
              className="flex items-center px-3 py-1.5 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 transition"
            >
              <ListIcon className="w-4 h-4 mr-1.5" />
              {showBatchInput ? 'Hide Batch Input' : 'Batch Input'}
            </button>
          </div>

          {/* Import/Export section */}  
          {/* hide import/export */}
          {/* <div className="flex flex-wrap items-center gap-3 display-none">
            <div className="text-sm text-slate-500 mr-2">Import/Export:</div>

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".xlsx,.xls"
              className="hidden"
            />

            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isImporting}
              className={`flex items-center px-3 py-1.5 rounded-md transition ${
                isImporting
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
              }`}
            >
              <FileUpIcon className="w-4 h-4 mr-1.5" />
              {isImporting ? 'Importing...' : 'Import Excel'}
            </button>

            <button
              onClick={handleExport}
              disabled={state.words.length === 0}
              className={`flex items-center px-3 py-1.5 rounded-md transition ${
                state.words.length === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
              }`}
            >
              <FileDownIcon className="w-4 h-4 mr-1.5" />
              Export Excel
            </button>

            <button
              onClick={downloadSampleTemplate}
              className="text-blue-600 text-sm hover:text-blue-800"
            >
              Download Template
            </button>
          </div> */}
        </div>

        {showManualControls && (
          <div className="bg-slate-50 p-4 rounded-md mb-4">
            <h3 className="font-medium text-slate-700 mb-2">Penempatan Manual</h3>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Arah
                </label>
                <select
                  value={direction}
                  onChange={(e) => setDirection(e.target.value as 'across' | 'down')}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="across">Mendatar</option>
                  <option value="down">Menurun</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Baris
                </label>
                <input
                  type="number"
                  value={row}
                  onChange={(e) => setRow(parseInt(e.target.value) || 0)}
                  min="0"
                  max={state.gridSize - 1}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Kolom
                </label>
                <input
                  type="number"
                  value={col}
                  onChange={(e) => setCol(parseInt(e.target.value) || 0)}
                  min="0"
                  max={state.gridSize - 1}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={handleAddWord}
                  disabled={!word.trim()}
                  className={`w-full px-4 py-2 rounded-md text-white font-medium
                    ${!word.trim() ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'}`}
                >
                  Tambah
                </button>
              </div>
            </div>
          </div>
        )}

        {showBatchInput && (
          <div className="bg-slate-50 p-4 rounded-md mb-4">
            <h3 className="font-medium text-slate-700 mb-2">Input Batch</h3>
            <p className="text-sm text-slate-600 mb-2">
              Masukkan satu kata per baris. Format: kata:petunjuk
            </p>
            <textarea
              value={batchWords}
              onChange={(e) => setBatchWords(e.target.value)}
              rows={5}
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
              placeholder="KOMPUTER:Alat elektronik untuk mengolah data&#10;INDONESIA:Negara kepulauan di Asia Tenggara"
            />
            <button
              onClick={handleAddBatchWords}
              disabled={!batchWords.trim()}
              className={`w-full px-4 py-2 rounded-md text-white font-medium
                ${!batchWords.trim() ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'}`}
            >
              Tambah Semua
            </button>
          </div>
        )}
      </div>

      {/* Generasi AI */}
      <div className="border-t pt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-slate-700">Generasi AI</h3>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="text-slate-500 hover:text-slate-700"
          >
            <SettingsIcon className="w-5 h-5" />
          </button>
        </div>

        {showSettings ? (
          <div className="bg-slate-50 p-4 rounded-md mb-4">
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Kunci API OpenRouter
            </label>
            <input
                            type="password"
                            value={openRouterKey}
                            onChange={(e) => setOpenRouterKey(e.target.value)}
                            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
                            placeholder="sk-or-..."
                          />
                          <p className="text-xs text-slate-500 mb-4">
                            Dapatkan kunci API gratis di <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">openrouter.ai</a>
                          </p>
                          <button
                            onClick={handleSaveKey}
                            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                          >
                            Simpan
                          </button>
                        </div>
                      ) : (
                        <>
                          {compatibilityScore !== null && (
                            <div className={`mb-4 p-3 rounded-md ${
                              compatibilityScore > 70 ? 'bg-green-50 text-green-800' :
                              compatibilityScore > 40 ? 'bg-yellow-50 text-yellow-800' :
                              'bg-red-50 text-red-800'
                            }`}>
                              <h4 className="font-medium mb-1">Skor Kompatibilitas: {compatibilityScore}%</h4>
                              {recommendations.length > 0 && (
                                <ul className="text-sm list-disc list-inside">
                                  {recommendations.map((rec, i) => (
                                    <li key={i}>{rec}</li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          )}

                          <div className="grid grid-cols-1 gap-4 mb-4">
                            <div>
                              <label className="block text-sm font-medium text-slate-700 mb-1">
                                Tema
                              </label>
                              <input
                                type="text"
                                value={theme}
                                onChange={(e) => setTheme(e.target.value)}
                                className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="mis., Pemrograman, Luar Angkasa, Film"
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-slate-700 mb-1">
                                  Jumlah Kata
                                </label>
                                <input
                                  type="number"
                                  value={wordCount}
                                  onChange={(e) => setWordCount(Math.max(1, Math.min(20, parseInt(e.target.value) || 10)))}
                                  min="1"
                                  max="20"
                                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-slate-700 mb-1">
                                  Bahasa
                                </label>
                                <select
                                  value={language}
                                  onChange={(e) => setLanguage(e.target.value as 'en' | 'id')}
                                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                  <option value="en">English</option>
                                  <option value="id">Indonesian</option>
                                </select>
                              </div>
                            </div>
                            <button
                              onClick={handleGenerateWords}
                              disabled={!theme.trim() || isGenerating || !openRouterKey}
                              className={`w-full px-4 py-2 rounded-md text-white font-medium flex items-center justify-center
                                ${(!theme.trim() || isGenerating || !openRouterKey)
                                  ? 'bg-blue-300'
                                  : 'bg-blue-600 hover:bg-blue-700'}`}
                            >
                              {isGenerating ? (
                                <>
                                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                                  Menghasilkan...
                                </>
                              ) : (
                                <>
                                  <WandIcon className="w-5 h-5 mr-2" />
                                  Hasilkan Kata-kata
                                </>
                              )}
                            </button>
                          </div>

                          {!openRouterKey && (
                            <p className="text-sm text-red-600">
                              Kunci API OpenRouter diperlukan untuk generasi AI.{' '}
                              <button
                                onClick={() => setShowSettings(true)}
                                className="text-blue-600 hover:underline"
                              >
                                Tambahkan kunci
                              </button>
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                );
              };

              // Fungsi untuk menganalisis kompatibilitas kata-kata
              function analyzeWordCompatibility(words: string[]): { score: number, recommendations: string[] } {
                if (words.length < 2) {
                  return { score: 0, recommendations: ['Tambahkan lebih banyak kata untuk analisis kompatibilitas'] };
                }

                const recommendations: string[] = [];
                let score = 100;

                // Periksa panjang kata
                const lengthDistribution = words.reduce((acc, word) => {
                  const length = word.length;
                  acc[length] = (acc[length] || 0) + 1;
                  return acc;
                }, {} as Record<number, number>);

                const uniqueLengths = Object.keys(lengthDistribution).length;
                if (uniqueLengths < 3) {
                  score -= 20;
                  recommendations.push('Tambahkan kata-kata dengan panjang yang lebih bervariasi');
                }

                // Periksa huruf umum
                const commonLetters = ['E', 'A', 'R', 'I', 'O', 'T', 'N', 'S', 'L'];
                const uncommonLetters = ['J', 'K', 'Q', 'X', 'Z'];

                const letterCounts: Record<string, number> = {};
                words.forEach(word => {
                  [...word].forEach(letter => {
                    letterCounts[letter] = (letterCounts[letter] || 0) + 1;
                  });
                });

                const hasCommonLetters = commonLetters.some(letter => letterCounts[letter] > 0);
                if (!hasCommonLetters) {
                  score -= 15;
                  recommendations.push('Sertakan kata-kata dengan huruf umum (E, A, R, I, O, T, N, S, L)');
                }

                const uncommonLetterCount = uncommonLetters.reduce((count, letter) =>
                  count + (letterCounts[letter] || 0), 0);
                const totalLetters = Object.values(letterCounts).reduce((sum, count) => sum + count, 0);

                if (uncommonLetterCount / totalLetters > 0.2) {
                  score -= 15;
                  recommendations.push('Terlalu banyak huruf yang tidak umum (J, K, Q, X, Z)');
                } else if (uncommonLetterCount === 0) {
                  score -= 5;
                  recommendations.push('Pertimbangkan untuk menambahkan beberapa kata dengan huruf yang tidak umum');
                }

                // Periksa potensi persilangan
                const potentialIntersections = commonLetters.filter(letter =>
                  (letterCounts[letter] || 0) >= words.length / 2
                ).length;

                if (potentialIntersections < 3) {
                  score -= 15;
                  recommendations.push('Kata-kata kurang memiliki huruf umum yang dapat berpotongan');
                }

                // Batasi skor minimum
                score = Math.max(0, score);

                return { score, recommendations };
              }

              export default WordInputForm;





