import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { HomeIcon, GridIcon, UsersIcon, SettingsIcon, LogOutIcon, FileTextIcon, PuzzleIcon } from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();

  // Navigation items
  const navItems = [
    {
      path: '/admin',
      label: 'Dashboard',
      icon: <HomeIcon className="w-5 h-5" />
    },
    {
      path: '/admin/categories',
      label: '<PERSON><PERSON><PERSON>',
      icon: <GridIcon className="w-5 h-5" />
    },
    {
      path: '/admin/crosswords',
      label: 'Teka-<PERSON><PERSON>lang',
      icon: <PuzzleIcon className="w-5 h-5" />
    },
    {
      path: '/admin/blogs',
      label: 'Blog',
      icon: <FileTextIcon className="w-5 h-5" />
    },
    {
      path: '/admin/users',
      label: 'Pengguna',
      icon: <UsersIcon className="w-5 h-5" />
    },
    {
      path: '/admin/settings',
      label: 'Pengaturan',
      icon: <SettingsIcon className="w-5 h-5" />
    }
  ];

  // Check if a path is active
  const isActive = (path: string) => {
    if (path === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-md">
        <div className="p-4 border-b">
          <h1 className="text-xl font-bold">Admin Panel</h1>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center p-2 rounded-md ${
                    isActive(item.path)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <span className="ml-3">{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>

          <div className="mt-8 pt-4 border-t">
            <Link
              to="/"
              className="flex items-center p-2 rounded-md text-gray-700 hover:bg-gray-100"
            >
              <LogOutIcon className="w-5 h-5" />
              <span className="ml-3">Kembali ke Situs</span>
            </Link>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
};

export default AdminLayout;
