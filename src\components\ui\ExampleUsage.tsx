import React, { useState } from 'react';
import { 
  LoadingSpinner, 
  ContentSkeleton, 
  CardSkeleton, 
  FeedbackMessage, 
  EmptyState 
} from './Feedback';
import { toastUtils } from './ToastProvider';
import { useAsyncOperation, withAsyncFeedback } from '../../hooks/useAsyncOperation';

/**
 * Example component demonstrating the usage of standardized UI components
 * This is for demonstration purposes only and should not be used in production
 */
export const UIComponentsDemo: React.FC = () => {
  const [showLoading, setShowLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showEmpty, setShowEmpty] = useState(false);

  // Example async function that simulates an API call
  const simulateApiCall = async (shouldSucceed = true): Promise<string> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldSucceed) {
          resolve('Data berhasil dimuat');
        } else {
          reject(new Error('Gagal memuat data'));
        }
      }, 2000);
    });
  };

  // Example usage of useAsyncOperation hook
  const { loading, error, execute } = useAsyncOperation(
    simulateApiCall,
    {
      showLoadingToast: true,
      showSuccessToast: true,
      loadingMessage: 'Memuat data...',
      successMessage: 'Data berhasil dimuat',
      errorMessage: 'Gagal memuat data'
    }
  );

  // Example usage of withAsyncFeedback utility
  const handleWithFeedback = withAsyncFeedback(
    simulateApiCall,
    {
      loadingMessage: 'Memproses dengan feedback...',
      successMessage: 'Berhasil dengan feedback!',
      errorMessage: 'Gagal dengan feedback'
    }
  );

  // Toggle loading state
  const handleToggleLoading = () => {
    setShowLoading(!showLoading);
  };

  // Toggle error state
  const handleToggleError = () => {
    setShowError(!showError);
  };

  // Toggle success state
  const handleToggleSuccess = () => {
    setShowSuccess(!showSuccess);
  };

  // Toggle empty state
  const handleToggleEmpty = () => {
    setShowEmpty(!showEmpty);
  };

  // Show toast examples
  const showToastExamples = () => {
    toastUtils.info('Ini adalah pesan informasi');
    setTimeout(() => {
      toastUtils.success('Operasi berhasil dilakukan');
    }, 1000);
    setTimeout(() => {
      toastUtils.error('Terjadi kesalahan saat memproses permintaan');
    }, 2000);
    setTimeout(() => {
      const loadingId = toastUtils.loading('Sedang memproses...');
      setTimeout(() => {
        toastUtils.update(loadingId, 'Proses selesai!', 'success');
      }, 3000);
    }, 3000);
  };

  // Execute async operation with hook
  const handleExecuteAsync = async () => {
    await execute(true);
  };

  // Execute async operation that fails
  const handleExecuteAsyncFail = async () => {
    await execute(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Komponen UI Standar</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Loading States</h2>
          <div className="space-y-4">
            <button
              onClick={handleToggleLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {showLoading ? 'Sembunyikan' : 'Tampilkan'} Loading
            </button>
            
            {showLoading && (
              <div className="space-y-4 mt-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Loading Spinner</h3>
                  <LoadingSpinner size="md" text="Memuat data..." />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Content Skeleton</h3>
                  <ContentSkeleton rows={3} />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Card Skeleton</h3>
                  <CardSkeleton count={2} />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Feedback Messages</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={handleToggleSuccess}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                {showSuccess ? 'Sembunyikan' : 'Tampilkan'} Sukses
              </button>
              
              <button
                onClick={handleToggleError}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                {showError ? 'Sembunyikan' : 'Tampilkan'} Error
              </button>
              
              <button
                onClick={handleToggleEmpty}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                {showEmpty ? 'Sembunyikan' : 'Tampilkan'} Empty
              </button>
            </div>
            
            {showSuccess && (
              <FeedbackMessage
                type="success"
                message="Operasi berhasil dilakukan"
                onClose={() => setShowSuccess(false)}
              />
            )}
            
            {showError && (
              <FeedbackMessage
                type="error"
                message="Terjadi kesalahan saat memproses permintaan"
                onClose={() => setShowError(false)}
              />
            )}
            
            {showEmpty && (
              <EmptyState message="Tidak ada data yang tersedia" />
            )}
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Toast Notifications</h2>
        <div className="space-y-4">
          <button
            onClick={showToastExamples}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Tampilkan Contoh Toast
          </button>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Async Operations</h2>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={handleExecuteAsync}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? 'Memproses...' : 'Execute Async (Success)'}
            </button>
            
            <button
              onClick={handleExecuteAsyncFail}
              className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
              disabled={loading}
            >
              Execute Async (Fail)
            </button>
            
            <button
              onClick={() => handleWithFeedback(true)}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              With Feedback (Success)
            </button>
            
            <button
              onClick={() => handleWithFeedback(false)}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              With Feedback (Fail)
            </button>
          </div>
          
          {error && (
            <FeedbackMessage
              type="error"
              message={error}
            />
          )}
        </div>
      </div>
    </div>
  );
};
