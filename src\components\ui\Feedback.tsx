import React, { memo } from 'react';
import { Loader2, AlertCircle, CheckCircle, Info, XCircle } from 'lucide-react';

// Types for feedback components
export type LoadingSize = 'sm' | 'md' | 'lg';
export type FeedbackType = 'success' | 'error' | 'info' | 'warning';

/**
 * Standardized loading spinner component
 */
export const LoadingSpinner = memo(({
  size = 'md',
  text = 'Memuat...',
  fullPage = false,
  className = ''
}: {
  size?: LoadingSize;
  text?: string;
  fullPage?: boolean;
  className?: string;
}) => {
  const sizeMap = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const spinnerSize = sizeMap[size];

  if (fullPage) {
    return (
      <div className="fixed inset-0 bg-newsprint bg-opacity-80 flex flex-col items-center justify-center z-50">
        <Loader2 className={`${spinnerSize} animate-spin text-ink mb-2`} />
        {text && <p className="text-primary-600 text-sm">{text}</p>}
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center justify-center py-6 ${className}`}>
      <Loader2 className={`${spinnerSize} animate-spin text-ink mb-2`} />
      {text && <p className="text-primary-600 text-sm">{text}</p>}
    </div>
  );
});

/**
 * Standardized skeleton loader for content
 */
export const ContentSkeleton = memo(({
  rows = 3,
  className = ''
}: {
  rows?: number;
  className?: string;
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {[...Array(rows)].map((_, i) => (
        <div key={i} className="h-4 bg-primary-200 rounded mb-2 w-full" />
      ))}
    </div>
  );
});

/**
 * Standardized card skeleton loader
 */
export const CardSkeleton = memo(({
  count = 1,
  className = ''
}: {
  count?: number;
  className?: string;
}) => {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ${className}`}>
      {[...Array(count)].map((_, i) => (
        <div key={i} className="card-paper overflow-hidden animate-pulse">
          <div className="h-40 bg-primary-300"></div>
          <div className="p-4">
            <div className="h-5 bg-primary-300 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-primary-200 rounded w-1/2 mb-3"></div>
            <div className="flex justify-between">
              <div className="h-6 bg-primary-300 rounded w-1/4"></div>
              <div className="h-6 bg-primary-300 rounded w-1/4"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});

/**
 * Enhanced feedback message component with Indonesian messages
 */
export const FeedbackMessage = memo(({
  type = 'info',
  message,
  className = '',
  onClose
}: {
  type?: FeedbackType;
  message: string;
  className?: string;
  onClose?: () => void;
}) => {
  const typeStyles = {
    success: 'bg-paper-primary border-ink-primary/20 text-ink-primary shadow-paper',
    error: 'bg-paper-primary border-red-400 text-red-700 shadow-paper',
    info: 'bg-paper-primary border-blue-400 text-blue-700 shadow-paper',
    warning: 'bg-paper-primary border-yellow-400 text-yellow-700 shadow-paper'
  };

  const icons = {
    success: <CheckCircle className="w-5 h-5 mr-2 text-green-600" />,
    error: <AlertCircle className="w-5 h-5 mr-2 text-red-600" />,
    info: <Info className="w-5 h-5 mr-2 text-blue-600" />,
    warning: <AlertCircle className="w-5 h-5 mr-2 text-yellow-600" />
  };

  const titles = {
    success: 'Berhasil!',
    error: 'Terjadi Kesalahan',
    info: 'Informasi',
    warning: 'Peringatan'
  };

  return (
    <div className={`${typeStyles[type]} border-l-4 px-4 py-3 rounded-md font-serif ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          {icons[type]}
        </div>
        <div className="ml-2 flex-1">
          <h4 className="font-semibold text-ink-dark">{titles[type]}</h4>
          <p className="text-sm mt-1 text-ink-muted leading-relaxed">{message}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-4 text-ink-light hover:text-ink-primary transition-colors"
            aria-label="Tutup pesan"
          >
            <XCircle className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
});

/**
 * Enhanced empty state component with better Indonesian messaging
 */
export const EmptyState = memo(({
  title = 'Belum Ada Data',
  message = 'Tidak ada data yang tersedia saat ini',
  actionText,
  onAction,
  className = ''
}: {
  title?: string;
  message?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}) => {
  return (
    <div className={`text-center py-16 px-4 ${className}`}>
      <div className="w-16 h-16 bg-paper-200 rounded-full flex items-center justify-center mx-auto mb-4">
        <BookOpenIcon className="w-8 h-8 text-ink-muted" />
      </div>
      <h3 className="text-xl font-bold text-ink-dark mb-2 font-serif">{title}</h3>
      <p className="text-ink-muted mb-6 max-w-md mx-auto leading-relaxed">{message}</p>
      {actionText && onAction && (
        <button
          onClick={onAction}
          className="btn-primary"
        >
          {actionText}
        </button>
      )}
    </div>
  );
});
