import React from 'react';

interface MonochromeCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'textured';
  className?: string;
  onClick?: () => void;
}

export const MonochromeCard: React.FC<MonochromeCardProps> = ({
  children,
  variant = 'default',
  className = '',
  onClick
}) => {
  const baseClasses = 'transition-all duration-200 ease-in-out';
  
  const variantClasses = {
    default: 'bg-paper-50 border border-paper-200 shadow-paper',
    elevated: 'bg-paper-50 border border-paper-200 shadow-paper-lg hover:shadow-paper-xl',
    outlined: 'bg-transparent border-2 border-ink-300 hover:border-ink-500',
    textured: 'bg-paper-50 bg-paper-texture border border-paper-200 shadow-paper'
  };

  const interactiveClasses = onClick 
    ? 'cursor-pointer hover:bg-paper-100 hover:border-paper-300 active:scale-[0.98]' 
    : '';

  return (
    <div
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${interactiveClasses}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

interface MonochromeButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
}

export const MonochromeButton: React.FC<MonochromeButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  className = ''
}) => {
  const baseClasses = 'font-serif font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ink-400 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const variantClasses = {
    primary: 'bg-ink-900 text-paper-50 border border-ink-900 hover:bg-ink-800 hover:border-ink-800 active:bg-ink-900',
    secondary: 'bg-paper-200 text-ink-900 border border-paper-300 hover:bg-paper-300 hover:border-paper-400 active:bg-paper-200',
    outline: 'bg-transparent text-ink-900 border-2 border-ink-300 hover:bg-ink-50 hover:border-ink-500 active:bg-ink-100',
    ghost: 'bg-transparent text-ink-700 hover:bg-paper-100 hover:text-ink-900 active:bg-paper-200'
  };

  return (
    <button
      className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

interface MonochromeInputProps {
  type?: 'text' | 'email' | 'password' | 'number';
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  error?: string;
}

export const MonochromeInput: React.FC<MonochromeInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  className = '',
  label,
  error
}) => {
  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label className="block text-sm font-serif font-medium text-ink-700">
          {label}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        className={`
          w-full px-3 py-2 font-mono text-ink-900 bg-paper-50 border border-paper-300 
          focus:outline-none focus:ring-2 focus:ring-ink-400 focus:border-transparent
          disabled:opacity-50 disabled:cursor-not-allowed
          placeholder:text-ink-400 placeholder:font-serif
          transition-all duration-200 ease-in-out
          ${error ? 'border-red-500 focus:ring-red-400' : ''}
        `}
      />
      {error && (
        <p className="text-sm text-red-600 font-serif">{error}</p>
      )}
    </div>
  );
};

interface MonochromeTextareaProps {
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  rows?: number;
  className?: string;
  label?: string;
  error?: string;
}

export const MonochromeTextarea: React.FC<MonochromeTextareaProps> = ({
  placeholder,
  value,
  onChange,
  disabled = false,
  rows = 4,
  className = '',
  label,
  error
}) => {
  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label className="block text-sm font-serif font-medium text-ink-700">
          {label}
        </label>
      )}
      <textarea
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        rows={rows}
        className={`
          w-full px-3 py-2 font-mono text-ink-900 bg-paper-50 border border-paper-300 
          focus:outline-none focus:ring-2 focus:ring-ink-400 focus:border-transparent
          disabled:opacity-50 disabled:cursor-not-allowed
          placeholder:text-ink-400 placeholder:font-serif
          transition-all duration-200 ease-in-out resize-vertical
          ${error ? 'border-red-500 focus:ring-red-400' : ''}
        `}
      />
      {error && (
        <p className="text-sm text-red-600 font-serif">{error}</p>
      )}
    </div>
  );
};
