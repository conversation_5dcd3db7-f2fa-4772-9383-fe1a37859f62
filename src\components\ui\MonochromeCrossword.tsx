import React from 'react';

interface MonochromeCrosswordCellProps {
  value?: string;
  number?: number;
  isBlocked?: boolean;
  isSelected?: boolean;
  isHighlighted?: boolean;
  isCorrect?: boolean;
  isIncorrect?: boolean;
  onClick?: () => void;
  onChange?: (value: string) => void;
  className?: string;
}

export const MonochromeCrosswordCell: React.FC<MonochromeCrosswordCellProps> = ({
  value = '',
  number,
  isBlocked = false,
  isSelected = false,
  isHighlighted = false,
  isCorrect = false,
  isIncorrect = false,
  onClick,
  onChange,
  className = ''
}) => {
  const baseClasses = 'relative w-8 h-8 md:w-10 md:h-10 border border-ink-900 transition-all duration-150';
  
  const stateClasses = {
    blocked: 'bg-ink-900',
    normal: 'bg-paper-50 hover:bg-paper-100',
    selected: 'bg-ink-200 ring-2 ring-ink-900',
    highlighted: 'bg-ink-100',
    correct: 'bg-green-100 border-green-500',
    incorrect: 'bg-red-100 border-red-500'
  };

  const getStateClass = () => {
    if (isBlocked) return stateClasses.blocked;
    if (isCorrect) return stateClasses.correct;
    if (isIncorrect) return stateClasses.incorrect;
    if (isSelected) return stateClasses.selected;
    if (isHighlighted) return stateClasses.highlighted;
    return stateClasses.normal;
  };

  if (isBlocked) {
    return (
      <div className={`${baseClasses} ${getStateClass()} ${className}`} />
    );
  }

  return (
    <div className={`${baseClasses} ${getStateClass()} ${className}`} onClick={onClick}>
      {/* Cell number */}
      {number && (
        <span className="absolute top-0 left-0 text-xs font-mono font-bold text-ink-900 leading-none p-0.5">
          {number}
        </span>
      )}
      
      {/* Input field */}
      <input
        type="text"
        value={value}
        onChange={(e) => onChange?.(e.target.value.toUpperCase())}
        className="absolute inset-0 w-full h-full text-center font-mono font-bold text-ink-900 bg-transparent border-none outline-none text-sm md:text-base"
        maxLength={1}
        style={{ caretColor: 'transparent' }}
      />
    </div>
  );
};

interface MonochromeCrosswordGridProps {
  grid: (string | null)[][];
  numbers?: (number | null)[][];
  selectedCell?: [number, number] | null;
  highlightedCells?: [number, number][];
  correctCells?: [number, number][];
  incorrectCells?: [number, number][];
  onCellClick?: (row: number, col: number) => void;
  onCellChange?: (row: number, col: number, value: string) => void;
  className?: string;
}

export const MonochromeCrosswordGrid: React.FC<MonochromeCrosswordGridProps> = ({
  grid,
  numbers,
  selectedCell,
  highlightedCells = [],
  correctCells = [],
  incorrectCells = [],
  onCellClick,
  onCellChange,
  className = ''
}) => {
  const isHighlighted = (row: number, col: number) => 
    highlightedCells.some(([r, c]) => r === row && c === col);
  
  const isCorrect = (row: number, col: number) => 
    correctCells.some(([r, c]) => r === row && c === col);
  
  const isIncorrect = (row: number, col: number) => 
    incorrectCells.some(([r, c]) => r === row && c === col);

  return (
    <div className={`inline-block bg-paper-50 p-4 border-2 border-ink-900 shadow-paper-lg ${className}`}>
      <div className="grid gap-0" style={{ gridTemplateColumns: `repeat(${grid[0]?.length || 0}, minmax(0, 1fr))` }}>
        {grid.map((row, rowIndex) =>
          row.map((cell, colIndex) => (
            <MonochromeCrosswordCell
              key={`${rowIndex}-${colIndex}`}
              value={cell || ''}
              number={numbers?.[rowIndex]?.[colIndex] || undefined}
              isBlocked={cell === null}
              isSelected={selectedCell?.[0] === rowIndex && selectedCell?.[1] === colIndex}
              isHighlighted={isHighlighted(rowIndex, colIndex)}
              isCorrect={isCorrect(rowIndex, colIndex)}
              isIncorrect={isIncorrect(rowIndex, colIndex)}
              onClick={() => onCellClick?.(rowIndex, colIndex)}
              onChange={(value) => onCellChange?.(rowIndex, colIndex, value)}
            />
          ))
        )}
      </div>
    </div>
  );
};

interface MonochromeClueProps {
  number: number;
  clue: string;
  isActive?: boolean;
  isCompleted?: boolean;
  onClick?: () => void;
  className?: string;
}

export const MonochromeClue: React.FC<MonochromeClueProps> = ({
  number,
  clue,
  isActive = false,
  isCompleted = false,
  onClick,
  className = ''
}) => {
  const baseClasses = 'flex items-start gap-3 p-3 cursor-pointer transition-all duration-150 border-b border-paper-200';
  
  const stateClasses = {
    normal: 'hover:bg-paper-100',
    active: 'bg-ink-100 border-ink-300',
    completed: 'bg-green-50 text-green-800'
  };

  const getStateClass = () => {
    if (isCompleted) return stateClasses.completed;
    if (isActive) return stateClasses.active;
    return stateClasses.normal;
  };

  return (
    <div 
      className={`${baseClasses} ${getStateClass()} ${className}`}
      onClick={onClick}
    >
      <span className="font-mono font-bold text-ink-900 text-sm min-w-[2rem]">
        {number}.
      </span>
      <span className="font-serif text-ink-800 leading-relaxed flex-1">
        {clue}
      </span>
      {isCompleted && (
        <span className="text-green-600 text-sm">✓</span>
      )}
    </div>
  );
};

interface MonochromeClueListProps {
  title: string;
  clues: Array<{
    number: number;
    clue: string;
    isCompleted?: boolean;
  }>;
  activeClue?: number;
  onClueClick?: (number: number) => void;
  className?: string;
}

export const MonochromeClueList: React.FC<MonochromeClueListProps> = ({
  title,
  clues,
  activeClue,
  onClueClick,
  className = ''
}) => {
  return (
    <div className={`bg-paper-50 border border-paper-300 shadow-paper ${className}`}>
      <div className="bg-ink-900 text-paper-50 px-4 py-3">
        <h3 className="font-serif font-bold text-lg">{title}</h3>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {clues.map((clue) => (
          <MonochromeClue
            key={clue.number}
            number={clue.number}
            clue={clue.clue}
            isActive={activeClue === clue.number}
            isCompleted={clue.isCompleted}
            onClick={() => onClueClick?.(clue.number)}
          />
        ))}
      </div>
    </div>
  );
};

interface MonochromeCrosswordStatsProps {
  completed: number;
  total: number;
  timeElapsed?: string;
  hintsUsed?: number;
  className?: string;
}

export const MonochromeCrosswordStats: React.FC<MonochromeCrosswordStatsProps> = ({
  completed,
  total,
  timeElapsed,
  hintsUsed,
  className = ''
}) => {
  const completionPercentage = Math.round((completed / total) * 100);

  return (
    <div className={`bg-paper-100 border border-paper-300 p-4 ${className}`}>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div className="font-mono text-2xl font-bold text-ink-900">
            {completionPercentage}%
          </div>
          <div className="font-serif text-sm text-ink-600">Selesai</div>
        </div>
        
        <div>
          <div className="font-mono text-2xl font-bold text-ink-900">
            {completed}/{total}
          </div>
          <div className="font-serif text-sm text-ink-600">Kata</div>
        </div>
        
        {timeElapsed && (
          <div>
            <div className="font-mono text-2xl font-bold text-ink-900">
              {timeElapsed}
            </div>
            <div className="font-serif text-sm text-ink-600">Waktu</div>
          </div>
        )}
        
        {hintsUsed !== undefined && (
          <div>
            <div className="font-mono text-2xl font-bold text-ink-900">
              {hintsUsed}
            </div>
            <div className="font-serif text-sm text-ink-600">Petunjuk</div>
          </div>
        )}
      </div>
      
      {/* Progress bar */}
      <div className="mt-4">
        <div className="bg-paper-300 h-2 border border-paper-400">
          <div 
            className="bg-ink-900 h-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
};
