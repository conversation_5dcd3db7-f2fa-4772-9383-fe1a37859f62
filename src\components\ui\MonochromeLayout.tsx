import React from 'react';

interface MonochromePageProps {
  children: React.ReactNode;
  variant?: 'default' | 'textured' | 'lined' | 'dotted';
  className?: string;
}

export const MonochromePage: React.FC<MonochromePageProps> = ({
  children,
  variant = 'default',
  className = ''
}) => {
  const backgroundClasses = {
    default: 'bg-paper-50',
    textured: 'bg-paper-50 bg-paper-texture',
    lined: 'bg-paper-50 bg-paper-lines',
    dotted: 'bg-paper-50 bg-paper-dots'
  };

  return (
    <div className={`min-h-screen ${backgroundClasses[variant]} ${className}`}>
      {children}
    </div>
  );
};

interface MonochromeContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export const MonochromeContainer: React.FC<MonochromeContainerProps> = ({
  children,
  size = 'lg',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <div className={`mx-auto px-4 sm:px-6 lg:px-8 ${sizeClasses[size]} ${className}`}>
      {children}
    </div>
  );
};

interface MonochromeHeaderProps {
  children: React.ReactNode;
  variant?: 'default' | 'newspaper' | 'minimal';
  className?: string;
}

export const MonochromeHeader: React.FC<MonochromeHeaderProps> = ({
  children,
  variant = 'default',
  className = ''
}) => {
  const variantClasses = {
    default: 'bg-paper-50 border-b border-paper-200 shadow-paper',
    newspaper: 'bg-paper-50 border-b-4 border-ink-900 bg-paper-lines',
    minimal: 'bg-transparent border-b border-paper-300'
  };

  return (
    <header className={`sticky top-0 z-50 ${variantClasses[variant]} ${className}`}>
      {children}
    </header>
  );
};

interface MonochromeSectionProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'bordered' | 'elevated';
  className?: string;
}

export const MonochromeSection: React.FC<MonochromeSectionProps> = ({
  children,
  title,
  subtitle,
  variant = 'default',
  className = ''
}) => {
  const variantClasses = {
    default: '',
    bordered: 'border border-paper-200 bg-paper-50 p-6 rounded-none',
    elevated: 'bg-paper-50 shadow-paper-lg p-6 border border-paper-200'
  };

  return (
    <section className={`${variantClasses[variant]} ${className}`}>
      {(title || subtitle) && (
        <div className="mb-6">
          {title && (
            <h2 className="text-2xl md:text-3xl font-serif font-bold text-ink-900 mb-2">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-ink-600 font-serif text-lg">
              {subtitle}
            </p>
          )}
          <div className="mt-4 h-px bg-ink-300"></div>
        </div>
      )}
      {children}
    </section>
  );
};

interface MonochromeGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const MonochromeGrid: React.FC<MonochromeGridProps> = ({
  children,
  cols = 3,
  gap = 'md',
  className = ''
}) => {
  const colsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6'
  };

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  return (
    <div className={`grid ${colsClasses[cols]} ${gapClasses[gap]} ${className}`}>
      {children}
    </div>
  );
};

interface MonochromeModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const MonochromeModal: React.FC<MonochromeModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-ink-900 bg-opacity-50 transition-opacity"
          onClick={onClose}
        />
        
        {/* Modal */}
        <div className={`
          relative bg-paper-50 border-2 border-ink-900 shadow-paper-xl
          w-full ${sizeClasses[size]} ${className}
        `}>
          {/* Header */}
          {title && (
            <div className="border-b border-paper-200 px-6 py-4">
              <h3 className="text-xl font-serif font-bold text-ink-900">
                {title}
              </h3>
              <button
                onClick={onClose}
                className="absolute top-4 right-4 text-ink-500 hover:text-ink-900 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}
          
          {/* Content */}
          <div className="px-6 py-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};
