import React from 'react';

interface MonochromeHeadingProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  variant?: 'newspaper' | 'serif' | 'mono';
  className?: string;
}

export const MonochromeHeading: React.FC<MonochromeHeadingProps> = ({
  children,
  level = 1,
  variant = 'newspaper',
  className = ''
}) => {
  const baseClasses = 'font-bold text-ink-900 leading-tight';
  
  const levelClasses = {
    1: 'text-4xl md:text-5xl lg:text-6xl',
    2: 'text-3xl md:text-4xl lg:text-5xl',
    3: 'text-2xl md:text-3xl lg:text-4xl',
    4: 'text-xl md:text-2xl lg:text-3xl',
    5: 'text-lg md:text-xl lg:text-2xl',
    6: 'text-base md:text-lg lg:text-xl'
  };

  const variantClasses = {
    newspaper: 'font-serif tracking-tight',
    serif: 'font-serif',
    mono: 'font-mono tracking-wider'
  };

  const Tag = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <Tag className={`
      ${baseClasses}
      ${levelClasses[level]}
      ${variantClasses[variant]}
      ${className}
    `}>
      {children}
    </Tag>
  );
};

interface MonochromeTextProps {
  children: React.ReactNode;
  variant?: 'body' | 'caption' | 'lead' | 'mono';
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'primary' | 'secondary' | 'muted' | 'accent';
  className?: string;
}

export const MonochromeText: React.FC<MonochromeTextProps> = ({
  children,
  variant = 'body',
  size = 'base',
  weight = 'normal',
  color = 'primary',
  className = ''
}) => {
  const variantClasses = {
    body: 'font-serif leading-relaxed',
    caption: 'font-serif text-sm leading-normal',
    lead: 'font-serif text-lg leading-relaxed',
    mono: 'font-mono leading-normal tracking-wide'
  };

  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  };

  const colorClasses = {
    primary: 'text-ink-900',
    secondary: 'text-ink-700',
    muted: 'text-ink-500',
    accent: 'text-ink-800'
  };

  return (
    <p className={`
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${weightClasses[weight]}
      ${colorClasses[color]}
      ${className}
    `}>
      {children}
    </p>
  );
};

interface MonochromeQuoteProps {
  children: React.ReactNode;
  author?: string;
  source?: string;
  variant?: 'default' | 'bordered' | 'highlighted';
  className?: string;
}

export const MonochromeQuote: React.FC<MonochromeQuoteProps> = ({
  children,
  author,
  source,
  variant = 'default',
  className = ''
}) => {
  const variantClasses = {
    default: 'border-l-4 border-ink-300 pl-6 italic',
    bordered: 'border-2 border-ink-300 p-6 bg-paper-100',
    highlighted: 'bg-ink-100 p-6 border-l-4 border-ink-900'
  };

  return (
    <blockquote className={`${variantClasses[variant]} ${className}`}>
      <p className="font-serif text-lg text-ink-800 leading-relaxed mb-4">
        "{children}"
      </p>
      {(author || source) && (
        <footer className="text-ink-600 font-serif">
          {author && <cite className="font-medium">— {author}</cite>}
          {source && <span className="text-sm ml-2">({source})</span>}
        </footer>
      )}
    </blockquote>
  );
};

interface MonochromeCodeProps {
  children: React.ReactNode;
  variant?: 'inline' | 'block';
  language?: string;
  className?: string;
}

export const MonochromeCode: React.FC<MonochromeCodeProps> = ({
  children,
  variant = 'inline',
  language,
  className = ''
}) => {
  if (variant === 'inline') {
    return (
      <code className={`
        font-mono text-sm bg-paper-200 text-ink-800 px-1.5 py-0.5 
        border border-paper-300 ${className}
      `}>
        {children}
      </code>
    );
  }

  return (
    <div className={`bg-paper-100 border border-paper-300 ${className}`}>
      {language && (
        <div className="bg-paper-200 px-4 py-2 border-b border-paper-300">
          <span className="font-mono text-xs text-ink-600 uppercase tracking-wide">
            {language}
          </span>
        </div>
      )}
      <pre className="p-4 overflow-x-auto">
        <code className="font-mono text-sm text-ink-800 leading-relaxed">
          {children}
        </code>
      </pre>
    </div>
  );
};

interface MonochromeBadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'filled' | 'dot';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const MonochromeBadge: React.FC<MonochromeBadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center font-serif font-medium';
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  const variantClasses = {
    default: 'bg-paper-200 text-ink-800 border border-paper-300',
    outline: 'bg-transparent text-ink-700 border border-ink-300',
    filled: 'bg-ink-900 text-paper-50 border border-ink-900',
    dot: 'bg-paper-100 text-ink-700 border border-paper-300 relative pl-6'
  };

  return (
    <span className={`
      ${baseClasses}
      ${sizeClasses[size]}
      ${variantClasses[variant]}
      ${className}
    `}>
      {variant === 'dot' && (
        <span className="absolute left-2 w-2 h-2 bg-ink-500 rounded-full"></span>
      )}
      {children}
    </span>
  );
};

interface MonochromeDividerProps {
  variant?: 'solid' | 'dashed' | 'dotted' | 'double';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  label?: string;
}

export const MonochromeDivider: React.FC<MonochromeDividerProps> = ({
  variant = 'solid',
  orientation = 'horizontal',
  className = '',
  label
}) => {
  const variantClasses = {
    solid: 'border-solid',
    dashed: 'border-dashed',
    dotted: 'border-dotted',
    double: 'border-double border-2'
  };

  if (orientation === 'vertical') {
    return (
      <div className={`
        border-l border-paper-300 ${variantClasses[variant]} h-full ${className}
      `} />
    );
  }

  if (label) {
    return (
      <div className={`relative ${className}`}>
        <div className={`border-t border-paper-300 ${variantClasses[variant]}`} />
        <div className="absolute inset-0 flex justify-center">
          <span className="bg-paper-50 px-4 text-sm font-serif text-ink-600">
            {label}
          </span>
        </div>
      </div>
    );
  }

  return (
    <hr className={`
      border-t border-paper-300 ${variantClasses[variant]} ${className}
    `} />
  );
};
