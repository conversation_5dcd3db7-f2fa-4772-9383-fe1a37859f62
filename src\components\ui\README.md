# Monochrome UI Components

Koleksi komponen UI dengan tema monochrome newspaper-style untuk aplikasi Tekateki.io. Komponen-komponen ini dirancang khusus untuk memberikan pengalaman visual yang konsisten dengan estetika koran klasik hitam-putih.

## 🎨 Design Philosophy

- **Monochrome Palette**: Menggunakan skala abu-abu dan hitam-putih untuk menciptakan kontras yang jelas
- **Typography**: Font serif untuk heading dan body text, font monospace untuk kode dan data
- **Paper Texture**: Background patterns yang meniru tekstur kertas koran
- **Minimalist**: Desain yang bersih dan fokus pada konten
- **Accessibility**: Kontras tinggi untuk keterbacaan yang optimal

## 📦 Installation

Komponen-komponen ini sudah terintegrasi dalam proyek. Import sesuai kebutuhan:

```tsx
import {
  MonochromeCard,
  MonochromeButton,
  MonochromeHeading,
  MonochromeText
} from '../components/ui';
```

## 🎯 Color Palette

### Paper Colors (Background)
```css
paper-50:  #fafafa  /* Lightest */
paper-100: #f5f5f5
paper-200: #e5e5e5
paper-300: #d4d4d4
paper-400: #a3a3a3
paper-500: #737373
paper-600: #525252
paper-700: #404040
paper-800: #262626
paper-900: #171717  /* Darkest */
```

### Ink Colors (Text & Borders)
```css
ink-50:  #f8f8f8   /* Lightest */
ink-100: #f0f0f0
ink-200: #e4e4e4
ink-300: #d1d1d1
ink-400: #b4b4b4
ink-500: #9a9a9a
ink-600: #818181
ink-700: #6a6a6a
ink-800: #5a5a5a
ink-900: #4a4a4a   /* Darkest */
```

## 🧩 Components

### Layout Components

#### MonochromePage
Container utama untuk halaman dengan background pattern.

```tsx
<MonochromePage variant="textured">
  {/* Content */}
</MonochromePage>
```

**Props:**
- `variant`: `'default' | 'textured' | 'lined' | 'dotted'`

#### MonochromeContainer
Container responsif dengan max-width.

```tsx
<MonochromeContainer size="lg">
  {/* Content */}
</MonochromeContainer>
```

#### MonochromeSection
Section dengan title dan divider.

```tsx
<MonochromeSection 
  title="Section Title"
  subtitle="Section description"
  variant="bordered"
>
  {/* Content */}
</MonochromeSection>
```

### UI Components

#### MonochromeCard
Card component dengan berbagai variant.

```tsx
<MonochromeCard variant="elevated" className="p-6">
  <MonochromeHeading level={3}>Card Title</MonochromeHeading>
  <MonochromeText>Card content...</MonochromeText>
</MonochromeCard>
```

**Variants:**
- `default`: Basic card dengan border dan shadow
- `elevated`: Card dengan shadow yang lebih prominent
- `outlined`: Card dengan border tebal tanpa background
- `textured`: Card dengan background texture

#### MonochromeButton
Button dengan gaya newspaper.

```tsx
<MonochromeButton variant="primary" size="md">
  Click Me
</MonochromeButton>
```

**Variants:**
- `primary`: Background hitam, text putih
- `secondary`: Background abu-abu, text hitam
- `outline`: Transparent dengan border
- `ghost`: Transparent tanpa border

#### MonochromeInput & MonochromeTextarea
Form inputs dengan styling konsisten.

```tsx
<MonochromeInput 
  label="Name"
  placeholder="Enter your name"
  value={value}
  onChange={setValue}
/>

<MonochromeTextarea 
  label="Message"
  rows={4}
  value={message}
  onChange={setMessage}
/>
```

### Typography Components

#### MonochromeHeading
Heading dengan berbagai level dan variant.

```tsx
<MonochromeHeading level={1} variant="newspaper">
  Main Title
</MonochromeHeading>
```

**Variants:**
- `newspaper`: Bold serif dengan tracking tight
- `serif`: Standard serif
- `mono`: Monospace dengan tracking wide

#### MonochromeText
Text component dengan berbagai variant.

```tsx
<MonochromeText variant="lead" size="lg">
  Lead paragraph text
</MonochromeText>
```

**Variants:**
- `body`: Standard body text
- `caption`: Small caption text
- `lead`: Large lead text
- `mono`: Monospace text

#### MonochromeQuote
Blockquote dengan styling elegant.

```tsx
<MonochromeQuote 
  variant="bordered"
  author="Author Name"
  source="Source"
>
  Quote content here...
</MonochromeQuote>
```

### Crossword Components

#### MonochromeCrosswordGrid
Grid untuk teka-teki silang.

```tsx
<MonochromeCrosswordGrid
  grid={gridData}
  numbers={numberData}
  selectedCell={[1, 1]}
  onCellClick={handleCellClick}
  onCellChange={handleCellChange}
/>
```

#### MonochromeClueList
Daftar clue dengan interaksi.

```tsx
<MonochromeClueList
  title="MENDATAR"
  clues={acrossClues}
  activeClue={activeClueNumber}
  onClueClick={handleClueClick}
/>
```

#### MonochromeCrosswordStats
Statistik permainan.

```tsx
<MonochromeCrosswordStats
  completed={8}
  total={12}
  timeElapsed="05:23"
  hintsUsed={2}
/>
```

## 🎨 Background Patterns

Tersedia 3 pattern background:

1. **paper-texture**: Pattern diamond subtle
2. **paper-lines**: Garis horizontal tipis
3. **paper-dots**: Titik-titik kecil

```css
.bg-paper-texture
.bg-paper-lines  
.bg-paper-dots
```

## 📱 Responsive Design

Semua komponen sudah responsive dengan breakpoint:
- `sm`: 640px+
- `md`: 768px+
- `lg`: 1024px+
- `xl`: 1280px+

## 🔧 Customization

### Menggunakan Utility Classes

```tsx
<MonochromeCard className="p-8 border-2 border-ink-900">
  {/* Custom styling */}
</MonochromeCard>
```

### Theme Utilities

```tsx
import { monochromeUtils } from '../components/ui';

// Menggunakan helper functions
const textColor = monochromeUtils.helpers.getTextColor('dark');
const borderColor = monochromeUtils.helpers.getBorderColor('strong');
```

## 🚀 Demo

Lihat semua komponen dalam aksi di halaman demo:
```
/demo/monochrome
```

## 📝 Best Practices

1. **Konsistensi**: Gunakan variant yang sama untuk komponen sejenis
2. **Kontras**: Pastikan kontras yang cukup antara text dan background
3. **Spacing**: Gunakan spacing yang konsisten (4, 8, 16, 24px)
4. **Typography**: Kombinasikan serif untuk content dan mono untuk data
5. **Hierarchy**: Gunakan level heading yang tepat untuk struktur konten

## 🎯 Examples

### Basic Layout
```tsx
<MonochromePage variant="textured">
  <MonochromeContainer>
    <MonochromeSection title="Welcome" variant="bordered">
      <MonochromeText variant="lead">
        Welcome to our crossword application.
      </MonochromeText>
    </MonochromeSection>
  </MonochromeContainer>
</MonochromePage>
```

### Card Grid
```tsx
<MonochromeGrid cols={3} gap="lg">
  {items.map(item => (
    <MonochromeCard key={item.id} variant="elevated">
      <MonochromeHeading level={4}>{item.title}</MonochromeHeading>
      <MonochromeText>{item.description}</MonochromeText>
    </MonochromeCard>
  ))}
</MonochromeGrid>
```

### Form
```tsx
<form className="space-y-4">
  <MonochromeInput label="Name" required />
  <MonochromeTextarea label="Message" rows={4} />
  <MonochromeButton variant="primary" type="submit">
    Submit
  </MonochromeButton>
</form>
```
