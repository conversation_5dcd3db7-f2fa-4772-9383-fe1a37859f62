import React from 'react';
import toast, { Toaster } from 'react-hot-toast';

/**
 * Toast provider component to be used at the root of the application
 * Provides consistent styling for toast notifications
 */
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          // Default styling for all toasts
          style: {
            background: '#fff',
            color: '#363636',
            boxShadow: '0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05)',
            padding: '16px',
            borderRadius: '8px',
          },
          // Custom styling for different toast types
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10B981',
              secondary: '#fff',
            },
            style: {
              border: '1px solid #D1FAE5',
              background: '#ECFDF5',
            },
          },
          error: {
            duration: 4000,
            iconTheme: {
              primary: '#EF4444',
              secondary: '#fff',
            },
            style: {
              border: '1px solid #FEE2E2',
              background: '#FEF2F2',
            },
          },
          loading: {
            duration: Infinity,
            style: {
              border: '1px solid #E0F2FE',
              background: '#F0F9FF',
            },
          },
        }}
      />
    </>
  );
};

/**
 * Toast utility functions for consistent toast messages
 */
export const toastUtils = {
  /**
   * Show a success toast notification
   * @param message The message to display
   */
  success: (message: string) => {
    return toast.success(message);
  },

  /**
   * Show an error toast notification
   * @param message The message to display
   */
  error: (message: string) => {
    return toast.error(message);
  },

  /**
   * Show an info toast notification
   * @param message The message to display
   */
  info: (message: string) => {
    return toast(message);
  },

  /**
   * Show a loading toast notification
   * @param message The message to display
   * @returns A toast ID that can be used to update or dismiss the toast
   */
  loading: (message: string) => {
    return toast.loading(message);
  },

  /**
   * Update an existing toast notification
   * @param id The ID of the toast to update
   * @param message The new message to display
   * @param type The new type of the toast
   */
  update: (id: string, message: string, type: 'success' | 'error' | 'loading' | 'default' = 'default') => {
    toast.dismiss(id);
    if (type === 'success') return toast.success(message);
    else if (type === 'error') return toast.error(message);
    else if (type === 'loading') return toast.loading(message);
    else return toast(message);
  },

  /**
   * Dismiss a specific toast notification
   * @param id The ID of the toast to dismiss
   */
  dismiss: (id: string) => {
    toast.dismiss(id);
  },

  /**
   * Dismiss all toast notifications
   */
  dismissAll: () => {
    toast.dismiss();
  }
};
