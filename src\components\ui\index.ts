// Monochrome UI Components Export
// This file provides a centralized export for all monochrome UI components

// Layout Components
export {
  MonochromePage,
  MonochromeContainer,
  MonochromeHeader,
  MonochromeSection,
  MonochromeGrid,
  MonochromeModal
} from './MonochromeLayout';

// Basic UI Components
export {
  MonochromeCard,
  MonochromeButton,
  MonochromeInput,
  MonochromeTextarea
} from './MonochromeCard';

// Typography Components
export {
  MonochromeHeading,
  MonochromeText,
  MonochromeQuote,
  MonochromeCode,
  MonochromeBadge,
  MonochromeDivider
} from './MonochromeTypography';

// Crossword-specific Components
export {
  MonochromeCrosswordCell,
  MonochromeCrosswordGrid,
  MonochromeClue,
  MonochromeClueList,
  MonochromeCrosswordStats
} from './MonochromeCrossword';

// Existing components (if any)
export * from './ToastProvider';
export * from './Feedback';

// Type definitions for component props
export type {
  // Layout types
  MonochromePageProps,
  MonochromeContainerProps,
  MonochromeHeaderProps,
  MonochromeSectionProps,
  MonochromeGridProps,
  MonochromeModalProps,
  
  // Basic UI types
  MonochromeCardProps,
  MonochromeButtonProps,
  MonochromeInputProps,
  MonochromeTextareaProps,
  
  // Typography types
  MonochromeHeadingProps,
  MonochromeTextProps,
  MonochromeQuoteProps,
  MonochromeCodeProps,
  MonochromeBadgeProps,
  MonochromeDividerProps,
  
  // Crossword types
  MonochromeCrosswordCellProps,
  MonochromeCrosswordGridProps,
  MonochromeClueProps,
  MonochromeClueListProps,
  MonochromeCrosswordStatsProps
} from './types';

// Utility functions for monochrome theme
export const monochromeUtils = {
  // Color palette helpers
  colors: {
    paper: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
    },
    ink: {
      50: '#f8f8f8',
      100: '#f0f0f0',
      200: '#e4e4e4',
      300: '#d1d1d1',
      400: '#b4b4b4',
      500: '#9a9a9a',
      600: '#818181',
      700: '#6a6a6a',
      800: '#5a5a5a',
      900: '#4a4a4a',
    }
  },
  
  // Common class combinations
  classes: {
    // Card variants
    cardDefault: 'bg-paper-50 border border-paper-200 shadow-paper',
    cardElevated: 'bg-paper-50 border border-paper-200 shadow-paper-lg hover:shadow-paper-xl',
    cardOutlined: 'bg-transparent border-2 border-ink-300 hover:border-ink-500',
    cardTextured: 'bg-paper-50 bg-paper-texture border border-paper-200 shadow-paper',
    
    // Button variants
    buttonPrimary: 'bg-ink-900 text-paper-50 border border-ink-900 hover:bg-ink-800',
    buttonSecondary: 'bg-paper-200 text-ink-900 border border-paper-300 hover:bg-paper-300',
    buttonOutline: 'bg-transparent text-ink-900 border-2 border-ink-300 hover:bg-ink-50',
    buttonGhost: 'bg-transparent text-ink-700 hover:bg-paper-100',
    
    // Typography variants
    headingNewspaper: 'font-serif font-bold text-ink-900 tracking-tight',
    textBody: 'font-serif text-ink-900 leading-relaxed',
    textMono: 'font-mono text-ink-800 tracking-wide',
    
    // Layout variants
    pageDefault: 'min-h-screen bg-paper-50',
    pageTextured: 'min-h-screen bg-paper-50 bg-paper-texture',
    pageLines: 'min-h-screen bg-paper-50 bg-paper-lines',
    pageDots: 'min-h-screen bg-paper-50 bg-paper-dots',
  },
  
  // Helper functions
  helpers: {
    // Get appropriate text color based on background
    getTextColor: (bgShade: 'light' | 'medium' | 'dark') => {
      switch (bgShade) {
        case 'light': return 'text-ink-900';
        case 'medium': return 'text-ink-800';
        case 'dark': return 'text-paper-50';
        default: return 'text-ink-900';
      }
    },
    
    // Get appropriate border color based on context
    getBorderColor: (context: 'subtle' | 'normal' | 'strong') => {
      switch (context) {
        case 'subtle': return 'border-paper-200';
        case 'normal': return 'border-paper-300';
        case 'strong': return 'border-ink-300';
        default: return 'border-paper-300';
      }
    },
    
    // Combine classes safely
    cn: (...classes: (string | undefined | null | false)[]) => {
      return classes.filter(Boolean).join(' ');
    }
  }
};

// Theme configuration object
export const monochromeTheme = {
  name: 'Monochrome Newspaper',
  description: 'A black and white newspaper-inspired theme for crossword applications',
  
  // Base configuration
  config: {
    fontFamily: {
      serif: ['Georgia', 'Times New Roman', 'serif'],
      mono: ['Courier New', 'monospace'],
    },
    
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
    },
    
    borderRadius: {
      none: '0',
      sm: '0.125rem',
      md: '0.25rem',
      lg: '0.5rem',
    },
    
    shadows: {
      paper: '0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
      paperLg: '0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08)',
      paperXl: '0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1)',
    }
  },
  
  // Component defaults
  defaults: {
    card: {
      variant: 'default' as const,
      padding: 'p-6',
    },
    button: {
      variant: 'primary' as const,
      size: 'md' as const,
    },
    heading: {
      variant: 'newspaper' as const,
      level: 1 as const,
    },
    text: {
      variant: 'body' as const,
      size: 'base' as const,
    }
  }
};
