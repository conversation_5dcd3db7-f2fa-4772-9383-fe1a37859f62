// Type definitions for Monochrome UI Components

import { ReactNode } from 'react';

// Base types
export type MonochromeVariant = 'default' | 'elevated' | 'outlined' | 'textured';
export type MonochromeSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type MonochromeColor = 'primary' | 'secondary' | 'muted' | 'accent';

// Layout Component Types
export interface MonochromePageProps {
  children: ReactNode;
  variant?: 'default' | 'textured' | 'lined' | 'dotted';
  className?: string;
}

export interface MonochromeContainerProps {
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export interface MonochromeHeaderProps {
  children: ReactNode;
  variant?: 'default' | 'newspaper' | 'minimal';
  className?: string;
}

export interface MonochromeSectionProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'bordered' | 'elevated';
  className?: string;
}

export interface MonochromeGridProps {
  children: ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export interface MonochromeModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

// Basic UI Component Types
export interface MonochromeCardProps {
  children: ReactNode;
  variant?: MonochromeVariant;
  className?: string;
  onClick?: () => void;
}

export interface MonochromeButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
}

export interface MonochromeInputProps {
  type?: 'text' | 'email' | 'password' | 'number';
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  error?: string;
}

export interface MonochromeTextareaProps {
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  rows?: number;
  className?: string;
  label?: string;
  error?: string;
}

// Typography Component Types
export interface MonochromeHeadingProps {
  children: ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  variant?: 'newspaper' | 'serif' | 'mono';
  className?: string;
}

export interface MonochromeTextProps {
  children: ReactNode;
  variant?: 'body' | 'caption' | 'lead' | 'mono';
  size?: MonochromeSize;
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: MonochromeColor;
  className?: string;
}

export interface MonochromeQuoteProps {
  children: ReactNode;
  author?: string;
  source?: string;
  variant?: 'default' | 'bordered' | 'highlighted';
  className?: string;
}

export interface MonochromeCodeProps {
  children: ReactNode;
  variant?: 'inline' | 'block';
  language?: string;
  className?: string;
}

export interface MonochromeBadgeProps {
  children: ReactNode;
  variant?: 'default' | 'outline' | 'filled' | 'dot';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface MonochromeDividerProps {
  variant?: 'solid' | 'dashed' | 'dotted' | 'double';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  label?: string;
}

// Crossword Component Types
export interface MonochromeCrosswordCellProps {
  value?: string;
  number?: number;
  isBlocked?: boolean;
  isSelected?: boolean;
  isHighlighted?: boolean;
  isCorrect?: boolean;
  isIncorrect?: boolean;
  onClick?: () => void;
  onChange?: (value: string) => void;
  className?: string;
}

export interface MonochromeCrosswordGridProps {
  grid: (string | null)[][];
  numbers?: (number | null)[][];
  selectedCell?: [number, number] | null;
  highlightedCells?: [number, number][];
  correctCells?: [number, number][];
  incorrectCells?: [number, number][];
  onCellClick?: (row: number, col: number) => void;
  onCellChange?: (row: number, col: number, value: string) => void;
  className?: string;
}

export interface MonochromeClueProps {
  number: number;
  clue: string;
  isActive?: boolean;
  isCompleted?: boolean;
  onClick?: () => void;
  className?: string;
}

export interface MonochromeClueListProps {
  title: string;
  clues: Array<{
    number: number;
    clue: string;
    isCompleted?: boolean;
  }>;
  activeClue?: number;
  onClueClick?: (number: number) => void;
  className?: string;
}

export interface MonochromeCrosswordStatsProps {
  completed: number;
  total: number;
  timeElapsed?: string;
  hintsUsed?: number;
  className?: string;
}

// Theme Types
export interface MonochromeColorPalette {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface MonochromeThemeColors {
  paper: MonochromeColorPalette;
  ink: MonochromeColorPalette;
}

export interface MonochromeThemeConfig {
  fontFamily: {
    serif: string[];
    mono: string[];
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
}

export interface MonochromeTheme {
  name: string;
  description: string;
  config: MonochromeThemeConfig;
  defaults: {
    card: {
      variant: MonochromeVariant;
      padding: string;
    };
    button: {
      variant: 'primary' | 'secondary' | 'outline' | 'ghost';
      size: 'sm' | 'md' | 'lg';
    };
    heading: {
      variant: 'newspaper' | 'serif' | 'mono';
      level: 1 | 2 | 3 | 4 | 5 | 6;
    };
    text: {
      variant: 'body' | 'caption' | 'lead' | 'mono';
      size: MonochromeSize;
    };
  };
}

// Utility Types
export type MonochromeClassName = string | undefined | null | false;

export interface MonochromeUtils {
  colors: MonochromeThemeColors;
  classes: Record<string, string>;
  helpers: {
    getTextColor: (bgShade: 'light' | 'medium' | 'dark') => string;
    getBorderColor: (context: 'subtle' | 'normal' | 'strong') => string;
    cn: (...classes: MonochromeClassName[]) => string;
  };
}

// Event Types
export type MonochromeClickHandler = () => void;
export type MonochromeChangeHandler<T = string> = (value: T) => void;
export type MonochromeCellClickHandler = (row: number, col: number) => void;
export type MonochromeCellChangeHandler = (row: number, col: number, value: string) => void;
