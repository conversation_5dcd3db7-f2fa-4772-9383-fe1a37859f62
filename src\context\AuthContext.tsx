import * as React from 'react';
const { createContext, useContext, useReducer, useEffect } = React;
import {
  loginUser as apiLogin,
  registerUser as apiRegister,
  logoutUser as apiLogout,
  fetchCurrentUser as apiFetchCurrentUser,
  loginWithGoogle as apiLoginWithGoogle,
  updateUserProfile as apiUpdateProfile,
  User as ApiUser, // Use User type from api.ts
  LoginCredentials, // Use from api.ts
  RegisterUserData, // Use from api.ts
  GoogleLoginData, // Use from api.ts
  ProfileUpdateData // Use from api.ts
} from '../services/api';

// Define User type (can be expanded, or use directly from api.ts if identical)
// For now, let's assume ApiUser from services/api.ts is what we want
type User = ApiUser;

// Define Auth State
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

// Define Auth Actions
type AuthAction =
  | { type: 'LOGIN_REQUEST' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'REGISTER_REQUEST' }
  | { type: 'REGISTER_SUCCESS'; payload: User } // Or just a success message
  | { type: 'REGISTER_FAILURE'; payload: string }
  | { type: 'LOGOUT_SUCCESS' }
  | { type: 'LOAD_USER_REQUEST' }
  | { type: 'LOAD_USER_SUCCESS'; payload: User }
  | { type: 'LOAD_USER_FAILURE' } // No error message needed, just means no active session
  | { type: 'CLEAR_ERROR' };

// Initial Auth State
const initialAuthState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: true, // Start with loading true to check for existing session
  error: null,
};

// Auth Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_REQUEST':
    case 'REGISTER_REQUEST':
    case 'LOAD_USER_REQUEST':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
    case 'LOAD_USER_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
    case 'REGISTER_FAILURE': // This will be for actual registration step failure
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT_SUCCESS':
    case 'LOAD_USER_FAILURE': // If loading user fails, it means not authenticated
      // localStorage.removeItem('token'); // Not using token for session based auth
      return {
        ...initialAuthState,
        isLoading: false, // Finished loading attempt
      };
    case 'REGISTER_SUCCESS': // This action might now just signify backend registration was ok
      return {
        ...state,
        isLoading: false, // Registration attempt finished
        error: null, // Clear previous errors
        // User and isAuthenticated are not set here, login will handle that.
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Define types for credentials and user data are now imported from services/api.ts

// Create Auth Context
interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<User>; // Returns User on success
  loginWithGoogle: (googleData: GoogleLoginData) => Promise<User>; // Returns User on success
  register: (userData: RegisterUserData) => Promise<User>; // Returns User on success (after auto-login)
  logout: () => Promise<void>;
  updateProfile: (profileData: { displayName?: string; avatarUrl?: string; bio?: string }) => Promise<User>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialAuthState);

  // Load user on mount and set up periodic session check
  useEffect(() => {
    const loadUserOnAppMount = async () => {
      dispatch({ type: 'LOAD_USER_REQUEST' });
      try {
        console.log('Checking for existing user session...');
        const user = await apiFetchCurrentUser();
        if (user) {
          console.log('User session found:', user.username);
          dispatch({ type: 'LOAD_USER_SUCCESS', payload: user });
        } else {
          console.log('No active user session found');
          dispatch({ type: 'LOAD_USER_FAILURE' });
        }
      } catch (error: unknown) {
        console.error('Failed to load user on mount:', error);
        dispatch({ type: 'LOAD_USER_FAILURE' });
      }
    };

    // Load user immediately on mount
    loadUserOnAppMount();

    // Set up periodic session check (every 5 minutes)
    // This helps maintain the session and detect if it expires
    const sessionCheckInterval = setInterval(() => {
      if (state.isAuthenticated) {
        console.log('Performing periodic session check...');
        apiFetchCurrentUser()
          .then(user => {
            if (!user && state.isAuthenticated) {
              console.warn('Session expired, logging out');
              dispatch({ type: 'LOAD_USER_FAILURE' });
            }
          })
          .catch(error => {
            console.error('Session check failed:', error);
            // Only log out if we get a 401 Unauthorized
            if (error.status === 401 && state.isAuthenticated) {
              dispatch({ type: 'LOAD_USER_FAILURE' });
            }
          });
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Clean up interval on unmount
    return () => clearInterval(sessionCheckInterval);
  }, [state.isAuthenticated]);

  const login = async (credentials: LoginCredentials): Promise<User> => {
    dispatch({ type: 'LOGIN_REQUEST' });
    try {
      const user = await apiLogin(credentials);
      dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      return user;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      throw err;
    }
  };

  const register = async (userData: RegisterUserData): Promise<User> => {
    dispatch({ type: 'REGISTER_REQUEST' });
    try {
      await apiRegister(userData);
      // After successful registration, attempt to log in the user
      // This assumes registration does not auto-login on the backend
      // and that registerUser API call returns enough info or just success.
      // If registerUser returns the user object, this explicit login might not be needed.
      // For now, let's assume we need to login to get the full user object and session.
      dispatch({ type: 'REGISTER_SUCCESS', payload: {} as User }); // Temporary payload, login will set user

      // Attempt to login after registration
      const user = await login({ email: userData.email, password: userData.password });
      return user; // login function already dispatches LOGIN_SUCCESS
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      dispatch({ type: 'REGISTER_FAILURE', payload: errorMessage });
      throw err;
    }
  };

  const logout = async () => {
    dispatch({ type: 'LOGIN_REQUEST' }); // Show loading state during logout
    try {
      await apiLogout();
      dispatch({ type: 'LOGOUT_SUCCESS' });
    } catch (err: unknown) {
      // Even if API logout fails, clear frontend state
      console.error("Logout API call failed, logging out locally:", err);
      dispatch({ type: 'LOGOUT_SUCCESS' });
      // Optionally dispatch a specific error for API logout failure if needed
      // dispatch({ type: 'LOGOUT_FAILURE', payload: 'Logout API call failed' });
    }
  };

  const loginWithGoogle = async (googleData: GoogleLoginData): Promise<User> => {
    dispatch({ type: 'LOGIN_REQUEST' });
    try {
      const user = await apiLoginWithGoogle(googleData);
      dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      return user;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Google login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      throw err;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const updateProfile = async (profileData: ProfileUpdateData): Promise<User> => {
    dispatch({ type: 'LOGIN_REQUEST' }); // Reuse LOGIN_REQUEST to show loading state
    try {
      const updatedUser = await apiUpdateProfile(profileData);
      dispatch({ type: 'LOGIN_SUCCESS', payload: updatedUser }); // Update user in state
      return updatedUser;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Profile update failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      throw err;
    }
  };

  return (
    <AuthContext.Provider value={{ ...state, login, loginWithGoogle, register, logout, updateProfile, clearError }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use Auth Context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
