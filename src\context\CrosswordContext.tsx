import * as React from 'react';
const { createContext, useContext, useReducer } = React;
import { CrosswordState, CrosswordAction } from '../types/crossword';
import { findPossiblePlacements, optimizeGrid } from '../utils/crosswordLogic';

// Initial state
const initialState: CrosswordState = {
  gridSize: 15,
  grid: Array(15).fill(null).map(() => Array(15).fill({ char: ' ', wordIds: [] })),
  words: [],
  clues: { across: {}, down: {} },
  wordPositions: [],
  wordNumber: 1,
  selectedWordId: null,
  selectedCell: null,
  selectedDirection: null,
  mode: 'edit',
  hintsRemaining: 3, // Default number of hints
  revealedCells: [],
  progress: 0,
  gameState: 'not-started',
  timeSpent: 0,
  gameStartTime: undefined,
  pausedTime: 0,
};

// Helper function to check if puzzle is completed
function checkPuzzleCompletion(state: CrosswordState, userAnswers: string[][]): boolean {
  if (state.mode !== 'play' || !userAnswers || state.gameState === 'completed') {
    return false;
  }

  let correctCells = 0;
  let totalCells = 0;

  for (let r = 0; r < state.gridSize; r++) {
    for (let c = 0; c < state.gridSize; c++) {
      if (state.grid[r][c].char !== ' ') {
        totalCells++;
        if (userAnswers[r][c] === state.grid[r][c].char) {
          correctCells++;
        }
      }
    }
  }

  return totalCells > 0 && correctCells === totalCells;
}

// Helper function to calculate progress
function calculateProgress(state: CrosswordState, userAnswers: string[][]): number {
  if (!userAnswers) return 0;

  let correctCells = 0;
  let totalCells = 0;

  for (let r = 0; r < state.gridSize; r++) {
    for (let c = 0; c < state.gridSize; c++) {
      if (state.grid[r][c].char !== ' ') {
        totalCells++;
        if (userAnswers[r][c] === state.grid[r][c].char) {
          correctCells++;
        }
      }
    }
  }

  return totalCells > 0 ? Math.round((correctCells / totalCells) * 100) : 0;
}

// Reducer function
function crosswordReducer(state: CrosswordState, action: CrosswordAction): CrosswordState {
  switch (action.type) {
    case 'ADD_WORD': {
      const { word, direction, row, col, clue } = action;
      const upperWord = word.toUpperCase();

      // Validasi posisi
      if (row < 0 || row >= state.gridSize || col < 0 || col >= state.gridSize) {
        return state;
      }

      // Validasi panjang kata
      if (direction === 'across' && col + upperWord.length > state.gridSize) {
        return state;
      }
      if (direction === 'down' && row + upperWord.length > state.gridSize) {
        return state;
      }

      // Validasi tumpang tindih
      let canPlace = true;
      for (let i = 0; i < upperWord.length; i++) {
        const r = direction === 'across' ? row : row + i;
        const c = direction === 'across' ? col + i : col;

        if (state.grid[r][c].char !== ' ' && state.grid[r][c].char !== upperWord[i]) {
          canPlace = false;
          break;
        }
      }

      if (!canPlace) return state;

      // Tambahkan kata ke grid
      const wordId = state.words.length + 1;
      const newGrid = state.grid.map(row => [...row]);

      for (let i = 0; i < upperWord.length; i++) {
        const r = direction === 'across' ? row : row + i;
        const c = direction === 'across' ? col + i : col;

        newGrid[r][c] = {
          char: upperWord[i],
          wordIds: [...newGrid[r][c].wordIds, wordId],
        };
      }

      // Update state
      return {
        ...state,
        grid: newGrid,
        words: [...state.words, upperWord],
        wordPositions: [
          ...state.wordPositions,
          { direction, row, col, number: state.wordNumber }
        ],
        clues: {
          ...state.clues,
          [direction]: {
            ...state.clues[direction],
            [state.wordNumber]: clue || '',
          }
        },
        wordNumber: state.wordNumber + 1,
        selectedWordId: wordId,
      };
    }

    case 'AUTO_PLACE_WORD': {
      const { word, clue } = action;
      const upperWord = word.toUpperCase();

      // Cari semua kemungkinan penempatan
      const placements = findPossiblePlacements(upperWord, state);

      if (placements.length === 0) {
        // Tidak dapat menempatkan kata
        return state;
      }

      // Pilih penempatan terbaik
      const [, direction, row, col] = placements[0];

      // Tambahkan kata ke grid
      const wordId = state.words.length + 1;
      const newGrid = state.grid.map(row => [...row]);

      for (let i = 0; i < upperWord.length; i++) {
        const r = direction === 'across' ? row : row + i;
        const c = direction === 'across' ? col + i : col;

        newGrid[r][c] = {
          char: upperWord[i],
          wordIds: [...newGrid[r][c].wordIds, wordId],
        };
      }

      // Update state
      return {
        ...state,
        grid: newGrid,
        words: [...state.words, upperWord],
        wordPositions: [
          ...state.wordPositions,
          { direction, row, col, number: state.wordNumber }
        ],
        clues: {
          ...state.clues,
          [direction]: {
            ...state.clues[direction],
            [state.wordNumber]: clue || '',
          }
        },
        wordNumber: state.wordNumber + 1,
        selectedWordId: wordId,
      };
    }

    case 'REMOVE_WORD': {
      const { wordId } = action;

      if (wordId < 1 || wordId > state.words.length) {
        return state;
      }

      // Hapus kata dari grid
      const newGrid = state.grid.map(row => [...row]);

      for (let r = 0; r < state.gridSize; r++) {
        for (let c = 0; c < state.gridSize; c++) {
          if (newGrid[r][c].wordIds.includes(wordId)) {
            // Hapus ID kata dari sel
            const newWordIds = newGrid[r][c].wordIds.filter(id => id !== wordId);

            if (newWordIds.length === 0) {
              // Jika tidak ada kata lain di sel ini, kosongkan
              newGrid[r][c] = { char: ' ', wordIds: [] };
            } else {
              // Jika masih ada kata lain, pertahankan karakter
              newGrid[r][c] = { ...newGrid[r][c], wordIds: newWordIds };
            }
          }
        }
      }

      // Hapus kata dan posisinya
      const wordPosition = state.wordPositions[wordId - 1];
      const direction = wordPosition.direction;
      const number = wordPosition.number;

      // Buat clues baru tanpa clue yang dihapus
      const newClues = {
        across: { ...state.clues.across },
        down: { ...state.clues.down }
      };
      delete newClues[direction][number];

      // Update state
      return {
        ...state,
        grid: newGrid,
        words: state.words.filter((_, i) => i !== wordId - 1),
        wordPositions: state.wordPositions.filter((_, i) => i !== wordId - 1),
        clues: newClues,
        selectedWordId: null,
      };
    }

    case 'SELECT_WORD': {
      return {
        ...state,
        selectedWordId: action.wordId,
      };
    }

    case 'SELECT_CELL': {
      const { row, col } = action;
      const wordIds = state.grid[row][col].wordIds;

      if (wordIds.length === 0) {
        return state;
      }

      // Set the selected cell
      const selectedCell: [number, number] = [row, col];

      // Jika ada kata yang dipilih sebelumnya, pilih kata berikutnya di sel
      if (state.selectedWordId && wordIds.includes(state.selectedWordId)) {
        const currentIndex = wordIds.indexOf(state.selectedWordId);
        const nextWordId = wordIds[(currentIndex + 1) % wordIds.length];

        // Get the direction of the next word
        const nextWordPosition = state.wordPositions.find(pos => pos.id === nextWordId);
        const nextDirection = nextWordPosition ? nextWordPosition.direction : state.selectedDirection;

        return {
          ...state,
          selectedWordId: nextWordId,
          selectedCell,
          selectedDirection: nextDirection,
        };
      }

      // Jika tidak ada kata yang dipilih sebelumnya, pilih kata pertama
      const firstWordId = wordIds[0];
      const firstWordPosition = state.wordPositions.find(pos => pos.id === firstWordId);
      const direction = firstWordPosition ? firstWordPosition.direction : 'across';

      return {
        ...state,
        selectedWordId: firstWordId,
        selectedCell,
        selectedDirection: direction,
      };
    }

    case 'SET_GRID_SIZE': {
      const { size } = action;

      if (size < 5 || size > 30) {
        return state;
      }

      // Buat grid baru dengan ukuran yang diinginkan
      const newGrid = Array(size).fill(null).map(() => Array(size).fill({ char: ' ', wordIds: [] }));

      // Salin grid lama ke grid baru jika memungkinkan
      for (let r = 0; r < Math.min(state.gridSize, size); r++) {
        for (let c = 0; c < Math.min(state.gridSize, size); c++) {
          newGrid[r][c] = state.grid[r][c];
        }
      }

      return {
        ...state,
        gridSize: size,
        grid: newGrid,
      };
    }

    case 'OPTIMIZE_GRID': {
      // Implementasi optimasi grid
      return optimizeGrid(state);
    }

    case 'RESET': {
      return {
        ...initialState,
        gridSize: state.gridSize,
        grid: Array(state.gridSize).fill(null).map(() => Array(state.gridSize).fill({ char: ' ', wordIds: [] })),
      };
    }

    case 'SET_MODE': {
      const { mode } = action;

      if (mode === 'play' && !state.userAnswers) {
        // Inisialisasi userAnswers jika beralih ke mode play
        const userAnswers = Array(state.gridSize).fill(null).map(() => Array(state.gridSize).fill(''));
        return {
          ...state,
          mode,
          userAnswers,
        };
      }

      return {
        ...state,
        mode,
      };
    }

    case 'SET_STATE': {
      return action.state;
    }

    case 'UPDATE_USER_ANSWER': {
      const { row, col, value } = action;

      if (!state.userAnswers) {
        return state;
      }

      const newUserAnswers = state.userAnswers.map(r => [...r]);
      newUserAnswers[row][col] = value.toUpperCase();

      // Calculate progress after updating the answer
      const newProgress = calculateProgress(state, newUserAnswers);

      // Check if puzzle is completed
      const isCompleted = checkPuzzleCompletion(state, newUserAnswers);

      return {
        ...state,
        userAnswers: newUserAnswers,
        progress: newProgress,
        gameState: isCompleted ? 'completed' : state.gameState,
      };
    }

    case 'LOAD_PUZZLE': {
      const { payload } = action;
      // Determine the next word number. If wordPositions is empty, start with 1.
      // Otherwise, find the max number in wordPositions and add 1.
      const nextWordNumber = payload.wordPositions.length > 0
        ? Math.max(...payload.wordPositions.map(p => p.number)) + 1
        : 1;

      // Determine the appropriate game state based on progress and completion
      let gameState: 'not-started' | 'playing' | 'paused' | 'completed' = 'not-started';

      // If puzzle is completed (progress = 100% or isSurrendered), set to completed state
      if (payload.progress === 100 || payload.isSurrendered) {
        gameState = 'completed';
      } else if (payload.timeSpent > 0 && payload.progress > 0) {
        // If there's progress but not completed, default to not-started to show pre-game interface
        gameState = 'not-started';
      }

      return {
        ...initialState, // Start from a clean slate for most fields
        gridSize: payload.gridSize,
        grid: payload.grid,
        words: payload.words,
        clues: payload.clues,
        wordPositions: payload.wordPositions,
        wordNumber: nextWordNumber,
        mode: 'play',
        selectedWordId: null,
        userAnswers: payload.userAnswers,
        hintsRemaining: 3, // Default number of hints
        revealedCells: payload.revealedCells,
        progress: payload.progress,
        gameState: gameState, // Use determined game state
        timeSpent: payload.timeSpent || 0, // Use timeSpent from payload if available
        gameStartTime: undefined,
        pausedTime: 0,
        isSurrendered: payload.isSurrendered,
      };
    }

    case 'RESET_USER_ANSWERS': {
      if (state.mode === 'play' && state.userAnswers) {
        return {
          ...state,
          userAnswers: Array(state.gridSize).fill(null).map(() => Array(state.gridSize).fill('')),
          selectedWordId: null, // Also reset selection
          progress: 0, // Reset progress
        };
      }
      return state;
    }

    case 'USE_HINT': {
      if (state.mode !== 'play' || !state.userAnswers || state.hintsRemaining === 0) {
        return state;
      }

      const { row, col } = action;

      // Check if this cell is already revealed or filled correctly
      if (
        state.revealedCells?.some(([r, c]) => r === row && c === col) ||
        state.userAnswers[row][col] === state.grid[row][col].char
      ) {
        return state; // Don't use a hint on already revealed or correct cells
      }

      // Create new userAnswers with the revealed letter
      const newUserAnswers = state.userAnswers.map(r => [...r]);
      newUserAnswers[row][col] = state.grid[row][col].char;

      // Add to revealed cells
      const newRevealedCells = [...(state.revealedCells || []), [row, col] as [number, number]];

      // Calculate new progress
      const newProgress = calculateProgress(state, newUserAnswers);

      // Check if puzzle is completed
      const isCompleted = checkPuzzleCompletion(state, newUserAnswers);

      return {
        ...state,
        userAnswers: newUserAnswers,
        revealedCells: newRevealedCells,
        hintsRemaining: (state.hintsRemaining || 0) - 1,
        progress: newProgress,
        gameState: isCompleted ? 'completed' : state.gameState,
      };
    }

    case 'UPDATE_PROGRESS': {
      if (state.mode !== 'play' || !state.userAnswers) {
        return state;
      }

      const newProgress = calculateProgress(state, state.userAnswers);
      const isCompleted = checkPuzzleCompletion(state, state.userAnswers);

      return {
        ...state,
        progress: newProgress,
        gameState: isCompleted ? 'completed' : state.gameState,
      };
    }

    case 'CHECK_ANSWERS': {
      if (state.mode !== 'play' || !state.userAnswers) {
        return state;
      }

      // Update progress when checking answers
      const newProgress = calculateProgress(state, state.userAnswers);
      const isCompleted = checkPuzzleCompletion(state, state.userAnswers);

      return {
        ...state,
        progress: newProgress,
        gameState: isCompleted ? 'completed' : state.gameState,
      };
    }

    case 'SURRENDER': {
      if (state.mode !== 'play' || !state.userAnswers) {
        return state;
      }

      // Fill in all answers with correct letters
      const newUserAnswers = Array(state.gridSize).fill(null).map((_, r) =>
        Array(state.gridSize).fill(null).map((_, c) =>
          state.grid[r][c].char
        )
      );

      return {
        ...state,
        userAnswers: newUserAnswers,
        progress: 100,
        isSurrendered: true,
      };
    }

    case 'TOGGLE_DIRECTION': {
      // Toggle between 'across' and 'down' directions
      if (!state.selectedWordId) {
        return state;
      }

      // Find the current word position
      const wordPosition = state.wordPositions.find(pos => pos.id === state.selectedWordId);
      if (!wordPosition) {
        return state;
      }

      // Get the current direction
      const currentDirection = wordPosition.direction;

      // Toggle to the opposite direction
      const newDirection = currentDirection === 'across' ? 'down' : 'across';

      // Find a word in the new direction at the current cell
      const [row, col] = state.selectedCell || [wordPosition.row, wordPosition.col];

      // Find all words that include this cell
      const cellWordIds = state.grid[row][col].wordIds;

      // Find a word in the new direction
      for (const wordId of cellWordIds) {
        const pos = state.wordPositions.find(p => p.id === wordId);
        if (pos && pos.direction === newDirection) {
          return {
            ...state,
            selectedWordId: wordId,
            selectedDirection: newDirection
          };
        }
      }

      return state;
    }

    case 'START_GAME': {
      if (state.mode !== 'play') {
        return state;
      }

      const now = Date.now();
      return {
        ...state,
        gameState: 'playing',
        gameStartTime: now,
        timeSpent: state.timeSpent || 0, // Keep existing time if resuming
        pausedTime: 0,
      };
    }

    case 'PAUSE_GAME': {
      if (state.mode !== 'play' || state.gameState !== 'playing') {
        return state;
      }

      return {
        ...state,
        gameState: 'paused',
        pausedTime: Date.now(), // Store when we paused
      };
    }

    case 'RESUME_GAME': {
      if (state.mode !== 'play' || state.gameState !== 'paused') {
        return state;
      }

      const now = Date.now();
      return {
        ...state,
        gameState: 'playing',
        gameStartTime: now, // Reset start time to now for new session
        pausedTime: 0,
      };
    }

    case 'COMPLETE_GAME': {
      if (state.mode !== 'play') {
        return state;
      }

      return {
        ...state,
        gameState: 'completed',
        progress: 100, // Ensure progress is set to 100% on completion
      };
    }

    case 'UPDATE_TIMER': {
      return {
        ...state,
        timeSpent: action.timeSpent,
      };
    }

    case 'INCREMENT_TIMER': {
      return {
        ...state,
        timeSpent: (state.timeSpent || 0) + 1,
      };
    }

    case 'RESET_TIMER': {
      return {
        ...state,
        timeSpent: 0,
        gameStartTime: undefined,
        pausedTime: 0,
        gameState: 'not-started',
      };
    }

    case 'PLAY_AGAIN': {
      if (state.mode !== 'play') {
        return state;
      }

      // Reset everything for a fresh game attempt while keeping puzzle structure
      return {
        ...state,
        userAnswers: Array(state.gridSize).fill(null).map(() => Array(state.gridSize).fill('')),
        revealedCells: [],
        progress: 0,
        gameState: 'not-started',
        timeSpent: 0,
        gameStartTime: undefined,
        pausedTime: 0,
        hintsRemaining: 3, // Reset to default hints
        selectedWordId: null,
        selectedCell: null,
        selectedDirection: null,
        isSurrendered: false,
      };
    }

    default:
      return state;
  }
}

// Create context
const CrosswordContext = createContext<{
  state: CrosswordState;
  dispatch: React.Dispatch<CrosswordAction>;
}>({
  state: initialState,
  dispatch: () => null,
});

// Provider component
export const CrosswordProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(crosswordReducer, initialState);
  const timerRef = React.useRef<NodeJS.Timeout | null>(null);

  // Timer management effect
  React.useEffect(() => {
    if (state.gameState === 'playing') {
      // Start or resume timer - increment every second
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      timerRef.current = setInterval(() => {
        // Use INCREMENT_TIMER action to avoid stale closure
        dispatch({ type: 'INCREMENT_TIMER' });
      }, 1000);
    } else {
      // Pause or stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [state.gameState]);

  return (
    <CrosswordContext.Provider value={{ state, dispatch }}>
      {children}
    </CrosswordContext.Provider>
  );
};

// Hook for using the context
export const useCrossword = () => {
  const context = useContext(CrosswordContext);
  if (!context) {
    throw new Error('useCrossword must be used within a CrosswordProvider');
  }
  return context;
};
