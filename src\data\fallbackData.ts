/**
 * Fallback data for when API calls fail
 * This helps maintain a good user experience even when the backend is unavailable
 */

import { CrosswordListItem } from '../services/api';

// Fallback featured crosswords
export const fallbackFeaturedCrosswords: CrosswordListItem[] = [
  {
    id: '1',
    title: '<PERSON><PERSON><PERSON><PERSON>',
    creator: 'admin',
    difficulty: 'mudah',
    plays: 1245,
    rating: 4.8,
    category_id: 'umum',
    category_name: 'Umum',
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Film Klasik Hollywood',
    creator: 'filmfan42',
    difficulty: 'sedang',
    plays: 987,
    rating: 4.6,
    category_id: 'film',
    category_name: 'Film',
    created_at: new Date().toISOString()
  },
  {
    id: '3',
    title: '<PERSON><PERSON><PERSON>',
    creator: 'techgeek',
    difficulty: 'sulit',
    plays: 756,
    rating: 4.5,
    category_id: 'sains',
    category_name: 'Sains',
    created_at: new Date().toISOString()
  },
  {
    id: '4',
    title: '<PERSON><PERSON><PERSON>',
    creator: 'historybuff',
    difficulty: 'sulit',
    plays: 632,
    rating: 4.7,
    category_id: 'sejarah',
    category_name: '<PERSON><PERSON><PERSON>',
    created_at: new Date().toISOString()
  }
];

// Fallback categories
export const fallbackCategories = [
  {
    id: 'umum',
    name: 'Umum',
    slug: 'umum',
    description: 'Teka-teki silang dengan tema pengetahuan umum',
    created_at: new Date().toISOString(),
    crossword_count: 42
  },
  {
    id: 'film',
    name: 'Film',
    slug: 'film',
    description: 'Teka-teki silang dengan tema film dan perfilman',
    created_at: new Date().toISOString(),
    crossword_count: 28
  },
  {
    id: 'sains',
    name: 'Sains',
    slug: 'sains',
    description: 'Teka-teki silang dengan tema sains dan teknologi',
    created_at: new Date().toISOString(),
    crossword_count: 35
  },
  {
    id: 'sejarah',
    name: 'Sejarah',
    slug: 'sejarah',
    description: 'Teka-teki silang dengan tema sejarah dunia dan Indonesia',
    created_at: new Date().toISOString(),
    crossword_count: 24
  },
  {
    id: 'olahraga',
    name: 'Olahraga',
    slug: 'olahraga',
    description: 'Teka-teki silang dengan tema olahraga',
    created_at: new Date().toISOString(),
    crossword_count: 18
  },
  {
    id: 'musik',
    name: 'Musik',
    slug: 'musik',
    description: 'Teka-teki silang dengan tema musik',
    created_at: new Date().toISOString(),
    crossword_count: 15
  },
  {
    id: 'sastra',
    name: 'Sastra',
    slug: 'sastra',
    description: 'Teka-teki silang dengan tema sastra dan literatur',
    created_at: new Date().toISOString(),
    crossword_count: 12
  },
  {
    id: 'kesehatan',
    name: 'Kesehatan',
    slug: 'kesehatan',
    description: 'Teka-teki silang dengan tema kesehatan',
    created_at: new Date().toISOString(),
    crossword_count: 9
  }
];
