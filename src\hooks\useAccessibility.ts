import { useEffect, useCallback, useRef } from 'react';
import { CrosswordState } from '../types/crossword';

interface AccessibilityOptions {
  state: CrosswordState;
  userAnswers: string[][];
  announceChanges?: boolean;
  enableScreenReader?: boolean;
  enableHighContrast?: boolean;
}

export const useAccessibility = ({
  state,
  userAnswers,
  announceChanges = true,
  enableScreenReader = true,
  enableHighContrast = false
}: AccessibilityOptions) => {
  const announcementRef = useRef<HTMLDivElement | null>(null);
  const lastAnnouncementRef = useRef<string>('');

  // Create screen reader announcement area
  useEffect(() => {
    if (!enableScreenReader) return;

    const existingAnnouncer = document.getElementById('crossword-announcer');
    if (existingAnnouncer) return;

    const announcer = document.createElement('div');
    announcer.id = 'crossword-announcer';
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `;
    
    document.body.appendChild(announcer);
    announcementRef.current = announcer;

    return () => {
      if (announcer.parentNode) {
        announcer.parentNode.removeChild(announcer);
      }
    };
  }, [enableScreenReader]);

  // Announce message to screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!enableScreenReader || !announcementRef.current || message === lastAnnouncementRef.current) return;

    lastAnnouncementRef.current = message;
    announcementRef.current.setAttribute('aria-live', priority);
    announcementRef.current.textContent = message;

    // Clear after announcement to allow repeated messages
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = '';
      }
    }, 1000);
  }, [enableScreenReader]);

  // Get cell description for screen readers
  const getCellDescription = useCallback((row: number, col: number): string => {
    const cell = state.grid[row]?.[col];
    if (!cell || cell.char === ' ') return 'Sel kosong';

    const userAnswer = userAnswers[row]?.[col] || '';
    const isCorrect = userAnswer.toLowerCase() === cell.char.toLowerCase();
    
    // Get word numbers for this cell
    const wordNumbers: string[] = [];
    cell.wordIds.forEach(wordId => {
      const wordPos = state.wordPositions.find(wp => wp.id === wordId);
      if (wordPos) {
        wordNumbers.push(`${wordPos.number} ${wordPos.direction === 'across' ? 'mendatar' : 'menurun'}`);
      }
    });

    const wordInfo = wordNumbers.length > 0 ? `, bagian dari kata ${wordNumbers.join(' dan ')}` : '';
    const answerInfo = userAnswer ? `, berisi huruf ${userAnswer}` : ', kosong';
    const statusInfo = userAnswer ? (isCorrect ? ', benar' : ', salah') : '';

    return `Baris ${row + 1}, kolom ${col + 1}${wordInfo}${answerInfo}${statusInfo}`;
  }, [state.grid, state.wordPositions, userAnswers]);

  // Get word description for screen readers
  const getWordDescription = useCallback((wordId: number): string => {
    const wordPos = state.wordPositions.find(wp => wp.id === wordId);
    const word = state.words.find(w => w.id === wordId);
    
    if (!wordPos || !word) return '';

    const clue = wordPos.direction === 'across' 
      ? state.clues.across[wordPos.number]
      : state.clues.down[wordPos.number];

    const direction = wordPos.direction === 'across' ? 'mendatar' : 'menurun';
    const length = word.word.length;
    
    // Count filled letters
    let filledCount = 0;
    for (let i = 0; i < length; i++) {
      const row = wordPos.direction === 'across' ? wordPos.row : wordPos.row + i;
      const col = wordPos.direction === 'across' ? wordPos.col + i : wordPos.col;
      if (userAnswers[row]?.[col]) filledCount++;
    }

    return `Kata ${wordPos.number} ${direction}, ${length} huruf, ${filledCount} huruf terisi. Petunjuk: ${clue}`;
  }, [state.wordPositions, state.words, state.clues, userAnswers]);

  // Announce cell selection
  const announceCellSelection = useCallback((row: number, col: number) => {
    if (!announceChanges) return;
    
    const description = getCellDescription(row, col);
    announce(description);
  }, [announceChanges, getCellDescription, announce]);

  // Announce word selection
  const announceWordSelection = useCallback((wordId: number) => {
    if (!announceChanges) return;
    
    const description = getWordDescription(wordId);
    announce(description);
  }, [announceChanges, getWordDescription, announce]);

  // Announce letter input
  const announceLetterInput = useCallback((letter: string, row: number, col: number) => {
    if (!announceChanges) return;
    
    const cell = state.grid[row]?.[col];
    const isCorrect = cell && letter.toLowerCase() === cell.char.toLowerCase();
    const status = isCorrect ? 'benar' : 'salah';
    
    announce(`Huruf ${letter} dimasukkan, ${status}`, 'assertive');
  }, [announceChanges, state.grid, announce]);

  // Announce word completion
  const announceWordCompletion = useCallback((wordId: number, isCorrect: boolean) => {
    if (!announceChanges) return;
    
    const wordPos = state.wordPositions.find(wp => wp.id === wordId);
    if (!wordPos) return;

    const direction = wordPos.direction === 'across' ? 'mendatar' : 'menurun';
    const status = isCorrect ? 'benar' : 'salah';
    
    announce(`Kata ${wordPos.number} ${direction} selesai, ${status}`, 'assertive');
  }, [announceChanges, state.wordPositions, announce]);

  // Announce game state changes
  const announceGameState = useCallback((gameState: string) => {
    if (!announceChanges) return;
    
    const messages = {
      'playing': 'Permainan dimulai',
      'paused': 'Permainan dijeda',
      'completed': 'Selamat! Teka-teki selesai',
      'not-started': 'Permainan belum dimulai'
    };
    
    const message = messages[gameState as keyof typeof messages];
    if (message) {
      announce(message, 'assertive');
    }
  }, [announceChanges, announce]);

  // Announce hint usage
  const announceHint = useCallback((hintType: string, wordId?: number) => {
    if (!announceChanges) return;
    
    let message = 'Bantuan digunakan';
    
    if (wordId) {
      const wordPos = state.wordPositions.find(wp => wp.id === wordId);
      if (wordPos) {
        const direction = wordPos.direction === 'across' ? 'mendatar' : 'menurun';
        message = `Bantuan untuk kata ${wordPos.number} ${direction}`;
      }
    }
    
    announce(message, 'assertive');
  }, [announceChanges, state.wordPositions, announce]);

  // Toggle high contrast mode
  const toggleHighContrast = useCallback(() => {
    const body = document.body;
    const hasHighContrast = body.classList.contains('high-contrast');
    
    if (hasHighContrast) {
      body.classList.remove('high-contrast');
      announce('Mode kontras tinggi dinonaktifkan');
    } else {
      body.classList.add('high-contrast');
      announce('Mode kontras tinggi diaktifkan');
    }
  }, [announce]);

  // Set up high contrast styles
  useEffect(() => {
    if (!enableHighContrast) return;

    const style = document.createElement('style');
    style.id = 'high-contrast-styles';
    style.textContent = `
      .high-contrast {
        --ink-900: #000000 !important;
        --ink-800: #1a1a1a !important;
        --ink-700: #333333 !important;
        --ink-600: #4d4d4d !important;
        --ink-500: #666666 !important;
        --ink-400: #808080 !important;
        --ink-300: #999999 !important;
        --ink-200: #cccccc !important;
        --ink-100: #e6e6e6 !important;
        --newsprint: #ffffff !important;
      }
      
      .high-contrast .crossword-cell {
        border: 3px solid #000000 !important;
      }
      
      .high-contrast .crossword-cell.selected {
        background-color: #ffff00 !important;
        color: #000000 !important;
      }
      
      .high-contrast .crossword-cell.highlighted {
        background-color: #cccccc !important;
        color: #000000 !important;
      }
    `;
    
    document.head.appendChild(style);

    return () => {
      const existingStyle = document.getElementById('high-contrast-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [enableHighContrast]);

  return {
    announce,
    getCellDescription,
    getWordDescription,
    announceCellSelection,
    announceWordSelection,
    announceLetterInput,
    announceWordCompletion,
    announceGameState,
    announceHint,
    toggleHighContrast
  };
};
