import { useState, useCallback } from 'react';
import { toastUtils } from '../components/ui/ToastProvider';
import {
  AppError,
  ErrorType,
  createAppError,
  handleFetchError,
  showErrorToast as showAppErrorToast
} from '../utils/errorHandler';

interface AsyncOperationOptions {
  showLoadingToast?: boolean;
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  loadingMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  retryOnNetworkError?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: AppError) => void;
}

/**
 * Hook for handling asynchronous operations with standardized loading and error states
 *
 * @param asyncFunction The async function to execute
 * @param options Configuration options for toast notifications
 * @returns Object containing loading state, error state, and execute function
 */
export function useAsyncOperation<T, P extends any[]>(
  asyncFunction: (...args: P) => Promise<T>,
  options: AsyncOperationOptions = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const [data, setData] = useState<T | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const {
    showLoadingToast = false,
    showSuccessToast = false,
    showErrorToast = true,
    loadingMessage = 'Memuat...',
    successMessage = 'Operasi berhasil',
    errorMessage = 'Terjadi kesalahan. Silakan coba lagi.',
    retryOnNetworkError = true,
    maxRetries = 2,
    retryDelay = 1000,
    onError
  } = options;

  const execute = useCallback(
    async (...args: P): Promise<T | null> => {
      setLoading(true);
      setError(null);

      let toastId;
      if (showLoadingToast) {
        toastId = toastUtils.loading(loadingMessage);
      }

      try {
        const result = await asyncFunction(...args);
        setData(result);
        setRetryCount(0); // Reset retry count on success

        if (showLoadingToast && toastId) {
          if (showSuccessToast) {
            toastUtils.update(toastId, successMessage, 'success');
          } else {
            toastUtils.dismiss(toastId);
          }
        } else if (showSuccessToast) {
          toastUtils.success(successMessage);
        }

        setLoading(false);
        return result;
      } catch (err) {
        // Convert to AppError
        let appError: AppError;

        if (err instanceof Error) {
          if (err.name === 'TypeError' && err.message === 'Failed to fetch') {
            appError = createAppError(
              ErrorType.NETWORK,
              'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
              undefined,
              err
            );
          } else if (err.name === 'AbortError') {
            appError = createAppError(
              ErrorType.TIMEOUT,
              'Permintaan memakan waktu terlalu lama. Silakan coba lagi.',
              undefined,
              err
            );
          } else if ('type' in err && Object.values(ErrorType).includes((err as any).type)) {
            appError = err as unknown as AppError;
          } else {
            appError = createAppError(
              ErrorType.UNKNOWN,
              err.message || errorMessage,
              undefined,
              err
            );
          }
        } else {
          appError = createAppError(
            ErrorType.UNKNOWN,
            typeof err === 'string' ? err : errorMessage,
            undefined,
            err
          );
        }

        // Handle network errors with retry logic
        if (retryOnNetworkError &&
            appError.type === ErrorType.NETWORK &&
            retryCount < maxRetries) {
          setRetryCount(prev => prev + 1);

          // Show retry toast
          if (showLoadingToast && toastId) {
            toastUtils.update(
              toastId,
              `Mencoba menghubungkan kembali... (${retryCount + 1}/${maxRetries})`,
              'loading'
            );
          }

          // Retry after delay
          setTimeout(() => {
            execute(...args);
          }, retryDelay);

          return null;
        }

        // Set error state
        setError(appError);

        // Call onError callback if provided
        if (onError) {
          onError(appError);
        }

        // Show error toast
        if (showLoadingToast && toastId) {
          if (showErrorToast) {
            toastUtils.update(toastId, appError.message, 'error');
          } else {
            toastUtils.dismiss(toastId);
          }
        } else if (showErrorToast) {
          showAppErrorToast(appError);
        }

        setLoading(false);
        return null;
      }
    },
    [
      asyncFunction,
      showLoadingToast,
      showSuccessToast,
      showErrorToast,
      loadingMessage,
      successMessage,
      errorMessage,
      retryOnNetworkError,
      maxRetries,
      retryDelay,
      retryCount,
      onError
    ]
  );

  return {
    loading,
    error,
    data,
    execute,
    retry: () => {
      setRetryCount(0);
      return execute();
    }
  };
}

/**
 * Utility function to wrap an async function with loading toast
 * Useful for one-off operations where you don't need to track loading state
 *
 * @param asyncFunction The async function to execute
 * @param options Configuration options for toast notifications
 * @returns A wrapped function that shows toast notifications
 */
export function withAsyncFeedback<T, P extends any[]>(
  asyncFunction: (...args: P) => Promise<T>,
  options: AsyncOperationOptions = {}
) {
  const {
    showLoadingToast = true,
    showSuccessToast = true,
    showErrorToast = true,
    loadingMessage = 'Memuat...',
    successMessage = 'Operasi berhasil',
    errorMessage = 'Terjadi kesalahan. Silakan coba lagi.',
    retryOnNetworkError = true,
    maxRetries = 2,
    retryDelay = 1000,
    onError
  } = options;

  let retryCount = 0;

  const executeWithRetry = async (...args: P): Promise<T | null> => {
    let toastId;
    if (showLoadingToast) {
      toastId = toastUtils.loading(loadingMessage);
    }

    try {
      const result = await asyncFunction(...args);

      // Reset retry count on success
      retryCount = 0;

      if (showLoadingToast && toastId) {
        if (showSuccessToast) {
          toastUtils.update(toastId, successMessage, 'success');
        } else {
          toastUtils.dismiss(toastId);
        }
      } else if (showSuccessToast) {
        toastUtils.success(successMessage);
      }

      return result;
    } catch (err) {
      // Convert to AppError
      let appError: AppError;

      if (err instanceof Error) {
        if (err.name === 'TypeError' && err.message === 'Failed to fetch') {
          appError = createAppError(
            ErrorType.NETWORK,
            'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
            undefined,
            err
          );
        } else if (err.name === 'AbortError') {
          appError = createAppError(
            ErrorType.TIMEOUT,
            'Permintaan memakan waktu terlalu lama. Silakan coba lagi.',
            undefined,
            err
          );
        } else if ('type' in err && Object.values(ErrorType).includes((err as any).type)) {
          appError = err as unknown as AppError;
        } else {
          appError = createAppError(
            ErrorType.UNKNOWN,
            err.message || errorMessage,
            undefined,
            err
          );
        }
      } else {
        appError = createAppError(
          ErrorType.UNKNOWN,
          typeof err === 'string' ? err : errorMessage,
          undefined,
          err
        );
      }

      // Handle network errors with retry logic
      if (retryOnNetworkError &&
          appError.type === ErrorType.NETWORK &&
          retryCount < maxRetries) {
        retryCount++;

        // Show retry toast
        if (showLoadingToast && toastId) {
          toastUtils.update(
            toastId,
            `Mencoba menghubungkan kembali... (${retryCount}/${maxRetries})`,
            'loading'
          );
        }

        // Retry after delay
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(executeWithRetry(...args));
          }, retryDelay);
        });
      }

      // Call onError callback if provided
      if (onError) {
        onError(appError);
      }

      // Show error toast
      if (showLoadingToast && toastId) {
        if (showErrorToast) {
          toastUtils.update(toastId, appError.message, 'error');
        } else {
          toastUtils.dismiss(toastId);
        }
      } else if (showErrorToast) {
        showAppErrorToast(appError);
      }

      return null;
    }
  };

  return executeWithRetry;
}
