import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppError, ErrorType, createAppError } from '../utils/errorHandler';

interface ErrorHandlerOptions {
  redirectOnNotFound?: boolean;
  notFoundRedirectPath?: string;
  redirectOnAuthError?: boolean;
  authRedirectPath?: string;
}

/**
 * Custom hook for handling errors in components
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const {
    redirectOnNotFound = true,
    notFoundRedirectPath = '/',
    redirectOnAuthError = true,
    authRedirectPath = '/login'
  } = options;

  const [error, setError] = useState<AppError | null>(null);
  const navigate = useNavigate();

  /**
   * Handle an error
   */
  const handleError = useCallback((err: unknown) => {
    // If it's already an AppError, use it directly
    if (err && typeof err === 'object' && 'type' in err && 'message' in err) {
      setError(err as AppError);
      
      // Handle redirects based on error type
      if (redirectOnNotFound && (err as AppError).type === ErrorType.NOT_FOUND) {
        navigate(notFoundRedirectPath);
        return;
      }
      
      if (redirectOnAuthError && 
          ((err as AppError).type === ErrorType.AUTHENTICATION || 
           (err as AppError).type === ErrorType.AUTHORIZATION)) {
        navigate(authRedirectPath, { 
          state: { from: window.location.pathname } 
        });
        return;
      }
      
      return;
    }
    
    // Convert Error object to AppError
    if (err instanceof Error) {
      const appError = createAppError(
        ErrorType.UNKNOWN,
        err.message,
        undefined,
        err
      );
      setError(appError);
      return;
    }
    
    // Handle other types of errors
    const message = err ? String(err) : 'Terjadi kesalahan yang tidak diketahui';
    setError(createAppError(ErrorType.UNKNOWN, message));
  }, [navigate, redirectOnNotFound, notFoundRedirectPath, redirectOnAuthError, authRedirectPath]);

  /**
   * Clear the current error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Wrap an async function with error handling
   */
  const withErrorHandling = useCallback(<T extends any[], R>(
    fn: (...args: T) => Promise<R>
  ) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args);
      } catch (err) {
        handleError(err);
        return null;
      }
    };
  }, [handleError]);

  return {
    error,
    handleError,
    clearError,
    withErrorHandling
  };
}

export default useErrorHandler;
