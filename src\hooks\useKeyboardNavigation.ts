import { useEffect, useCallback, useRef } from 'react';
import { GameState } from '../types/crossword';

interface KeyboardNavigationOptions {
  gameState: GameState;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onMoveLeft: () => void;
  onMoveRight: () => void;
  onToggleDirection: () => void;
  onToggleFocusMode: () => void;
  onTogglePause: () => void;
  onUseHint: () => void;
  onCheckAnswers: () => void;
  onDeleteLetter: () => void;
  onInputLetter: (letter: string) => void;
  onSelectNextWord: () => void;
  onSelectPrevWord: () => void;
  isEnabled?: boolean;
}

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  description: string;
  action: () => void;
  category: 'navigation' | 'game' | 'input' | 'system';
}

export const useKeyboardNavigation = ({
  gameState,
  onMoveUp,
  onMoveDown,
  onMoveLeft,
  onMoveRight,
  onToggleDirection,
  onToggleFocusMode,
  onTogglePause,
  onUseHint,
  onCheckAnswers,
  onDeleteLetter,
  onInputLetter,
  onSelectNextWord,
  onSelectPrevWord,
  isEnabled = true
}: KeyboardNavigationOptions) => {
  const shortcutsRef = useRef<KeyboardShortcut[]>([]);
  const helpVisibleRef = useRef(false);

  // Define keyboard shortcuts
  const shortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'ArrowUp',
      description: 'Pindah ke atas',
      action: onMoveUp,
      category: 'navigation'
    },
    {
      key: 'ArrowDown',
      description: 'Pindah ke bawah',
      action: onMoveDown,
      category: 'navigation'
    },
    {
      key: 'ArrowLeft',
      description: 'Pindah ke kiri',
      action: onMoveLeft,
      category: 'navigation'
    },
    {
      key: 'ArrowRight',
      description: 'Pindah ke kanan',
      action: onMoveRight,
      category: 'navigation'
    },
    {
      key: 'Tab',
      description: 'Pindah ke kata berikutnya',
      action: onSelectNextWord,
      category: 'navigation'
    },
    {
      key: 'Tab',
      shiftKey: true,
      description: 'Pindah ke kata sebelumnya',
      action: onSelectPrevWord,
      category: 'navigation'
    },
    {
      key: ' ',
      description: 'Ubah arah (mendatar/menurun)',
      action: onToggleDirection,
      category: 'navigation'
    },

    // Game control shortcuts
    {
      key: 'f',
      ctrlKey: true,
      description: 'Mode Fokus',
      action: onToggleFocusMode,
      category: 'system'
    },
    {
      key: 'p',
      ctrlKey: true,
      description: 'Jeda/Lanjutkan',
      action: onTogglePause,
      category: 'game'
    },
    {
      key: 'h',
      ctrlKey: true,
      description: 'Gunakan Bantuan',
      action: onUseHint,
      category: 'game'
    },
    {
      key: 'Enter',
      ctrlKey: true,
      description: 'Periksa Jawaban',
      action: onCheckAnswers,
      category: 'game'
    },

    // Input shortcuts
    {
      key: 'Backspace',
      description: 'Hapus huruf',
      action: onDeleteLetter,
      category: 'input'
    },
    {
      key: 'Delete',
      description: 'Hapus huruf',
      action: onDeleteLetter,
      category: 'input'
    },

    // Help shortcut
    {
      key: '?',
      description: 'Tampilkan bantuan keyboard',
      action: () => toggleKeyboardHelp(),
      category: 'system'
    },
    {
      key: 'F1',
      description: 'Tampilkan bantuan keyboard',
      action: () => toggleKeyboardHelp(),
      category: 'system'
    }
  ];

  shortcutsRef.current = shortcuts;

  // Toggle keyboard help modal
  const toggleKeyboardHelp = useCallback(() => {
    helpVisibleRef.current = !helpVisibleRef.current;
    
    // Create and show help modal
    if (helpVisibleRef.current) {
      showKeyboardHelp();
    } else {
      hideKeyboardHelp();
    }
  }, []);

  // Show keyboard help modal
  const showKeyboardHelp = useCallback(() => {
    const existingModal = document.getElementById('keyboard-help-modal');
    if (existingModal) return;

    const modal = document.createElement('div');
    modal.id = 'keyboard-help-modal';
    modal.className = 'fixed inset-0 bg-ink-900 bg-opacity-75 flex items-center justify-center z-50';
    
    const categories = {
      navigation: 'Navigasi',
      game: 'Kontrol Game',
      input: 'Input',
      system: 'Sistem'
    };

    const helpContent = `
      <div class="bg-newsprint p-6 rounded-sm border-4 border-ink-900 shadow-paper-xl max-w-2xl max-h-[80vh] overflow-auto">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-bold text-ink-900 font-serif uppercase">Bantuan Keyboard</h2>
          <button id="close-help" class="text-ink-900 hover:text-ink-700 text-2xl font-bold">&times;</button>
        </div>
        
        ${Object.entries(categories).map(([category, title]) => `
          <div class="mb-6">
            <h3 class="text-lg font-bold text-ink-900 font-serif mb-3 border-b-2 border-ink-900 pb-1">${title}</h3>
            <div class="space-y-2">
              ${shortcuts
                .filter(s => s.category === category)
                .map(shortcut => {
                  const keyDisplay = [
                    shortcut.ctrlKey ? 'Ctrl' : '',
                    shortcut.shiftKey ? 'Shift' : '',
                    shortcut.altKey ? 'Alt' : '',
                    shortcut.key === ' ' ? 'Space' : shortcut.key
                  ].filter(Boolean).join(' + ');
                  
                  return `
                    <div class="flex justify-between items-center py-1">
                      <span class="font-serif text-ink-800">${shortcut.description}</span>
                      <kbd class="bg-ink-900 text-newsprint px-2 py-1 rounded-sm font-mono text-sm">${keyDisplay}</kbd>
                    </div>
                  `;
                }).join('')}
            </div>
          </div>
        `).join('')}
        
        <div class="text-center mt-6">
          <p class="text-ink-600 font-serif text-sm">Tekan ESC atau klik di luar untuk menutup</p>
        </div>
      </div>
    `;

    modal.innerHTML = helpContent;
    document.body.appendChild(modal);

    // Add event listeners
    const closeBtn = modal.querySelector('#close-help');
    const handleClose = () => {
      hideKeyboardHelp();
    };

    closeBtn?.addEventListener('click', handleClose);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) handleClose();
    });

    // ESC to close
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
        document.removeEventListener('keydown', handleEsc);
      }
    };
    document.addEventListener('keydown', handleEsc);
  }, [shortcuts]);

  // Hide keyboard help modal
  const hideKeyboardHelp = useCallback(() => {
    const modal = document.getElementById('keyboard-help-modal');
    if (modal) {
      modal.remove();
      helpVisibleRef.current = false;
    }
  }, []);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isEnabled || gameState === 'not-started') return;

    // Don't handle if user is typing in an input field
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }

    // Find matching shortcut
    const matchingShortcut = shortcutsRef.current.find(shortcut => {
      return (
        shortcut.key === event.key &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.shiftKey === event.shiftKey &&
        !!shortcut.altKey === event.altKey
      );
    });

    if (matchingShortcut) {
      event.preventDefault();
      matchingShortcut.action();
      return;
    }

    // Handle letter input
    if (event.key.length === 1 && /[a-zA-Z]/.test(event.key) && !event.ctrlKey && !event.altKey) {
      event.preventDefault();
      onInputLetter(event.key.toUpperCase());
    }
  }, [isEnabled, gameState, onInputLetter]);

  // Set up keyboard event listeners
  useEffect(() => {
    if (!isEnabled) return;

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      hideKeyboardHelp();
    };
  }, [handleKeyDown, isEnabled, hideKeyboardHelp]);

  return {
    shortcuts: shortcutsRef.current,
    showKeyboardHelp,
    hideKeyboardHelp,
    toggleKeyboardHelp
  };
};
