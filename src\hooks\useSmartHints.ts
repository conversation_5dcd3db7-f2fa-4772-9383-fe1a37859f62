import { useMemo, useCallback } from 'react';
import { CrosswordState } from '../types/crossword';

interface WordDifficulty {
  wordNumber: number; // Use word number instead of wordId
  direction: 'across' | 'down';
  difficulty: number;
  intersections: number;
  length: number;
  completedLetters: number;
  priority: number;
  row: number;
  col: number;
}

interface HintSuggestion {
  type: 'letter' | 'word' | 'intersection';
  wordNumber: number; // Use word number instead of wordId
  direction: 'across' | 'down';
  position?: { row: number; col: number };
  reason: string;
  priority: number;
}

export const useSmartHints = (state: CrosswordState, userAnswers: string[][]) => {
  // Calculate word difficulty and priority
  const wordDifficulties = useMemo(() => {
    if (!state.wordPositions || !userAnswers || !state.grid) return [];

    return state.wordPositions.map((wordPos): WordDifficulty => {
      // Get the word from the grid based on position
      let wordText = '';
      const wordCells: { row: number; col: number }[] = [];

      // Calculate word length and get cells
      let currentRow = wordPos.row;
      let currentCol = wordPos.col;

      while (currentRow < state.grid.length &&
             currentCol < state.grid[0].length &&
             state.grid[currentRow]?.[currentCol]?.char &&
             state.grid[currentRow][currentCol].char !== ' ') {
        wordCells.push({ row: currentRow, col: currentCol });
        wordText += state.grid[currentRow][currentCol].char;

        if (wordPos.direction === 'across') {
          currentCol++;
        } else {
          currentRow++;
        }
      }

      if (wordText.length === 0) {
        return {
          wordNumber: wordPos.number,
          direction: wordPos.direction,
          difficulty: 0,
          intersections: 0,
          length: 0,
          completedLetters: 0,
          priority: 0,
          row: wordPos.row,
          col: wordPos.col
        };
      }

      // Count intersections with other words
      let intersections = 0;

      // Count intersections
      wordCells.forEach(cell => {
        const gridCell = state.grid[cell.row]?.[cell.col];
        if (gridCell && gridCell.wordIds.length > 1) {
          intersections++;
        }
      });

      // Count completed letters
      let completedLetters = 0;
      wordCells.forEach(cell => {
        const userAnswer = userAnswers[cell.row]?.[cell.col];
        const correctAnswer = state.grid[cell.row]?.[cell.col]?.char;
        if (userAnswer && userAnswer.toLowerCase() === correctAnswer?.toLowerCase()) {
          completedLetters++;
        }
      });

      // Calculate difficulty score
      const lengthFactor = Math.max(1, wordText.length - 3); // Longer words are harder
      const intersectionFactor = intersections * 2; // More intersections = more helpful
      const completionFactor = completedLetters > 0 ? completedLetters * 3 : 0; // Partially completed words get priority

      const difficulty = lengthFactor + intersectionFactor;
      const priority = intersectionFactor + completionFactor + (wordText.length > 6 ? 5 : 0);

      return {
        wordNumber: wordPos.number,
        direction: wordPos.direction,
        difficulty,
        intersections,
        length: wordText.length,
        completedLetters,
        priority,
        row: wordPos.row,
        col: wordPos.col
      };
    }).sort((a, b) => b.priority - a.priority);
  }, [state.wordPositions, state.words, state.grid, userAnswers]);

  // Get smart hint suggestions
  const getHintSuggestions = useCallback((): HintSuggestion[] => {
    if (!state.wordPositions || !userAnswers || wordDifficulties.length === 0) return [];

    const suggestions: HintSuggestion[] = [];

    // Strategy 1: Suggest intersection letters (highest priority)
    wordDifficulties.slice(0, 3).forEach(wordDiff => {
      for (let i = 0; i < wordDiff.length; i++) {
        const row = wordDiff.direction === 'across' ? wordDiff.row : wordDiff.row + i;
        const col = wordDiff.direction === 'across' ? wordDiff.col + i : wordDiff.col;

        const gridCell = state.grid[row]?.[col];
        const userAnswer = userAnswers[row]?.[col];

        // If cell is empty and has intersections
        if (gridCell && !userAnswer && gridCell.wordIds && gridCell.wordIds.length > 1) {
          suggestions.push({
            type: 'intersection',
            wordNumber: wordDiff.wordNumber,
            direction: wordDiff.direction,
            position: { row, col },
            reason: `Huruf persilangan untuk kata ${wordDiff.wordNumber} ${wordDiff.direction === 'across' ? 'mendatar' : 'menurun'}`,
            priority: 100 + wordDiff.intersections * 10
          });
        }
      }
    });

    // Strategy 2: Suggest first letter of high-priority words
    wordDifficulties.slice(0, 5).forEach(wordDiff => {
      const firstRow = wordDiff.row;
      const firstCol = wordDiff.col;
      const userAnswer = userAnswers[firstRow]?.[firstCol];

      if (!userAnswer && wordDiff.completedLetters === 0) {
        suggestions.push({
          type: 'letter',
          wordNumber: wordDiff.wordNumber,
          direction: wordDiff.direction,
          position: { row: firstRow, col: firstCol },
          reason: `Huruf pertama kata ${wordDiff.wordNumber} ${wordDiff.direction === 'across' ? 'mendatar' : 'menurun'}`,
          priority: 80 + wordDiff.priority
        });
      }
    });

    // Strategy 3: Suggest completing partially filled words
    wordDifficulties.forEach(wordDiff => {
      if (wordDiff.completedLetters > 0 && wordDiff.completedLetters < wordDiff.length) {
        // Find next empty letter
        for (let i = 0; i < wordDiff.length; i++) {
          const row = wordDiff.direction === 'across' ? wordDiff.row : wordDiff.row + i;
          const col = wordDiff.direction === 'across' ? wordDiff.col + i : wordDiff.col;
          const userAnswer = userAnswers[row]?.[col];

          if (!userAnswer) {
            suggestions.push({
              type: 'letter',
              wordNumber: wordDiff.wordNumber,
              direction: wordDiff.direction,
              position: { row, col },
              reason: `Lanjutkan kata ${wordDiff.wordNumber} ${wordDiff.direction === 'across' ? 'mendatar' : 'menurun'} yang sudah dimulai`,
              priority: 90 + wordDiff.completedLetters * 5
            });
            break; // Only suggest one letter per word
          }
        }
      }
    });

    // Strategy 4: Suggest revealing entire short words
    wordDifficulties.forEach(wordDiff => {
      if (wordDiff.length <= 4 && wordDiff.completedLetters === 0) {
        suggestions.push({
          type: 'word',
          wordNumber: wordDiff.wordNumber,
          direction: wordDiff.direction,
          reason: `Buka kata pendek ${wordDiff.length} huruf`,
          priority: 60 + (5 - wordDiff.length) * 10
        });
      }
    });

    return suggestions.sort((a, b) => b.priority - a.priority);
  }, [state, userAnswers, wordDifficulties]);

  // Get best hint suggestion
  const getBestHint = useCallback((): HintSuggestion | null => {
    const suggestions = getHintSuggestions();
    return suggestions.length > 0 ? suggestions[0] : null;
  }, [getHintSuggestions]);

  // Get hint for specific word
  const getWordHint = useCallback((wordNumber: number, direction: 'across' | 'down'): HintSuggestion | null => {
    const suggestions = getHintSuggestions();
    return suggestions.find(s => s.wordNumber === wordNumber && s.direction === direction) || null;
  }, [getHintSuggestions]);

  // Get strategic hint explanation
  const getHintExplanation = useCallback((suggestion: HintSuggestion): string => {
    const clue = suggestion.direction === 'across'
      ? state.clues.across[suggestion.wordNumber]
      : state.clues.down[suggestion.wordNumber];

    return `${suggestion.reason}. Petunjuk: "${clue}"`;
  }, [state.clues]);

  return {
    wordDifficulties,
    getHintSuggestions,
    getBestHint,
    getWordHint,
    getHintExplanation
  };
};
