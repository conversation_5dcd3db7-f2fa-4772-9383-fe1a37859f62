import { useEffect, useRef, useCallback, useState } from 'react';
import { GameState } from '../types/crossword';

interface TimerManagerOptions {
  gameState: GameState;
  initialTime?: number;
  onAutoSave?: () => void;
  autoSaveInterval?: number; // in seconds
}

export const useTimerManager = ({
  gameState,
  initialTime = 0,
  onAutoSave,
  autoSaveInterval = 30
}: TimerManagerOptions) => {
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const pausedTimeRef = useRef<number>(initialTime);
  const [currentTime, setCurrentTime] = useState<number>(initialTime);

  // Use ref to store the latest onAutoSave callback to avoid dependency issues
  const onAutoSaveRef = useRef(onAutoSave);

  // Update the ref when onAutoSave changes
  useEffect(() => {
    onAutoSaveRef.current = onAutoSave;
  }, [onAutoSave]);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (autoSaveRef.current) {
      clearInterval(autoSaveRef.current);
      autoSaveRef.current = null;
    }
  }, []);

  // Start timer
  const startTimer = useCallback(() => {
    clearTimers();

    const now = Date.now();
    startTimeRef.current = now;



    // Main timer - updates every second
    timerRef.current = setInterval(() => {
      if (startTimeRef.current) {
        const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
        const totalTime = pausedTimeRef.current + elapsed;

        setCurrentTime(totalTime);
      }
    }, 1000);

    // Auto-save timer - runs at specified interval
    if (onAutoSaveRef.current && autoSaveInterval > 0) {
      autoSaveRef.current = setInterval(() => {
        if (onAutoSaveRef.current) {
          onAutoSaveRef.current();
        }
      }, autoSaveInterval * 1000);
    }
  }, [autoSaveInterval, clearTimers]); // Removed onAutoSave from dependencies

  // Pause timer
  const pauseTimer = useCallback(() => {

    if (startTimeRef.current) {
      const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
      pausedTimeRef.current = pausedTimeRef.current + elapsed;
      setCurrentTime(pausedTimeRef.current);
    }
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    startTimer();
  }, [startTimer]);

  // Reset timer
  const resetTimer = useCallback(() => {
    clearTimers();
    startTimeRef.current = null;
    pausedTimeRef.current = 0;
    setCurrentTime(0);
  }, [clearTimers]);

  // Handle game state changes
  useEffect(() => {
    switch (gameState) {
      case 'playing':
        if (pausedTimeRef.current > 0) {
          // Resuming from pause
          resumeTimer();
        } else {
          // Starting fresh
          startTimer();
        }
        break;
      case 'paused':
        pauseTimer();
        break;
      case 'not-started':
      case 'completed':
        clearTimers();
        break;
      default:
        break;
    }
  }, [gameState, startTimer, pauseTimer, resumeTimer, clearTimers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  // Initialize timer with external time - only on mount
  useEffect(() => {
    pausedTimeRef.current = initialTime;
    setCurrentTime(initialTime);
  }, []); // Empty dependency array - only run on mount

  return {
    currentTime,
    isRunning: gameState === 'playing',
    isPaused: gameState === 'paused',
    startTimer,
    pauseTimer,
    resumeTimer,
    resetTimer,
    clearTimers
  };
};
