/* Import enhanced crossword styles */
@import 'styles/crossword-enhancements.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #fafafa;
  --foreground: #171717;
  --paper-primary: #ffffff;
  --paper-secondary: #f5f5f5;
  --paper-tertiary: #f9f9f9;
  --ink-primary: #171717;
  --ink-dark: #262626;
  --ink-secondary: #404040;
  --ink-muted: #737373;
  --ink-light: #a3a3a3;

  /* Enhanced paper textures */
  --paper-texture-subtle: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.03) 1px, transparent 0);
  --paper-texture-medium: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.06) 1px, transparent 0);
  --paper-texture-strong: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0);

  /* Newspaper grid patterns */
  --newspaper-lines: linear-gradient(90deg, transparent 24%, rgba(0,0,0,0.02) 25%, rgba(0,0,0,0.02) 26%, transparent 27%);
  --newspaper-grid: linear-gradient(90deg, rgba(0,0,0,0.03) 1px, transparent 1px), linear-gradient(0deg, rgba(0,0,0,0.03) 1px, transparent 1px);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #171717;
    --foreground: #f5f5f5;
    --paper-primary: #262626;
    --paper-secondary: #404040;
    --paper-tertiary: #333333;
    --ink-primary: #f5f5f5;
    --ink-dark: #333333;
    --ink-secondary: #d4d4d4;
    --ink-muted: #a3a3a3;
    --ink-light: #737373;

    /* Dark mode paper textures */
    --paper-texture-subtle: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.03) 1px, transparent 0);
    --paper-texture-medium: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.06) 1px, transparent 0);
    --paper-texture-strong: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);

    /* Dark mode newspaper patterns */
    --newspaper-lines: linear-gradient(90deg, transparent 24%, rgba(255,255,255,0.02) 25%, rgba(255,255,255,0.02) 26%, transparent 27%);
    --newspaper-grid: linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px), linear-gradient(0deg, rgba(255,255,255,0.03) 1px, transparent 1px);
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  background-image:
    var(--paper-texture-subtle),
    var(--newspaper-lines);
  background-size: 20px 20px, 40px 40px;
  font-family: Georgia, 'Times New Roman', serif;
  line-height: 1.6;
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bg-black {
  background-color: var(--ink-primary) !important;
}
/* Paper texture overlay */
.paper-bg {
  background-color: var(--paper-primary);
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.12) 1px, transparent 0),
    linear-gradient(45deg, transparent 24%, rgba(0,0,0,0.06) 25%, rgba(0,0,0,0.06) 26%, transparent 27%, transparent 74%, rgba(0,0,0,0.06) 75%, rgba(0,0,0,0.06) 76%, transparent 77%),
    linear-gradient(-45deg, transparent 24%, rgba(0,0,0,0.03) 25%, rgba(0,0,0,0.03) 26%, transparent 27%, transparent 74%, rgba(0,0,0,0.03) 75%, rgba(0,0,0,0.03) 76%, transparent 77%);
  background-size: 12px 12px, 25px 25px, 25px 25px;
}

/* Ink-like text styling */
.ink-text {
  color: var(--ink-primary);
  text-shadow: 0 0 1px rgba(0,0,0,0.1);
}

.ink-text-muted {
  color: var(--ink-muted);
}

.text-ink {
  color: var(--ink-primary);
  text-shadow: 0 0 1px rgba(0,0,0,0.1);
}

.text-ink-muted {
  color: var(--ink-muted);
}

/* Paper card styling */
.paper-card {
  background-color: var(--paper-primary);
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow:
    0 2px 4px rgba(0,0,0,0.1),
    0 1px 2px rgba(0,0,0,0.06),
    inset 0 0 0 1px rgba(255,255,255,0.1);
}

/* Button styling with ink effect */
.ink-button {
  background-color: var(--ink-primary);
  color: var(--paper-primary);
  border: 2px solid var(--ink-primary);
  transition: all 0.2s ease;
}

.ink-button:hover {
  background-color: transparent;
  color: var(--ink-primary);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.ink-button-outline {
  background-color: transparent;
  color: var(--ink-primary);
  border: 2px solid var(--ink-primary);
}

.ink-button-outline:hover {
  background-color: var(--ink-primary);
  color: var(--paper-primary);
}

/* Form input styling */
.paper-input {
  background-color: var(--paper-primary);
  border: 2px solid rgba(0,0,0,0.1);
  color: var(--ink-primary);
  transition: all 0.2s ease;
}

.paper-input:focus {
  border-color: var(--ink-primary);
  box-shadow: 0 0 0 3px rgba(0,0,0,0.1);
  outline: none;
}

/* Crossword grid styling */
.crossword-cell {
  background-color: var(--paper-primary);
  border: 1px solid rgba(0,0,0,0.2);
  color: var(--ink-primary);
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.crossword-cell.selected {
  background-color: rgba(0,0,0,0.1);
  border-color: var(--ink-primary);
  box-shadow: inset 0 0 0 2px var(--ink-primary);
}

.crossword-cell.highlighted {
  background-color: rgba(0,0,0,0.05);
}

.crossword-cell.black {
  background-color: var(--ink-primary);
  border-color: var(--ink-primary);
}

/* Navigation styling */
.nav-paper {
  background-color: var(--paper-primary);
  border-bottom: 2px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Additional paper texture variations */
.paper-bg-subtle {
  background-color: var(--paper-secondary);
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0);
  background-size: 20px 20px;
}

.paper-bg-medium {
  background-color: var(--paper-primary);
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0),
    linear-gradient(90deg, transparent 49%, rgba(0,0,0,0.05) 50%, rgba(0,0,0,0.05) 51%, transparent 52%);
  background-size: 15px 15px, 30px 30px;
}

.paper-bg-strong {
  background-color: var(--paper-primary);
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0),
    linear-gradient(45deg, transparent 24%, rgba(0,0,0,0.08) 25%, rgba(0,0,0,0.08) 26%, transparent 27%, transparent 74%, rgba(0,0,0,0.08) 75%, rgba(0,0,0,0.08) 76%, transparent 77%),
    linear-gradient(-45deg, transparent 24%, rgba(0,0,0,0.04) 25%, rgba(0,0,0,0.04) 26%, transparent 27%, transparent 74%, rgba(0,0,0,0.04) 75%, rgba(0,0,0,0.04) 76%, transparent 77%);
  background-size: 10px 10px, 20px 20px, 20px 20px;
}

.paper-bg-lined {
  background-color: var(--paper-primary);
  background-image:
    linear-gradient(90deg, transparent 0%, transparent 49%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.1) 51%, transparent 52%, transparent 100%);
  background-size: 25px 25px;
}

.paper-bg-grid {
  background-color: var(--paper-primary);
  background-image:
    linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px),
    linear-gradient(0deg, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced utility classes for Tekateki.io */
@layer components {
  /* Main layout utilities */
  .bg-paper-main {
    background-color: var(--background);
    background-image: var(--paper-texture-subtle), var(--newspaper-lines);
    background-size: 20px 20px, 40px 40px;
  }

  .bg-paper-section {
    background-color: var(--paper-tertiary);
    background-image: var(--paper-texture-medium);
    background-size: 15px 15px;
  }

  /* Card utilities */
  .card-paper {
    background-color: var(--paper-primary);
    border: 1px solid rgba(0,0,0,0.08);
    box-shadow:
      0 2px 4px rgba(0,0,0,0.06),
      0 1px 2px rgba(0,0,0,0.04),
      inset 0 0 0 1px rgba(255,255,255,0.1);
    border-radius: 0.375rem;
    transition: all 0.2s ease;
  }

  .card-paper:hover {
    box-shadow:
      0 4px 8px rgba(0,0,0,0.1),
      0 2px 4px rgba(0,0,0,0.06),
      inset 0 0 0 1px rgba(255,255,255,0.1);
    transform: translateY(-1px);
  }

  /* Button utilities */
  .btn-primary {
    background-color: var(--ink-primary);
    color: var(--paper-primary);
    border: 2px solid var(--ink-primary);
    padding: 0.75rem 1.5rem;
    font-family: Georgia, 'Times New Roman', serif;
    font-weight: 600;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover {
    background-color: transparent;
    color: var(--ink-primary);
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
  }

  .btn-secondary {
    background-color: var(--paper-primary);
    color: var(--ink-primary);
    border: 2px solid var(--ink-primary);
    padding: 0.75rem 1.5rem;
    font-family: Georgia, 'Times New Roman', serif;
    font-weight: 600;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-secondary:hover {
    background-color: var(--ink-primary);
    color: var(--paper-primary);
  }

  .btn-outline {
    background-color: transparent;
    color: var(--ink-primary);
    border: 2px solid var(--ink-secondary);
    padding: 0.5rem 1rem;
    font-family: Georgia, 'Times New Roman', serif;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-outline:hover {
    background-color: var(--ink-secondary);
    color: var(--paper-primary);
    border-color: var(--ink-primary);
  }

  /* Typography utilities */
  .text-ink-dark {
    color: var(--ink-primary);
    text-shadow: 0 0 1px rgba(0,0,0,0.1);
  }

  .text-ink-muted {
    color: var(--ink-muted);
  }

  .text-ink-light {
    color: var(--ink-light);
  }

  /* Navigation utilities */
  .nav-link {
    color: var(--ink-secondary);
    font-family: Georgia, 'Times New Roman', serif;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    text-decoration: none;
    position: relative;
  }

  .nav-link:hover {
    color: var(--ink-primary);
    background-color: rgba(0,0,0,0.05);
  }

  .nav-link.active {
    color: var(--ink-primary);
    font-weight: 600;
  }

  .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background-color: var(--ink-primary);
  }

  /* Loading spinner */
  .spinner {
    border-color: var(--ink-primary);
  }

  /* Enhanced animations for crossword UI */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slideIn {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3) translateY(-50px);
    }
    50% {
      opacity: 1;
      transform: scale(1.05) translateY(-10px);
    }
    70% {
      transform: scale(0.95) translateY(0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Enhanced focus styles for accessibility */
  .focus-paper:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,0,0,0.1);
    border-color: var(--ink-primary);
  }

  /* Crossword-specific utilities */
  .crossword-grid-shadow {
    box-shadow:
      0 4px 6px rgba(0,0,0,0.1),
      0 2px 4px rgba(0,0,0,0.06),
      inset 0 0 0 1px rgba(255,255,255,0.1);
  }

  /* Enhanced paper textures for crossword theme */
  .bg-newsprint {
    background-color: #fefefe;
    background-image:
      radial-gradient(circle at 1px 1px, rgba(0,0,0,0.08) 1px, transparent 0),
      linear-gradient(90deg, transparent 49%, rgba(0,0,0,0.02) 50%, rgba(0,0,0,0.02) 51%, transparent 52%);
    background-size: 12px 12px, 20px 20px;
  }

  .content-paper {
    background-color: var(--paper-primary);
    background-image: var(--paper-texture-medium);
    background-size: 15px 15px;
    border-radius: 0.5rem;
    box-shadow:
      0 2px 4px rgba(0,0,0,0.06),
      0 1px 2px rgba(0,0,0,0.04);
  }

  /* Enhanced shadow utilities */
  .shadow-paper-lg {
    box-shadow:
      0 10px 15px rgba(0,0,0,0.1),
      0 4px 6px rgba(0,0,0,0.05),
      inset 0 0 0 1px rgba(255,255,255,0.1);
  }

  /* Additional color utilities for crossword theme */
  .bg-primary-25 {
    background-color: rgba(59, 130, 246, 0.05);
  }

  .bg-primary-50 {
    background-color: rgba(59, 130, 246, 0.1);
  }

  .text-newsprint {
    color: #fefefe;
  }

  .border-ink-secondary {
    border-color: var(--ink-secondary);
  }

  /* Crossword cell enhancements */
  .crossword-cell {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    min-width: 32px;
    min-height: 32px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-transform: uppercase;
    user-select: none;
    cursor: pointer;
  }

  .crossword-cell.empty {
    cursor: default;
  }

  .crossword-cell.selected {
    z-index: 10;
  }

  /* Enhanced hover effects */
  .crossword-cell:not(.empty):hover {
    transform: scale(1.02);
    z-index: 5;
  }

  /* Progress bar enhancements */
  .progress-bar-animated {
    background-image: linear-gradient(
      45deg,
      rgba(255,255,255,0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255,255,255,0.15) 50%,
      rgba(255,255,255,0.15) 75%,
      transparent 75%
    );
    background-size: 1rem 1rem;
    animation: progress-stripe 1s linear infinite;
  }

  @keyframes progress-stripe {
    0% { background-position: 0 0; }
    100% { background-position: 1rem 0; }
  }

  /* Virtual keyboard specific animations */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slideInUp {
    animation: slideInUp 0.3s ease-out;
  }

  /* Enhanced mobile responsiveness */
  @media (max-width: 768px) {
    .crossword-cell {
      min-width: 28px;
      min-height: 28px;
      font-size: 12px;
    }

    .virtual-keyboard-offset {
      padding-bottom: 280px; /* Space for virtual keyboard */
    }
  }

  /* Hero section specific utilities for better contrast */
  .hero-bg {
    background: linear-gradient(135deg, #171717 0%, #404040 50%, #262626 100%);
  }

  .hero-text-primary {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }

  .hero-text-secondary {
    color: #f3f4f6;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
  }

  .hero-text-accent {
    color: #fef3c7;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
  }

  .hero-button-primary {
    background-color: #ffffff;
    color: #1f2937;
    border: 2px solid #ffffff;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
  }

  .hero-button-primary:hover {
    background-color: #f9fafb;
    color: #111827;
    box-shadow: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
  }

  .hero-button-secondary {
    background-color: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
    backdrop-filter: blur(4px);
  }

  .hero-button-secondary:hover {
    background-color: rgba(255,255,255,0.15);
    color: #ffffff;
  }

  .hero-badge {
    background-color: rgba(255,255,255,0.15);
    color: #ffffff;
    border: 1px solid rgba(255,255,255,0.25);
    backdrop-filter: blur(8px);
  }

  .hero-stats {
    background-color: #ffffff;
    color: #1f2937;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body {
    background-size: 15px 15px, 30px 30px;
  }

  .bg-paper-main {
    background-size: 15px 15px, 30px 30px;
  }

  .bg-paper-section {
    background-size: 12px 12px;
  }

  .paper-bg {
    background-size: 10px 10px, 20px 20px, 20px 20px;
  }

  .paper-bg-medium {
    background-size: 12px 12px, 25px 25px;
  }

  .paper-bg-strong {
    background-size: 8px 8px, 15px 15px, 15px 15px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  .btn-outline {
    padding: 0.5rem 0.875rem;
    font-size: 0.875rem;
  }
}
