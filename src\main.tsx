import { lazy, Suspense } from 'react';
// StrictMode is commented out since we're not using it to prevent double renders
// import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import { registerServiceWorker, initServiceWorkerAndConnectionStatus } from './utils/serviceWorkerRegistration';

// Lazy load the App component
const App = lazy(() => import('./App.tsx'));

// Lazy load analytics in production only
if (import.meta.env.PROD) {
  // Use dynamic import for analytics to reduce initial bundle size
  import('./utils/analytics').then(({ initAnalytics }) => {
    initAnalytics();
  });

  // Update Google Analytics measurement ID in the HTML - only in production
  if (typeof document !== 'undefined') {
    const analyticsId = import.meta.env.VITE_GOOGLE_ANALYTICS_ID;
    if (analyticsId) {
      // Update the script tag with the correct measurement ID
      const scripts = document.querySelectorAll('script');
      scripts.forEach(script => {
        if (script.textContent && script.textContent.includes('G-MEASUREMENT_ID')) {
          script.textContent = script.textContent.replace(/G-MEASUREMENT_ID/g, analyticsId);
        }
      });

      // Update Google site verification
      const verificationCode = import.meta.env.VITE_GOOGLE_SITE_VERIFICATION;
      if (verificationCode) {
        const metaTags = document.querySelectorAll('meta');
        metaTags.forEach(tag => {
          if (tag.name === 'google-site-verification') {
            tag.content = verificationCode;
          }
        });
      }
    }
  }
}

// Loading component for Suspense fallback - optimized to be lightweight
const LoadingSpinner = () => (
  <div className="flex items-center justify-center w-full h-full min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Preload critical assets
const preloadAssets = () => {
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    // Use requestIdleCallback to preload assets during browser idle time
    window.requestIdleCallback(() => {
      // Preload main images
      const preloadLinks = [
        '/images/logo.png',
        '/images/background.jpg'
      ];

      preloadLinks.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = url.endsWith('.jpg') || url.endsWith('.png') ? 'image' : 'script';
        link.href = url;
        document.head.appendChild(link);
      });
    });
  }
};

// Render the app
// In development, React.StrictMode causes components to render twice
// This is intentional to help catch bugs, but can cause duplicate API calls
// In production, we don't need StrictMode
const AppWithSuspense = (
  <Suspense fallback={<LoadingSpinner />}>
    <App />
  </Suspense>
);

// Only use StrictMode in development if you want to catch potential issues
// Comment out the condition to disable StrictMode even in development
const rootElement = document.getElementById('root');
if (rootElement) {
  const root = createRoot(rootElement);
  root.render(
    import.meta.env.DEV ? (
      // <StrictMode>{AppWithSuspense}</StrictMode> // Uncomment to enable StrictMode in development
      AppWithSuspense // No StrictMode to prevent double renders
    ) : (
      AppWithSuspense
    )
  );

  // Call preloadAssets after render
  if (import.meta.env.PROD) {
    preloadAssets();
  }

  // Register service worker for offline support with error handling
  try {
    initServiceWorkerAndConnectionStatus(
      // Online callback
      () => console.log('App is online - full functionality available'),
      // Offline callback
      () => console.log('App is offline - limited functionality available')
    );
  } catch (error) {
    console.error('Failed to initialize service worker:', error);
    // Aplikasi masih bisa berjalan tanpa service worker
    console.log('Application will continue without offline support');
  }
}

// Export something to make fast refresh work
export const enableFastRefresh = true;
