import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import SEO from '../components/SEO';

const AboutPage: React.FC = () => {
  // Create structured data for about page
  const structuredData = useMemo(() => {
    const origin = typeof window !== 'undefined' ? window.location.origin : '';

    // Create structured data for about page
    const aboutStructuredData = {
      "@context": "https://schema.org",
      "@type": "AboutPage",
      "name": "Tentang Kami | Tekateki.io",
      "description": "Pelajari tentang platform teka-teki silang online kami. Misi kami adalah menyediakan permainan teka-teki silang berkualitas dalam bahasa Indonesia.",
      "url": `${origin}/tentang-kami`,
      "publisher": {
        "@type": "Organization",
        "name": "Tekateki.io",
        "logo": {
          "@type": "ImageObject",
          "url": `${origin}/images/logo.png`
        }
      }
    };

    return [aboutStructuredData];
  }, []);

  return (
    <div className="min-h-screen bg-slate-50">
      <SEO
        title="Tentang Kami | Tekateki.io"
        description="Pelajari tentang platform teka-teki silang online kami. Misi kami adalah menyediakan permainan teka-teki silang berkualitas dalam bahasa Indonesia."
        keywords="tentang tts, tentang teka teki silang online, misi tts, tim tts, sejarah tts, platform teka teki silang"
        structuredData={structuredData}
      />

      {/* About Header */}
      <section className="bg-gradient-to-r from-blue-700 to-blue-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Tentang Kami
          </h1>
          <p className="text-lg mb-0 max-w-3xl">
            Mengenal lebih dekat platform teka-teki silang online dalam bahasa Indonesia.
          </p>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold mb-6">Cerita Kami</h2>

            <div className="prose prose-lg">
              <p>
                Tekateki.io lahir dari kecintaan kami terhadap permainan kata dan bahasa Indonesia. Kami memulai perjalanan ini pada tahun 2022 dengan misi sederhana: menyediakan platform teka-teki silang berkualitas dalam bahasa Indonesia yang dapat diakses oleh semua orang.
              </p>

              <p>
                Sebagai pecinta teka-teki silang, kami menyadari bahwa sebagian besar platform yang tersedia didominasi oleh konten berbahasa Inggris. Kami ingin mengubah hal tersebut dengan menciptakan platform yang tidak hanya menyenangkan tetapi juga mendukung pelestarian dan pengembangan bahasa Indonesia.
              </p>

              <p>
                Sejak diluncurkan, platform kami telah berkembang menjadi komunitas yang aktif dengan ribuan pengguna dari seluruh Indonesia dan bahkan dari luar negeri. Kami terus berinovasi dan menambahkan fitur-fitur baru untuk meningkatkan pengalaman bermain teka-teki silang online.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold mb-6">Misi Kami</h2>

            <div className="prose prose-lg">
              <p>
                Misi kami adalah menyediakan platform teka-teki silang online yang:
              </p>

              <ul>
                <li>Mempromosikan penggunaan bahasa Indonesia yang baik dan benar</li>
                <li>Menyediakan hiburan yang mendidik dan mengasah kemampuan kognitif</li>
                <li>Dapat diakses oleh semua kalangan, dari pelajar hingga profesional</li>
                <li>Mendukung pendidikan dan pembelajaran bahasa Indonesia</li>
                <li>Membangun komunitas pecinta teka-teki silang di Indonesia</li>
              </ul>

              <p>
                Kami percaya bahwa bermain teka-teki silang bukan hanya sekadar hiburan, tetapi juga cara yang efektif untuk meningkatkan kosakata, pengetahuan umum, dan kemampuan berpikir kritis.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold mb-6">Tim Kami</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xl">AB</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Andi Budiman</h3>
                <p className="text-gray-600 text-center mb-2">Pendiri & CEO</p>
                <p className="text-gray-700 text-center">
                  Pecinta teka-teki silang sejak kecil dan berpengalaman dalam pengembangan web selama lebih dari 10 tahun.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xl">DP</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Dewi Pratiwi</h3>
                <p className="text-gray-600 text-center mb-2">Kepala Konten</p>
                <p className="text-gray-700 text-center">
                  Ahli bahasa Indonesia dengan pengalaman mengajar selama 8 tahun dan penulis teka-teki silang untuk berbagai media.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xl">RS</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Rudi Santoso</h3>
                <p className="text-gray-600 text-center mb-2">Pengembang Utama</p>
                <p className="text-gray-700 text-center">
                  Insinyur perangkat lunak dengan keahlian dalam pengembangan aplikasi web interaktif dan pengalaman pengguna.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xl">LW</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Lisa Wijaya</h3>
                <p className="text-gray-600 text-center mb-2">Desainer UI/UX</p>
                <p className="text-gray-700 text-center">
                  Desainer dengan pengalaman dalam menciptakan antarmuka yang intuitif dan menarik untuk berbagai platform digital.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Bergabunglah dengan Komunitas Kami</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Jadilah bagian dari komunitas pecinta teka-teki silang terbesar di Indonesia. Mainkan, buat, dan bagikan teka-teki silang Anda sekarang!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/play" className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-md font-medium text-lg">
              Mainkan Sekarang
            </Link>
            <Link to="/register" className="bg-transparent border-2 border-white hover:bg-white/10 px-8 py-3 rounded-md font-medium text-lg">
              Daftar Akun
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
