import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { CalendarIcon, UserIcon, TagIcon, Loader2 } from 'lucide-react';
import SEO from '../components/SEO';
import Breadcrumb from '../components/Breadcrumb';
import { trackPageView, trackEvent } from '../utils/analytics';
import { getBlogPosts, BlogPost } from '../services/api';

// Mock blog data - in a real app, this would come from an API
export const blogPosts = [
  {
    id: 1,
    title: 'Tips Mengerjakan Teka-Teki Silang dengan Cepat',
    slug: 'tips-mengerjakan-teka-teki-silang-dengan-cepat',
    excerpt: 'Pelajari cara menyelesaikan teka-teki silang dengan lebih cepat dan efisien dengan tips dari para ahli.',
    content: `
      <p>Teka-teki silang adalah permainan kata yang menyenangkan dan menantang. <PERSON><PERSON><PERSON> adalah beberapa tips untuk membantu Anda menyelesaikan teka-teki silang dengan lebih cepat:</p>

      <h2>1. <PERSON><PERSON> dengan yang Anda <PERSON>hu</h2>
      <p>Isi terlebih dahulu jawaban yang Anda yakin benar. Ini akan memberikan petunjuk untuk jawaban lain yang bersinggungan.</p>

      <h2>2. Perhatikan Bentuk Kata</h2>
      <p>Perhatikan jumlah huruf dan pola kata. Kata dengan huruf yang tidak umum seperti J, Q, X, atau Z biasanya lebih mudah diidentifikasi.</p>

      <h2>3. Kenali Pola Petunjuk</h2>
      <p>Pembuat teka-teki silang sering menggunakan pola tertentu dalam petunjuk mereka. Misalnya, jika petunjuk diakhiri dengan tanda tanya, jawabannya mungkin adalah permainan kata atau lelucon.</p>

      <h2>4. Gunakan Pensil</h2>
      <p>Selalu gunakan pensil sehingga Anda dapat menghapus jawaban yang salah dengan mudah.</p>

      <h2>5. Jangan Takut untuk Melewati</h2>
      <p>Jika Anda kesulitan dengan satu petunjuk, lewati dan kembali nanti. Seringkali, setelah Anda mengisi lebih banyak kotak, petunjuk yang sulit menjadi lebih jelas.</p>
    `,
    author: 'Budi Santoso',
    date: '2023-10-15',
    category: 'Tips & Trik',
    image: '/images/blog/crossword-tips.jpg'
  },
  {
    id: 2,
    title: 'Sejarah Teka-Teki Silang di Indonesia',
    slug: 'sejarah-teka-teki-silang-di-indonesia',
    excerpt: 'Mengenal sejarah dan perkembangan teka-teki silang di Indonesia dari masa ke masa.',
    content: `
      <p>Teka-teki silang atau yang sering disingkat TTS telah menjadi bagian dari budaya populer Indonesia selama beberapa dekade. Mari kita telusuri sejarahnya:</p>

      <h2>Awal Mula TTS di Indonesia</h2>
      <p>Teka-teki silang pertama kali diperkenalkan di Indonesia pada era kolonial Belanda. Namun, popularitasnya meningkat pesat pada tahun 1970-an ketika beberapa majalah dan koran mulai menerbitkan TTS secara reguler.</p>

      <h2>Era Keemasan</h2>
      <p>Tahun 1980-an hingga 1990-an dapat dianggap sebagai era keemasan TTS di Indonesia. Pada masa ini, hampir setiap koran dan majalah memiliki rubrik TTS mereka sendiri. Bahkan ada majalah khusus yang didedikasikan untuk teka-teki silang dan permainan kata lainnya.</p>

      <h2>TTS di Era Digital</h2>
      <p>Dengan kemajuan teknologi, TTS juga beradaptasi ke platform digital. Saat ini, ada banyak aplikasi dan situs web yang menawarkan teka-teki silang dalam bahasa Indonesia, membuatnya lebih mudah diakses oleh generasi baru.</p>

      <h2>Manfaat Budaya dan Pendidikan</h2>
      <p>TTS tidak hanya menjadi hiburan tetapi juga alat pendidikan yang efektif. Banyak guru bahasa Indonesia menggunakan TTS sebagai metode untuk memperkaya kosakata siswa dan meningkatkan pemahaman bahasa.</p>
    `,
    author: 'Dewi Lestari',
    date: '2023-09-22',
    category: 'Sejarah',
    image: '/images/blog/crossword-history.jpg'
  },
  {
    id: 3,
    title: 'Manfaat Teka-Teki Silang untuk Kesehatan Otak',
    slug: 'manfaat-teka-teki-silang-untuk-kesehatan-otak',
    excerpt: 'Temukan bagaimana mengerjakan teka-teki silang secara rutin dapat meningkatkan fungsi kognitif dan kesehatan otak Anda.',
    content: `
      <p>Tahukah Anda bahwa mengerjakan teka-teki silang secara rutin dapat memberikan manfaat signifikan bagi kesehatan otak? Berikut adalah beberapa manfaatnya:</p>

      <h2>Meningkatkan Memori</h2>
      <p>Mengerjakan teka-teki silang melibatkan penarikan informasi dari memori jangka panjang. Latihan ini dapat memperkuat koneksi saraf di otak yang terkait dengan memori.</p>

      <h2>Memperluas Kosakata</h2>
      <p>TTS memperkenalkan Anda pada kata-kata baru dan memperkuat pemahaman Anda tentang kata-kata yang sudah Anda ketahui, sehingga memperkaya kosakata Anda.</p>

      <h2>Meningkatkan Konsentrasi</h2>
      <p>Menyelesaikan teka-teki silang membutuhkan fokus dan konsentrasi, yang dapat melatih kemampuan Anda untuk tetap fokus pada tugas-tugas lain dalam kehidupan sehari-hari.</p>

      <h2>Mengurangi Risiko Demensia</h2>
      <p>Beberapa penelitian menunjukkan bahwa aktivitas mental yang menantang seperti mengerjakan teka-teki silang dapat membantu mengurangi risiko demensia dan penurunan kognitif terkait usia.</p>

      <h2>Mengurangi Stres</h2>
      <p>Mengerjakan teka-teki silang dapat menjadi bentuk meditasi aktif yang membantu mengurangi stres dan kecemasan dengan mengalihkan pikiran Anda dari kekhawatiran sehari-hari.</p>
    `,
    author: 'Dr. Andi Wijaya',
    date: '2023-08-30',
    category: 'Kesehatan',
    image: '/images/blog/crossword-brain-health.jpg'
  }
];

// Interface to extend BlogPost with category
interface ExtendedBlogPost extends BlogPost {
  category?: string;
}

const BlogPage: React.FC = () => {
  const [posts, setPosts] = useState<ExtendedBlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const location = useLocation();

  // Fetch blog posts from API
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const result = await getBlogPosts(1, 100, 'published');

        // Add temporary category field for each post based on title/content
        const postsWithCategories = result.posts.map(post => {
          let category = 'Umum';

          // Simple logic to determine category based on title or content
          const titleLower = post.title.toLowerCase();
          if (titleLower.includes('tips') || titleLower.includes('cara')) {
            category = 'Tips & Trik';
          } else if (titleLower.includes('sejarah') || titleLower.includes('asal')) {
            category = 'Sejarah';
          } else if (titleLower.includes('kesehatan') || titleLower.includes('otak') || titleLower.includes('manfaat')) {
            category = 'Kesehatan';
          }

          return { ...post, category };
        });

        setPosts(postsWithCategories);

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(postsWithCategories.map(post => post.category || 'Umum')));
        setCategories(uniqueCategories);

        setError(null);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Gagal memuat artikel blog. Silakan coba lagi nanti.');

        // Use fallback data if API fails
        setPosts(blogPosts.map(post => ({
          id: String(post.id),
          title: post.title,
          slug: post.slug,
          content: post.content,
          excerpt: post.excerpt,
          featured_image: post.image,
          author_id: '',
          author_name: post.author,
          status: 'published' as const,
          created_at: post.date,
          updated_at: post.date,
          category: post.category
        })));

        setCategories(Array.from(new Set(blogPosts.map(post => post.category))));
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();

    // Track page view with content type
    trackPageView(location.pathname, 'Blog Tekateki.io - Artikel Seputar Teka-Teki Silang', 'blog_index');
  }, [location.pathname]);

  // Filter posts by category
  const filteredPosts = selectedCategory
    ? posts.filter(post => post.category === selectedCategory)
    : posts;

  // Track category selection
  useEffect(() => {
    if (selectedCategory) {
      trackEvent('filter', 'blog_category', selectedCategory);
    }
  }, [selectedCategory]);

  // Create structured data for the blog page
  const blogData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "Blog Tekateki.io",
    "description": "Artikel dan tips seputar teka-teki silang, permainan kata, dan asah otak.",
    "url": window.location.href,
    "inLanguage": "id-ID",
    "publisher": {
      "@type": "Organization",
      "name": "Tekateki.io",
      "logo": {
        "@type": "ImageObject",
        "url": `${window.location.origin}/images/logo.png`,
        "width": "192",
        "height": "192"
      }
    },
    "blogPost": filteredPosts.map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt || '',
      "author": {
        "@type": "Person",
        "name": post.author_name || 'Admin',
        "url": `${window.location.origin}/author/${post.author_id || 'admin'}`
      },
      "datePublished": post.created_at,
      "dateModified": post.updated_at,
      "image": post.featured_image || '/images/blog/default-blog.jpg',
      "url": `${window.location.origin}/blog/${post.slug}`,
      "articleSection": post.category || 'Umum',
      "keywords": `teka teki silang, TTS, ${post.category || 'Umum'}, blog TTS`,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${window.location.origin}/blog/${post.slug}`
      }
    }))
  };

  // Create breadcrumb structured data
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Beranda",
        "item": window.location.origin
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": window.location.href
      }
    ]
  };

  // Combine structured data
  const structuredData = [blogData, breadcrumbData];

  return (
    <div className="container mx-auto px-4 py-8">
      <SEO
        title="Blog Tekateki.io - Artikel Seputar Teka-Teki Silang"
        description="Kumpulan artikel, tips, dan informasi menarik seputar teka-teki silang dan permainan kata."
        keywords="blog teka teki silang, artikel TTS, tips TTS, sejarah teka-teki silang, manfaat TTS"
        structuredData={structuredData}
        canonicalUrl={`${window.location.origin}/blog`}
      />

      {/* Breadcrumb navigation */}
      <Breadcrumb
        items={[
          { name: 'Blog', href: '/blog', current: true }
        ]}
      />

      <h1 className="text-3xl font-bold mb-8">Blog Tekateki.io</h1>

      {/* Category filter */}
      <div className="flex flex-wrap gap-2 mb-8">
        <button
          className={`px-4 py-2 rounded-full text-sm font-medium ${
            selectedCategory === null ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => setSelectedCategory(null)}
        >
          Semua
        </button>
        {categories.map(category => (
          <button
            key={category}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              selectedCategory === category ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600" />
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-8">
          <p>{error}</p>
        </div>
      )}

      {/* No posts message */}
      {!loading && !error && filteredPosts.length === 0 && (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Tidak ada artikel ditemukan</h2>
          <p className="text-gray-600">
            {selectedCategory
              ? `Tidak ada artikel dalam kategori "${selectedCategory}"`
              : 'Belum ada artikel blog yang tersedia saat ini'}
          </p>
        </div>
      )}

      {/* Blog posts */}
      {!loading && filteredPosts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPosts.map(post => (
            <Link to={`/blog/${post.slug}`} key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
              <div className="h-48 overflow-hidden">
                <img
                  src={post.featured_image || '/images/blog/default-blog.jpg'}
                  alt={post.title}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/images/blog/default-blog.jpg';
                  }}
                />
              </div>
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <CalendarIcon className="w-4 h-4 mr-1" />
                  <span>{new Date(post.created_at).toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                  <span className="mx-2">•</span>
                  <UserIcon className="w-4 h-4 mr-1" />
                  <span>{post.author_name || 'Admin'}</span>
                </div>
                <h2 className="text-xl font-bold mb-2 line-clamp-2">{post.title}</h2>
                <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
                <div className="flex items-center">
                  <TagIcon className="w-4 h-4 text-blue-600 mr-1" />
                  <span className="text-sm text-blue-600">{post.category}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default BlogPage;
