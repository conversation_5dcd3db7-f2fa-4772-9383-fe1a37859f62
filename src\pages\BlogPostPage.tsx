import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useLocation } from 'react-router-dom';
import { CalendarIcon, UserIcon, TagIcon, ArrowLeftIcon, Loader2 } from 'lucide-react';
import SEO from '../components/SEO';
import Breadcrumb from '../components/Breadcrumb';
import { trackPageView, trackEvent } from '../utils/analytics';
import { getBlogPostBySlug, getBlogPosts, BlogPost } from '../services/api';
import { blogPosts as fallbackBlogPosts } from './BlogPage';

// Mock blog data - in a real app, this would be fetched from an API
// This should match the data in BlogPage.tsx
const blogPosts = [
  {
    id: 1,
    title: 'Tips Mengerjakan Teka-Teki Silang dengan <PERSON>pat',
    slug: 'tips-mengerjakan-teka-teki-silang-dengan-cepat',
    excerpt: 'Pelajari cara menyelesaikan teka-teki silang dengan lebih cepat dan efisien dengan tips dari para ahli.',
    content: `
      <p>Teka-teki silang adalah permainan kata yang menyenangkan dan menantang. Berikut adalah beberapa tips untuk membantu Anda menyelesaikan teka-teki silang dengan lebih cepat:</p>

      <h2>1. Mulai dengan yang Anda Tahu</h2>
      <p>Isi terlebih dahulu jawaban yang Anda yakin benar. Ini akan memberikan petunjuk untuk jawaban lain yang bersinggungan.</p>

      <h2>2. Perhatikan Bentuk Kata</h2>
      <p>Perhatikan jumlah huruf dan pola kata. Kata dengan huruf yang tidak umum seperti J, Q, X, atau Z biasanya lebih mudah diidentifikasi.</p>

      <h2>3. Kenali Pola Petunjuk</h2>
      <p>Pembuat teka-teki silang sering menggunakan pola tertentu dalam petunjuk mereka. Misalnya, jika petunjuk diakhiri dengan tanda tanya, jawabannya mungkin adalah permainan kata atau lelucon.</p>

      <h2>4. Gunakan Pensil</h2>
      <p>Selalu gunakan pensil sehingga Anda dapat menghapus jawaban yang salah dengan mudah.</p>

      <h2>5. Jangan Takut untuk Melewati</h2>
      <p>Jika Anda kesulitan dengan satu petunjuk, lewati dan kembali nanti. Seringkali, setelah Anda mengisi lebih banyak kotak, petunjuk yang sulit menjadi lebih jelas.</p>
    `,
    author: 'Budi Santoso',
    date: '2023-10-15',
    category: 'Tips & Trik',
    image: '/images/blog/crossword-tips.jpg'
  },
  {
    id: 2,
    title: 'Sejarah Teka-Teki Silang di Indonesia',
    slug: 'sejarah-teka-teki-silang-di-indonesia',
    excerpt: 'Mengenal sejarah dan perkembangan teka-teki silang di Indonesia dari masa ke masa.',
    content: `
      <p>Teka-teki silang atau yang sering disingkat TTS telah menjadi bagian dari budaya populer Indonesia selama beberapa dekade. Mari kita telusuri sejarahnya:</p>

      <h2>Awal Mula TTS di Indonesia</h2>
      <p>Teka-teki silang pertama kali diperkenalkan di Indonesia pada era kolonial Belanda. Namun, popularitasnya meningkat pesat pada tahun 1970-an ketika beberapa majalah dan koran mulai menerbitkan TTS secara reguler.</p>

      <h2>Era Keemasan</h2>
      <p>Tahun 1980-an hingga 1990-an dapat dianggap sebagai era keemasan TTS di Indonesia. Pada masa ini, hampir setiap koran dan majalah memiliki rubrik TTS mereka sendiri. Bahkan ada majalah khusus yang didedikasikan untuk teka-teki silang dan permainan kata lainnya.</p>

      <h2>TTS di Era Digital</h2>
      <p>Dengan kemajuan teknologi, TTS juga beradaptasi ke platform digital. Saat ini, ada banyak aplikasi dan situs web yang menawarkan teka-teki silang dalam bahasa Indonesia, membuatnya lebih mudah diakses oleh generasi baru.</p>

      <h2>Manfaat Budaya dan Pendidikan</h2>
      <p>TTS tidak hanya menjadi hiburan tetapi juga alat pendidikan yang efektif. Banyak guru bahasa Indonesia menggunakan TTS sebagai metode untuk memperkaya kosakata siswa dan meningkatkan pemahaman bahasa.</p>
    `,
    author: 'Dewi Lestari',
    date: '2023-09-22',
    category: 'Sejarah',
    image: '/images/blog/crossword-history.jpg'
  },
  {
    id: 3,
    title: 'Manfaat Teka-Teki Silang untuk Kesehatan Otak',
    slug: 'manfaat-teka-teki-silang-untuk-kesehatan-otak',
    excerpt: 'Temukan bagaimana mengerjakan teka-teki silang secara rutin dapat meningkatkan fungsi kognitif dan kesehatan otak Anda.',
    content: `
      <p>Tahukah Anda bahwa mengerjakan teka-teki silang secara rutin dapat memberikan manfaat signifikan bagi kesehatan otak? Berikut adalah beberapa manfaatnya:</p>

      <h2>Meningkatkan Memori</h2>
      <p>Mengerjakan teka-teki silang melibatkan penarikan informasi dari memori jangka panjang. Latihan ini dapat memperkuat koneksi saraf di otak yang terkait dengan memori.</p>

      <h2>Memperluas Kosakata</h2>
      <p>TTS memperkenalkan Anda pada kata-kata baru dan memperkuat pemahaman Anda tentang kata-kata yang sudah Anda ketahui, sehingga memperkaya kosakata Anda.</p>

      <h2>Meningkatkan Konsentrasi</h2>
      <p>Menyelesaikan teka-teki silang membutuhkan fokus dan konsentrasi, yang dapat melatih kemampuan Anda untuk tetap fokus pada tugas-tugas lain dalam kehidupan sehari-hari.</p>

      <h2>Mengurangi Risiko Demensia</h2>
      <p>Beberapa penelitian menunjukkan bahwa aktivitas mental yang menantang seperti mengerjakan teka-teki silang dapat membantu mengurangi risiko demensia dan penurunan kognitif terkait usia.</p>

      <h2>Mengurangi Stres</h2>
      <p>Mengerjakan teka-teki silang dapat menjadi bentuk meditasi aktif yang membantu mengurangi stres dan kecemasan dengan mengalihkan pikiran Anda dari kekhawatiran sehari-hari.</p>
    `,
    author: 'Dr. Andi Wijaya',
    date: '2023-08-30',
    category: 'Kesehatan',
    image: '/images/blog/crossword-brain-health.jpg'
  }
];

// Interface to extend BlogPost with category
interface ExtendedBlogPost extends BlogPost {
  category?: string;
}

const BlogPostPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const location = useLocation();
  const [post, setPost] = useState<ExtendedBlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<ExtendedBlogPost[]>([]);

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true);

        // Fetch the blog post by slug
        const fetchedPost = await getBlogPostBySlug(slug || '');

        // Add category based on title/content
        let category = 'Umum';
        const titleLower = fetchedPost.title.toLowerCase();
        if (titleLower.includes('tips') || titleLower.includes('cara')) {
          category = 'Tips & Trik';
        } else if (titleLower.includes('sejarah') || titleLower.includes('asal')) {
          category = 'Sejarah';
        } else if (titleLower.includes('kesehatan') || titleLower.includes('otak') || titleLower.includes('manfaat')) {
          category = 'Kesehatan';
        }

        const postWithCategory: ExtendedBlogPost = {
          ...fetchedPost,
          category
        };

        setPost(postWithCategory);

        // Fetch all posts to find related ones
        const allPostsResult = await getBlogPosts(1, 100, 'published');

        // Add categories to all posts
        const postsWithCategories = allPostsResult.posts.map(p => {
          let postCategory = 'Umum';
          const postTitleLower = p.title.toLowerCase();
          if (postTitleLower.includes('tips') || postTitleLower.includes('cara')) {
            postCategory = 'Tips & Trik';
          } else if (postTitleLower.includes('sejarah') || postTitleLower.includes('asal')) {
            postCategory = 'Sejarah';
          } else if (postTitleLower.includes('kesehatan') || postTitleLower.includes('otak') || postTitleLower.includes('manfaat')) {
            postCategory = 'Kesehatan';
          }

          return { ...p, category: postCategory };
        });

        // Find related posts (same category, excluding current post)
        const related = postsWithCategories
          .filter(p => p.category === category && p.id !== fetchedPost.id)
          .slice(0, 3);

        setRelatedPosts(related);
        setError(null);

        // Track page view with content type and category
        trackPageView(
          location.pathname,
          `${fetchedPost.title} | Blog TTS`,
          'blog_post'
        );

        // Track event for blog post view
        trackEvent('view', 'blog_post', category);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError('Artikel tidak ditemukan atau terjadi kesalahan saat memuat artikel.');

        // Try to find the post in the fallback data
        const fallbackPost = blogPosts.find(p => p.slug === slug);

        if (fallbackPost) {
          // Convert to ExtendedBlogPost format
          const convertedPost: ExtendedBlogPost = {
            id: String(fallbackPost.id),
            title: fallbackPost.title,
            slug: fallbackPost.slug,
            content: fallbackPost.content,
            excerpt: fallbackPost.excerpt,
            featured_image: fallbackPost.image,
            author_id: '',
            author_name: fallbackPost.author,
            status: 'published',
            created_at: fallbackPost.date,
            updated_at: fallbackPost.date,
            category: fallbackPost.category
          };

          setPost(convertedPost);

          // Find related posts from fallback data
          const related = blogPosts
            .filter(p => p.category === fallbackPost.category && p.id !== fallbackPost.id)
            .slice(0, 3)
            .map(p => ({
              id: String(p.id),
              title: p.title,
              slug: p.slug,
              content: p.content,
              excerpt: p.excerpt,
              featured_image: p.image,
              author_id: '',
              author_name: p.author,
              status: 'published' as const,
              created_at: p.date,
              updated_at: p.date,
              category: p.category
            }));

          setRelatedPosts(related);
        }
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchBlogPost();
    }
  }, [slug, location.pathname]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">Artikel tidak ditemukan</h1>
        <p className="mb-4">Maaf, artikel yang Anda cari tidak ditemukan.</p>
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        <Link to="/blog" className="text-blue-600 hover:underline flex items-center">
          <ArrowLeftIcon className="w-4 h-4 mr-1" />
          Kembali ke Blog
        </Link>
      </div>
    );
  }

  // Create structured data for the blog post
  const blogPostingData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt || '',
    "image": [
      post.featured_image || '/images/blog/default-blog.jpg'
    ],
    "datePublished": post.created_at,
    "dateModified": post.updated_at,
    "author": {
      "@type": "Person",
      "name": post.author_name || 'Admin',
      "url": `${window.location.origin}/author/${post.author_id || 'admin'}`
    },
    "publisher": {
      "@type": "Organization",
      "name": "TTS - Teka Teki Silang Online",
      "logo": {
        "@type": "ImageObject",
        "url": `${window.location.origin}/images/logo.png`,
        "width": "192",
        "height": "192"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": window.location.href
    },
    "articleBody": post.content.replace(/<[^>]*>/g, ' ').trim(),
    "keywords": `teka teki silang, TTS, ${post.category || 'Umum'}, blog TTS`,
    "articleSection": post.category || 'Umum',
    "inLanguage": "id-ID"
  };

  // Create breadcrumb structured data
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Beranda",
        "item": window.location.origin
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": `${window.location.origin}/blog`
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": post.title,
        "item": window.location.href
      }
    ]
  };

  // Combine structured data
  const structuredData = [blogPostingData, breadcrumbData];

  return (
    <div className="container mx-auto px-4 py-8">
      <SEO
        title={`${post.title} | Blog TTS`}
        description={post.excerpt || ''}
        keywords={`teka teki silang, TTS, ${post.category || 'Umum'}, blog TTS`}
        ogTitle={post.title}
        ogDescription={post.excerpt || ''}
        ogImage={post.featured_image || '/images/blog/default-blog.jpg'}
        structuredData={structuredData}
        author={post.author_name || 'Admin'}
        publishedDate={post.created_at}
        modifiedDate={post.updated_at}
        canonicalUrl={`${window.location.origin}/blog/${post.slug}`}
      />

      {/* Breadcrumb navigation */}
      <Breadcrumb
        items={[
          { name: 'Blog', href: '/blog' },
          { name: post.title, href: `/blog/${post.slug}`, current: true }
        ]}
      />

      <article className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Featured image */}
        <div className="h-64 md:h-96 overflow-hidden">
          <img
            src={post.featured_image || '/images/blog/default-blog.jpg'}
            alt={post.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/images/blog/default-blog.jpg';
            }}
          />
        </div>

        <div className="p-6 md:p-8">
          {/* Post metadata */}
          <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
            <div className="flex items-center mr-4 mb-2">
              <CalendarIcon className="w-4 h-4 mr-1" />
              <span>{new Date(post.created_at).toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
            </div>
            <div className="flex items-center mr-4 mb-2">
              <UserIcon className="w-4 h-4 mr-1" />
              <span>{post.author_name || 'Admin'}</span>
            </div>
            <div className="flex items-center mb-2">
              <TagIcon className="w-4 h-4 mr-1" />
              <span>{post.category || 'Umum'}</span>
            </div>
          </div>

          {/* Post title */}
          <h1 className="text-3xl md:text-4xl font-bold mb-6">{post.title}</h1>

          {/* Post content */}
          <div
            className="prose prose-blue max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>
      </article>

      {/* Related posts */}
      {relatedPosts.length > 0 && (
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Artikel Terkait</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {relatedPosts.map(relatedPost => (
              <Link
                to={`/blog/${relatedPost.slug}`}
                key={relatedPost.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
              >
                <div className="h-40 overflow-hidden">
                  <img
                    src={relatedPost.featured_image || '/images/blog/default-blog.jpg'}
                    alt={relatedPost.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/images/blog/default-blog.jpg';
                    }}
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-lg mb-2 line-clamp-2">{relatedPost.title}</h3>
                  <p className="text-sm text-gray-600 line-clamp-2">{relatedPost.excerpt}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogPostPage;
