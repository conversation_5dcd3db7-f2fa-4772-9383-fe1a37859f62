import React, { useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { BrainIcon, ClockIcon, HeartIcon, TrophyIcon } from 'lucide-react';
import SEO from '../components/SEO';
import { getStructuredData } from '../utils/seoUtils';

const CasualLandingPage: React.FC = () => {
  // Create specific structured data for this landing page
  const structuredData = useMemo(() => {
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    
    // Base structured data from common utility
    const baseStructuredData = getStructuredData();
    
    // Game structured data for casual players
    const gameStructuredData = {
      "@context": "https://schema.org",
      "@type": "Game",
      "name": "Teka-Teki Silang untuk Hiburan",
      "description": "Kumpulan teka-teki silang untuk hiburan dan mengisi waktu luang. Tersedia berbagai tingkat kesulitan untuk semua usia.",
      "genre": "Puzzle, Word Game",
      "gamePlatform": "Web Browser, Mobile",
      "applicationCategory": "Game",
      "audience": {
        "@type": "Audience",
        "audienceType": "Puzzle Enthusiasts, Casual Gamers"
      },
      "inLanguage": "id-ID",
      "publisher": {
        "@type": "Organization",
        "name": "TTS - Teka Teki Silang Online",
        "url": origin
      }
    };
    
    return [...baseStructuredData, gameStructuredData];
  }, []);

  return (
    <div className="min-h-screen bg-slate-50">
      <SEO
        title="Teka-Teki Silang untuk Hiburan | Asah Otak di Waktu Luang"
        description="Kumpulan teka-teki silang untuk hiburan dan mengisi waktu luang. Tersedia berbagai tingkat kesulitan untuk semua usia. Main gratis sekarang!"
        keywords="teka teki silang hiburan, tts online gratis, game asah otak, permainan kata, teka teki silang santai, game mengisi waktu luang"
        structuredData={structuredData}
      />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Isi Waktu Luang dengan Teka-Teki Silang yang Mengasah Otak
              </h1>
              <p className="text-xl mb-6">
                Nikmati koleksi teka-teki silang dengan berbagai tema menarik. Tersedia untuk semua tingkat kemampuan, dari pemula hingga ahli.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/teka-teki-silang/hiburan" className="bg-white text-purple-700 hover:bg-purple-50 font-bold py-3 px-8 rounded-lg shadow-lg transition">
                  Main Sekarang
                </Link>
                <Link to="/register" className="bg-transparent border-2 border-white hover:bg-white/10 font-bold py-3 px-8 rounded-lg transition">
                  Daftar Gratis
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <img src="/images/default-img.png" alt="Orang bermain Teka-Teki Silang" className="w-full max-w-md rounded-lg shadow-lg" />
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits for Casual Players */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Mengapa Main TTS di Waktu Luang?</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="bg-purple-50 p-6 rounded-lg shadow-md text-center">
              <BrainIcon className="w-12 h-12 mx-auto mb-4 text-purple-600" />
              <h3 className="text-xl font-bold mb-2">Asah Otak</h3>
              <p className="text-gray-700">Latih kemampuan berpikir dan menjaga ketajaman mental Anda.</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg shadow-md text-center">
              <ClockIcon className="w-12 h-12 mx-auto mb-4 text-purple-600" />
              <h3 className="text-xl font-bold mb-2">Isi Waktu Luang</h3>
              <p className="text-gray-700">Cara produktif dan menyenangkan untuk menghabiskan waktu luang Anda.</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg shadow-md text-center">
              <HeartIcon className="w-12 h-12 mx-auto mb-4 text-purple-600" />
              <h3 className="text-xl font-bold mb-2">Kurangi Stres</h3>
              <p className="text-gray-700">Fokus pada teka-teki membantu menenangkan pikiran dan mengurangi stres.</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg shadow-md text-center">
              <TrophyIcon className="w-12 h-12 mx-auto mb-4 text-purple-600" />
              <h3 className="text-xl font-bold mb-2">Tantang Diri</h3>
              <p className="text-gray-700">Tingkatkan level kesulitan seiring kemampuan Anda bertambah.</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Featured Casual Puzzles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">TTS Hiburan Pilihan</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-purple-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-purple-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Tema Hiburan</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Mudah</p>
                <p className="text-gray-700 mb-4">Berisi pertanyaan seputar film, musik, dan budaya pop yang populer di Indonesia.</p>
                <Link to="/teka-teki-silang/hiburan/pop-culture" className="text-purple-600 hover:text-purple-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-pink-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-pink-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Tema Kuliner</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Sedang</p>
                <p className="text-gray-700 mb-4">Berisi pertanyaan seputar makanan, minuman, dan kuliner khas Indonesia.</p>
                <Link to="/teka-teki-silang/hiburan/kuliner" className="text-purple-600 hover:text-purple-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-indigo-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-indigo-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Tema Olahraga</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Menantang</p>
                <p className="text-gray-700 mb-4">Berisi pertanyaan seputar olahraga dan atlet terkenal dari Indonesia dan dunia.</p>
                <Link to="/teka-teki-silang/hiburan/olahraga" className="text-purple-600 hover:text-purple-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Testimonials from Casual Players */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Apa Kata Para Pemain</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"Saya suka main TTS ini di waktu istirahat kantor. Menyegarkan pikiran dan menambah kosakata baru."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold mr-3">L</div>
                <div>
                  <h4 className="font-bold">Linda</h4>
                  <p className="text-sm text-gray-600">Karyawan Swasta</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"TTS tema kuliner sangat menarik! Saya jadi tahu banyak nama makanan tradisional yang belum pernah saya dengar sebelumnya."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold mr-3">H</div>
                <div>
                  <h4 className="font-bold">Hendra</h4>
                  <p className="text-sm text-gray-600">Pecinta Kuliner</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"Di usia pensiun, TTS ini jadi kegiatan favorit saya setiap pagi. Membantu menjaga otak tetap aktif dan menambah pengetahuan baru."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold mr-3">B</div>
                <div>
                  <h4 className="font-bold">Budi</h4>
                  <p className="text-sm text-gray-600">Pensiunan</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Call to Action */}
      <section className="py-16 bg-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Siap Untuk Mengisi Waktu Luang dengan Cara yang Menyenangkan?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Bergabunglah dengan ribuan pemain lainnya dan nikmati koleksi teka-teki silang yang terus diperbarui.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/teka-teki-silang/hiburan"
              className="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 rounded-md font-medium text-lg inline-block"
            >
              Main Sekarang - Gratis!
            </Link>
            <Link
              to="/register"
              className="bg-transparent border-2 border-white hover:bg-white/10 px-8 py-4 rounded-md font-medium text-lg inline-block"
            >
              Daftar Akun
            </Link>
          </div>
          <p className="mt-6 text-sm text-purple-100">
            Tidak perlu registrasi untuk mulai bermain. Daftar untuk menyimpan progres dan membuat TTS sendiri.
          </p>
        </div>
      </section>
    </div>
  );
};

export default CasualLandingPage;
