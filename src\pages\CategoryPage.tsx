import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate, useLocation } from 'react-router-dom';
import { StarIcon, UsersIcon, SearchIcon, FilterIcon, GridIcon, ListIcon, BookOpenIcon, TrophyIcon, ClockIcon } from 'lucide-react';
import { getCrosswords, getCategoryById, getCategories, CrosswordListItem, CrosswordFilters, Category } from '../services/api';
import SEO from '../components/SEO';
import Breadcrumb from '../components/Breadcrumb';

import { FeedbackMessage, CardSkeleton } from '../components/ui/Feedback';

// Interface for crossword display
interface CrosswordDisplay extends CrosswordListItem {
  image: string;
  category_slug?: string;
  category_name?: string;
}

// Enhanced Grid Card Component
const CrosswordGridCard: React.FC<{ crossword: CrosswordDisplay }> = ({ crossword }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'mudah': return 'bg-primary-400 text-newsprint';
      case 'sedang': return 'bg-primary-600 text-newsprint';
      case 'sulit': return 'bg-primary-800 text-newsprint';
      default: return 'bg-ink-secondary text-newsprint';
    }
  };

  const getCrosswordUrl = (crossword: CrosswordDisplay) => {
    if (crossword.category_slug && crossword.slug) {
      return `/teka-teki-silang/${crossword.category_slug}/${crossword.slug}`;
    }
    return `/play/${crossword.id}`;
  };

  const getDynamicIcon = (title: string) => {
    const titleLower = title.toLowerCase();
    if (titleLower.includes('sejarah') || titleLower.includes('budaya')) {
      return <BookOpenIcon className="w-5 h-5 text-ink-muted" />;
    }
    if (titleLower.includes('olahraga') || titleLower.includes('kompetisi')) {
      return <TrophyIcon className="w-5 h-5 text-ink-muted" />;
    }
    return <ClockIcon className="w-5 h-5 text-ink-muted" />;
  };

  return (
    <Link
      to={getCrosswordUrl(crossword)}
      className="card-paper overflow-hidden hover:shadow-paper-lg transition-all duration-300 group"
    >
      <div className="h-32 bg-paper-section relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-paper-200 to-paper-300 opacity-50"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          {getDynamicIcon(crossword.title)}
        </div>
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(crossword.difficulty)}`}>
            {crossword.difficulty}
          </span>
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-bold text-lg mb-2 line-clamp-2 text-ink-dark font-serif group-hover:text-ink-primary transition-colors">
          {crossword.title}
        </h3>
        <p className="text-sm text-ink-muted mb-3 font-serif">Oleh: {crossword.creator}</p>

        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center space-x-3">
            <span className="flex items-center text-ink-muted">
              <StarIcon className="w-4 h-4 text-primary-600 mr-1" />
              {crossword.rating}
            </span>
            <span className="flex items-center text-ink-muted">
              <UsersIcon className="w-4 h-4 text-primary-600 mr-1" />
              {crossword.plays}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

// Enhanced List Card Component
const CrosswordListCard: React.FC<{ crossword: CrosswordDisplay }> = ({ crossword }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'mudah': return 'bg-primary-400 text-newsprint';
      case 'sedang': return 'bg-primary-600 text-newsprint';
      case 'sulit': return 'bg-primary-800 text-newsprint';
      default: return 'bg-ink-secondary text-newsprint';
    }
  };

  const getCrosswordUrl = (crossword: CrosswordDisplay) => {
    if (crossword.category_slug && crossword.slug) {
      return `/teka-teki-silang/${crossword.category_slug}/${crossword.slug}`;
    }
    return `/play/${crossword.id}`;
  };

  return (
    <Link
      to={getCrosswordUrl(crossword)}
      className="card-paper p-4 hover:shadow-paper-lg transition-all duration-300 group flex items-center gap-4"
    >
      <div className="w-16 h-16 bg-paper-section rounded-lg flex items-center justify-center flex-shrink-0">
        <BookOpenIcon className="w-8 h-8 text-ink-muted" />
      </div>

      <div className="flex-grow">
        <h3 className="font-bold text-lg mb-1 text-ink-dark font-serif group-hover:text-ink-primary transition-colors">
          {crossword.title}
        </h3>
        <p className="text-sm text-ink-muted mb-2 font-serif">Oleh: {crossword.creator}</p>

        <div className="flex items-center gap-4 text-sm">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(crossword.difficulty)}`}>
            {crossword.difficulty}
          </span>
          <span className="flex items-center text-ink-muted">
            <StarIcon className="w-4 h-4 text-primary-600 mr-1" />
            {crossword.rating}
          </span>
          <span className="flex items-center text-ink-muted">
            <UsersIcon className="w-4 h-4 text-primary-600 mr-1" />
            {crossword.plays}
          </span>
        </div>
      </div>
    </Link>
  );
};

const CategoryPage: React.FC = () => {
  const { categoryId, categorySlug } = useParams<{ categoryId?: string, categorySlug?: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Enhanced state management
  const [crosswords, setCrosswords] = useState<CrosswordDisplay[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Enhanced filter and search state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('');
  const [difficulty, setDifficulty] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('popular');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Redirect from /play to /teka-teki-silang/semua
  useEffect(() => {
    if (location.pathname === '/play' && !categoryId && !categorySlug) {
      navigate('/teka-teki-silang/semua', { replace: true });
    }
  }, [location.pathname, categoryId, categorySlug, navigate]);

  // Get category name
  const getCategoryName = useCallback(() => {
    if (category) {
      return category.name;
    }
    return categoryId ? 'Loading...' : 'Semua Kategori';
  }, [category, categoryId]);

  // Fetch all categories
  useEffect(() => {
    const fetchAllCategories = async () => {
      try {
        const allCategories = await getCategories();
        setCategories(allCategories);
      } catch (error) {
        console.error('Error fetching all categories:', error);
      }
    };

    fetchAllCategories();
  }, []);

  // Fetch category data
  useEffect(() => {
    const fetchCategory = async () => {
      // If we have a categorySlug but no categoryId, we need to fetch by slug
      if (categorySlug && !categoryId) {
        try {
          // Use the API to get category by slug
          const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:1111'}/api/categories/${categorySlug}`, {
            headers: {
              'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5'
            }
          });
          if (response.ok) {
            const data = await response.json();
            if (data.status === 'success') {
              setCategory(data.data);
            } else {
              console.error('Error fetching category by slug:', data.message);
            }
          } else {
            console.error('Error fetching category by slug:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching category by slug:', error);
        }
        return;
      }

      // If we have a categoryId, fetch by ID as before
      if (!categoryId) {
        setCategory(null);
        return;
      }

      try {
        const categoryData = await getCategoryById(categoryId);
        setCategory(categoryData);
      } catch (error) {
        console.error('Error fetching category:', error);
        // Keep the category as null if there's an error
      }
    };

    fetchCategory();
  }, [categoryId, categorySlug]);

  // Enhanced fetch crosswords function
  const fetchCrosswords = useCallback(async () => {
    setIsLoading(true);
    setLoadingError(null);

    try {
      // Prepare filters
      const filters: CrosswordFilters = {
        is_public: true
      };

      // Prioritize the selected category from the dropdown over URL parameters
      if (selectedCategoryId) {
        filters.category_id = selectedCategoryId;
        console.log('Using selectedCategoryId from dropdown:', selectedCategoryId);
      } else if (category) {
        filters.category_id = category.id;
        console.log('Using category from URL:', category.id);
      } else if (categoryId) {
        filters.category_id = categoryId;
        console.log('Using categoryId from URL param:', categoryId);
      }

      if (difficulty) {
        filters.difficulty = difficulty as 'mudah' | 'sedang' | 'sulit';
      }

      // Add search query if provided
      if (debouncedSearchQuery.trim()) {
        filters.search = debouncedSearchQuery.trim();
      }

      // Get crosswords from API
      const result = await getCrosswords(filters, {
        page: currentPage,
        limit: 12,
        sort: sortBy
      });

      // Add image URLs to crosswords
      const crosswordsWithImages = result.crosswords.map((crossword) => ({
        ...crossword,
        image: `/images/teka-teki-silang.jpg`
      }));

      setCrosswords(crosswordsWithImages);
      setTotalPages(result.pagination?.totalPages || 1);
      setIsLoading(false);

      return crosswordsWithImages;
    } catch (error) {
      console.error('Error fetching crosswords:', error);
      setLoadingError('Gagal memuat daftar teka-teki silang');

      // Fallback to mock data if API fails
      const mockCrosswords = Array(12).fill(null).map((_, index) => ({
        id: `${index + 1}`,
        title: `${getCategoryName()} #${index + 1}`,
        creator: ['admin', 'puzzlemaster', 'wordsmith', 'crosswordpro'][Math.floor(Math.random() * 4)],
        difficulty: ['mudah', 'sedang', 'sulit'][Math.floor(Math.random() * 3)] as 'mudah' | 'sedang' | 'sulit',
        plays: Math.floor(Math.random() * 1000) + 100,
        rating: (Math.random() * 2 + 3).toFixed(1) as unknown as number,
        image: `/images/default-img.png`,
        created_at: new Date().toISOString()
      }));

      setCrosswords(mockCrosswords);
      setTotalPages(3);
      setIsLoading(false);
      return mockCrosswords;
    }
  }, [categoryId, category, currentPage, difficulty, selectedCategoryId, debouncedSearchQuery, sortBy, getCategoryName]);

  // Effect to fetch crosswords when dependencies change
  useEffect(() => {
    fetchCrosswords();
  }, [fetchCrosswords]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  // Enhanced loading state with Indonesian messages
  if (isLoading && crosswords.length === 0) {
    return (
      <div className="min-h-screen bg-paper-main">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <div className="h-8 bg-paper-section rounded animate-pulse mb-4"></div>
            <div className="h-12 bg-paper-section rounded animate-pulse"></div>
          </div>
          <CardSkeleton count={12} className="grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" />
        </div>
      </div>
    );
  }

  // Enhanced error state
  if (loadingError && crosswords.length === 0) {
    return (
      <div className="min-h-screen bg-paper-main">
        <div className="container mx-auto px-4 py-8">
          <FeedbackMessage
            type="error"
            message="Gagal memuat daftar teka-teki silang. Silakan coba lagi."
          />
        </div>
      </div>
    );
  }



  // Get SEO-friendly URL for crossword
  const getCrosswordUrl = (crossword: CrosswordDisplay) => {
    // If we have a category from the current page and category is not semua
    if (category && crossword.slug && category.slug !== 'semua') {
      return `/teka-teki-silang/${category.slug}/${crossword.slug}`;
    }
    // If the crossword has category information from the API
    else if (crossword.category_slug && crossword.slug) {
      return `/teka-teki-silang/${crossword.category_slug}/${crossword.slug}`;
    }
    // Fallback to old URL format
    return `/play/${crossword.id}`;
  };

  // Create structured data for category page
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${getCategoryName()} - Teka Teki Silang`,
    "description": `Kumpulan teka-teki silang dalam kategori ${getCategoryName()}. Mainkan berbagai teka-teki silang dengan tingkat kesulitan berbeda.`,
    "url": window.location.href,
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": crosswords.map((crossword, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "url": `${window.location.origin}${getCrosswordUrl(crossword)}`,
        "name": crossword.title
      }))
    }
  };

  return (
    <div className="min-h-screen bg-paper-main">
      <SEO
        title={`${getCategoryName()} | TTS - Teka Teki Silang Online`}
        description={`Jelajahi teka-teki silang dalam kategori ${getCategoryName()}. Temukan berbagai teka-teki silang dengan tingkat kesulitan berbeda.`}
        keywords={`teka teki silang, TTS, ${getCategoryName()}, puzzle game, kategori TTS`}
        ogTitle={`${getCategoryName()} - Koleksi Teka-Teki Silang`}
        ogDescription={`Jelajahi koleksi teka-teki silang dalam kategori ${getCategoryName()}.`}
        structuredData={structuredData}
      />

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb navigation */}
        <Breadcrumb
          items={[
            { name: 'Main', href: '/teka-teki-silang/semua' },
            ...(category ? [{ name: getCategoryName(), href: `/teka-teki-silang/${category.slug}`, current: true }] :
                categoryId ? [{ name: getCategoryName(), href: `/category/${categoryId}`, current: true }] :
                !category && !categoryId ? [{ name: 'Semua Kategori', href: '/teka-teki-silang/semua', current: true }] : [])
          ]}
        />

        {/* Enhanced Header Section */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-ink-dark font-serif newspaper-heading">
            {getCategoryName()}
          </h1>
          {category?.description && (
            <p className="text-lg text-ink-muted max-w-3xl newspaper-body">
              {category.description}
            </p>
          )}
          <div className="w-24 h-1 bg-ink-primary mt-4"></div>
        </div>

        {/* Enhanced Search and Filter Section */}
        <div className="card-paper p-6 mb-8">
          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ink-muted w-5 h-5" />
              <input
                type="text"
                placeholder="Cari teka-teki silang..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-ink-light rounded-lg focus:outline-none focus:ring-2 focus:ring-ink-primary focus:border-ink-primary font-serif"
              />
            </div>
          </div>

          {/* Filter Toggle Button */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-ink-dark font-serif">Filter & Urutkan</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline flex items-center gap-2"
            >
              <FilterIcon className="w-4 h-4" />
              {showFilters ? 'Sembunyikan Filter' : 'Tampilkan Filter'}
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-ink-dark mb-2 font-serif">
                  Kategori
                </label>
                <select
                  className="w-full px-3 py-2 border border-ink-light rounded-lg focus:outline-none focus:ring-2 focus:ring-ink-primary font-serif"
                  value={selectedCategoryId}
                  onChange={(e) => {
                    setSelectedCategoryId(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="">Semua Kategori</option>
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <label className="block text-sm font-medium text-ink-dark mb-2 font-serif">
                  Tingkat Kesulitan
                </label>
                <select
                  className="w-full px-3 py-2 border border-ink-light rounded-lg focus:outline-none focus:ring-2 focus:ring-ink-primary font-serif"
                  value={difficulty}
                  onChange={(e) => {
                    setDifficulty(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="">Semua Tingkat</option>
                  <option value="mudah">Mudah</option>
                  <option value="sedang">Sedang</option>
                  <option value="sulit">Sulit</option>
                </select>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="block text-sm font-medium text-ink-dark mb-2 font-serif">
                  Urutkan Berdasarkan
                </label>
                <select
                  className="w-full px-3 py-2 border border-ink-light rounded-lg focus:outline-none focus:ring-2 focus:ring-ink-primary font-serif"
                  value={sortBy}
                  onChange={(e) => {
                    setSortBy(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="popular">Terpopuler</option>
                  <option value="newest">Terbaru</option>
                  <option value="rating">Peringkat Tertinggi</option>
                  <option value="difficulty">Tingkat Kesulitan</option>
                </select>
              </div>

              {/* View Mode Toggle */}
              <div>
                <label className="block text-sm font-medium text-ink-dark mb-2 font-serif">
                  Tampilan
                </label>
                <div className="flex rounded-lg border border-ink-light overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`flex-1 px-3 py-2 flex items-center justify-center gap-2 transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-ink-primary text-newsprint'
                        : 'bg-newsprint text-ink-primary hover:bg-paper-section'
                    }`}
                  >
                    <GridIcon className="w-4 h-4" />
                    Grid
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`flex-1 px-3 py-2 flex items-center justify-center gap-2 transition-colors ${
                      viewMode === 'list'
                        ? 'bg-ink-primary text-newsprint'
                        : 'bg-newsprint text-ink-primary hover:bg-paper-section'
                    }`}
                  >
                    <ListIcon className="w-4 h-4" />
                    List
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="flex justify-between items-center text-sm text-ink-muted">
            <span className="font-serif">
              Menampilkan {crosswords.length} dari {totalPages * 12} teka-teki silang
            </span>
            {isLoading && (
              <span className="flex items-center gap-2 font-serif">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-ink-primary"></div>
                Memuat...
              </span>
            )}
          </div>
        </div>

        {/* Enhanced Crossword Listing */}
        <div className={`${
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        } mb-8`}>
          {crosswords.map((crossword) => (
            viewMode === 'grid' ? (
              <CrosswordGridCard key={crossword.id} crossword={crossword} />
            ) : (
              <CrosswordListCard key={crossword.id} crossword={crossword} />
            )
          ))}
        </div>

        {/* Enhanced Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg font-serif transition-colors ${
                  currentPage === 1
                    ? 'bg-paper-section text-ink-light cursor-not-allowed'
                    : 'card-paper text-ink-dark hover:shadow-paper-lg'
                }`}
              >
                ← Sebelumnya
              </button>

              {/* Page Numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-10 h-10 rounded-lg font-serif font-medium transition-colors ${
                        currentPage === pageNum
                          ? 'bg-ink-primary text-newsprint'
                          : 'card-paper text-ink-dark hover:shadow-paper-lg'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg font-serif transition-colors ${
                  currentPage === totalPages
                    ? 'bg-paper-section text-ink-light cursor-not-allowed'
                    : 'card-paper text-ink-dark hover:shadow-paper-lg'
                }`}
              >
                Selanjutnya →
              </button>
            </nav>
          </div>
        )}

        {/* Empty State */}
        {crosswords.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <BookOpenIcon className="w-16 h-16 text-ink-muted mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-ink-dark mb-2 font-serif">
              Tidak ada teka-teki silang ditemukan
            </h3>
            <p className="text-ink-muted font-serif">
              Coba ubah filter atau kata kunci pencarian Anda.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPage;


