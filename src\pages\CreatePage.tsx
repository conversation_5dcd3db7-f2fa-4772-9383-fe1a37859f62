import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CrosswordApp from '../components/CrosswordApp';
import { createCrossword, getCategories, Category } from '../services/api';
import { useCrossword } from '../context/CrosswordContext';
import { useAuth } from '../context/AuthContext';
import { generateDescription } from '../utils/descriptionGeneratorV2';
import { generateTitle } from '../utils/titleGenerator';
import { Wand2 } from 'lucide-react';

const CreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useCrossword();
  const auth = useAuth(); // Get auth context
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [difficulty, setDifficulty] = useState<'mudah' | 'sedang' | 'sulit'>('sedang');
  const [categoryId, setCategoryId] = useState<string>('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Initialize the crossword state when the component mounts
  useEffect(() => {
    // Set the mode to edit
    dispatch({ type: 'SET_MODE', mode: 'edit' });

    // Reset the grid
    dispatch({ type: 'RESET' });
  }, [dispatch]);

  // Fetch categories
  useEffect(() => {
    const fetchCategoriesData = async () => {
      setIsLoadingCategories(true);
      try {
        const data = await getCategories();
        setCategories(data);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. You can still create a crossword without selecting a category.');
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategoriesData();
  }, []);

  const handleSave = async () => {
    // Validate form
    if (!title.trim()) {
      setError('Judul tidak boleh kosong');
      return;
    }

    // Check if there are any words in the crossword
    // We'll check both the words array and the wordPositions array
    // Also add debug logging to help troubleshoot
    console.log("Current crossword state:", state);

    const hasWords = state.words.length > 0 ||
                    state.wordPositions.length > 0 ||
                    Object.keys(state.clues.across).length > 0 ||
                    Object.keys(state.clues.down).length > 0;

    if (!hasWords) {
      setError('Teka-teki silang harus memiliki minimal 1 kata');
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // Save crossword to API
      const crosswordData = {
        title,
        description,
        difficulty,
        category_id: categoryId || undefined, // Only include if selected
        user_id: auth.isAuthenticated && auth.user ? auth.user.id : undefined,
        state: state
      };

      console.log("Saving crossword data:", crosswordData);

      const id = await createCrossword(crosswordData);

      // Show success message
      setError(null);
      setSuccessMessage('Teka-teki silang berhasil disimpan!');

      // Navigate to the crossword page after a short delay
      setTimeout(() => {
        navigate(`/play/${id}`);
      }, 1500);
    } catch (err) {
      console.error('Error saving crossword:', err);
      setError('Gagal menyimpan teka-teki silang. Silakan coba lagi.');
      setSuccessMessage(null);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Buat Teka-Teki Silang Baru</h1>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">
                  Judul
                </label>
                <button
                  type="button"
                  onClick={() => {
                    if (state.words.length > 0) {
                      // Get the selected category name if available
                      const selectedCategory = categories.find(cat => cat.id === categoryId);

                      // Generate title
                      const generatedTitle = generateTitle(
                        state,
                        categoryId || 'umum',
                        selectedCategory?.name || 'Umum',
                        difficulty
                      );

                      setTitle(generatedTitle);
                    } else {
                      setError('Harap tambahkan minimal 1 kata untuk menghasilkan judul otomatis');
                    }
                  }}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Wand2 className="w-4 h-4 mr-1" />
                  Buat Otomatis
                </button>
              </div>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Masukkan judul teka-teki silang"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tingkat Kesulitan
              </label>
              <select
                value={difficulty}
                onChange={(e) => setDifficulty(e.target.value as 'mudah' | 'sedang' | 'sulit')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="mudah">Mudah</option>
                <option value="sedang">Sedang</option>
                <option value="sulit">Sulit</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Kategori
              </label>
              <select
                value={categoryId}
                onChange={(e) => setCategoryId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoadingCategories}
              >
                <option value="">Pilih Kategori (Opsional)</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {isLoadingCategories && (
                <p className="mt-1 text-sm text-gray-500">Memuat kategori...</p>
              )}
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Deskripsi
              </label>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => {
                    if (title.trim() && state.words.length > 0) {
                      // Get the selected category name if available
                      const selectedCategory = categories.find(cat => cat.id === categoryId);

                      // Generate description with custom parameters
                      const generatedDescription = generateDescription(
                        title,
                        state,
                        {
                          TINGKAT_KESULITAN: difficulty,
                          TEMA_UTAMA: selectedCategory?.name || 'umum',
                          KATEGORI_1: selectedCategory?.name || 'pengetahuan umum',
                          KATEGORI_2: 'budaya',
                          KATEGORI_3: 'hiburan',
                          NAMA_KREATOR: auth.isAuthenticated && auth.user?.display_name ? auth.user.display_name : 'Admin',
                        },
                        false // Use basic template
                      );

                      setDescription(generatedDescription);
                    } else {
                      setError('Harap isi judul dan tambahkan minimal 1 kata untuk menghasilkan deskripsi otomatis');
                    }
                  }}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Wand2 className="w-4 h-4 mr-1" />
                  Buat Otomatis
                </button>
                <button
                  type="button"
                  onClick={() => {
                    if (title.trim() && state.words.length > 0) {
                      // Get the selected category name if available
                      const selectedCategory = categories.find(cat => cat.id === categoryId);

                      // Generate description with custom parameters and extended template
                      const generatedDescription = generateDescription(
                        title,
                        state,
                        {
                          TINGKAT_KESULITAN: difficulty,
                          TEMA_UTAMA: selectedCategory?.name || 'umum',
                          KATEGORI_1: selectedCategory?.name || 'pengetahuan umum',
                          KATEGORI_2: 'budaya',
                          KATEGORI_3: 'hiburan',
                          NAMA_KREATOR: auth.isAuthenticated && auth.user?.display_name ? auth.user.display_name : 'Admin',
                          EDISI_VOLUME: Math.floor(Math.random() * 10) + 1,
                          JUMLAH_KATA_BARU: Math.floor(state.words.length * 0.7),
                          JUMLAH_KATA_TERSEMBUNYI: Math.floor(state.words.length * 0.3),
                        },
                        true // Use extended template
                      );

                      setDescription(generatedDescription);
                    } else {
                      setError('Harap isi judul dan tambahkan minimal 1 kata untuk menghasilkan deskripsi otomatis');
                    }
                  }}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-800"
                >
                  <Wand2 className="w-4 h-4 mr-1" />
                  Detail+
                </button>
              </div>
            </div>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={5}
              placeholder="Masukkan deskripsi teka-teki silang (opsional) atau gunakan tombol 'Buat Otomatis'"
            />
            <p className="mt-1 text-xs text-gray-500">
              Deskripsi akan ditampilkan pada halaman main dan membantu pemain memahami teka-teki silang Anda.
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Editor Teka-Teki Silang</h2>
          <p className="text-gray-600 mb-6">
            Gunakan editor di bawah ini untuk membuat teka-teki silang Anda. Klik pada sel untuk menambahkan kata baru.
          </p>

          <CrosswordApp />
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            {successMessage}
          </div>
        )}

        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSaving ? 'Menyimpan...' : 'Simpan Teka-Teki Silang'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreatePage;
