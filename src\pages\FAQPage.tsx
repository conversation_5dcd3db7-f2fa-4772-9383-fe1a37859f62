import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import SEO from '../components/SEO';
import HelpNav from '../components/HelpNav';

const FAQPage: React.FC = () => {
  // Create FAQ structured data
  const structuredData = useMemo(() => {
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    
    const faqStructuredData = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Apa itu Teka-Teki Silang Online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Teka-Teki Silang Online adalah platform permainan kata silang interaktif berbahasa Indonesia yang dapat dimainkan langsung di browser tanpa perlu mengunduh aplikasi. Tersedia ribuan puzzle dengan berbagai tema dan tingkat kesulitan."
          }
        },
        {
          "@type": "Question",
          "name": "Apakah bermain teka-teki silang gratis?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Ya, semua teka-teki silang di platform kami dapat dimainkan secara gratis. Anda dapat mengakses ribuan puzzle tanpa biaya apapun."
          }
        },
        {
          "@type": "Question",
          "name": "Apa manfaat bermain teka-teki silang?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Bermain teka-teki silang memiliki banyak manfaat, di antaranya meningkatkan kosakata, melatih kemampuan berpikir logis, meningkatkan konsentrasi, mencegah penurunan fungsi kognitif, dan sebagai sarana hiburan yang mendidik."
          }
        },
        {
          "@type": "Question",
          "name": "Bagaimana cara bermain teka-teki silang online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Cukup kunjungi halaman 'Main' di website kami, pilih teka-teki yang ingin dimainkan, dan mulai mengisi kotak-kotak dengan jawaban berdasarkan petunjuk yang diberikan. Anda dapat bermain di komputer maupun perangkat mobile."
          }
        },
        {
          "@type": "Question",
          "name": "Bisakah saya membuat teka-teki silang sendiri?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Ya, Anda dapat membuat teka-teki silang sendiri dengan mendaftar akun di platform kami. Setelah login, kunjungi halaman 'Buat TTS' dan ikuti panduan yang tersedia."
          }
        }
      ]
    };
    
    return [faqStructuredData];
  }, []);

  return (
    <div className="min-h-screen bg-slate-50">
      <SEO
        title="Pertanyaan Umum (FAQ) | TTS - Teka Teki Silang Online"
        description="Temukan jawaban untuk pertanyaan umum seputar teka-teki silang online. Panduan lengkap untuk pemain baru dan lama."
        keywords="faq tts, pertanyaan teka teki silang, bantuan tts, cara main tts, teka teki silang online"
        structuredData={structuredData}
      />
      
      {/* FAQ Header */}
      <section className="bg-gradient-to-r from-blue-700 to-blue-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Pertanyaan Umum (FAQ)
          </h1>
          <p className="text-lg mb-0 max-w-3xl">
            Temukan jawaban untuk pertanyaan umum seputar teka-teki silang online di platform kami.
          </p>
        </div>
      </section>
      
      {/* FAQ Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <HelpNav />
            
            {/* FAQ Categories */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4">Kategori</h2>
              <div className="flex flex-wrap gap-2">
                <a href="#umum" className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200">
                  Umum
                </a>
                <a href="#bermain" className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200">
                  Bermain
                </a>
                <a href="#akun" className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200">
                  Akun
                </a>
                <a href="#teknis" className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200">
                  Teknis
                </a>
              </div>
            </div>
            
            {/* General Questions */}
            <div id="umum" className="mb-12">
              <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">Pertanyaan Umum</h2>
              
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apa itu Teka-Teki Silang Online?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Teka-Teki Silang Online adalah platform permainan kata silang interaktif berbahasa Indonesia yang dapat dimainkan langsung di browser tanpa perlu mengunduh aplikasi. Tersedia ribuan puzzle dengan berbagai tema dan tingkat kesulitan.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apakah bermain teka-teki silang gratis?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Ya, semua teka-teki silang di platform kami dapat dimainkan secara gratis. Anda dapat mengakses ribuan puzzle tanpa biaya apapun.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apa manfaat bermain teka-teki silang?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Bermain teka-teki silang memiliki banyak manfaat, di antaranya:
                    </p>
                    <ul>
                      <li>Meningkatkan kosakata dan kemampuan bahasa</li>
                      <li>Melatih kemampuan berpikir logis dan pemecahan masalah</li>
                      <li>Meningkatkan konsentrasi dan fokus</li>
                      <li>Mengurangi stres dan memberikan hiburan</li>
                      <li>Memperluas pengetahuan umum</li>
                    </ul>
                    <p>
                      Untuk informasi lebih lanjut, kunjungi halaman <Link to="/manfaat-tts" className="text-blue-600 hover:text-blue-800">Manfaat Teka-Teki Silang</Link>.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Playing Questions */}
            <div id="bermain" className="mb-12">
              <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">Bermain</h2>
              
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Bagaimana cara bermain teka-teki silang?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Untuk bermain teka-teki silang, ikuti langkah-langkah berikut:
                    </p>
                    <ol>
                      <li>Pilih teka-teki yang ingin Anda mainkan dari halaman utama atau kategori</li>
                      <li>Klik pada kotak atau petunjuk untuk memulai</li>
                      <li>Ketik jawaban Anda menggunakan keyboard</li>
                      <li>Gunakan tombol Tab atau klik kotak lain untuk berpindah</li>
                      <li>Klik tombol "Periksa" untuk memeriksa jawaban Anda</li>
                    </ol>
                    <p>
                      Untuk panduan lebih detail, kunjungi halaman <Link to="/cara-bermain" className="text-blue-600 hover:text-blue-800">Cara Bermain</Link>.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apa arti tingkat kesulitan pada teka-teki?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Kami mengkategorikan teka-teki silang dalam beberapa tingkat kesulitan:
                    </p>
                    <ul>
                      <li><strong>Mudah</strong>: Cocok untuk pemula, menggunakan kata-kata umum dan petunjuk sederhana</li>
                      <li><strong>Sedang</strong>: Memerlukan pengetahuan umum yang lebih luas dan beberapa kata yang kurang umum</li>
                      <li><strong>Sulit</strong>: Menggunakan kosakata yang lebih kompleks dan petunjuk yang lebih menantang</li>
                      <li><strong>Ahli</strong>: Dirancang untuk pemain berpengalaman, dengan petunjuk kompleks dan kata-kata yang jarang digunakan</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Bisakah saya menyimpan kemajuan permainan?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Ya, kemajuan permainan Anda akan otomatis disimpan jika Anda sudah login ke akun Anda. Anda dapat melanjutkan permainan kapan saja dengan mengunjungi kembali teka-teki yang sama.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Account Questions */}
            <div id="akun" className="mb-12">
              <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">Akun</h2>
              
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Bagaimana cara membuat akun?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Untuk membuat akun, klik tombol "Daftar" di pojok kanan atas halaman. Isi formulir pendaftaran dengan informasi yang diminta, lalu klik "Daftar". Anda juga dapat mendaftar menggunakan akun Google Anda.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Bisakah saya membuat teka-teki silang sendiri?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Ya, Anda dapat membuat teka-teki silang sendiri dengan mendaftar akun di platform kami. Setelah login, kunjungi halaman 'Buat TTS' dan ikuti panduan yang tersedia.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Technical Questions */}
            <div id="teknis" className="mb-12">
              <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">Teknis</h2>
              
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apakah saya perlu menginstal aplikasi untuk bermain?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Tidak, platform kami adalah aplikasi berbasis web yang dapat diakses langsung melalui browser Anda. Tidak perlu mengunduh atau menginstal aplikasi tambahan.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apakah platform ini berfungsi di perangkat mobile?</h3>
                  <div className="prose prose-lg">
                    <p>
                      Ya, platform kami dirancang dengan responsif dan dapat dimainkan dengan baik di smartphone dan tablet. Antarmuka akan menyesuaikan dengan ukuran layar perangkat Anda.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Contact Section */}
            <div className="bg-blue-50 rounded-lg p-6">
              <h2 className="text-xl font-bold mb-4">Masih punya pertanyaan?</h2>
              <p className="mb-4">
                Jika Anda tidak menemukan jawaban untuk pertanyaan Anda, silakan hubungi kami melalui:
              </p>
              <p>
                Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FAQPage;
