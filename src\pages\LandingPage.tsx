import React, { lazy, Suspense, useMemo, memo } from 'react';
import { Link } from 'react-router-dom';
import { TrendingUpIcon, BookOpenIcon, UsersIcon, PlayIcon, PlusIcon, ArrowRightIcon } from 'lucide-react';
import SEO from '../components/SEO';
import { getStructuredData } from '../utils/seoUtils';

// Lazy load components to reduce initial bundle size
const FeaturedCrosswords = lazy(() => import('../components/FeaturedCrosswords'));
const CategoryList = lazy(() => import('../components/CategoryList'));

// Enhanced loading component with better visual feedback
const SectionLoader = memo(() => (
  <div className="flex flex-col justify-center items-center p-12">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-ink-primary spinner mb-4"></div>
    <p className="text-ink-muted text-sm font-serif">Memuat konten...</p>
  </div>
));

// Enhanced feature card component with better styling and animations
const FeatureCard = memo(({
  icon,
  title,
  description,
  delay = 0
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}) => (
  <div
    className="card-paper p-6 text-center group hover:shadow-paper-lg transition-all duration-300"
    style={{ animationDelay: `${delay}ms` }}
  >
    <div className="mb-4 transform group-hover:scale-110 transition-transform duration-300">
      {icon}
    </div>
    <h3 className="text-xl font-bold mb-3 text-ink-dark font-serif">{title}</h3>
    <p className="text-ink-muted leading-relaxed">{description}</p>
  </div>
));

// Enhanced benefit card component
const BenefitCard = memo(({
  number,
  title,
  description,
  delay = 0
}: {
  number: number;
  title: string;
  description: string;
  delay?: number;
}) => (
  <div
    className="card-paper p-6 text-center group hover:shadow-paper-lg transition-all duration-300"
    style={{ animationDelay: `${delay}ms` }}
  >
    <div className="w-16 h-16 bg-ink-primary rounded-full flex items-center justify-center text-newsprint text-2xl font-bold mb-4 mx-auto group-hover:bg-ink-secondary transition-colors duration-300">
      {number}
    </div>
    <h3 className="text-xl font-bold mb-3 text-ink-dark font-serif">{title}</h3>
    <p className="text-ink-muted leading-relaxed">{description}</p>
  </div>
));

const LandingPage: React.FC = () => {
  // Use useMemo to prevent recalculating structured data on each render
  const structuredData = useMemo(() => getStructuredData(), []);

  return (
    <div className="min-h-screen bg-paper-main">
      <SEO
        title="Teka Teki Silang Online Gratis | Asah Otak & Tambah Kosakata"
        description="Mainkan teka-teki silang online berbahasa Indonesia gratis. Game asah otak dengan ribuan puzzle, berbagai tema dan tingkat kesulitan. Tanpa unduhan, langsung main!"
        keywords="teka teki silang online, game teka teki silang, teka teki silang gratis, main teka teki silang, aplikasi teka teki silang, permainan kata silang, game asah otak online, teka teki silang bahasa Indonesia"
        structuredData={structuredData}
      />
      {/* Hero Section - Optimized contrast and readability */}
      <section className="relative bg-gradient-to-br from-paper-900 via-ink-primary to-paper-800 text-white py-24 overflow-hidden">
        {/* Background pattern overlay with better contrast */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'var(--newspaper-grid)',
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        {/* Subtle gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/10"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-3/5 text-center lg:text-left">
              <div className="mb-6">
                <span className="inline-flex items-center bg-white/15 text-white text-sm px-4 py-2 rounded-full font-medium backdrop-blur-sm border border-white/25 shadow-lg">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse shadow-sm"></span>
                  Platform Teka-Teki Silang #1 di Indonesia
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-serif leading-tight text-white drop-shadow-sm">
                Mainkan <span className="text-yellow-100">Teka-Teki Silang</span> Online Gratis
              </h1>

              <p className="text-xl md:text-2xl mb-8 text-gray-100 leading-relaxed max-w-2xl drop-shadow-sm">
                Asah otak dan perkaya kosakata dengan ribuan puzzle interaktif dalam bahasa Indonesia.
                <span className="block mt-2 text-lg text-gray-200">Gratis, tanpa unduhan, dan bisa dimainkan kapan saja!</span>
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link
                  to="/teka-teki-silang/semua"
                  className="group bg-white text-gray-900 hover:bg-gray-100 font-bold py-4 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center border-2 border-white"
                >
                  <PlayIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                  Main Sekarang
                </Link>
                <Link
                  to="/create"
                  className="group bg-transparent border-2 border-white hover:bg-white/15 font-bold py-4 px-8 rounded-lg transition-all duration-300 text-white flex items-center justify-center backdrop-blur-sm"
                >
                  <PlusIcon className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform" />
                  Buat TTS Sendiri
                </Link>
              </div>

              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-200">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-400 rounded-full mr-2 shadow-sm"></div>
                  <span className="font-medium">100% Gratis</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-400 rounded-full mr-2 shadow-sm"></div>
                  <span className="font-medium">Tanpa Registrasi</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-purple-400 rounded-full mr-2 shadow-sm"></div>
                  <span className="font-medium">Mobile Friendly</span>
                </div>
              </div>
            </div>

            <div className="lg:w-2/5 flex justify-center">
              <div className="relative">
                {/* Decorative elements with better visibility */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>

                {/* Main image container with better contrast */}
                <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
                  <img
                    src="/images/default-img.png"
                    alt="Teka-Teki Silang Online Gratis - Tekateki.io"
                    className="w-80 h-80 object-cover rounded-xl shadow-2xl"
                    loading="eager"
                  />

                  {/* Floating stats with high contrast */}
                  <div className="absolute -top-6 -right-6 bg-white text-gray-900 px-4 py-2 rounded-lg shadow-xl border border-gray-200">
                    <div className="text-2xl font-bold text-gray-900">1000+</div>
                    <div className="text-xs text-gray-600 font-medium">Puzzle Tersedia</div>
                  </div>

                  <div className="absolute -bottom-6 -left-6 bg-white text-gray-900 px-4 py-2 rounded-lg shadow-xl border border-gray-200">
                    <div className="text-2xl font-bold text-gray-900">50K+</div>
                    <div className="text-xs text-gray-600 font-medium">Pengguna Aktif</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Crosswords */}
      <section className="py-20 bg-paper-main">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-ink-dark font-serif">
              Teka-Teki Silang Unggulan
            </h2>
            <p className="text-lg text-ink-muted max-w-2xl mx-auto leading-relaxed">
              Pilihan terbaik dari koleksi puzzle kami. Mulai dari tingkat pemula hingga ahli,
              temukan tantangan yang sesuai dengan kemampuan Anda.
            </p>
            <div className="w-24 h-1 bg-ink-primary mx-auto mt-6"></div>
          </div>

          <Suspense fallback={<SectionLoader />}>
            <FeaturedCrosswords />
          </Suspense>

          <div className="text-center mt-12">
            <Link
              to="/teka-teki-silang/semua"
              className="group inline-flex items-center btn-outline hover:btn-primary transition-all duration-300"
            >
              Lihat Semua Puzzle
              <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-20 bg-paper-section">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-ink-dark font-serif">
              Jelajahi Kategori Menarik
            </h2>
            <p className="text-lg text-ink-muted max-w-2xl mx-auto leading-relaxed">
              Dari sejarah hingga teknologi, dari budaya hingga olahraga.
              Temukan puzzle dengan tema yang Anda sukai dan perluas wawasan.
            </p>
            <div className="w-24 h-1 bg-ink-primary mx-auto mt-6"></div>
          </div>

          <Suspense fallback={<SectionLoader />}>
            <CategoryList />
          </Suspense>
        </div>
      </section>

      {/* Resources Section - New section highlighting our SEO-enhancing pages */}
      <section className="py-12 bg-paper-main">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center text-ink-dark mb-8 font-serif">
            Panduan & Sumber Daya
          </h2>
          <p className="text-center text-primary-700 max-w-3xl mx-auto mb-12">
            Pelajari cara bermain teka-teki silang, dapatkan tips dan strategi, serta temukan manfaat bermain TTS untuk kesehatan otak dan kemampuan bahasa.
          </p>
          <div className="grid md:grid-cols-3 gap-8">
            <Link to="/cara-bermain" className="card-paper p-6 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Cara Bermain</h3>
              <p className="text-primary-700 text-center">Panduan lengkap cara bermain teka-teki silang untuk pemula. Pelajari aturan dasar dan teknik bermain.</p>
            </Link>
            <Link to="/tips-strategi-tts" className="card-paper p-6 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Tips & Strategi</h3>
              <p className="text-primary-700 text-center">Kumpulan tips dan strategi untuk menyelesaikan teka-teki silang dengan lebih cepat dan efektif.</p>
            </Link>
            <Link to="/manfaat-tts" className="card-paper p-6 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Manfaat TTS</h3>
              <p className="text-primary-700 text-center">Pelajari manfaat bermain teka-teki silang untuk kesehatan otak, kemampuan bahasa, dan perkembangan kognitif.</p>
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits Section - Enhanced with better visual design */}
      <section className="py-20 bg-paper-section">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-ink-dark font-serif">
              Manfaat Bermain Teka-Teki Silang Online
            </h2>
            <p className="text-lg text-ink-muted max-w-3xl mx-auto leading-relaxed">
              Lebih dari sekadar hiburan, teka-teki silang memberikan manfaat nyata untuk
              perkembangan kognitif dan kemampuan bahasa Anda.
            </p>
            <div className="w-24 h-1 bg-ink-primary mx-auto mt-6"></div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <BenefitCard
              number={1}
              title="Perkaya Kosakata"
              description="Temukan dan pelajari kata-kata baru dalam bahasa Indonesia melalui permainan yang menyenangkan dan interaktif."
              delay={0}
            />
            <BenefitCard
              number={2}
              title="Latih Kemampuan Berpikir"
              description="Asah otak dan tingkatkan kemampuan berpikir logis dengan memecahkan teka-teki yang menantang setiap hari."
              delay={100}
            />
            <BenefitCard
              number={3}
              title="Hiburan Edukatif"
              description="Nikmati hiburan yang mendidik dan bermanfaat untuk semua usia, dari pelajar hingga dewasa dan lansia."
              delay={200}
            />
          </div>
        </div>
      </section>

      {/* Features - Enhanced with better styling and animations */}
      <section className="py-20 bg-paper-main">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-ink-dark font-serif">
              Mengapa Memilih Tekateki.io?
            </h2>
            <p className="text-lg text-ink-muted max-w-2xl mx-auto leading-relaxed">
              Platform teka-teki silang terdepan dengan fitur-fitur unggulan yang
              dirancang khusus untuk pengalaman bermain yang optimal.
            </p>
            <div className="w-24 h-1 bg-ink-primary mx-auto mt-6"></div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<TrendingUpIcon className="w-12 h-12 mx-auto text-ink-primary" />}
              title="Konten Selalu Terbaru"
              description="Nikmati teka-teki silang baru setiap hari dengan berbagai tingkat kesulitan, dari pemula hingga ahli."
              delay={0}
            />
            <FeatureCard
              icon={<BookOpenIcon className="w-12 h-12 mx-auto text-ink-primary" />}
              title="Kategori Beragam"
              description="Jelajahi puzzle dalam berbagai kategori menarik mulai dari sejarah, budaya, teknologi, hingga hiburan."
              delay={100}
            />
            <FeatureCard
              icon={<UsersIcon className="w-12 h-12 mx-auto text-ink-primary" />}
              title="Komunitas Aktif"
              description="Bergabung dengan komunitas pecinta TTS, mainkan kreasi pengguna lain dan bagikan puzzle buatan Anda."
              delay={200}
            />
          </div>
        </div>
      </section>

      {/* User Segments Section - New section targeting different user groups */}
      <section className="py-16 bg-paper-main">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center text-ink-dark mb-12 font-serif">
            Teka-Teki Silang untuk Semua
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card-paper p-6 border-t-4 border-primary-600 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Untuk Pelajar</h3>
              <p className="text-primary-700 text-center mb-4">Tingkatkan kosakata dan pengetahuan umum sambil bermain. Cocok untuk tugas sekolah dan belajar bahasa Indonesia.</p>
              <div className="text-center">
                <Link to="/untuk-pelajar" className="text-ink hover:text-primary-600 font-medium">
                  Lihat TTS untuk Pelajar &rarr;
                </Link>
              </div>
            </div>
            <div className="card-paper p-6 border-t-4 border-primary-700 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Untuk Guru</h3>
              <p className="text-primary-700 text-center mb-4">Gunakan teka-teki silang sebagai alat pembelajaran interaktif di kelas. Buat TTS kustom sesuai materi pelajaran.</p>
              <div className="text-center">
                <Link to="/untuk-guru" className="text-ink hover:text-primary-600 font-medium">
                  Lihat TTS untuk Guru &rarr;
                </Link>
              </div>
            </div>
            <div className="card-paper p-6 border-t-4 border-primary-800 hover:shadow-paper-lg transition-shadow">
              <h3 className="text-xl font-bold text-center mb-3 text-ink-dark">Untuk Hiburan</h3>
              <p className="text-primary-700 text-center mb-4">Isi waktu luang dengan permainan yang mengasah otak. Tersedia berbagai tingkat kesulitan untuk semua usia.</p>
              <div className="text-center">
                <Link to="/untuk-hiburan" className="text-ink hover:text-primary-600 font-medium">
                  Lihat TTS untuk Hiburan &rarr;
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - New section with social proof */}
      <section className="py-16 bg-paper-section">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center text-ink-dark mb-12 font-serif">
            Apa Kata Mereka Tentang Kami
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card-paper p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center text-ink font-bold mr-4">AS</div>
                <div>
                  <h4 className="font-bold text-ink-dark">Andi Suryadi</h4>
                  <p className="text-sm text-primary-600">Guru Bahasa Indonesia</p>
                </div>
              </div>
              <p className="text-primary-700 italic">"Saya menggunakan platform ini untuk membuat teka-teki silang sebagai bahan ajar di kelas. Siswa sangat antusias dan kosakata mereka meningkat pesat!"</p>
            </div>
            <div className="card-paper p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center text-ink font-bold mr-4">DP</div>
                <div>
                  <h4 className="font-bold text-ink-dark">Dewi Pratiwi</h4>
                  <p className="text-sm text-primary-600">Mahasiswa</p>
                </div>
              </div>
              <p className="text-primary-700 italic">"TTS online ini sangat membantu saya belajar istilah-istilah baru. Interfacenya user-friendly dan bisa dimainkan di mana saja lewat HP."</p>
            </div>
            <div className="card-paper p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center text-ink font-bold mr-4">BW</div>
                <div>
                  <h4 className="font-bold text-ink-dark">Budi Winarno</h4>
                  <p className="text-sm text-primary-600">Pensiunan</p>
                </div>
              </div>
              <p className="text-primary-700 italic">"Di usia pensiun, teka-teki silang ini jadi kegiatan favorit saya setiap pagi. Membantu menjaga otak tetap aktif dan menambah pengetahuan baru."</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
