import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState<string | null>(null);
  const auth = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null); // Clear local form error
    auth.clearError(); // Clear previous errors from context

    if (!email.trim() || !password.trim()) {
      setFormError('Email and password are required.');
      return;
    }

    try {
      await auth.login({ email, password });
      // On successful login, navigate to home page or dashboard
      navigate('/');
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : '<PERSON><PERSON> failed. Please try again.';
      setFormError(message);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-10 rounded-xl shadow-lg">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {formError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span className="block sm:inline">{formError}</span>
            </div>
          )}
           {auth.error && !formError && ( // Display context error if no form error
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span className="block sm:inline">{auth.error}</span>
            </div>
          )}

          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm">
              {/* Placeholder for "Forgot password?" if needed later */}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={auth.isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {auth.isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>

        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <div className="mt-6">
            <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID || ""}>
              <div className="flex justify-center">
                {/* Display Google client ID for debugging */}
                {/* {import.meta.env.DEV && (
                  <div className="text-xs text-gray-400 mb-2">
                    Using Google Client ID: {import.meta.env.VITE_GOOGLE_CLIENT_ID ?
                      `${import.meta.env.VITE_GOOGLE_CLIENT_ID.substring(0, 8)}...` :
                      'Not set'}
                  </div>
                )} */}

                <GoogleLogin
                  onSuccess={(credentialResponse) => {
                    if (credentialResponse.credential) {
                      try {
                        // Decode the JWT token to get user information
                        const base64Url = credentialResponse.credential.split('.')[1];
                        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                        }).join(''));

                        const decoded = JSON.parse(jsonPayload);

                        console.log("Google login successful, token info:", {
                          sub: decoded.sub,
                          email: decoded.email,
                          name: decoded.name,
                          picture: decoded.picture ? 'present' : 'not present',
                          token_length: credentialResponse.credential.length
                        });

                        auth.loginWithGoogle({
                          token_id: credentialResponse.credential,
                          email: decoded.email,
                          name: decoded.name,
                          picture: decoded.picture
                        }).then(() => {
                          navigate('/');
                        }).catch((err) => {
                          console.error("Google login API error:", err);
                          const message = err instanceof Error ? err.message : 'Google login failed. Please try again.';
                          setFormError(message);
                        });
                      } catch (error) {
                        console.error("Error decoding Google token:", error);
                        setFormError('Error processing Google login. Please try again.');
                      }
                    } else {
                      console.error("No credential in Google response");
                      setFormError('Google login failed: No credential received');
                    }
                  }}
                  onError={() => {
                    console.error("Google login error occurred");
                    setFormError('Google login failed. Please try again.');
                  }}
                  useOneTap
                  text="signin_with"
                  shape="rectangular"
                  theme="outline"
                  logo_alignment="center"
                  width="250px"
                />
              </div>
            </GoogleOAuthProvider>
          </div>
        </div>

        <div className="mt-6 text-sm text-center">
          <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500">
            Don't have an account? Sign up
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
