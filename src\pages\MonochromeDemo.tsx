import React, { useState } from 'react';
import {
  MonochromeContainer,
  MonochromeGrid,
  MonochromeModal
} from '../components/ui/MonochromeLayout';
import {
  MonochromeCard,
  MonochromeButton,
  MonochromeInput,
  MonochromeTextarea
} from '../components/ui/MonochromeCard';
import {
  MonochromeHeading,
  MonochromeText,
  MonochromeQuote,
  MonochromeCode,
  MonochromeBadge,
  MonochromeDivider
} from '../components/ui/MonochromeTypography';
import {
  MonochromeCrosswordGrid,
  MonochromeClueList,
  MonochromeCrosswordStats
} from '../components/ui/MonochromeCrossword';

const MonochromeDemo: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCell, setSelectedCell] = useState<[number, number] | null>([1, 1]);

  // Sample crossword data
  const sampleGrid = [
    [null, 'K', 'A', 'T', 'A', null],
    ['B', 'U', 'K', 'U', null, 'M'],
    ['A', 'N', 'A', 'K', null, 'A'],
    ['H', 'A', 'R', 'I', null, 'T'],
    [null, 'N', null, null, null, 'A'],
    [null, 'G', 'U', 'R', 'U', null]
  ];

  const sampleNumbers = [
    [null, 1, null, null, null, null],
    [2, null, null, null, null, 3],
    [null, null, null, null, null, null],
    [null, null, null, null, null, null],
    [null, 4, null, null, null, null],
    [null, 5, null, null, null, null]
  ];

  const sampleCluesAcross = [
    { number: 1, clue: 'Satuan bahasa yang mengandung arti', isCompleted: true },
    { number: 2, clue: 'Kumpulan halaman yang dijilid', isCompleted: false },
    { number: 3, clue: 'Hari ketujuh dalam seminggu', isCompleted: false }
  ];

  const sampleCluesDown = [
    { number: 4, clue: 'Bunga yang harum', isCompleted: false },
    { number: 5, clue: 'Pengajar di sekolah', isCompleted: true }
  ];

  return (
    <div className="paper-bg-medium min-h-screen">
      <header className="nav-paper">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <h1 className="ink-text text-4xl font-bold text-center font-serif">
            TEKATEKI.IO
          </h1>
          <p className="ink-text-muted text-center mt-2 font-serif">
            Demo Komponen UI Paper & Ink Theme
          </p>
        </div>
      </header>

      <MonochromeContainer className="py-8">
        {/* Typography Section */}
        <section className="paper-card p-6 mb-8">
          <h2 className="ink-text text-3xl font-bold font-serif mb-2">Typography</h2>
          <p className="ink-text-muted font-serif text-lg mb-6">Komponen teks dengan gaya koran klasik</p>
          <div className="h-px bg-black opacity-20 mb-6"></div>
          <div className="space-y-6">
            <div>
              <MonochromeHeading level={2} variant="newspaper">
                Heading Newspaper Style
              </MonochromeHeading>
              <MonochromeHeading level={3} variant="serif">
                Heading Serif Style
              </MonochromeHeading>
              <MonochromeHeading level={4} variant="mono">
                Heading Mono Style
              </MonochromeHeading>
            </div>

            <div>
              <MonochromeText variant="lead">
                Ini adalah teks lead yang digunakan untuk paragraf pembuka dengan ukuran yang lebih besar.
              </MonochromeText>
              <MonochromeText variant="body">
                Ini adalah teks body normal yang digunakan untuk konten utama. Font serif memberikan
                kesan klasik dan mudah dibaca seperti koran tradisional.
              </MonochromeText>
              <MonochromeText variant="mono" size="sm">
                Ini adalah teks monospace yang cocok untuk kode atau data.
              </MonochromeText>
            </div>

            <MonochromeQuote
              variant="bordered"
              author="Penulis Terkenal"
              source="Buku Klasik"
            >
              Kutipan ini menunjukkan bagaimana teks quote ditampilkan dengan gaya yang elegan
              dan mudah dibedakan dari teks biasa.
            </MonochromeQuote>

            <div className="flex flex-wrap gap-2">
              <MonochromeBadge variant="default">Default</MonochromeBadge>
              <MonochromeBadge variant="outline">Outline</MonochromeBadge>
              <MonochromeBadge variant="filled">Filled</MonochromeBadge>
              <MonochromeBadge variant="dot">With Dot</MonochromeBadge>
            </div>
          </div>
        </section>

        <MonochromeDivider label="Komponen UI" className="my-8" />

        {/* Cards and Buttons Section */}
        <section className="paper-card p-6 mb-8">
          <h2 className="ink-text text-3xl font-bold font-serif mb-2">Cards & Buttons</h2>
          <p className="ink-text-muted font-serif text-lg mb-6">Komponen interaktif dengan desain paper & ink</p>
          <div className="h-px bg-black opacity-20 mb-6"></div>
          <MonochromeGrid cols={3} gap="lg">
            <MonochromeCard variant="default" className="p-6">
              <MonochromeHeading level={4} className="mb-3">Default Card</MonochromeHeading>
              <MonochromeText variant="body" className="mb-4">
                Card dengan style default yang bersih dan minimalis.
              </MonochromeText>
              <MonochromeButton variant="primary" size="sm">
                Action
              </MonochromeButton>
            </MonochromeCard>

            <MonochromeCard variant="elevated" className="p-6">
              <MonochromeHeading level={4} className="mb-3">Elevated Card</MonochromeHeading>
              <MonochromeText variant="body" className="mb-4">
                Card dengan shadow yang lebih menonjol untuk emphasis.
              </MonochromeText>
              <MonochromeButton variant="secondary" size="sm">
                Action
              </MonochromeButton>
            </MonochromeCard>

            <MonochromeCard variant="textured" className="p-6">
              <MonochromeHeading level={4} className="mb-3">Textured Card</MonochromeHeading>
              <MonochromeText variant="body" className="mb-4">
                Card dengan tekstur paper untuk efek vintage.
              </MonochromeText>
              <MonochromeButton variant="outline" size="sm">
                Action
              </MonochromeButton>
            </MonochromeCard>
          </MonochromeGrid>

          <div className="mt-8 flex flex-wrap gap-4">
            <MonochromeButton variant="primary">Primary Button</MonochromeButton>
            <MonochromeButton variant="secondary">Secondary Button</MonochromeButton>
            <MonochromeButton variant="outline">Outline Button</MonochromeButton>
            <MonochromeButton variant="ghost">Ghost Button</MonochromeButton>
            <MonochromeButton
              variant="primary"
              onClick={() => setIsModalOpen(true)}
            >
              Open Modal
            </MonochromeButton>
          </div>
        </section>

        <MonochromeDivider className="my-8" />

        {/* Form Section */}
        <section className="paper-card p-6 mb-8">
          <h2 className="ink-text text-3xl font-bold font-serif mb-2">Form Elements</h2>
          <p className="ink-text-muted font-serif text-lg mb-6">Input dan form dengan gaya paper & ink</p>
          <div className="h-px bg-black opacity-20 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <MonochromeInput
                label="Nama Lengkap"
                placeholder="Masukkan nama Anda"
              />
              <MonochromeInput
                label="Email"
                type="email"
                placeholder="<EMAIL>"
              />
              <MonochromeInput
                label="Password"
                type="password"
                placeholder="••••••••"
              />
            </div>
            <div>
              <MonochromeTextarea
                label="Pesan"
                placeholder="Tulis pesan Anda di sini..."
                rows={6}
              />
            </div>
          </div>
        </section>

        {/* Crossword Demo Section */}
        <section className="paper-card p-6 mb-8">
          <h2 className="ink-text text-3xl font-bold font-serif mb-2">Crossword Components</h2>
          <p className="ink-text-muted font-serif text-lg mb-6">Komponen khusus untuk teka-teki silang</p>
          <div className="h-px bg-black opacity-20 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <MonochromeCrosswordGrid
                grid={sampleGrid}
                numbers={sampleNumbers}
                selectedCell={selectedCell}
                highlightedCells={[[1, 0], [1, 1], [1, 2], [1, 3]]}
                correctCells={[[0, 1], [0, 2], [0, 3], [0, 4]]}
                onCellClick={(row, col) => setSelectedCell([row, col])}
                className="mx-auto"
              />

              <MonochromeCrosswordStats
                completed={8}
                total={12}
                timeElapsed="05:23"
                hintsUsed={2}
                className="mt-6"
              />
            </div>

            <div className="space-y-4">
              <MonochromeClueList
                title="MENDATAR"
                clues={sampleCluesAcross}
                activeClue={1}
              />
              <MonochromeClueList
                title="MENURUN"
                clues={sampleCluesDown}
                activeClue={5}
              />
            </div>
          </div>
        </section>

        {/* Code Example */}
        <section className="paper-card p-6 mb-8">
          <h2 className="ink-text text-3xl font-bold font-serif mb-2">Code Example</h2>
          <p className="ink-text-muted font-serif text-lg mb-6">Contoh penggunaan komponen</p>
          <div className="h-px bg-black opacity-20 mb-6"></div>
          <MonochromeCode variant="block" language="tsx">
{`import { MonochromeCard, MonochromeButton } from './ui/MonochromeCard';

function MyComponent() {
  return (
    <MonochromeCard variant="elevated" className="p-6">
      <h3>Card Title</h3>
      <p>Card content goes here...</p>
      <MonochromeButton variant="primary">
        Action Button
      </MonochromeButton>
    </MonochromeCard>
  );
}`}
          </MonochromeCode>
        </section>
      </MonochromeContainer>

      {/* Modal Demo */}
      <MonochromeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Demo Modal"
        size="md"
      >
        <div className="space-y-4">
          <MonochromeText variant="body">
            Ini adalah contoh modal dengan gaya monochrome. Modal ini menggunakan
            border tebal dan shadow untuk memberikan kesan newspaper yang kuat.
          </MonochromeText>
          <div className="flex gap-3 justify-end">
            <MonochromeButton
              variant="outline"
              onClick={() => setIsModalOpen(false)}
            >
              Batal
            </MonochromeButton>
            <MonochromeButton
              variant="primary"
              onClick={() => setIsModalOpen(false)}
            >
              OK
            </MonochromeButton>
          </div>
        </div>
      </MonochromeModal>
    </div>
  );
};

export default MonochromeDemo;
