import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, TagIcon, WifiOffIcon, Minimize2Icon, PlayIcon, PauseIcon, ClockIcon } from 'lucide-react';
import {
  getCrosswordById,
  recordCrosswordPlay,
  getCategoryById,
  getUserProgress,
  saveUserProgress,
  Category,
  CrosswordData as ApiCrosswordData
} from '../services/api';
import { useAuth } from '../context/AuthContext';
import { useCrossword } from '../context/CrosswordContext';
import CrosswordGrid from '../components/CrosswordGrid';
import CrosswordClues from '../components/CrosswordClues';
import PlayControls from '../components/PlayControls';
import Breadcrumb from '../components/Breadcrumb';
import SEO from '../components/SEO';

import { isOnline } from '../utils/serviceWorkerRegistration';
import GameHeader from '../components/GameHeader';
import PreGameInterface from '../components/PreGameInterface';
import PauseOverlay from '../components/PauseOverlay';
import MobileFloatingClues from '../components/MobileFloatingClues';
import GameInfo from '../components/GameInfo';
import AnswerValidation, { useAnswerValidation } from '../components/AnswerValidation';
import { useTimerManager } from '../hooks/useTimerManager';
import { useSmartHints } from '../hooks/useSmartHints';
import { useKeyboardNavigation } from '../hooks/useKeyboardNavigation';
import { useAccessibility } from '../hooks/useAccessibility';
import HintSuggestions from '../components/HintSuggestions';
import KeyboardShortcutsModal from '../components/KeyboardShortcutsModal';
import CompletionModal from '../components/CompletionModal';

// Helper function to format time in MM:SS format - memoized
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const PlayPage: React.FC = () => {
  const { puzzleId, categorySlug, puzzleSlug } = useParams<{ puzzleId?: string, categorySlug?: string, puzzleSlug?: string }>();
  const navigate = useNavigate();
  const { state: contextState, dispatch } = useCrossword();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [showResults, setShowResults] = useState(false);
  const [progressSaved, setProgressSaved] = useState(false);
  const [isOffline, setIsOffline] = useState(!isOnline());
  const [offlineError, setOfflineError] = useState<string | null>(null);
  const [focusMode, setFocusMode] = useState(false);
  const [completionSaveInProgress, setCompletionSaveInProgress] = useState(false);
  const [completionProcessed, setCompletionProcessed] = useState(false);

  // Store the actual crossword ID (needed for user progress when using slug URLs)
  const [actualCrosswordId, setActualCrosswordId] = useState<string | undefined>(puzzleId);

  // Update offline status when it changes
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      setOfflineError(null);
    };

    const handleOffline = () => {
      setIsOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Local state for puzzle metadata not stored in context
  const [puzzleTitle, setPuzzleTitle] = useState<string>('');
  const [puzzleCreator, setPuzzleCreator] = useState<string>('');
  const [puzzleDifficulty, setPuzzleDifficulty] = useState<string>('');
  const [puzzleDescription, setPuzzleDescription] = useState<string>('');
  const [category, setCategory] = useState<Category | null>(null);

  const [isDescriptionOpen, setIsDescriptionOpen] = useState(false);
  const toggleDescription = useCallback(() => {
    setIsDescriptionOpen(!isDescriptionOpen);
  }, [isDescriptionOpen]);

  // Mobile floating clues panel state
  const [isMobile, setIsMobile] = useState(false);
  const [isFloatingCluesExpanded, setIsFloatingCluesExpanded] = useState(false);

  // Enhanced UI state
  const [showHintSuggestions, setShowHintSuggestions] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [showCompletionModal, setShowCompletionModal] = useState(false);

  // Check if device is mobile - memoized
  const checkMobile = useCallback(() => {
    setIsMobile(window.innerWidth < 768);
  }, []);

  useEffect(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [checkMobile]);

  // Toggle focus mode
  const toggleFocusMode = useCallback(() => {
    setFocusMode(!focusMode);
  }, [focusMode]);

  // Initialize enhanced features
  const { validationState, validateAnswer, clearValidation } = useAnswerValidation();

  // Function to save progress with timer data to localStorage - defined early to avoid hoisting issues
  const saveProgressWithTimerData = useCallback((crosswordId: string) => {
    if (!contextState.userAnswers) return;

    try {
      const progressData = {
        userAnswers: contextState.userAnswers,
        revealedCells: contextState.revealedCells || [],
        progress: contextState.progress || 0,
        isSurrendered: contextState.isSurrendered || false,
        timeSpent: contextState.timeSpent || 0,
        gameState: contextState.gameState || 'not-started',
        timestamp: new Date().getTime()
      };

      localStorage.setItem(`crossword_progress_${crosswordId}`, JSON.stringify(progressData));
      console.log('Saved progress with timer to localStorage for crossword:', crosswordId);
    } catch (error) {
      console.error('Error saving progress with timer to localStorage:', error);
    }
  }, [contextState.userAnswers, contextState.revealedCells, contextState.progress, contextState.isSurrendered, contextState.timeSpent, contextState.gameState]);

  // Auto-save callback - memoized to prevent timer restarts
  const handleAutoSave = useCallback(() => {
    if (actualCrosswordId && user) {
      saveProgressWithTimerData(actualCrosswordId);
    }
  }, [actualCrosswordId, user, saveProgressWithTimerData]);

  // Enhanced timer management
  const timerManager = useTimerManager({
    gameState: contextState.gameState || 'not-started',
    initialTime: contextState.timeSpent || 0,
    onAutoSave: handleAutoSave,
    autoSaveInterval: 30 // Save every 30 seconds
  });

  // Sync timer with context state - removed to prevent infinite loop
  // Timer state is now managed internally by useTimerManager
  // and will be synced when saving progress

  // Enhanced keyboard navigation - will be enabled after function definitions
  // const keyboardNavigation = useKeyboardNavigation({...});

  // Smart hints system
  const { getBestHint, getHintSuggestions, getHintExplanation } = useSmartHints(
    contextState,
    contextState.userAnswers || []
  );

  // Accessibility features
  const {
    announce,
    announceCellSelection,
    announceWordSelection,
    announceLetterInput,
    announceGameState,
    toggleHighContrast
  } = useAccessibility({
    state: contextState,
    userAnswers: contextState.userAnswers || [],
    announceChanges: true,
    enableScreenReader: true
  });

  // Game state management functions - memoized with useCallback
  const startGame = useCallback(() => {
    console.log('Starting game - current timeSpent:', contextState.timeSpent);
    dispatch({ type: 'START_GAME' });
    setFocusMode(true); // Automatically enter focus mode when starting
  }, [contextState.timeSpent, dispatch]);

  const pauseGame = useCallback(() => {
    console.log('Pausing game - current timeSpent:', contextState.timeSpent);
    dispatch({ type: 'PAUSE_GAME' });
  }, [contextState.timeSpent, dispatch]);

  const resumeGame = useCallback(() => {
    console.log('Resuming game - current timeSpent:', contextState.timeSpent);
    dispatch({ type: 'RESUME_GAME' });
  }, [contextState.timeSpent, dispatch]);

  const togglePauseResume = useCallback(() => {
    if (contextState.gameState === 'playing') {
      pauseGame();
    } else if (contextState.gameState === 'paused') {
      resumeGame();
    }
  }, [contextState.gameState, pauseGame, resumeGame]);

  // Toggle floating clues panel
  const toggleFloatingClues = useCallback(() => {
    setIsFloatingCluesExpanded(!isFloatingCluesExpanded);
  }, [isFloatingCluesExpanded]);

  // Enhanced Keyboard Navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle keyboard events if user is typing in an input field
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Handle keyboard shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'f':
            event.preventDefault();
            toggleFocusMode();
            break;
          case 'h':
            event.preventDefault();
            // Trigger hint usage if available and a cell is selected
            if ((contextState.hintsRemaining || 0) > 0 && contextState.selectedCell) {
              const [row, col] = contextState.selectedCell;
              dispatch({ type: 'USE_HINT', row, col });
            }
            break;
          case 'r':
            event.preventDefault();
            resetPuzzleAnswers();
            break;
          default:
            break;
        }
        return;
      }

      // Handle navigation keys
      switch (event.key) {
        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowLeft':
        case 'ArrowRight':
          event.preventDefault();
          handleArrowNavigation(event.key);
          break;
        case 'Tab':
          event.preventDefault();
          handleTabNavigation();
          break;
        case 'Enter':
          event.preventDefault();
          checkAnswers();
          break;
        case 'Escape':
          event.preventDefault();
          handleEscapeKey();
          break;
        default:
          break;
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [contextState.hintsRemaining, isFloatingCluesExpanded]);

  // Handle arrow key navigation
  const handleArrowNavigation = (key: string) => {
    if (!contextState.selectedWordId || !contextState.wordPositions) return;

    const selectedPosition = contextState.wordPositions[contextState.selectedWordId - 1];
    if (!selectedPosition) return;

    const { row, col, direction } = selectedPosition;
    const wordLength = contextState.words[contextState.selectedWordId - 1]?.length || 0;

    // Calculate current position within the word based on selected cell
    let currentCellIndex = 0;
    if (contextState.selectedCell) {
      const [selectedRow, selectedCol] = contextState.selectedCell;
      if (direction === 'across') {
        currentCellIndex = selectedCol - col;
      } else {
        currentCellIndex = selectedRow - row;
      }
    }

    let newRow = row;
    let newCol = col;

    switch (key) {
      case 'ArrowUp':
        if (direction === 'down' && currentCellIndex > 0) {
          // Move up within the current down word
          newRow = row + currentCellIndex - 1;
          newCol = col;
        } else {
          // Find word above
          findWordInDirection('up', row, col);
          return;
        }
        break;
      case 'ArrowDown':
        if (direction === 'down' && currentCellIndex < wordLength - 1) {
          // Move down within the current down word
          newRow = row + currentCellIndex + 1;
          newCol = col;
        } else {
          // Find word below
          findWordInDirection('down', row, col);
          return;
        }
        break;
      case 'ArrowLeft':
        if (direction === 'across' && currentCellIndex > 0) {
          // Move left within the current across word
          newRow = row;
          newCol = col + currentCellIndex - 1;
        } else {
          // Find word to the left
          findWordInDirection('left', row, col);
          return;
        }
        break;
      case 'ArrowRight':
        if (direction === 'across' && currentCellIndex < wordLength - 1) {
          // Move right within the current across word
          newRow = row;
          newCol = col + currentCellIndex + 1;
        } else {
          // Find word to the right
          findWordInDirection('right', row, col);
          return;
        }
        break;
    }

    // Select the cell at the new position
    dispatch({ type: 'SELECT_CELL', row: newRow, col: newCol });
  };

  // Find word in a specific direction
  const findWordInDirection = (direction: 'up' | 'down' | 'left' | 'right', fromRow: number, fromCol: number) => {
    if (!contextState.wordPositions) return;

    // Find the closest word in the specified direction
    let closestWord = null;
    let closestDistance = Infinity;

    contextState.wordPositions.forEach((position, index) => {
      const { row, col } = position;
      let distance = Infinity;

      switch (direction) {
        case 'up':
          if (row < fromRow && col === fromCol) {
            distance = fromRow - row;
          }
          break;
        case 'down':
          if (row > fromRow && col === fromCol) {
            distance = row - fromRow;
          }
          break;
        case 'left':
          if (col < fromCol && row === fromRow) {
            distance = fromCol - col;
          }
          break;
        case 'right':
          if (col > fromCol && row === fromRow) {
            distance = col - fromCol;
          }
          break;
      }

      if (distance < closestDistance) {
        closestDistance = distance;
        closestWord = index + 1;
      }
    });

    if (closestWord) {
      dispatch({ type: 'SELECT_WORD', wordId: closestWord });
    }
  };

  // Handle Tab key navigation (toggle direction)
  const handleTabNavigation = () => {
    if (!contextState.selectedWordId || !contextState.wordPositions) return;

    const selectedPosition = contextState.wordPositions[contextState.selectedWordId - 1];
    if (!selectedPosition) return;

    const { row, col, direction } = selectedPosition;

    // Find intersecting word with opposite direction
    const intersectingWord = contextState.wordPositions.find((position, index) => {
      if (index === contextState.selectedWordId! - 1) return false; // Skip current word

      const { row: wordRow, col: wordCol, direction: wordDirection } = position;
      const wordLength = contextState.words[index]?.length || 0;

      // Check if directions are opposite
      if ((direction === 'across' && wordDirection === 'down') ||
          (direction === 'down' && wordDirection === 'across')) {

        // Check if words intersect at the current position
        if (direction === 'across') {
          return wordRow <= row &&
                 wordRow + wordLength > row &&
                 wordCol === col;
        } else {
          return wordCol <= col &&
                 wordCol + wordLength > col &&
                 wordRow === row;
        }
      }

      return false;
    });

    if (intersectingWord) {
      const wordIndex = contextState.wordPositions.indexOf(intersectingWord);
      dispatch({ type: 'SELECT_WORD', wordId: wordIndex + 1 });
    }
  };

  // Handle Escape key
  const handleEscapeKey = () => {
    if (isFloatingCluesExpanded) {
      setIsFloatingCluesExpanded(false);
    } else if (focusMode) {
      setFocusMode(false);
    } else if (contextState.selectedWordId) {
      dispatch({ type: 'SELECT_WORD', wordId: null });
    }
  };

  // Fetch category information
  const fetchCategory = async (categoryId: string) => {
    if (!categoryId) return;
    try {
      const categoryData = await getCategoryById(categoryId);
      setCategory(categoryData);
    } catch (error) {
      console.error('Error fetching category:', error);
    }
  };

  // Function to load user progress
  const loadUserProgress = async (crosswordId: string) => {
    if (!user) return null;

    try {
      const progress = await getUserProgress(crosswordId);
      return progress;
    } catch (error) {
      console.error('Error loading user progress:', error);
      return null;
    }
  };

  // Function to save user progress to localStorage
  const saveProgressToLocalStorage = useCallback((crosswordId: string) => {
    if (!contextState.userAnswers) return;

    try {
      // Make sure userAnswers is a valid 2D array
      if (!Array.isArray(contextState.userAnswers) ||
          contextState.userAnswers.length === 0 ||
          !Array.isArray(contextState.userAnswers[0])) {
        console.error('Invalid userAnswers format:', contextState.userAnswers);
        return;
      }

      const progressData = {
        userAnswers: contextState.userAnswers,
        revealedCells: contextState.revealedCells || [],
        progress: contextState.progress || 0,
        isSurrendered: contextState.isSurrendered || false,
        timestamp: new Date().getTime()
      };

      localStorage.setItem(`crossword_progress_${crosswordId}`, JSON.stringify(progressData));
      console.log('Saved progress to localStorage for crossword:', crosswordId);
    } catch (error) {
      console.error('Error saving progress to localStorage:', error);
    }
  }, [contextState.userAnswers, contextState.revealedCells, contextState.progress, contextState.isSurrendered]);

  // Function to load user progress from localStorage
  const loadProgressFromLocalStorage = (crosswordId: string) => {
    try {
      const savedData = localStorage.getItem(`crossword_progress_${crosswordId}`);
      if (!savedData) return null;

      const progressData = JSON.parse(savedData);

      // Check if the saved data is not too old (24 hours)
      const now = new Date().getTime();
      if (progressData.timestamp && (now - progressData.timestamp > 24 * 60 * 60 * 1000)) {
        console.log('Saved progress is too old, removing from localStorage');
        localStorage.removeItem(`crossword_progress_${crosswordId}`);
        return null;
      }

      console.log('Loaded progress from localStorage for crossword:', crosswordId);
      return progressData;
    } catch (error) {
      console.error('Error loading progress from localStorage:', error);
      return null;
    }
  };

  // Function to save user progress
  const saveProgress = useCallback(async (isCompleted = false) => {
    if (!actualCrosswordId || !contextState.userAnswers) return;

    try {
      console.log('Saving progress with userAnswers:', contextState.userAnswers);

      // Make sure userAnswers is a valid 2D array
      if (!Array.isArray(contextState.userAnswers) ||
          contextState.userAnswers.length === 0 ||
          !Array.isArray(contextState.userAnswers[0])) {
        console.error('Invalid userAnswers format:', contextState.userAnswers);
        return;
      }

      // Always save to localStorage regardless of user login status
      saveProgressToLocalStorage(actualCrosswordId);

      // Only save to API if user is logged in and game is started
      if (user && contextState.gameState === 'playing') {
        await saveUserProgress(
          actualCrosswordId,
          {
            userAnswers: contextState.userAnswers,
            revealedCells: contextState.revealedCells || [],
            progress: contextState.progress || 0
          },
          isCompleted || contextState.isSurrendered || false,
          contextState.timeSpent || 0 // Include timer data
        );
        setProgressSaved(true);
      }
    } catch (error) {
      console.error('Error saving user progress:', error);
    }
  }, [actualCrosswordId, contextState.userAnswers, contextState.revealedCells, contextState.progress, contextState.isSurrendered, contextState.timeSpent, contextState.gameState, user, saveProgressToLocalStorage]);



  // Save progress when user answers change or user surrenders
  useEffect(() => {
    if (actualCrosswordId && contextState.userAnswers && !isLoading) {
      // Use a debounce to avoid too many storage operations
      const timer = setTimeout(() => {
        // Always save to localStorage with timer data
        saveProgressWithTimerData(actualCrosswordId);

        // Only save to API if user is logged in and game is started
        if (user && contextState.gameState === 'playing') {
          saveProgress(contextState.isSurrendered);
        }
      }, 2000); // Save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contextState.userAnswers, contextState.isSurrendered, user, actualCrosswordId, isLoading]);

  // Auto-save is now handled by useTimerManager hook
  // Removed duplicate auto-save timer to prevent conflicts

  // Handle puzzle completion - stop timer and save final time
  useEffect(() => {
    if (contextState.gameState === 'completed' && actualCrosswordId && !isLoading && !completionSaveInProgress && !completionProcessed) {
      console.log('🎉 Puzzle completed! Final time:', timerManager.currentTime);

      // Update the context with the final time
      dispatch({ type: 'UPDATE_TIMER', timeSpent: timerManager.currentTime });

      // Handle completion save and modal display
      const handleCompletionSave = async () => {
        try {
          // Set flags to prevent multiple triggers
          setCompletionSaveInProgress(true);
          setCompletionProcessed(true);
          console.log('Starting completion save process...');

          // Save completion data to localStorage immediately
          saveProgressWithTimerData(actualCrosswordId);
          console.log('✅ localStorage save completed');

          // Save completion to API if user is logged in
          if (user) {
            console.log('Saving completion to API...');
            await saveProgress(true); // Mark as completed
            console.log('✅ API save completed');
          }

          // Announce completion for accessibility
          if (announce) {
            announce(`Selamat! Anda telah menyelesaikan teka-teki silang dalam waktu ${formatTime(timerManager.currentTime)}`);
          }

          // Show completion modal after successful save with delay for better UX
          setTimeout(() => {
            console.log('✅ Showing completion modal after successful save');
            setShowCompletionModal(true);
          }, 1000);

          // Reset save flag immediately after scheduling the modal
          setCompletionSaveInProgress(false);

        } catch (error) {
          console.error('❌ Error during completion save process:', error);

          // Still show modal even if API save fails, but log the error
          // The localStorage save should have succeeded
          setTimeout(() => {
            console.log('⚠️ Showing completion modal despite save error (localStorage saved)');
            setShowCompletionModal(true);
          }, 1000);

          // Reset save flag even on error
          setCompletionSaveInProgress(false);
        }
      };

      // Execute the completion save process
      handleCompletionSave();
    }
  }, [contextState.gameState, actualCrosswordId, timerManager.currentTime, user, isLoading, completionSaveInProgress, completionProcessed, dispatch, saveProgressWithTimerData, saveProgress, announce]);

  useEffect(() => {
    const fetchAndLoadPuzzle = async () => {
      // If we have a categorySlug and puzzleSlug but no puzzleId, we need to fetch by slug
      if (categorySlug && puzzleSlug && !puzzleId) {
        try {
          // Use the API to get crossword by slug
          const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:1111'}/api/crosswords/${puzzleSlug}`, {
            headers: {
              'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5'
            }
          });
          if (response.ok) {
            const data = await response.json();
            if (data.status === 'success' && data.data) {
              // We found the crossword by slug, now redirect to the old URL format for compatibility
              // This is temporary until we update all the code to use the new URL format
              const crosswordId = data.data.id;
              if (crosswordId) {
                // Store the actual crossword ID for user progress
                setActualCrosswordId(crosswordId);

                // Load the crossword data
                const apiCrosswordData: ApiCrosswordData = data.data;
                recordCrosswordPlay(crosswordId).catch(console.error);

                if (apiCrosswordData.category_id) {
                  fetchCategory(apiCrosswordData.category_id);
                }

                setPuzzleTitle(apiCrosswordData.title);
                setPuzzleCreator(apiCrosswordData.creator || 'Anonymous');
                setPuzzleDifficulty(apiCrosswordData.difficulty || 'sedang');
                setPuzzleDescription(apiCrosswordData.description || '');

                // Try to load progress from localStorage first
                const localStorageProgress = loadProgressFromLocalStorage(crosswordId);

                // Then try to load user progress from API if user is logged in
                const userProgress = user ? await loadUserProgress(crosswordId) : null;

                if (apiCrosswordData.state) {
                  // Define an interface for the structure of word objects within apiCrosswordData.state.words
                  interface ApiWordEntry {
                    word: string;
                    clue?: string; // Assuming clue might also be part of this object from backend
                  }

                  // Determine which progress data to use (API takes precedence over localStorage)
                  let userAnswersToUse = Array(apiCrosswordData.state!.gridSize).fill(null).map(() => Array(apiCrosswordData.state!.gridSize).fill(''));
                  let revealedCellsToUse: number[][] = [];
                  let progressToUse = 0;
                  let isSurrenderedToUse = false;
                  let timeSpentToUse = 0;
                  let gameStateToUse: 'not-started' | 'playing' | 'paused' = 'not-started';

                  // First try to use localStorage data
                  if (localStorageProgress) {
                    userAnswersToUse = localStorageProgress.userAnswers;
                    revealedCellsToUse = localStorageProgress.revealedCells || [];
                    progressToUse = localStorageProgress.progress || 0;
                    isSurrenderedToUse = localStorageProgress.isSurrendered || false;
                    timeSpentToUse = localStorageProgress.timeSpent || 0;
                    gameStateToUse = localStorageProgress.gameState || 'not-started';
                    console.log('Loaded from localStorage - timeSpent:', timeSpentToUse, 'gameState:', gameStateToUse);
                  }

                  // Then override with API data if available (takes precedence)
                  if (userProgress && userProgress.progress_data) {
                    if (userProgress.progress_data.userAnswers && userProgress.progress_data.userAnswers.length > 0) {
                      userAnswersToUse = userProgress.progress_data.userAnswers;
                    }
                    if (userProgress.progress_data.revealedCells && Array.isArray(userProgress.progress_data.revealedCells)) {
                      revealedCellsToUse = userProgress.progress_data.revealedCells;
                    }
                    if (userProgress.progress_data.progress !== undefined) {
                      progressToUse = userProgress.progress_data.progress;
                    }
                    if (userProgress.is_completed) {
                      isSurrenderedToUse = true;
                    }
                    // Load timer data from API if available
                    if (userProgress.time_spent !== undefined && userProgress.time_spent !== null) {
                      timeSpentToUse = userProgress.time_spent;
                    }
                    // API data doesn't store gameState, so keep it as 'not-started' to show pre-game interface
                    gameStateToUse = 'not-started';
                    console.log('Loaded from API - timeSpent:', timeSpentToUse, 'gameState:', gameStateToUse);
                  }

                  const payload = {
                    gridSize: apiCrosswordData.state!.gridSize,
                    grid: apiCrosswordData.state!.grid,
                    words: apiCrosswordData.state!.words.map((w: ApiWordEntry | string) => typeof w === 'string' ? w : w.word || ''),
                    clues: apiCrosswordData.state!.clues,
                    wordPositions: apiCrosswordData.state!.wordPositions,
                    userAnswers: userAnswersToUse,
                    revealedCells: revealedCellsToUse as [number, number][],
                    progress: progressToUse,
                    isSurrendered: isSurrenderedToUse, // Add isSurrendered to payload
                    timeSpent: timeSpentToUse // Add timeSpent to payload
                  };

                  // Load the puzzle first
                  dispatch({ type: 'LOAD_PUZZLE', payload });

                  // Set game state if it was saved
                  if (gameStateToUse === 'paused') {
                    dispatch({ type: 'PAUSE_GAME' });
                  } else if (gameStateToUse === 'playing') {
                    // Don't auto-resume, let user manually resume
                    dispatch({ type: 'PAUSE_GAME' });
                  }

                  setIsLoading(false);
                  return;
                }
              }
            }
          }
        } catch (error) {
          console.error('Error fetching crossword by slug:', error);
        }
      }

      // If we don't have a puzzleId or couldn't find by slug, show loading error
      if (!puzzleId) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        // Store the actual crossword ID for user progress
        setActualCrosswordId(puzzleId);

        const apiCrosswordData: ApiCrosswordData = await getCrosswordById(puzzleId);
        recordCrosswordPlay(puzzleId).catch(console.error);

        if (apiCrosswordData.category_id) {
          fetchCategory(apiCrosswordData.category_id);
        }

        setPuzzleTitle(apiCrosswordData.title);
        setPuzzleCreator(apiCrosswordData.creator || 'Anonymous');
        setPuzzleDifficulty(apiCrosswordData.difficulty || 'sedang');
        setPuzzleDescription(apiCrosswordData.description || '');

        // Try to load progress from localStorage first
        const localStorageProgress = loadProgressFromLocalStorage(puzzleId);

        // Then try to load user progress from API if user is logged in
        const userProgress = user ? await loadUserProgress(puzzleId) : null;

        console.log("Local storage progress:", localStorageProgress);
        console.log("User progress from API:", userProgress);

        // Log the user answers if available
        if (userProgress && userProgress.progress_data) {
          console.log("User answers from API progress:", userProgress.progress_data.userAnswers);
        }

        if (localStorageProgress) {
          console.log("User answers from localStorage:", localStorageProgress.userAnswers);
        }

        if (apiCrosswordData.state) {
          // Define an interface for the structure of word objects within apiCrosswordData.state.words
          interface ApiWordEntry {
            word: string;
            clue?: string; // Assuming clue might also be part of this object from backend
            // Add other properties if they exist and are relevant
          }

          // Determine which progress data to use (API takes precedence over localStorage)
          let userAnswersToUse = Array(apiCrosswordData.state!.gridSize).fill(null).map(() => Array(apiCrosswordData.state!.gridSize).fill(''));
          let revealedCellsToUse: [number, number][] = [];
          let progressToUse = 0;
          let isSurrenderedToUse = false;
          let timeSpentToUse = 0;
          let gameStateToUse: 'not-started' | 'playing' | 'paused' = 'not-started';

          // First try to use localStorage data
          if (localStorageProgress) {
            userAnswersToUse = localStorageProgress.userAnswers;
            // Convert number[][] to [number, number][] if needed
            revealedCellsToUse = (localStorageProgress.revealedCells || []).map((cell: number[]) =>
              Array.isArray(cell) && cell.length >= 2 ? [cell[0], cell[1]] as [number, number] : [0, 0] as [number, number]
            );
            progressToUse = localStorageProgress.progress || 0;
            isSurrenderedToUse = localStorageProgress.isSurrendered || false;
            timeSpentToUse = localStorageProgress.timeSpent || 0;
            gameStateToUse = localStorageProgress.gameState || 'not-started';
            console.log('Loaded from localStorage - timeSpent:', timeSpentToUse, 'gameState:', gameStateToUse);
          }

          // Then override with API data if available (takes precedence)
          if (userProgress && userProgress.progress_data) {
            if (userProgress.progress_data.userAnswers && userProgress.progress_data.userAnswers.length > 0) {
              userAnswersToUse = userProgress.progress_data.userAnswers;
            }
            if (userProgress.progress_data.revealedCells && Array.isArray(userProgress.progress_data.revealedCells)) {
              // Convert number[][] to [number, number][] if needed
              revealedCellsToUse = userProgress.progress_data.revealedCells.map((cell: number[]) =>
                Array.isArray(cell) && cell.length >= 2 ? [cell[0], cell[1]] as [number, number] : [0, 0] as [number, number]
              );
            }
            if (userProgress.progress_data.progress !== undefined) {
              progressToUse = userProgress.progress_data.progress;
            }
            if (userProgress.is_completed) {
              isSurrenderedToUse = true;
            }
            // Load timer data from API if available
            if (userProgress.time_spent !== undefined && userProgress.time_spent !== null) {
              timeSpentToUse = userProgress.time_spent;
            }
            // API data doesn't store gameState, so keep it as 'not-started' to show pre-game interface
            gameStateToUse = 'not-started';
            console.log('Loaded from API - timeSpent:', timeSpentToUse, 'gameState:', gameStateToUse);
          }

          const payload = {
            gridSize: apiCrosswordData.state!.gridSize, // Non-null assertion as we are inside if (apiCrosswordData.state)
            grid: apiCrosswordData.state!.grid,
            // Ensure words is string[] for the context
            words: apiCrosswordData.state!.words.map((w: ApiWordEntry | string) => typeof w === 'string' ? w : w.word || ''),
            clues: apiCrosswordData.state!.clues,
            wordPositions: apiCrosswordData.state!.wordPositions,
            userAnswers: userAnswersToUse,
            revealedCells: revealedCellsToUse as [number, number][],
            progress: progressToUse,
            isSurrendered: isSurrenderedToUse,
            timeSpent: timeSpentToUse // Add timeSpent to payload
          };

          dispatch({ type: 'LOAD_PUZZLE', payload });

          // Set game state if it was saved
          if (gameStateToUse === 'paused') {
            dispatch({ type: 'PAUSE_GAME' });
          } else if (gameStateToUse === 'playing') {
            // Don't auto-resume, let user manually resume
            dispatch({ type: 'PAUSE_GAME' });
          }
        } else {
          // Handle case where crossword.state is not available (e.g., show error or use mock)
          console.error("Puzzle state not found in API response for puzzleId:", puzzleId);
          // Potentially dispatch a RESET or show an error message to the user
          dispatch({ type: 'RESET' }); // Reset to a blank state
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching or loading crossword:', error);
        setIsLoading(false);

        // Check if the error is due to being offline
        if (!isOnline()) {
          setOfflineError('Anda sedang offline dan teka-teki silang ini belum tersimpan di cache. Silakan coba lagi saat Anda online.');
        }

        // Potentially dispatch a RESET or show an error message
        dispatch({ type: 'RESET' });
      }
    };

    fetchAndLoadPuzzle();

    // Cleanup when component unmounts or puzzleId changes
    return () => {
      // Save progress before unmounting
      if (actualCrosswordId && contextState.userAnswers) {
        // Always save to localStorage regardless of user login status
        saveProgressToLocalStorage(actualCrosswordId);

        // Save to API if user is logged in
        if (user) {
          saveProgress(contextState.isSurrendered);
        }
      }
      dispatch({ type: 'RESET' }); // Reset context to initial state
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [puzzleId, dispatch, user]);


  const checkAnswers = () => {
    if (!contextState.grid || !contextState.userAnswers || !actualCrosswordId) return;

    // Dispatch CHECK_ANSWERS action to update progress
    dispatch({ type: 'CHECK_ANSWERS', showResults: true });

    // Show results in the grid
    setShowResults(true);

    // Always save to localStorage
    saveProgressToLocalStorage(actualCrosswordId);

    // Save progress to API after checking answers if user is logged in
    if (user) {
      saveProgress(contextState.progress === 100);
    }
  };

  const resetPuzzleAnswers = () => {
    if (!actualCrosswordId) return;

    dispatch({ type: 'RESET_USER_ANSWERS' });
    setShowResults(false);

    // Always save to localStorage
    saveProgressToLocalStorage(actualCrosswordId);

    // Save progress to API after resetting answers if user is logged in
    if (user) {
      saveProgress(false);
    }
  };

  const playAgain = () => {
    if (!actualCrosswordId) return;

    // Reset everything for a fresh game attempt
    dispatch({ type: 'PLAY_AGAIN' });
    setShowResults(false);
    setShowCompletionModal(false);
    setCompletionProcessed(false); // Reset completion flag to allow new completion

    // Clear localStorage progress
    localStorage.removeItem(`crossword_progress_${actualCrosswordId}`);

    // Clear API progress if user is logged in
    if (user) {
      saveProgress(false); // Save empty progress to API
    }

    console.log('🔄 Starting fresh game attempt');
  };

  // Enhanced keyboard navigation
  const keyboardNavigation = useKeyboardNavigation({
    gameState: contextState.gameState || 'not-started',
    onMoveUp: () => handleArrowNavigation('ArrowUp'),
    onMoveDown: () => handleArrowNavigation('ArrowDown'),
    onMoveLeft: () => handleArrowNavigation('ArrowLeft'),
    onMoveRight: () => handleArrowNavigation('ArrowRight'),
    onToggleDirection: () => dispatch({ type: 'TOGGLE_DIRECTION' }),
    onToggleFocusMode: toggleFocusMode,
    onTogglePause: togglePauseResume,
    onUseHint: () => {
      // Use hint functionality
      if ((contextState.hintsRemaining || 0) > 0 && contextState.selectedCell) {
        const [row, col] = contextState.selectedCell;
        dispatch({ type: 'USE_HINT', row, col });
      }
    },
    onCheckAnswers: checkAnswers,
    onDeleteLetter: () => {
      if (contextState.selectedCell) {
        const [row, col] = contextState.selectedCell;
        dispatch({ type: 'UPDATE_USER_ANSWER', row, col, value: '' });
      }
    },
    onInputLetter: (letter: string) => {
      if (contextState.selectedCell) {
        const [row, col] = contextState.selectedCell;
        dispatch({ type: 'UPDATE_USER_ANSWER', row, col, value: letter });
      }
    },
    onSelectNextWord: () => {
      // Select next word in sequence
      if (contextState.selectedWordId && contextState.wordPositions) {
        const currentIndex = contextState.wordPositions.findIndex(wp => wp.number === contextState.selectedWordId);
        const nextIndex = (currentIndex + 1) % contextState.wordPositions.length;
        const nextWordNumber = contextState.wordPositions[nextIndex].number;
        dispatch({ type: 'SELECT_WORD', wordId: nextWordNumber });
      }
    },
    onSelectPrevWord: () => {
      // Select previous word in sequence
      if (contextState.selectedWordId && contextState.wordPositions) {
        const currentIndex = contextState.wordPositions.findIndex(wp => wp.number === contextState.selectedWordId);
        const prevIndex = currentIndex === 0 ? contextState.wordPositions.length - 1 : currentIndex - 1;
        const prevWordNumber = contextState.wordPositions[prevIndex].number;
        dispatch({ type: 'SELECT_WORD', wordId: prevWordNumber });
      }
    },
    isEnabled: contextState.mode === 'play' && contextState.gameState !== 'not-started'
  });

  // Calculate score based on hints used and completion - memoized
  const calculateScore = useCallback(() => {
    if (!contextState) return 0;

    // Base score is 1000
    let score = 1000;

    // Deduct points for hints used (50 points per hint)
    const hintsUsed = (contextState.hintsRemaining !== undefined) ? (3 - contextState.hintsRemaining) : 0;
    const hintsDeduction = hintsUsed * 50;
    score -= hintsDeduction;

    // Bonus for completion
    if (contextState.progress === 100) {
      score += 200;
    }

    // Ensure score doesn't go below 0
    return Math.max(0, score);
  }, [contextState]);

  // Memoized structured data to prevent re-computation on every render
  const structuredData = useMemo(() => {
    // Create structured data for the puzzle page
    const gameStructuredData = {
      "@context": "https://schema.org",
      "@type": "Game",
      "name": puzzleTitle,
      "description": puzzleDescription || `Teka-teki silang "${puzzleTitle}" dengan tingkat kesulitan ${puzzleDifficulty}`,
      "creator": {
        "@type": "Person",
        "name": puzzleCreator
      },
      "genre": category?.name || "Teka-teki silang",
      "gameLocation": {
        "@type": "WebSite",
        "name": "TTS - Teka Teki Silang Online",
        "url": window.location.origin
      },
      "numberOfPlayers": {
        "@type": "QuantitativeValue",
        "minValue": 1,
        "maxValue": 1
      },
      "audience": {
        "@type": "Audience",
        "audienceType": "Puzzle Enthusiasts"
      },
      "inLanguage": "id",
      "difficulty": puzzleDifficulty
    };

    // Create breadcrumb structured data
    const breadcrumbStructuredData = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Beranda",
          "item": window.location.origin
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Main",
          "item": `${window.location.origin}/play`
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": category?.name || "Teka-teki silang",
          "item": category ? `${window.location.origin}/teka-teki-silang/${category.slug}` : `${window.location.origin}/play`
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": puzzleTitle,
          "item": window.location.href
        }
      ]
    };

    // Create article structured data (treating the puzzle as content)
    const articleStructuredData = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": puzzleTitle,
      "description": puzzleDescription || `Teka-teki silang "${puzzleTitle}" dengan tingkat kesulitan ${puzzleDifficulty}. Dibuat oleh ${puzzleCreator}.`,
      "author": {
        "@type": "Person",
        "name": puzzleCreator
      },
      "publisher": {
        "@type": "Organization",
        "name": "TTS - Teka Teki Silang Online",
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/images/logo.png`
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": window.location.href
      },
      "keywords": `teka teki silang, TTS, ${category?.name || ''}, ${puzzleDifficulty}, puzzle game`,
      "genre": category?.name || "Teka-teki silang",
      "inLanguage": "id"
    };

    return [gameStructuredData, breadcrumbStructuredData, articleStructuredData];
  }, [puzzleTitle, puzzleDescription, puzzleDifficulty, puzzleCreator, category]);

  // Show offline error message if there is one
  if (offlineError) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-paper-50 bg-paper-texture">
        <div className="bg-newsprint border-2 border-ink-900 p-6 mb-6 max-w-md w-full shadow-paper-xl rounded-lg">
          <div className="flex items-center mb-4">
            <div className="bg-ink-900 p-2 rounded-full mr-3">
              <WifiOffIcon className="w-6 h-6 text-newsprint" />
            </div>
            <h2 className="text-ink-900 font-serif font-bold text-lg">Kesalahan Koneksi</h2>
          </div>
          <p className="text-ink-700 font-serif leading-relaxed mb-4">{offlineError}</p>
          <div className="bg-ink-100 border-l-4 border-ink-900 p-3 rounded">
            <p className="text-ink-800 text-sm font-serif">
              💡 <strong>Tips:</strong> Pastikan koneksi internet Anda stabil dan coba muat ulang halaman.
            </p>
          </div>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="bg-ink-900 text-newsprint px-6 py-3 rounded-lg font-serif font-semibold hover:bg-ink-800 transition-all duration-200 shadow-paper border-2 border-ink-900 hover:shadow-paper-lg flex items-center"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Kembali
        </button>
      </div>
    );
  }

  if (isLoading || contextState.mode !== 'play') { // Ensure context is in play mode and loaded
    return (
      <div className="min-h-screen flex items-center justify-center bg-paper-50 bg-paper-texture">
        <div className="text-center card-paper p-8 border-2 border-ink-900 shadow-paper-xl">
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-ink-200 border-t-ink-900 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-ink-900 rounded-full animate-pulse"></div>
            </div>
          </div>
          <h2 className="text-ink-900 font-serif text-xl font-bold mb-2">Memuat Teka-Teki Silang</h2>
          <p className="text-ink-700 text-sm font-serif">Mohon tunggu sebentar...</p>
          <div className="mt-4 flex justify-center">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-ink-900 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-ink-900 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-ink-900 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show offline indicator if we're offline but have cached data
  const OfflineIndicator = () => isOffline ? (
    <div className="bg-primary-100 border-l-4 border-primary-500 p-2 mb-4">
      <div className="flex items-center">
        <WifiOffIcon className="w-4 h-4 text-primary-600 mr-2" />
        <p className="text-primary-700 text-sm">
          Anda sedang offline. Perubahan akan disimpan secara lokal dan disinkronkan saat Anda online kembali.
        </p>
      </div>
    </div>
  ) : null;

  return (
    <div className={`min-h-screen transition-all duration-300 ${
      focusMode
        ? 'bg-ink-900 text-newsprint bg-paper-dots'
        : 'bg-paper-50 bg-paper-texture'
    }`}>
      <SEO
        title={`${puzzleTitle} | Tekateki.io - Teka Teki Silang Online`}
        description={puzzleDescription || `Mainkan teka-teki silang "${puzzleTitle}" dengan tingkat kesulitan ${puzzleDifficulty}. Dibuat oleh ${puzzleCreator}.`}
        keywords={`teka teki silang, TTS, ${category?.name || ''}, ${puzzleDifficulty}, puzzle game, tekateki`}
        ogTitle={`Mainkan "${puzzleTitle}" di Tekateki.io`}
        ogDescription={puzzleDescription || `Teka-teki silang dengan tingkat kesulitan ${puzzleDifficulty}. Dibuat oleh ${puzzleCreator}.`}
        structuredData={structuredData}
      />

      {/* Loading overlay with enhanced styling */}
      {isLoading && (
        <div className="fixed inset-0 bg-paper-50 bg-opacity-95 flex items-center justify-center z-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-ink-900 mx-auto mb-4"></div>
            <p className="text-ink-900 font-serif text-lg">Memuat teka-teki silang...</p>
            <p className="text-ink-700 text-sm mt-2">Mohon tunggu sebentar</p>
          </div>
        </div>
      )}

      <div className={`container mx-auto px-4 py-8 relative ${focusMode ? 'z-10' : ''}`}>
        {/* Focus Mode Exit Button - Enhanced styling */}
        {focusMode && (
          <button
            onClick={toggleFocusMode}
            className="fixed top-4 right-4 z-50 p-3 bg-newsprint rounded-full shadow-paper-lg hover:bg-ink-100 text-ink-900 border-2 border-ink-900 transition-all duration-200 hover:scale-105"
            title="Keluar dari Mode Fokus"
            aria-label="Keluar dari Mode Fokus"
          >
            <Minimize2Icon className="w-6 h-6" />
          </button>
        )}

        {/* Elements that are conditionally visible based on focus mode */}
        <div className={`${
          focusMode
            ? 'opacity-0 h-0 overflow-hidden pointer-events-none'
            : 'opacity-100'
        } transition-all duration-300`}>
          {/* Offline indicator */}
          <OfflineIndicator />

          {/* Breadcrumb navigation with enhanced styling */}
          <div className="mb-6">
            <Breadcrumb
              items={[
                { name: 'Beranda', href: '/play' },
                ...(category ? [{ name: category.name, href: `/teka-teki-silang/${category.slug}` }] : []),
                { name: puzzleTitle, href: puzzleId ? `/play/${puzzleId}` :
                    (category && puzzleSlug) ? `/teka-teki-silang/${category.slug}/${puzzleSlug}` : '',
                  current: true }
              ]}
            />
          </div>
        </div>

        {/* Enhanced Puzzle Header with newspaper styling */}
        <div className={`${
          focusMode
            ? 'opacity-0 h-0 overflow-hidden pointer-events-none'
            : 'opacity-100'
        } transition-all duration-300`}>
          <GameHeader
            puzzleTitle={puzzleTitle}
            puzzleCreator={puzzleCreator}
            puzzleDifficulty={puzzleDifficulty}
            puzzleDescription={puzzleDescription}
            category={category}
            progress={contextState.progress || 0}
            gridSize={contextState.gridSize}
            isDescriptionOpen={isDescriptionOpen}
            onToggleDescription={toggleDescription}
            onToggleFocusMode={toggleFocusMode}
            calculateScore={calculateScore}
            timeSpent={contextState.timeSpent || 0}
          />
        </div>

        {/* Pre-game Interface - Show for both not-started and completed states */}
        {(contextState.gameState === 'not-started' || contextState.gameState === 'completed') ? (
          <PreGameInterface
            timeSpent={contextState.timeSpent || 0}
            gridSize={contextState.gridSize}
            hintsRemaining={contextState.hintsRemaining || 0}
            puzzleDifficulty={puzzleDifficulty}
            progress={contextState.progress || 0}
            gameState={contextState.gameState}
            onStartGame={startGame}
            onPlayAgain={playAgain}
          />
        ) : contextState.gameState === 'paused' ? (
          <PauseOverlay
            timeSpent={contextState.timeSpent || 0}
            onResumeGame={resumeGame}
          />
        ) : /* Enhanced Focus Mode Content */
        focusMode ? (
          <div className="max-w-7xl mx-auto">
            {/* Focus mode header */}
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-newsprint font-serif mb-2 uppercase tracking-wide">
                Mode Fokus - {puzzleTitle}
              </h2>
              <div className="flex items-center justify-center gap-4 text-sm text-newsprint opacity-90 font-mono mb-4">
                <span>Progress: {contextState.progress || 0}%</span>
                <span>•</span>
                <span>Bantuan: {contextState.hintsRemaining || 0}</span>
                <span>•</span>
                <span>Ukuran: {contextState.gridSize}×{contextState.gridSize}</span>
              </div>

              {/* Timer and Controls */}
              <div className="flex items-center justify-center gap-4">
                <div className="bg-newsprint text-ink-900 px-4 py-2 rounded-sm border-2 border-ink-900 shadow-paper">
                  <div className="flex items-center gap-2">
                    <ClockIcon className="w-4 h-4" />
                    <span className="font-mono font-bold">
                      {formatTime(contextState.timeSpent || 0)}
                    </span>
                  </div>
                </div>

                <button
                  onClick={togglePauseResume}
                  className="bg-newsprint text-ink-900 p-2 rounded-sm border-2 border-ink-900 hover:bg-ink-100 transition-all duration-200 shadow-paper hover:shadow-paper-lg"
                  title={contextState.gameState === 'playing' ? 'Jeda' : 'Lanjutkan'}
                  aria-label={contextState.gameState === 'playing' ? 'Jeda permainan' : 'Lanjutkan permainan'}
                >
                  {contextState.gameState === 'playing' ? (
                    <PauseIcon className="w-5 h-5" />
                  ) : (
                    <PlayIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Grid section - takes 2 columns on large screens */}
              <div className="lg:col-span-2 space-y-6">
                {/* Enhanced crossword grid container */}
                <div className="bg-newsprint rounded-sm border-4 border-ink-900 p-4 shadow-paper-xl bg-paper-dots">
                  <div className="aspect-square w-full max-w-2xl mx-auto">
                    <CrosswordGrid showResults={showResults} />
                  </div>
                </div>

                {/* Compact controls for focus mode */}
                <div className="bg-newsprint rounded-sm border-4 border-ink-900 p-4 bg-paper-lines">
                  <PlayControls
                    onCheckAnswers={checkAnswers}
                    onResetAnswers={resetPuzzleAnswers}
                  />
                </div>
              </div>

              {/* Clues section - takes 1 column */}
              <div className="lg:col-span-1">
                <div className="bg-newsprint rounded-sm border-4 border-ink-900 p-4 h-full min-h-[600px] max-h-[calc(100vh-200px)] overflow-hidden flex flex-col bg-paper-texture">
                  <div className="flex items-center justify-between mb-4 pb-3 border-b-4 border-ink-900">
                    <h3 className="text-xl font-bold text-ink-900 font-serif uppercase">Petunjuk</h3>
                    {category && (
                      <span className="bg-ink-900 text-newsprint text-xs font-medium px-2.5 py-1 rounded-sm font-serif">
                        {category.name}
                      </span>
                    )}
                  </div>
                  <div className="flex-1 overflow-auto">
                    <CrosswordClues />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Enhanced Normal Mode Layout */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main game area - 2 columns on large screens */}
            <div className="lg:col-span-2 space-y-6">
              {/* Enhanced crossword grid with newspaper styling */}
              <div className="bg-newsprint border-4 border-ink-900 p-4 shadow-paper-lg bg-paper-dots rounded-sm">
                <div className="aspect-square w-full max-w-3xl mx-auto">
                  <CrosswordGrid showResults={showResults} />
                </div>
              </div>

              {/* Enhanced play controls */}
              <div className="bg-newsprint border-4 border-ink-900 p-4 shadow-paper-lg bg-paper-lines rounded-sm">
                <PlayControls
                  onCheckAnswers={checkAnswers}
                  onResetAnswers={resetPuzzleAnswers}
                />
              </div>
            </div>

            {/* Sidebar - 1 column on large screens */}
            <div className="lg:col-span-1 space-y-6">
              {/* Enhanced clues section */}
              <div className="bg-newsprint border-4 border-ink-900 p-4 h-full min-h-[600px] max-h-[calc(100vh-200px)] overflow-hidden flex flex-col shadow-paper-lg bg-paper-texture rounded-sm">
                <div className="flex items-center justify-between mb-4 pb-3 border-b-4 border-ink-900">
                  <h2 className="text-xl font-bold text-ink-900 font-serif uppercase">Petunjuk</h2>
                  {category && (
                    <div className="flex items-center bg-ink-900 text-newsprint px-3 py-1 rounded-sm">
                      <TagIcon className="w-3 h-3 mr-1" />
                      <span className="text-xs font-medium font-serif">{category.name}</span>
                    </div>
                  )}
                </div>
                <div className="flex-1 overflow-auto">
                  <CrosswordClues />
                </div>
              </div>

              {/* Enhanced game info panel */}
              <GameInfo
                puzzleCreator={puzzleCreator}
                puzzleDifficulty={puzzleDifficulty}
                gridSize={contextState.gridSize}
                hintsRemaining={contextState.hintsRemaining || 0}
                progress={contextState.progress || 0}
                timeSpent={contextState.timeSpent || 0}
                gameState={contextState.gameState || 'not-started'}
                category={category}
                onTogglePauseResume={togglePauseResume}
              />
            </div>
          </div>
        )}

        {/* Mobile Floating Clues Panel */}
        {isMobile && !focusMode && (
          <MobileFloatingClues
            isExpanded={isFloatingCluesExpanded}
            onToggle={toggleFloatingClues}
          />
        )}

        {/* Completion Modal */}
        <CompletionModal
          isOpen={showCompletionModal}
          onClose={() => setShowCompletionModal(false)}
          completionTime={timerManager.currentTime}
          hintsUsed={(contextState.hintsRemaining !== undefined) ? (3 - contextState.hintsRemaining) : 0}
          totalHints={3}
          progress={contextState.progress || 0}
          onPlayAgain={playAgain}
          onBackToMenu={() => {
            setShowCompletionModal(false);
            navigate('/play');
          }}
        />
      </div>
    </div>
  );
};

export default PlayPage;
