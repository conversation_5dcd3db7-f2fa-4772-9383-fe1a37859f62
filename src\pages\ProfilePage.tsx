import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { getAllUserProgress, UserProgress, getCategoryById } from '../services/api';
import { UserIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

// Interface for category data with slug
interface CategoryWithSlug {
  id: string;
  slug: string;
  name: string;
}

const ProfilePage: React.FC = () => {
  const auth = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [bio, setBio] = useState('');
  const [userProgress, setUserProgress] = useState<UserProgress[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [progressLoading, setProgressLoading] = useState(false);
  const [categories, setCategories] = useState<Record<string, CategoryWithSlug>>({});

  // Load user data into form
  useEffect(() => {
    if (auth.user) {
      setDisplayName(auth.user.displayName || '');
      setAvatarUrl(auth.user.avatar_url || '');
      setBio(auth.user.bio || '');
    }
  }, [auth.user]);

  // Get SEO-friendly URL for a crossword
  const getCrosswordUrl = (progress: UserProgress) => {
    // If we have the category in our cache and the crossword has a slug
    const categoryId = progress.category_id;
    if (categoryId && categories[categoryId] && progress.slug) {
      return `/teka-teki-silang/${categories[categoryId].slug}/${progress.slug}`;
    }
    // Fallback to the old URL format
    return `/play/${progress.crossword_id}`;
  };

  // Fetch user progress
  useEffect(() => {
    const fetchUserProgress = async () => {
      if (!auth.isAuthenticated) return;

      setProgressLoading(true);
      try {
        const progress = await getAllUserProgress();
        setUserProgress(progress);

        // Collect unique category IDs from progress
        const categoryIds = new Set<string>();
        progress.forEach(item => {
          if (item.category_id) {
            categoryIds.add(item.category_id);
          }
        });

        // Fetch category data for each unique category ID
        const categoryData: Record<string, CategoryWithSlug> = {};
        for (const id of categoryIds) {
          try {
            const category = await getCategoryById(id);
            if (category && category.slug) {
              categoryData[id] = {
                id: category.id,
                slug: category.slug,
                name: category.name
              };
            }
          } catch (error) {
            console.error(`Error fetching category ${id}:`, error);
          }
        }

        setCategories(categoryData);
      } catch (err) {
        console.error('Error fetching user progress:', err);
      } finally {
        setProgressLoading(false);
      }
    };

    fetchUserProgress();
  }, [auth.isAuthenticated]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Only include fields that have changed
      const updateData: { displayName?: string; avatarUrl?: string; bio?: string } = {};

      if (displayName !== (auth.user?.displayName || '')) {
        updateData.displayName = displayName;
      }

      if (avatarUrl !== (auth.user?.avatar_url || '')) {
        updateData.avatarUrl = avatarUrl;
      }

      if (bio !== (auth.user?.bio || '')) {
        updateData.bio = bio;
      }

      // Only make API call if there are changes
      if (Object.keys(updateData).length > 0) {
        await auth.updateProfile(updateData);
        setSuccessMessage('Profil berhasil diperbarui');
      } else {
        setSuccessMessage('Tidak ada perubahan yang dilakukan');
      }

      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Gagal memperbarui profil');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'mudah': return 'Mudah';
      case 'sedang': return 'Sedang';
      case 'sulit': return 'Sulit';
      default: return difficulty;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Profil Pengguna</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Profile Information */}
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            {isEditing ? (
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-1">
                    Nama Tampilan
                  </label>
                  <input
                    type="text"
                    id="displayName"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="avatarUrl" className="block text-sm font-medium text-gray-700 mb-1">
                    URL Avatar
                  </label>
                  <input
                    type="text"
                    id="avatarUrl"
                    value={avatarUrl}
                    onChange={(e) => setAvatarUrl(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/avatar.jpg"
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  <textarea
                    id="bio"
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Ceritakan tentang diri Anda..."
                  ></textarea>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
                    <p>{error}</p>
                  </div>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setIsEditing(false);
                      // Reset form values
                      if (auth.user) {
                        setDisplayName(auth.user.displayName || '');
                        setAvatarUrl(auth.user.avatar_url || '');
                        setBio(auth.user.bio || '');
                      }
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    disabled={isLoading}
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Menyimpan...' : 'Simpan'}
                  </button>
                </div>
              </form>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    {avatarUrl ? (
                      <img
                        src={avatarUrl}
                        alt={`Profil pengguna ${displayName || auth.user?.username}`}
                        className="w-16 h-16 rounded-full object-cover"
                        onError={(e) => {
                          // If image fails to load, show default icon
                          e.currentTarget.style.display = 'none';
                          document.getElementById('fallback-avatar')!.style.display = 'flex';
                        }}
                      />
                    ) : (
                      <div
                        id="fallback-avatar"
                        className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center"
                      >
                        <UserIcon className="w-8 h-8 text-gray-500" />
                      </div>
                    )}
                    <div className="ml-4">
                      <h2 className="text-xl font-semibold">
                        {displayName || auth.user?.username}
                      </h2>
                      <p className="text-gray-600">@{auth.user?.username}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    Edit
                  </button>
                </div>

                {successMessage && (
                  <div className="mb-4 p-3 bg-green-100 border-l-4 border-green-500 text-green-700">
                    <p>{successMessage}</p>
                  </div>
                )}

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                    Email
                  </h3>
                  <p>{auth.user?.email}</p>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                    Bio
                  </h3>
                  <p className="whitespace-pre-wrap">{bio || 'Belum ada bio.'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">
                    Bergabung Sejak
                  </h3>
                  <p>{auth.user?.created_at ? formatDate(auth.user.created_at) : 'Tidak diketahui'}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* User Progress */}
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Progres Teka-Teki Silang</h2>

            {progressLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
              </div>
            ) : userProgress.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Teka-Teki
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Kesulitan
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progres
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Terakhir Dimainkan
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {userProgress.map((progress) => (
                      <tr key={progress.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Link
                            to={getCrosswordUrl(progress)}
                            className="text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            {progress.crossword_title || 'Teka-Teki #' + progress.crossword_id.substring(0, 8)}
                          </Link>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${progress.difficulty === 'mudah' ? 'bg-green-100 text-green-800' :
                              progress.difficulty === 'sedang' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'}`}>
                            {getDifficultyLabel(progress.difficulty || 'sedang')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="w-full bg-gray-200 rounded-full h-2.5 max-w-[100px]">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full"
                              style={{ width: `${progress.progress_data.progress || 0}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 mt-1">
                            {progress.progress_data.progress || 0}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {progress.is_completed ? (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              Selesai
                            </span>
                          ) : (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              Dalam Progres
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(progress.updated_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Anda belum memainkan teka-teki silang apa pun.</p>
                <Link to="/play" className="mt-2 inline-block text-blue-600 hover:text-blue-800 hover:underline">
                  Mulai main sekarang!
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
