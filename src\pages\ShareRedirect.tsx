import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { API_URL } from '../config';

const ShareRedirect: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const redirectToCrossword = async () => {
      if (!code) {
        navigate('/');
        return;
      }

      try {
        // Fetch the crossword ID from the share code
        const response = await fetch(`${API_URL}/api/share/${code}`);
        const data = await response.json();
        
        if (data.status === 'success' && data.data?.crossword_id) {
          // Redirect to the crossword page
          navigate(`/play/${data.data.crossword_id}`);
        } else {
          setError('Link berbagi tidak valid atau telah kedaluwarsa.');
          // Redirect to the home page after a delay
          setTimeout(() => {
            navigate('/');
          }, 3000);
        }
      } catch (error) {
        console.error('Error fetching share link:', error);
        setError('Terjadi kesalahan saat memproses link berbagi.');
        // Redirect to the home page after a delay
        setTimeout(() => {
          navigate('/');
        }, 3000);
      }
    };

    redirectToCrossword();
  }, [code, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50">
      <div className="text-center">
        {error ? (
          <div>
            <h1 className="text-xl font-semibold text-red-600 mb-2">{error}</h1>
            <p className="text-gray-600">Mengalihkan ke halaman utama...</p>
          </div>
        ) : (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 className="text-xl font-semibold text-gray-800">Mengalihkan ke teka-teki silang...</h1>
          </>
        )}
      </div>
    </div>
  );
};

export default ShareRedirect;
