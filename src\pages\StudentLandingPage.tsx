import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { BookOpenIcon, GraduationCapIcon, BrainIcon, UsersIcon } from 'lucide-react';
import SEO from '../components/SEO';
import { getStructuredData } from '../utils/seoUtils';

const StudentLandingPage: React.FC = () => {
  // Create specific structured data for this landing page
  const structuredData = useMemo(() => {
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    
    // Base structured data from common utility
    const baseStructuredData = getStructuredData();
    
    // Educational content structured data
    const educationalContentData = {
      "@context": "https://schema.org",
      "@type": "EducationalResource",
      "name": "Teka-Teki Silang untuk Pelajar",
      "description": "Kumpulan teka-teki silang edukatif untuk pelajar. Tingkatkan kosakata dan pengetahuan umum sambil bermain.",
      "educationalLevel": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Se<PERSON>lah <PERSON>eng<PERSON>",
      "educationalUse": "Pembel<PERSON>ran Bahasa, Pengaya<PERSON> Kosa<PERSON>",
      "learningResourceType": "Game Edukatif",
      "inLanguage": "id-ID",
      "audience": {
        "@type": "EducationalAudience",
        "educationalRole": "student"
      },
      "publisher": {
        "@type": "Organization",
        "name": "TTS - Teka Teki Silang Online",
        "url": origin
      }
    };
    
    return [...baseStructuredData, educationalContentData];
  }, []);

  return (
    <div className="min-h-screen bg-slate-50">
      <SEO
        title="Teka-Teki Silang untuk Pelajar | Belajar Sambil Bermain"
        description="Kumpulan teka-teki silang edukatif untuk pelajar. Tingkatkan kosakata dan pengetahuan umum sambil bermain. Cocok untuk tugas sekolah dan belajar bahasa Indonesia."
        keywords="teka teki silang untuk pelajar, tts edukatif, game edukasi, belajar bahasa indonesia, permainan kata untuk siswa, teka teki silang sekolah"
        structuredData={structuredData}
      />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Belajar Bahasa Indonesia Jadi Menyenangkan dengan Teka-Teki Silang
              </h1>
              <p className="text-xl mb-6">
                Tingkatkan kosakata dan pengetahuan umum sambil bermain. Cocok untuk tugas sekolah dan belajar bahasa Indonesia.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/teka-teki-silang/pendidikan" className="bg-white text-blue-700 hover:bg-blue-50 font-bold py-3 px-8 rounded-lg shadow-lg transition">
                  Mulai Belajar
                </Link>
                <Link to="/register" className="bg-transparent border-2 border-white hover:bg-white/10 font-bold py-3 px-8 rounded-lg transition">
                  Daftar Gratis
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <img src="/images/default-img.png" alt="Pelajar bermain Teka-Teki Silang" className="w-full max-w-md rounded-lg shadow-lg" />
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits for Students */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Manfaat TTS untuk Pelajar</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="bg-blue-50 p-6 rounded-lg shadow-md text-center">
              <BookOpenIcon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-bold mb-2">Perkaya Kosakata</h3>
              <p className="text-gray-700">Pelajari kata-kata baru dan tingkatkan kemampuan bahasa Indonesia Anda.</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-lg shadow-md text-center">
              <GraduationCapIcon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-bold mb-2">Dukung Pembelajaran</h3>
              <p className="text-gray-700">Cocok untuk tugas sekolah dan memperdalam pemahaman materi pelajaran.</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-lg shadow-md text-center">
              <BrainIcon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-bold mb-2">Asah Kemampuan Berpikir</h3>
              <p className="text-gray-700">Latih logika dan kemampuan memecahkan masalah dengan cara yang menyenangkan.</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-lg shadow-md text-center">
              <UsersIcon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-bold mb-2">Belajar Bersama</h3>
              <p className="text-gray-700">Main bersama teman atau keluarga untuk pengalaman belajar yang lebih seru.</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Featured Educational Puzzles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">TTS Edukatif Pilihan</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-blue-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-blue-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Kosakata Bahasa Indonesia</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Mudah</p>
                <p className="text-gray-700 mb-4">Cocok untuk pelajar SD dan SMP yang ingin memperkaya kosakata bahasa Indonesia.</p>
                <Link to="/teka-teki-silang/pendidikan/kosakata" className="text-blue-600 hover:text-blue-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-green-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-green-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Pengetahuan Umum</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Sedang</p>
                <p className="text-gray-700 mb-4">Berisi pertanyaan seputar pengetahuan umum yang sering muncul dalam pelajaran sekolah.</p>
                <Link to="/teka-teki-silang/pendidikan/pengetahuan-umum" className="text-blue-600 hover:text-blue-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-purple-100 flex items-center justify-center">
                <span className="text-6xl font-bold text-purple-600">TTS</span>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">Istilah Sains</h3>
                <p className="text-gray-700 mb-4">Tingkat kesulitan: Menantang</p>
                <p className="text-gray-700 mb-4">Berisi istilah-istilah sains yang sering digunakan dalam pelajaran IPA di sekolah.</p>
                <Link to="/teka-teki-silang/pendidikan/sains" className="text-blue-600 hover:text-blue-800 font-medium">
                  Main Sekarang &rarr;
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Testimonials from Students */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Apa Kata Para Pelajar</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"TTS ini sangat membantu saya belajar kata-kata baru. Saya jadi lebih mudah mengerjakan tugas bahasa Indonesia."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">A</div>
                <div>
                  <h4 className="font-bold">Andi</h4>
                  <p className="text-sm text-gray-600">Siswa SD Kelas 6</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"Saya suka main TTS ini bareng teman-teman. Jadi seperti kompetisi siapa yang paling banyak tahu jawabannya."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">R</div>
                <div>
                  <h4 className="font-bold">Rina</h4>
                  <p className="text-sm text-gray-600">Siswi SMP Kelas 8</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"TTS istilah sains membantu saya mengingat istilah-istilah yang sulit dalam pelajaran biologi dan kimia."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">D</div>
                <div>
                  <h4 className="font-bold">Dimas</h4>
                  <p className="text-sm text-gray-600">Siswa SMA Kelas 11</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Call to Action */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Siap Untuk Belajar Sambil Bermain?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Bergabunglah dengan ribuan pelajar lainnya dan tingkatkan kemampuan bahasa Indonesia Anda dengan cara yang menyenangkan.
          </p>
          <Link
            to="/teka-teki-silang/pendidikan"
            className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-md font-medium text-lg inline-block"
          >
            Mulai Belajar Sekarang
          </Link>
        </div>
      </section>
    </div>
  );
};

export default StudentLandingPage;
