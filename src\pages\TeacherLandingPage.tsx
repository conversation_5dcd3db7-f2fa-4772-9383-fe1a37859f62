import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { BookOpenIcon, PencilIcon, UsersIcon, BarChartIcon } from 'lucide-react';
import SEO from '../components/SEO';
import { getStructuredData } from '../utils/seoUtils';

const TeacherLandingPage: React.FC = () => {
  // Create specific structured data for this landing page
  const structuredData = useMemo(() => {
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    
    // Base structured data from common utility
    const baseStructuredData = getStructuredData();
    
    // Educational content structured data for teachers
    const educationalContentData = {
      "@context": "https://schema.org",
      "@type": "EducationalResource",
      "name": "Teka-Teki Silang untuk Guru",
      "description": "Platform teka-teki silang untuk guru dan pendidik. Buat TTS kustom sesuai materi pelajaran dan gunakan sebagai alat pembelajaran interaktif di kela<PERSON>.",
      "educationalLevel": "<PERSON><PERSON><PERSON> Dasar, Se<PERSON>lah <PERSON>engah Pertama, Sekolah Menengah Atas",
      "educationalUse": "Alat Pembelajaran, Penilaian",
      "learningResourceType": "Game Edukatif, Alat Pengajar",
      "inLanguage": "id-ID",
      "audience": {
        "@type": "EducationalAudience",
        "educationalRole": "teacher"
      },
      "publisher": {
        "@type": "Organization",
        "name": "TTS - Teka Teki Silang Online",
        "url": origin
      }
    };
    
    return [...baseStructuredData, educationalContentData];
  }, []);

  return (
    <div className="min-h-screen bg-slate-50">
      <SEO
        title="Teka-Teki Silang untuk Guru | Alat Pembelajaran Interaktif"
        description="Platform teka-teki silang untuk guru dan pendidik. Buat TTS kustom sesuai materi pelajaran dan gunakan sebagai alat pembelajaran interaktif di kelas."
        keywords="teka teki silang untuk guru, tts pendidikan, alat pembelajaran interaktif, media pembelajaran bahasa indonesia, teka teki silang kustom, game edukasi untuk kelas"
        structuredData={structuredData}
      />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-800 to-green-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Tingkatkan Pembelajaran di Kelas dengan Teka-Teki Silang Interaktif
              </h1>
              <p className="text-xl mb-6">
                Buat TTS kustom sesuai materi pelajaran dan gunakan sebagai alat pembelajaran interaktif yang menyenangkan di kelas.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/create" className="bg-white text-green-700 hover:bg-green-50 font-bold py-3 px-8 rounded-lg shadow-lg transition">
                  Buat TTS Kustom
                </Link>
                <Link to="/teka-teki-silang/pendidikan" className="bg-transparent border-2 border-white hover:bg-white/10 font-bold py-3 px-8 rounded-lg transition">
                  Lihat Contoh TTS
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <img src="/images/default-img.png" alt="Guru menggunakan Teka-Teki Silang di kelas" className="w-full max-w-md rounded-lg shadow-lg" />
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits for Teachers */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Manfaat TTS untuk Guru</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="bg-green-50 p-6 rounded-lg shadow-md text-center">
              <PencilIcon className="w-12 h-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-xl font-bold mb-2">Buat TTS Kustom</h3>
              <p className="text-gray-700">Rancang teka-teki silang sesuai dengan materi pelajaran yang sedang diajarkan.</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg shadow-md text-center">
              <UsersIcon className="w-12 h-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-xl font-bold mb-2">Tingkatkan Partisipasi</h3>
              <p className="text-gray-700">Buat siswa lebih aktif dan antusias dalam belajar dengan metode yang menyenangkan.</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg shadow-md text-center">
              <BookOpenIcon className="w-12 h-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-xl font-bold mb-2">Perkuat Pemahaman</h3>
              <p className="text-gray-700">Bantu siswa mengingat istilah dan konsep penting dengan cara yang efektif.</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg shadow-md text-center">
              <BarChartIcon className="w-12 h-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-xl font-bold mb-2">Pantau Kemajuan</h3>
              <p className="text-gray-700">Lihat statistik penyelesaian dan identifikasi area yang perlu ditingkatkan.</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Cara Menggunakan TTS di Kelas</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold mb-4">1</div>
              <h3 className="text-xl font-bold mb-3">Buat TTS Kustom</h3>
              <p className="text-gray-700 mb-4">Rancang teka-teki silang dengan kata-kata dan definisi yang sesuai dengan materi pelajaran Anda.</p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Pilih tingkat kesulitan yang sesuai</li>
                <li>Tambahkan petunjuk yang jelas</li>
                <li>Sesuaikan dengan kurikulum</li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold mb-4">2</div>
              <h3 className="text-xl font-bold mb-3">Bagikan ke Siswa</h3>
              <p className="text-gray-700 mb-4">Bagikan tautan TTS kepada siswa untuk dimainkan di kelas atau sebagai pekerjaan rumah.</p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Kirim tautan melalui email</li>
                <li>Tampilkan di layar proyektor</li>
                <li>Cetak untuk aktivitas offline</li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold mb-4">3</div>
              <h3 className="text-xl font-bold mb-3">Evaluasi Hasil</h3>
              <p className="text-gray-700 mb-4">Pantau kemajuan siswa dan gunakan hasil sebagai bahan evaluasi pembelajaran.</p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Lihat statistik penyelesaian</li>
                <li>Identifikasi konsep yang sulit</li>
                <li>Berikan umpan balik</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* Testimonials from Teachers */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Apa Kata Para Guru</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"Saya menggunakan TTS ini untuk mengajar kosakata bahasa Indonesia. Siswa saya jadi lebih antusias dan kosakata mereka meningkat pesat!"</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold mr-3">S</div>
                <div>
                  <h4 className="font-bold">Sri Wahyuni</h4>
                  <p className="text-sm text-gray-600">Guru Bahasa Indonesia SD</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"TTS kustom sangat membantu untuk mengajarkan istilah-istilah IPA yang sulit. Siswa jadi lebih mudah mengingat konsep-konsep penting."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold mr-3">B</div>
                <div>
                  <h4 className="font-bold">Budi Santoso</h4>
                  <p className="text-sm text-gray-600">Guru IPA SMP</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-md">
              <p className="text-gray-700 italic mb-4">"Saya menggunakan TTS sebagai aktivitas penutup di akhir bab. Ini membantu siswa mengingat istilah-istilah penting sebelum ujian."</p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold mr-3">M</div>
                <div>
                  <h4 className="font-bold">Maya Indrawati</h4>
                  <p className="text-sm text-gray-600">Guru Sejarah SMA</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Call to Action */}
      <section className="py-16 bg-green-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Siap Untuk Meningkatkan Pembelajaran di Kelas Anda?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Bergabunglah dengan ribuan guru yang telah menggunakan teka-teki silang sebagai alat pembelajaran interaktif.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="bg-white text-green-600 hover:bg-green-50 px-8 py-4 rounded-md font-medium text-lg inline-block"
            >
              Daftar Akun Guru
            </Link>
            <Link
              to="/create"
              className="bg-transparent border-2 border-white hover:bg-white/10 px-8 py-4 rounded-md font-medium text-lg inline-block"
            >
              Buat TTS Sekarang
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TeacherLandingPage;
