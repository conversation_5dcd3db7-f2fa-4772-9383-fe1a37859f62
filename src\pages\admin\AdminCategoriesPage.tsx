import React, { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon } from 'lucide-react';
import { getCategories, Category } from '../../services/api';
import CategoryForm from '../../components/admin/CategoryForm';
import DeleteConfirmationModal from '../../components/admin/DeleteConfirmationModal';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminCategoriesPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        const data = await getCategories();
        setCategories(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Handle add category
  const handleAddCategory = () => {
    setEditingCategory(null);
    setShowAddForm(true);
  };

  // Handle edit category
  const handleEditCategory = (category: Category) => {
    setShowAddForm(false);
    setEditingCategory(category);
  };

  // Handle delete category
  const handleDeleteConfirm = (categoryId: string) => {
    setShowDeleteConfirm(categoryId);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Kelola Kategori</h1>
          <button
            onClick={handleAddCategory}
            className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Tambah Kategori
          </button>
        </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Add Category Form */}
      {showAddForm && (
        <CategoryForm
          onCancel={() => setShowAddForm(false)}
          onSuccess={(newCategory) => {
            setCategories([...categories, newCategory]);
            setShowAddForm(false);
          }}
        />
      )}

      {/* Edit Category Form */}
      {editingCategory && (
        <CategoryForm
          category={editingCategory}
          onCancel={() => setEditingCategory(null)}
          onSuccess={(updatedCategory) => {
            setCategories(
              categories.map((cat) =>
                cat.id === updatedCategory.id ? updatedCategory : cat
              )
            );
            setEditingCategory(null);
          }}
        />
      )}

      {/* Categories Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nama
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Deskripsi
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Jumlah TTS
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tanggal Dibuat
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                  Tidak ada kategori yang ditemukan
                </td>
              </tr>
            ) : (
              categories.map((category) => (
                <tr key={category.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{category.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-gray-500 truncate max-w-xs">
                      {category.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-gray-500">{category.crossword_count}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-gray-500">
                      {new Date(category.created_at).toLocaleDateString('id-ID')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEditCategory(category)}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleDeleteConfirm(category.id)}
                      className="text-red-600 hover:text-red-900"
                      disabled={category.crossword_count > 0}
                      title={
                        category.crossword_count > 0
                          ? 'Tidak dapat menghapus kategori yang memiliki TTS'
                          : 'Hapus kategori'
                      }
                    >
                      <TrashIcon className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <DeleteConfirmationModal
          categoryId={showDeleteConfirm}
          categoryName={categories.find(cat => cat.id === showDeleteConfirm)?.name}
          onCancel={() => setShowDeleteConfirm(null)}
          onConfirm={(deletedId) => {
            setCategories(categories.filter((cat) => cat.id !== deletedId));
            setShowDeleteConfirm(null);
          }}
        />
      )}
      </div>
    </AdminLayout>
  );
};

export default AdminCategoriesPage;
