import React, { useState, useEffect } from 'react';
import { PencilIcon, TrashIcon, EyeIcon } from 'lucide-react';
import { getCrosswords, CrosswordListItem } from '../../services/api';
import AdminLayout from '../../components/admin/AdminLayout';
import DeleteConfirmationModal from '../../components/admin/DeleteConfirmationModal';
import { Link } from 'react-router-dom';

const AdminCrosswordsPage: React.FC = () => {
  const [crosswords, setCrosswords] = useState<CrosswordListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [crosswordToDelete, setCrosswordToDelete] = useState<CrosswordListItem | null>(null);

  // Fetch crosswords on component mount and when page changes
  useEffect(() => {
    const fetchCrosswords = async () => {
      try {
        setLoading(true);
        const result = await getCrosswords({}, { page: currentPage, limit: 10 });
        setCrosswords(result.crosswords);
        setTotalPages(result.pagination?.totalPages || 1);
        setError(null);
      } catch (err) {
        setError('Failed to load crosswords. Please try again.');
        console.error('Error fetching crosswords:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCrosswords();
  }, [currentPage]);

  // Handle delete crossword
  const handleDeleteClick = (crossword: CrosswordListItem) => {
    setCrosswordToDelete(crossword);
    setShowDeleteModal(true);
  };

  // Confirm delete crossword
  const handleConfirmDelete = async () => {
    if (!crosswordToDelete) return;

    try {
      // TODO: Implement deleteCrossword API function
      // await deleteCrossword(crosswordToDelete.id);
      setCrosswords(crosswords.filter(c => c.id !== crosswordToDelete.id));
      setShowDeleteModal(false);
      setCrosswordToDelete(null);
    } catch (err) {
      setError('Failed to delete crossword. Please try again.');
      console.error('Error deleting crossword:', err);
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get difficulty label
  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'mudah':
        return 'Mudah';
      case 'sedang':
        return 'Sedang';
      case 'sulit':
        return 'Sulit';
      default:
        return difficulty;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'mudah':
        return 'bg-green-100 text-green-800';
      case 'sedang':
        return 'bg-yellow-100 text-yellow-800';
      case 'sulit':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Kelola Teka-Teki Silang</h1>
          <Link
            to="/create"
            className="bg-purple-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Buat TTS Baru
          </Link>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Judul
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pembuat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Kesulitan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Dimainkan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rating
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tanggal Dibuat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aksi
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {crosswords.map(crossword => (
                      <tr key={crossword.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {crossword.title}
                          </div>
                          {crossword.description && (
                            <div className="text-sm text-gray-500">
                              {crossword.description.length > 50
                                ? `${crossword.description.substring(0, 50)}...`
                                : crossword.description}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {crossword.creator || 'Unknown'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getDifficultyColor(crossword.difficulty)}`}>
                            {getDifficultyLabel(crossword.difficulty)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {crossword.plays}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {crossword.rating ? crossword.rating.toFixed(1) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(crossword.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link
                            to={`/play/${crossword.id}`}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            <EyeIcon className="w-5 h-5" />
                          </Link>
                          <Link
                            to={`/edit/${crossword.id}`}
                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                          >
                            <PencilIcon className="w-5 h-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(crossword)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <TrashIcon className="w-5 h-5" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    &laquo; Sebelumnya
                  </button>
                  {[...Array(totalPages)].map((_, i) => (
                    <button
                      key={i}
                      onClick={() => handlePageChange(i + 1)}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === i + 1 ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                      currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    Selanjutnya &raquo;
                  </button>
                </nav>
              </div>
            )}
          </>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && crosswordToDelete && (
          <DeleteConfirmationModal
            title="Hapus Teka-Teki Silang"
            message={`Apakah Anda yakin ingin menghapus TTS "${crosswordToDelete.title}"? Tindakan ini tidak dapat dibatalkan.`}
            onCancel={() => {
              setShowDeleteModal(false);
              setCrosswordToDelete(null);
            }}
            onConfirm={handleConfirmDelete}
          />
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminCrosswordsPage;
