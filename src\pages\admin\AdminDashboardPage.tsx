import React from 'react';
import { Link } from 'react-router-dom';
import { GridIcon, UsersIcon, SettingsIcon, PuzzleIcon, FileTextIcon } from 'lucide-react';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminDashboardPage: React.FC = () => {
  // Dashboard cards
  const dashboardCards = [
    {
      title: 'Kelola Kategori',
      description: 'Tambah, edit, atau hapus kategori TTS',
      icon: <GridIcon className="w-8 h-8" />,
      color: 'bg-blue-500',
      link: '/admin/categories'
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      description: 'Lihat dan kelola akun pengguna',
      icon: <UsersIcon className="w-8 h-8" />,
      color: 'bg-green-500',
      link: '/admin/users'
    },
    {
      title: 'Kelola TTS',
      description: 'Lihat dan kelola teka-teki silang',
      icon: <PuzzleIcon className="w-8 h-8" />,
      color: 'bg-purple-500',
      link: '/admin/crosswords'
    },
    {
      title: 'Ke<PERSON>la Blog',
      description: 'Tambah, edit, atau hapus artikel blog',
      icon: <FileTextIcon className="w-8 h-8" />,
      color: 'bg-pink-500',
      link: '/admin/blogs'
    },
    {
      title: 'Pengaturan',
      description: 'Konfigurasi sistem dan preferensi',
      icon: <SettingsIcon className="w-8 h-8" />,
      color: 'bg-yellow-500',
      link: '/admin/settings'
    }
  ];

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Dashboard Admin</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {dashboardCards.map((card, index) => (
            <Link
              key={index}
              to={card.link}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
            >
              <div className={`${card.color} p-4 flex justify-center items-center text-white`}>
                {card.icon}
              </div>
              <div className="p-4">
                <h2 className="font-bold text-lg mb-1">{card.title}</h2>
                <p className="text-sm text-gray-600">{card.description}</p>
              </div>
            </Link>
          ))}
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Aktivitas Terbaru</h2>
          <p className="text-gray-600">Belum ada aktivitas terbaru.</p>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboardPage;
