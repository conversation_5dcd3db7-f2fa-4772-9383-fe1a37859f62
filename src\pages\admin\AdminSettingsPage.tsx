import React, { useState, useEffect } from 'react';
import { SaveIcon } from 'lucide-react';
import { getSettings, updateSettings } from '../../services/api';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminSettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const result = await getSettings();
        setSettings(result);
        setError(null);
      } catch (err) {
        setError('Failed to load settings. Please try again.');
        console.error('Error fetching settings:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle input change
  const handleInputChange = (key: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      await updateSettings(settings);
      setSuccess('Pengaturan berhasil disimpan.');
      setError(null);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to save settings. Please try again.');
      console.error('Error saving settings:', err);
      setSuccess(null);
    } finally {
      setSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Pengaturan Sistem</h1>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Site Title */}
                <div>
                  <label htmlFor="site_title" className="block text-sm font-medium text-gray-700 mb-1">
                    Judul Situs
                  </label>
                  <input
                    type="text"
                    id="site_title"
                    value={settings.site_title || ''}
                    onChange={(e) => handleInputChange('site_title', e.target.value)}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Judul yang akan ditampilkan di tab browser dan SEO
                  </p>
                </div>

                {/* Site Description */}
                <div>
                  <label htmlFor="site_description" className="block text-sm font-medium text-gray-700 mb-1">
                    Deskripsi Situs
                  </label>
                  <input
                    type="text"
                    id="site_description"
                    value={settings.site_description || ''}
                    onChange={(e) => handleInputChange('site_description', e.target.value)}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Deskripsi singkat untuk SEO dan meta tags
                  </p>
                </div>

                {/* Max Hints */}
                <div>
                  <label htmlFor="max_hints" className="block text-sm font-medium text-gray-700 mb-1">
                    Jumlah Maksimum Petunjuk
                  </label>
                  <input
                    type="number"
                    id="max_hints"
                    min="0"
                    max="10"
                    value={settings.max_hints || '3'}
                    onChange={(e) => handleInputChange('max_hints', e.target.value)}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Jumlah petunjuk yang dapat digunakan pemain dalam satu permainan
                  </p>
                </div>

                {/* Enable Google Login */}
                <div>
                  <label htmlFor="enable_google_login" className="block text-sm font-medium text-gray-700 mb-1">
                    Aktifkan Login Google
                  </label>
                  <select
                    id="enable_google_login"
                    value={settings.enable_google_login || 'true'}
                    onChange={(e) => handleInputChange('enable_google_login', e.target.value)}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  >
                    <option value="true">Ya</option>
                    <option value="false">Tidak</option>
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    Aktifkan atau nonaktifkan opsi login dengan Google
                  </p>
                </div>

                {/* Default Difficulty */}
                <div>
                  <label htmlFor="default_difficulty" className="block text-sm font-medium text-gray-700 mb-1">
                    Tingkat Kesulitan Default
                  </label>
                  <select
                    id="default_difficulty"
                    value={settings.default_difficulty || 'sedang'}
                    onChange={(e) => handleInputChange('default_difficulty', e.target.value)}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  >
                    <option value="mudah">Mudah</option>
                    <option value="sedang">Sedang</option>
                    <option value="sulit">Sulit</option>
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    Tingkat kesulitan default untuk TTS baru
                  </p>
                </div>
              </div>

              <div className="mt-8">
                <button
                  type="submit"
                  disabled={saving}
                  className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    saving ? 'opacity-75 cursor-not-allowed' : ''
                  }`}
                >
                  {saving ? (
                    <>
                      <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <SaveIcon className="w-5 h-5 mr-2" />
                      Simpan Pengaturan
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminSettingsPage;
