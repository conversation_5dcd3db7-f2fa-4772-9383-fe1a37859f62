import { CrosswordState, Cell } from '../types/crossword'; // Added Cell import
import {
  API_BASE_URL,
  getHeaders,
  fetchWithErrorHandling,
  handleApiResponse,
  createQueryString,
  ApiResponse
} from './apiUtils';
import {
  cachePuzzle,
  getCachedPuzzle,
  cachePuzzleList,
  getCachedPuzzleList,
  isCacheAvailable
} from '../utils/cacheManager';
import { isOnline } from '../utils/serviceWorkerRegistration';

// Interface for pagination data
export interface PaginationData {
  page: number;
  limit: number;
  total: number; // Total items across all pages
  totalPages: number;
}

// Interface for crossword data from API
export interface CrosswordData {
  id: string;
  title: string;
  description?: string;
  grid_size: number;
  grid_data: Cell[][]; // Changed from any
  words: string[];
  clues: {
    across: Record<number, string>;
    down: Record<number, string>;
  };
  word_positions: {
    direction: 'across' | 'down';
    row: number;
    col: number;
    number: number;
  }[];
  difficulty: 'mudah' | 'sedang' | 'sulit';
  user_id?: string;
  creator?: string;
  is_public: boolean;
  plays: number;
  rating?: number;
  category_id?: string;
  created_at: string;
  updated_at: string;
  state?: CrosswordState;
}

// Interface for crossword list item
export interface CrosswordListItem {
  id: string;
  title: string;
  slug?: string;
  description?: string;
  creator?: string;
  difficulty: 'mudah' | 'sedang' | 'sulit';
  plays: number;
  rating?: number;
  created_at: string;
  category_id?: string;
  category_slug?: string;
  category_name?: string;
}

// Interface for filter options
export interface CrosswordFilters {
  category_id?: string;
  difficulty?: 'mudah' | 'sedang' | 'sulit';
  is_public?: boolean;
  user_id?: string;
  search?: string;
}

// Interface for pagination options
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sort?: string;
}

// Interface for category data
export interface Category {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  image_url?: string;
  created_at: string;
  crossword_count: number;
}

/**
 * Get all crosswords with optional filters and pagination
 * Uses cache when available and falls back to network
 */
export const getCrosswords = async (
  filters: CrosswordFilters = {},
  pagination: PaginationOptions = {}
): Promise<{ crosswords: CrosswordListItem[], pagination?: PaginationData }> => {
  try {
    // Build query string
    const queryParams = new URLSearchParams();

    // Add filters
    if (filters.category_id && filters.category_id !== '') {
      console.log('API: Adding category_id filter:', filters.category_id);
      queryParams.append('category_id', filters.category_id);
    } else {
      console.log('API: No category_id filter added, value is:', filters.category_id);
    }
    if (filters.difficulty) queryParams.append('difficulty', filters.difficulty);
    if (filters.is_public !== undefined) queryParams.append('is_public', filters.is_public ? '1' : '0');
    if (filters.user_id) queryParams.append('user_id', filters.user_id);
    if (filters.search) queryParams.append('search', filters.search);

    // Add pagination
    if (pagination.page) queryParams.append('page', pagination.page.toString());
    if (pagination.limit) queryParams.append('limit', pagination.limit.toString());
    if (pagination.sort) queryParams.append('sort', pagination.sort);

    // Create a cache key based on filters and pagination
    const cacheKey = `crosswords_${queryParams.toString() || 'all'}`;

    // Check if we're online
    const online = isOnline();

    // Try to get from cache first if cache is available
    if (isCacheAvailable()) {
      const cachedData = getCachedPuzzleList(cacheKey);

      // If we have cached data and we're offline, or we're just using cache-first strategy
      if (cachedData) {
        console.log('Using cached crossword list data for:', cacheKey);

        // If we're offline, return cached data immediately
        if (!online) {
          return {
            crosswords: cachedData as unknown as CrosswordListItem[],
            pagination: {
              page: pagination.page || 1,
              limit: pagination.limit || cachedData.length,
              total: cachedData.length,
              totalPages: Math.ceil(cachedData.length / (pagination.limit || 10))
            }
          };
        }

        // If we're online, return cached data but also fetch fresh data in the background
        // This is the "stale-while-revalidate" pattern
        setTimeout(() => {
          fetchFreshCrosswords();
        }, 0);

        return {
          crosswords: cachedData as unknown as CrosswordListItem[],
          pagination: {
            page: pagination.page || 1,
            limit: pagination.limit || cachedData.length,
            total: cachedData.length,
            totalPages: Math.ceil(cachedData.length / (pagination.limit || 10))
          }
        };
      }
    }

    // If we're offline and don't have cached data, return empty array
    if (!online) {
      console.log('Offline and no cached data available');
      return {
        crosswords: [],
        pagination: {
          page: pagination.page || 1,
          limit: pagination.limit || 10,
          total: 0,
          totalPages: 0
        }
      };
    }

    // If we're online or cache is not available, fetch from network
    return await fetchFreshCrosswords();

    // Helper function to fetch fresh data from network
    async function fetchFreshCrosswords() {
      // Make request
      const response = await fetch(`${API_BASE_URL}/api/crosswords?${queryParams.toString()}`, {
        headers: getHeaders()
      });
      const data: ApiResponse<CrosswordListItem[]> = await response.json();

      if (data.status === 'error' || !data.data) {
        throw new Error(data.message || 'Failed to fetch crosswords');
      }

      // Cache the result if cache is available
      if (isCacheAvailable() && data.data) {
        cachePuzzleList(cacheKey, data.data as unknown as CrosswordData[]);
      }

      return {
        crosswords: data.data,
        pagination: data.pagination
      };
    }
  } catch (error) {
    console.error('Error fetching crosswords:', error);

    // If we're offline and there was an error, try to return cached data
    if (!isOnline() && isCacheAvailable()) {
      // Rebuild query params for cache key
      const queryParams = new URLSearchParams();
      if (filters.category_id && filters.category_id !== '') queryParams.append('category_id', filters.category_id);
      if (filters.difficulty) queryParams.append('difficulty', filters.difficulty);
      if (filters.is_public !== undefined) queryParams.append('is_public', filters.is_public ? '1' : '0');
      if (filters.user_id) queryParams.append('user_id', filters.user_id);
      if (filters.search) queryParams.append('search', filters.search);
      if (pagination.page) queryParams.append('page', pagination.page.toString());
      if (pagination.limit) queryParams.append('limit', pagination.limit.toString());
      if (pagination.sort) queryParams.append('sort', pagination.sort);

      const cacheKey = `crosswords_${queryParams.toString() || 'all'}`;
      const cachedData = getCachedPuzzleList(cacheKey);

      if (cachedData) {
        console.log('Offline mode: Using cached crossword list data');
        return {
          crosswords: cachedData as unknown as CrosswordListItem[],
          pagination: {
            page: pagination.page || 1,
            limit: pagination.limit || cachedData.length,
            total: cachedData.length,
            totalPages: Math.ceil(cachedData.length / (pagination.limit || 10))
          }
        };
      }
    }

    throw error;
  }
};

export const getFeaturedCrosswords = async (): Promise<CrosswordListItem[]> => {
  const apiResponse = await fetchWithErrorHandling<CrosswordListItem[]>(
    `${API_BASE_URL}/api/featured`,
    {
      // credentials: 'include',
      headers: getHeaders()
    },
    true // Show error toast
  );

  return handleApiResponse(apiResponse, 'Gagal memuat teka-teki silang unggulan');
};

// --- User Authentication API Functions ---

// Types for auth (can be moved to a shared types file if used elsewhere, e.g. AuthContext)
export interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  avatar_url?: string;
  bio?: string;
  role: string; // Added role
  auth_provider?: string; // Added auth_provider
  // Add other fields returned by your backend for a logged-in user
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface GoogleLoginData {
  token_id: string;
  email: string;
  name: string;
  picture?: string;
}

export interface RegisterUserData {
  username: string;
  email: string;
  password: string;
  displayName?: string;
}

/**
 * Register a new user
 */
export const registerUser = async (userData: RegisterUserData): Promise<{ userId: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/register`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(userData),
      credentials: 'include', // Include cookies in the request
    });
    const data: ApiResponse<{ userId: string }> = await response.json();

    if (data.status === 'error' || !data.data?.userId) {
      throw new Error(data.message || 'User registration failed');
    }

    return data.data;
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

/**
 * Log in a user
 */
export const loginUser = async (credentials: LoginCredentials): Promise<User> => {
  try {
    console.log('Attempting to log in user:', credentials.email);
    console.log('API URL:', API_BASE_URL);

    // Log cookies before login attempt
    console.log('Cookies before login:', document.cookie);

    // Attempt the login
    console.log('Sending login request to API');
    const response = await fetch(`${API_BASE_URL}/api/users/login`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(credentials),
      credentials: 'include', // Include cookies in the request
    });

    // Log response headers for debugging
    console.log('Login response headers:', {
      'content-type': response.headers.get('content-type'),
      'set-cookie': response.headers.get('set-cookie'), // Note: This will likely be null due to browser security
      'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
      'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
    });

    // Check for non-200 responses
    if (!response.ok) {
      console.error(`Login failed with status: ${response.status}`);
      const errorText = await response.text();
      console.error('Error response:', errorText);

      try {
        // Try to parse as JSON
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || `Login failed with status ${response.status}`);
      } catch (parseError) {
        // If parsing fails, use the raw text
        throw new Error(`Login failed: ${errorText || response.statusText}`);
      }
    }

    const data: ApiResponse<{ user: User }> = await response.json();
    console.log('Login response:', data);

    if (data.status === 'error' || !data.data?.user) {
      throw new Error(data.message || 'Login failed');
    }

    // Log cookies after login to see if session cookie was set
    console.log('Cookies after login:', document.cookie);

    console.log('Login successful for user:', data.data.user.username);

    // Verify the session was created by making a test request
    try {
      console.log('Verifying session was created properly...');
      const verifyUser = await fetchCurrentUser();
      if (verifyUser) {
        console.log('Session verification successful');
      } else {
        console.warn('Session verification failed - user not found after login');
      }
    } catch (verifyError) {
      console.warn('Error verifying session after login:', verifyError);
    }

    return data.data.user;
  } catch (error) {
    console.error('Error logging in user:', error);
    throw error;
  }
};

/**
 * Log in a user with Google
 */
export const loginWithGoogle = async (googleData: GoogleLoginData): Promise<User> => {
  try {
    console.log('Google login data:', {
      email: googleData.email,
      name: googleData.name,
      token_length: googleData.token_id.length,
      has_picture: !!googleData.picture
    });

    // Google login doesn't need CSRF token as it's coming from an external source
    const response = await fetch(`${API_BASE_URL}/api/users/google-login`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(googleData),
      credentials: 'include', // Include cookies in the request
    });

    // Check for non-200 responses
    if (!response.ok) {
      console.error(`Google login failed with status: ${response.status}`);
      const errorText = await response.text();
      console.error('Error response:', errorText);

      try {
        // Try to parse as JSON
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || `Google login failed with status ${response.status}`);
      } catch (parseError) {
        // If parsing fails, use the raw text
        throw new Error(`Google login failed: ${errorText || response.statusText}`);
      }
    }

    const data: ApiResponse<{ user: User }> = await response.json();
    console.log('Google login response:', data);

    if (data.status === 'error' || !data.data?.user) {
      throw new Error(data.message || 'Google login failed');
    }

    return data.data.user;
  } catch (error) {
    console.error('Error logging in with Google:', error);
    throw error;
  }
};

/**
 * Log out the current user
 */
export const logoutUser = async (): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/logout`, {
      method: 'POST', // POST is often preferred for logout to prevent CSRF via GET
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });
    const data: ApiResponse<null> = await response.json();

    if (data.status === 'error') {
      throw new Error(data.message || 'Logout failed');
    }

    // No specific data to return on successful logout usually
  } catch (error) {
    console.error('Error logging out user:', error);
    throw error;
  }
};

/**
 * Fetch the current logged-in user's details
 */
export const fetchCurrentUser = async (): Promise<User | null> => {
  try {
    console.log('Fetching current user session...');

    // Add cache-busting parameter to prevent browser caching
    const cacheBuster = `_cb=${Date.now()}`;
    const url = `${API_BASE_URL}/api/users/me?${cacheBuster}`;

    // Log cookies for debugging
    console.log('Cookies before request:', document.cookie);

    console.log(`Making request to ${url}`);
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    // Log response headers for debugging
    console.log('Response headers:', {
      'content-type': response.headers.get('content-type'),
      'set-cookie': response.headers.get('set-cookie'), // Note: This will likely be null due to browser security
      'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
      'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
    });

    // Handle unauthorized responses
    if (response.status === 401) { // Unauthorized
      console.log('User is not authenticated (401 response)');
      return null;
    }

    // Handle other non-200 responses
    if (!response.ok) {
      console.error(`Failed to fetch current user: ${response.status} ${response.statusText}`);
      return null;
    }

    const data: ApiResponse<{ user: User }> = await response.json();
    console.log('Fetch current user response:', data);

    if (data.status === 'error' || !data.data?.user) {
      console.warn('API returned success status but no user data');
      return null;
    }

    // Log cookies after request to see if any were set
    console.log('Cookies after request:', document.cookie);

    console.log('User session retrieved successfully:', data.data.user.username);
    return data.data.user;
  } catch (error) {
    console.error('Error fetching current user:', error);
    // For fetchCurrentUser, it might be better to return null on error
    // rather than throwing, so the app can handle "no user" gracefully.
    return null;
  }
};

/**
 * Get a specific crossword by ID
 * Uses cache when available and falls back to network
 */
export const getCrosswordById = async (id: string): Promise<CrosswordData> => {
  try {
    // Check if we're online
    const online = isOnline();

    // Try to get from cache first if cache is available
    if (isCacheAvailable()) {
      const cachedData = getCachedPuzzle(id);

      // If we have cached data and we're offline, or we're just using cache-first strategy
      if (cachedData) {
        console.log(`Using cached crossword data for ID: ${id}`);

        // If we're offline, return cached data immediately
        if (!online) {
          return cachedData;
        }

        // If we're online, return cached data but also fetch fresh data in the background
        // This is the "stale-while-revalidate" pattern
        setTimeout(() => {
          fetchFreshCrossword().catch(err =>
            console.log(`Background refresh of crossword ${id} failed:`, err)
          );
        }, 0);

        return cachedData;
      }
    }

    // If we're offline and don't have cached data, throw an error
    if (!online) {
      throw new Error('Anda sedang offline dan data teka-teki silang ini belum tersimpan di cache.');
    }

    // If we're online or cache is not available, fetch from network
    return await fetchFreshCrossword();

    // Helper function to fetch fresh data from network
    async function fetchFreshCrossword(): Promise<CrosswordData> {
      const response = await fetch(`${API_BASE_URL}/api/crosswords/${id}`, {
        headers: getHeaders()
      });
      const data: ApiResponse<CrosswordData> = await response.json();

      if (data.status === 'error' || !data.data) {
        throw new Error(data.message || 'Failed to fetch crossword');
      }

      // Cache the result if cache is available
      if (isCacheAvailable() && data.data) {
        cachePuzzle(id, data.data);
      }

      return data.data;
    }
  } catch (error) {
    console.error(`Error fetching crossword ${id}:`, error);

    // If we're offline and there was an error, try to return cached data
    if (!isOnline() && isCacheAvailable()) {
      const cachedData = getCachedPuzzle(id);

      if (cachedData) {
        console.log(`Offline mode: Using cached crossword data for ID: ${id}`);
        return cachedData;
      }
    }

    throw error;
  }
};

/**
 * Create a new crossword
 */
export const createCrossword = async (crossword: {
  title: string;
  description?: string;
  difficulty?: 'mudah' | 'sedang' | 'sulit';
  category_id?: string;
  state: CrosswordState;
}): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/crosswords`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(crossword),
      credentials: 'include', // Include cookies in the request
    });

    const data = await response.json();

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to create crossword');
    }

    // Handle both response formats:
    // 1. { status: 'success', data: { id: 'xxx' } }
    // 2. { status: 'success', id: 'xxx' }
    const id = data.data?.id || data.id;

    if (!id) {
      throw new Error('No ID returned from server');
    }

    return id;
  } catch (error) {
    console.error('Error creating crossword:', error);
    throw error;
  }
};

/**
 * Update an existing crossword
 */
export const updateCrossword = async (
  id: string,
  crossword: {
    title?: string;
    description?: string;
    difficulty?: 'mudah' | 'sedang' | 'sulit';
    category_id?: string;
    state?: CrosswordState;
  }
): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/crosswords/${id}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(crossword),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json(); // Expect no specific data field

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to update crossword');
    }
  } catch (error) {
    console.error(`Error updating crossword ${id}:`, error);
    throw error;
  }
};

/**
 * Record a play for a crossword
 */
export const recordCrosswordPlay = async (id: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/crosswords/${id}/play`, {
      method: 'POST',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json(); // Expect no specific data field

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to record play');
    }
  } catch (error) {
    console.error(`Error recording play for crossword ${id}:`, error);
    // Silently fail - this is not critical
  }
};

/**
 * Rate a crossword
 */
export const rateCrossword = async (id: string, rating: number): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/crosswords/${id}/rate`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ rating }),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json(); // Expect no specific data field

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to rate crossword');
    }
  } catch (error) {
    console.error(`Error rating crossword ${id}:`, error);
    throw error;
  }
};

/**
 * Get all categories
 */
export const getCategories = async (): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/categories`, {
      headers: getHeaders(),
    });
    const data: ApiResponse<Category[]> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch categories');
    }

    return data.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

/**
 * Get a specific category by ID
 */
export const getCategoryById = async (id: string): Promise<Category> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/categories/${id}`, {
      headers: getHeaders()
    });
    const data: ApiResponse<Category> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch category');
    }

    return data.data;
  } catch (error) {
    console.error(`Error fetching category ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new category
 */
export const createCategory = async (category: {
  name: string;
  description?: string;
  image_url?: string;
}): Promise<Category> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/categories`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(category),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<Category> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to create category');
    }

    return data.data;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
};

/**
 * Update an existing category
 */
export const updateCategory = async (
  id: string,
  category: {
    name?: string;
    description?: string;
    image_url?: string;
  }
): Promise<Category> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/categories/${id}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(category),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<Category> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to update category');
    }

    return data.data;
  } catch (error) {
    console.error(`Error updating category ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a category
 */
export const deleteCategory = async (id: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/categories/${id}`, {
      method: 'DELETE',
      headers: getHeaders(),
    });

    const data: ApiResponse<null> = await response.json(); // Expect no specific data field

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to delete category');
    }
  } catch (error) {
    console.error(`Error deleting category ${id}:`, error);
    throw error;
  }
};

// --- User Progress API Functions ---

// Interface for user progress data
export interface UserProgress {
  id: string;
  user_id: string;
  crossword_id: string;
  progress_data: {
    userAnswers: string[][];
    revealedCells?: [number, number][];
    progress: number;
  };
  is_completed: boolean;
  time_spent?: number;
  created_at: string;
  updated_at: string;
  crossword_title?: string; // Included when getting all progress
  difficulty?: string; // Included when getting all progress
  slug?: string; // Crossword slug for SEO-friendly URLs
  category_id?: string; // Category ID for SEO-friendly URLs
}

/**
 * Get user progress for a specific crossword
 */
export const getUserProgress = async (crosswordId: string): Promise<UserProgress | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/progress/${crosswordId}`, {
      method: 'GET',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    // If 404, return null (no progress found)
    if (response.status === 404) {
      return null;
    }

    const data: ApiResponse<UserProgress> = await response.json();
    console.log('User progress API response:', data);

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to fetch user progress');
    }

    // Log the progress data for debugging
    if (data.data) {
      console.log('User progress data:', data.data);
      console.log('User answers:', data.data.progress_data?.userAnswers);
    }

    return data.data || null;
  } catch (error) {
    console.error(`Error fetching user progress for crossword ${crosswordId}:`, error);
    // Return null instead of throwing for better UX
    return null;
  }
};

/**
 * Get all progress entries for the current user
 */
export const getAllUserProgress = async (): Promise<UserProgress[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/progress`, {
      method: 'GET',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<UserProgress[]> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch user progress');
    }

    return data.data;
  } catch (error) {
    console.error('Error fetching all user progress:', error);
    throw error;
  }
};

/**
 * Save user progress for a crossword
 */
export const saveUserProgress = async (
  crosswordId: string,
  progressData: {
    userAnswers: string[][];
    revealedCells?: [number, number][];
    progress: number;
  },
  isCompleted: boolean = false,
  timeSpent?: number
): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/progress/${crosswordId}`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        progress_data: progressData,
        is_completed: isCompleted,
        time_spent: timeSpent,
      }),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<{ id: string }> = await response.json();
    console.log('Save progress API response:', data);

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to save user progress');
    }

    // Return the ID if available, or a placeholder if not
    return data.data?.id || 'progress-saved';
  } catch (error) {
    console.error(`Error saving user progress for crossword ${crosswordId}:`, error);
    throw error;
  }
};

/**
 * Delete user progress for a crossword
 */
export const deleteUserProgress = async (crosswordId: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/progress/${crosswordId}`, {
      method: 'DELETE',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json();

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to delete user progress');
    }
  } catch (error) {
    console.error(`Error deleting user progress for crossword ${crosswordId}:`, error);
    throw error;
  }
};

/**
 * Interface for profile update data
 */
export interface ProfileUpdateData {
  displayName?: string;
  avatarUrl?: string;
  bio?: string;
}

/**
 * Update user profile
 */
export const updateUserProfile = async (profileData: ProfileUpdateData): Promise<User> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/profile`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(profileData),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<{ user: User }> = await response.json();

    if (data.status === 'error' || !data.data?.user) {
      throw new Error(data.message || 'Failed to update profile');
    }

    return data.data.user;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// --- Admin User Management API Functions ---

/**
 * Get all users (admin only)
 */
export const getAllUsers = async (page = 1, limit = 10): Promise<{ users: User[], pagination: PaginationData }> => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });

    const response = await fetch(`${API_BASE_URL}/api/users/admin?${queryParams.toString()}`, {
      method: 'GET',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<User[]> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch users');
    }

    return {
      users: data.data,
      pagination: data.pagination || {
        page,
        limit,
        total: data.data.length,
        totalPages: Math.ceil(data.data.length / limit)
      }
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * Get a user by ID (admin only)
 */
export const getUserById = async (id: string): Promise<User> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/admin/${id}`, {
      method: 'GET',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<User> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch user');
    }

    return data.data;
  } catch (error) {
    console.error(`Error fetching user ${id}:`, error);
    throw error;
  }
};

/**
 * Update a user (admin only)
 */
export const updateUser = async (id: string, userData: {
  displayName?: string;
  avatarUrl?: string;
  bio?: string;
  role?: 'user' | 'admin';
}): Promise<User> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/admin/${id}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(userData),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<User> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to update user');
    }

    return data.data;
  } catch (error) {
    console.error(`Error updating user ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a user (admin only)
 */
export const deleteUser = async (id: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/users/admin/${id}`, {
      method: 'DELETE',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json();

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to delete user');
    }
  } catch (error) {
    console.error(`Error deleting user ${id}:`, error);
    throw error;
  }
};

// --- Blog Management API Functions ---

// Interface for blog post data
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  author_id: string;
  author_name?: string;
  author_username?: string;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
}

/**
 * Get all blog posts with optional filters
 */
export const getBlogPosts = async (
  page = 1,
  limit = 10,
  status?: 'draft' | 'published'
): Promise<{ posts: BlogPost[], pagination: PaginationData }> => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });

    if (status) {
      queryParams.append('status', status);
    }

    const response = await fetch(`${API_BASE_URL}/api/blogs?${queryParams.toString()}`, {
      headers: getHeaders()
    });
    const data: ApiResponse<BlogPost[]> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch blog posts');
    }

    return {
      posts: data.data,
      pagination: data.pagination || {
        page,
        limit,
        total: data.data.length,
        totalPages: Math.ceil(data.data.length / limit)
      }
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    throw error;
  }
};

/**
 * Get a blog post by ID
 */
export const getBlogPostById = async (id: string): Promise<BlogPost> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/blogs/${id}`, {
      headers: getHeaders()
    });
    const data: ApiResponse<BlogPost> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch blog post');
    }

    return data.data;
  } catch (error) {
    console.error(`Error fetching blog post ${id}:`, error);
    throw error;
  }
};

/**
 * Get a blog post by slug
 */
export const getBlogPostBySlug = async (slug: string): Promise<BlogPost> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/blogs?slug=${encodeURIComponent(slug)}`, {
      headers: getHeaders()
    });
    const data: ApiResponse<BlogPost> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch blog post');
    }

    return data.data;
  } catch (error) {
    console.error(`Error fetching blog post with slug ${slug}:`, error);
    throw error;
  }
};

/**
 * Create a new blog post (admin only)
 */
export const createBlogPost = async (blogPost: {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  status?: 'draft' | 'published';
}): Promise<BlogPost> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/blogs`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(blogPost),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<BlogPost> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to create blog post');
    }

    return data.data;
  } catch (error) {
    console.error('Error creating blog post:', error);
    throw error;
  }
};

/**
 * Update a blog post (admin only)
 */
export const updateBlogPost = async (
  id: string,
  blogPost: {
    title?: string;
    slug?: string;
    content?: string;
    excerpt?: string;
    featured_image?: string;
    status?: 'draft' | 'published';
  }
): Promise<BlogPost> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/blogs/${id}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(blogPost),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<BlogPost> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to update blog post');
    }

    return data.data;
  } catch (error) {
    console.error(`Error updating blog post ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a blog post (admin only)
 */
export const deleteBlogPost = async (id: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/blogs/${id}`, {
      method: 'DELETE',
      headers: getHeaders(),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<null> = await response.json();

    if (data.status === 'error') {
      throw new Error(data.message || 'Failed to delete blog post');
    }
  } catch (error) {
    console.error(`Error deleting blog post ${id}:`, error);
    throw error;
  }
};

// --- Settings Management API Functions ---

/**
 * Get all settings
 */
export const getSettings = async (): Promise<Record<string, string>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/settings`, {
      headers: getHeaders()
    });
    const data: ApiResponse<Record<string, string>> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch settings');
    }

    return data.data;
  } catch (error) {
    console.error('Error fetching settings:', error);
    throw error;
  }
};

/**
 * Get a specific setting
 */
export const getSetting = async (key: string): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/settings/${key}`, {
      headers: getHeaders()
    });
    const data: ApiResponse<{ key: string, value: string }> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to fetch setting');
    }

    return data.data.value;
  } catch (error) {
    console.error(`Error fetching setting ${key}:`, error);
    throw error;
  }
};

/**
 * Update settings (admin only)
 */
export const updateSettings = async (settings: Record<string, string>): Promise<Record<string, string>> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/settings`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(settings),
      credentials: 'include', // Include cookies in the request
    });

    const data: ApiResponse<Record<string, string>> = await response.json();

    if (data.status === 'error' || !data.data) {
      throw new Error(data.message || 'Failed to update settings');
    }

    return data.data;
  } catch (error) {
    console.error('Error updating settings:', error);
    throw error;
  }
};
