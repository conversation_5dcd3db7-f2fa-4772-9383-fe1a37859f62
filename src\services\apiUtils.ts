import { toastUtils } from '../components/ui/ToastProvider';
import {
  AppError,
  ErrorType,
  createAppError,
  handleFetchError,
  handleApiResponseError,
  showErrorToast
} from '../utils/errorHandler';

// API base URL
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:1111';

/**
 * Get standard headers for API requests
 *
 * @param contentType Optional content type (defaults to application/json)
 * @returns Headers object for fetch requests
 */
export const getHeaders = (contentType = 'application/json'): HeadersInit => {
  return {
    'Content-Type': contentType,
    'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5'
  };
};

// Interface for API response
export interface ApiResponse<T> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  pagination?: PaginationData;
}

// Interface for pagination data
export interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Standardized fetch function with error handling
 *
 * @param url The URL to fetch
 * @param options Fetch options
 * @param showErrorToast Whether to show an error toast on failure
 * @param timeoutMs Timeout in milliseconds (default: 30000)
 * @returns The response data
 */
export async function fetchWithErrorHandling<T>(
  url: string,
  options: RequestInit = {},
  showErrorToast = true,
  timeoutMs = 30000
): Promise<ApiResponse<T>> {
  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    // Add signal to options
    const fetchOptions = {
      ...options,
      signal: controller.signal
    };

    const response = await fetch(url, fetchOptions);

    // Clear timeout
    clearTimeout(timeoutId);

    // Handle HTTP error responses
    if (!response.ok) {
      const appError = handleApiResponseError(response);

      if (showErrorToast) {
        showErrorToast(appError);
      }

      // Special handling for 404 errors
      if (appError.statusCode === 404) {
        return {
          status: 'error',
          message: 'Data yang Anda cari tidak ditemukan.'
        };
      }

      // Special handling for authentication errors
      if (appError.statusCode === 401) {
        // Redirect to login page if authentication error
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }

      return {
        status: 'error',
        message: appError.message
      };
    }

    // Handle non-JSON responses
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      // Return a success response for non-JSON responses
      return {
        status: 'success',
        message: 'Non-JSON response received'
      };
    }

    const data: ApiResponse<T> = await response.json();

    // Handle API error responses
    if (data.status === 'error') {
      const errorType = data.message?.includes('tidak ditemukan')
        ? ErrorType.NOT_FOUND
        : ErrorType.SERVER;

      const appError = createAppError(
        errorType,
        data.message || 'Terjadi kesalahan pada server',
        response.status
      );

      if (showErrorToast) {
        showErrorToast(appError);
      }

      return data;
    }

    return data;
  } catch (error) {
    // Clear timeout
    clearTimeout(timeoutId);

    // Handle fetch errors (network, timeout, etc.)
    const appError = handleFetchError(error);

    if (showErrorToast) {
      showErrorToast(appError);
    }

    console.error('API error:', appError);

    return {
      status: 'error',
      message: appError.message
    };
  }
}

/**
 * Standardized function to handle API responses
 *
 * @param apiResponse The API response to handle
 * @param errorMessage Optional custom error message
 * @returns The data from the API response
 */
export function handleApiResponse<T>(
  apiResponse: ApiResponse<T>,
  errorMessage = 'Terjadi kesalahan pada server'
): T {
  if (apiResponse.status === 'error' || !apiResponse.data) {
    // Determine error type based on message content
    let errorType = ErrorType.SERVER;

    if (apiResponse.message) {
      if (apiResponse.message.includes('tidak ditemukan')) {
        errorType = ErrorType.NOT_FOUND;
      } else if (apiResponse.message.includes('tidak memiliki izin')) {
        errorType = ErrorType.AUTHORIZATION;
      } else if (apiResponse.message.includes('login') || apiResponse.message.includes('sesi')) {
        errorType = ErrorType.AUTHENTICATION;
      } else if (apiResponse.message.includes('tidak valid')) {
        errorType = ErrorType.VALIDATION;
      }
    }

    // Create and throw a standardized error
    const appError = createAppError(
      errorType,
      apiResponse.message || errorMessage
    );

    throw appError;
  }

  return apiResponse.data;
}

/**
 * Utility function to create a query string from parameters
 *
 * @param params Object containing query parameters
 * @returns Query string (including ? prefix if not empty)
 */
export function createQueryString(params: Record<string, any>): string {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, value.toString());
    }
  });

  const queryString = queryParams.toString();
  return queryString ? `?${queryString}` : '';
}
