/**
 * CSRF Protection
 *
 * This application uses SameSite cookies for CSRF protection instead of tokens.
 *
 * SameSite cookies are a more modern and simpler approach to CSRF protection.
 * When cookies are set with SameSite=Strict or SameSite=Lax, browsers will not
 * send them in cross-site requests, which prevents CSRF attacks.
 *
 * This file exists only for backward compatibility with code that might still
 * reference it, but the functions are now no-ops.
 */

// These functions are kept for backward compatibility but don't do anything
export const setCsrfToken = (_token: string): void => {};

export const getCsrfToken = (): string | null => {
  return null;
};

export const clearCsrfToken = (): void => {};

export const hasCsrfToken = (): boolean => {
  return false;
};
