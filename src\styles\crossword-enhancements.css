/* Enhanced Crossword Styles */
/* Additional styling for crossword components with paper/ink theme */

/* Crossword grid container */
.crossword-grid-container {
  background-color: var(--paper-primary);
  border: 3px solid var(--ink-primary);
  box-shadow: 
    0 4px 8px rgba(0,0,0,0.15),
    0 2px 4px rgba(0,0,0,0.1),
    inset 0 0 0 1px rgba(255,255,255,0.1);
  padding: 8px;
}

/* Individual crossword cell enhancements */
.crossword-cell {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
  min-width: 32px;
  min-height: 32px;
  font-size: 14px;
  text-transform: uppercase;
  user-select: none;
  cursor: pointer;
  transition: all 0.15s ease;
}

.crossword-cell:hover:not(.black) {
  background-color: rgba(0,0,0,0.08);
}

.crossword-cell.correct {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
}

.crossword-cell.incorrect {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.crossword-cell.revealed {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Cell number styling */
.crossword-cell-number {
  position: absolute;
  top: 1px;
  left: 2px;
  font-size: 10px;
  font-weight: 700;
  color: var(--ink-primary);
  line-height: 1;
  pointer-events: none;
  font-family: 'Courier New', monospace;
}

/* Cell input styling */
.crossword-cell-input {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: inherit;
  color: var(--ink-primary);
  outline: none;
  caret-color: transparent;
  text-transform: uppercase;
}

/* Clue list styling */
.clue-list {
  background-color: var(--paper-primary);
  border: 2px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  max-height: 400px;
  overflow-y: auto;
}

.clue-list-header {
  background-color: var(--ink-primary);
  color: var(--paper-primary);
  padding: 12px 16px;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
  border-bottom: 2px solid var(--ink-primary);
}

.clue-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  cursor: pointer;
  transition: all 0.15s ease;
  background-color: var(--paper-primary);
}

.clue-item:hover {
  background-color: rgba(0,0,0,0.03);
}

.clue-item.active {
  background-color: rgba(0,0,0,0.08);
  border-left: 4px solid var(--ink-primary);
  padding-left: 12px;
}

.clue-item.completed {
  background-color: rgba(34, 197, 94, 0.05);
  color: #166534;
}

.clue-number {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  color: var(--ink-primary);
  font-size: 14px;
  min-width: 24px;
  flex-shrink: 0;
}

.clue-text {
  font-family: Georgia, 'Times New Roman', serif;
  color: var(--ink-primary);
  line-height: 1.4;
  flex: 1;
}

.clue-completed-indicator {
  color: #22c55e;
  font-weight: 700;
  font-size: 16px;
}

/* Crossword stats styling */
.crossword-stats {
  background-color: var(--paper-secondary);
  border: 2px solid rgba(0,0,0,0.1);
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-family: 'Courier New', monospace;
  font-size: 24px;
  font-weight: 700;
  color: var(--ink-primary);
  line-height: 1;
}

.stat-label {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 12px;
  color: var(--ink-muted);
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Progress bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: rgba(0,0,0,0.1);
  border: 1px solid rgba(0,0,0,0.2);
  margin-top: 16px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--ink-primary);
  transition: width 0.3s ease;
  background-image: linear-gradient(
    45deg,
    rgba(255,255,255,0.1) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255,255,255,0.1) 50%,
    rgba(255,255,255,0.1) 75%,
    transparent 75%
  );
  background-size: 8px 8px;
  animation: progress-stripe 1s linear infinite;
}

@keyframes progress-stripe {
  0% { background-position: 0 0; }
  100% { background-position: 8px 0; }
}

/* Crossword controls */
.crossword-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.control-button {
  background-color: var(--paper-primary);
  border: 2px solid var(--ink-primary);
  color: var(--ink-primary);
  padding: 8px 16px;
  font-family: Georgia, 'Times New Roman', serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.control-button:hover {
  background-color: var(--ink-primary);
  color: var(--paper-primary);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-button:disabled:hover {
  background-color: var(--paper-primary);
  color: var(--ink-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .crossword-cell {
    min-width: 28px;
    min-height: 28px;
    font-size: 12px;
  }
  
  .crossword-cell-number {
    font-size: 8px;
    top: 0px;
    left: 1px;
  }
  
  .clue-item {
    padding: 10px 12px;
    gap: 8px;
  }
  
  .clue-number {
    font-size: 12px;
    min-width: 20px;
  }
  
  .clue-text {
    font-size: 14px;
  }
  
  .crossword-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .crossword-cell {
    min-width: 24px;
    min-height: 24px;
    font-size: 11px;
  }
  
  .crossword-grid-container {
    padding: 4px;
  }
  
  .crossword-stats {
    grid-template-columns: 1fr;
  }
  
  .control-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* Print styles */
@media print {
  .crossword-grid-container,
  .clue-list,
  .crossword-stats {
    background: white !important;
    border-color: black !important;
    box-shadow: none !important;
  }
  
  .crossword-cell {
    background: white !important;
    border-color: black !important;
  }
  
  .crossword-cell.black {
    background: black !important;
  }
  
  .crossword-controls {
    display: none !important;
  }
}
