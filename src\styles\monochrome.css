/* Monochrome Theme Styles */
/* Additional CSS for monochrome newspaper-style components */

/* Base styles for monochrome theme */
.monochrome-theme {
  --paper-50: #fafafa;
  --paper-100: #f5f5f5;
  --paper-200: #e5e5e5;
  --paper-300: #d4d4d4;
  --paper-400: #a3a3a3;
  --paper-500: #737373;
  --paper-600: #525252;
  --paper-700: #404040;
  --paper-800: #262626;
  --paper-900: #171717;

  --ink-50: #f8f8f8;
  --ink-100: #f0f0f0;
  --ink-200: #e4e4e4;
  --ink-300: #d1d1d1;
  --ink-400: #b4b4b4;
  --ink-500: #9a9a9a;
  --ink-600: #818181;
  --ink-700: #6a6a6a;
  --ink-800: #5a5a5a;
  --ink-900: #4a4a4a;
}

/* Typography enhancements */
.font-serif {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

.font-mono {
  font-feature-settings: "tnum" 1, "zero" 1;
  font-variant-numeric: tabular-nums;
}

/* Newspaper-style typography */
.newspaper-heading {
  font-family: Georgia, 'Times New Roman', serif;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.1;
  color: var(--ink-900);
}

.newspaper-body {
  font-family: Georgia, 'Times New Roman', serif;
  line-height: 1.6;
  color: var(--ink-900);
}

.newspaper-caption {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--ink-700);
}

/* Paper texture backgrounds */
.bg-paper-texture {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cg fill-opacity="0.03"%3E%3Cpolygon fill="%23000" points="50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40"/%3E%3C/g%3E%3C/svg%3E');
}

.bg-paper-lines {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="20" viewBox="0 0 100 20"%3E%3Cg fill="none" stroke="%23000" stroke-width="0.5" stroke-opacity="0.05"%3E%3Cline x1="0" y1="20" x2="100" y2="20"/%3E%3C/g%3E%3C/svg%3E');
}

.bg-paper-dots {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"%3E%3Cg fill="%23000" fill-opacity="0.02"%3E%3Ccircle cx="10" cy="10" r="1"/%3E%3C/g%3E%3C/svg%3E');
}

/* Enhanced shadows for paper effect */
.shadow-paper {
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

.shadow-paper-lg {
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 2px 4px rgba(0, 0, 0, 0.08);
}

.shadow-paper-xl {
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Crossword-specific styles */
.crossword-cell {
  position: relative;
  aspect-ratio: 1;
  border: 1px solid var(--ink-900);
  background: var(--paper-50);
  transition: all 0.15s ease;
}

.crossword-cell:hover:not(.blocked) {
  background: var(--paper-100);
}

.crossword-cell.selected {
  background: var(--ink-200);
  box-shadow: inset 0 0 0 2px var(--ink-900);
}

.crossword-cell.highlighted {
  background: var(--ink-100);
}

.crossword-cell.blocked {
  background: var(--ink-900);
}

.crossword-cell.correct {
  background: #dcfce7;
  border-color: #16a34a;
}

.crossword-cell.incorrect {
  background: #fef2f2;
  border-color: #dc2626;
}

.crossword-cell input {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  background: transparent;
  border: none;
  outline: none;
  caret-color: transparent;
  font-size: 0.875rem;
  color: var(--ink-900);
}

@media (min-width: 768px) {
  .crossword-cell input {
    font-size: 1rem;
  }
}

.crossword-cell-number {
  position: absolute;
  top: 1px;
  left: 1px;
  font-size: 0.625rem;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  line-height: 1;
  color: var(--ink-900);
  pointer-events: none;
  padding: 1px;
}

/* Clue list styles */
.clue-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease;
  border-bottom: 1px solid var(--paper-200);
}

.clue-item:hover {
  background: var(--paper-100);
}

.clue-item.active {
  background: var(--ink-100);
  border-color: var(--ink-300);
}

.clue-item.completed {
  background: #f0fdf4;
  color: #166534;
}

.clue-number {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  color: var(--ink-900);
  font-size: 0.875rem;
  min-width: 2rem;
  flex-shrink: 0;
}

.clue-text {
  font-family: Georgia, 'Times New Roman', serif;
  color: var(--ink-800);
  line-height: 1.5;
  flex: 1;
}

/* Button enhancements */
.monochrome-button {
  font-family: Georgia, 'Times New Roman', serif;
  font-weight: 500;
  transition: all 0.2s ease;
  border-width: 1px;
  border-style: solid;
}

.monochrome-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ink-400), 0 0 0 4px rgba(154, 154, 154, 0.1);
}

.monochrome-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.monochrome-button:active:not(:disabled) {
  transform: translateY(1px);
}

/* Card enhancements */
.monochrome-card {
  transition: all 0.2s ease;
  border-width: 1px;
  border-style: solid;
}

.monochrome-card.interactive:hover {
  transform: translateY(-1px);
}

.monochrome-card.interactive:active {
  transform: translateY(0);
}

/* Input enhancements */
.monochrome-input {
  font-family: 'Courier New', monospace;
  transition: all 0.2s ease;
  border-width: 1px;
  border-style: solid;
}

.monochrome-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ink-400);
}

.monochrome-input::placeholder {
  font-family: Georgia, 'Times New Roman', serif;
  color: var(--ink-400);
}

/* Modal enhancements */
.monochrome-modal-backdrop {
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease;
}

.monochrome-modal {
  animation: slideIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Progress bar */
.progress-bar {
  background: var(--paper-300);
  height: 0.5rem;
  border: 1px solid var(--paper-400);
  overflow: hidden;
}

.progress-fill {
  background: var(--ink-900);
  height: 100%;
  transition: width 0.3s ease;
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Background color utilities */
.bg-paper-main {
  background-color: var(--paper-50);
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cg fill-opacity="0.02"%3E%3Cpolygon fill="%23000" points="50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40"/%3E%3C/g%3E%3C/svg%3E');
}

.bg-paper-section {
  background-color: var(--paper-100);
}

.bg-newsprint {
  background-color: var(--paper-50);
}

/* Text color utilities */
.text-ink-dark {
  color: var(--ink-900);
}

.text-ink-muted {
  color: var(--ink-600);
}

.text-ink-light {
  color: var(--ink-400);
}

.text-ink-primary {
  color: var(--ink-800);
}

.text-newsprint {
  color: var(--paper-50);
}

/* Border color utilities */
.border-ink-light {
  border-color: var(--ink-300);
}

.border-ink-primary {
  border-color: var(--ink-800);
}

/* Background color utilities for components */
.bg-ink-primary {
  background-color: var(--ink-800);
}

.bg-ink-secondary {
  background-color: var(--ink-600);
}

/* Primary color utilities */
.bg-primary-400 {
  background-color: #60a5fa;
}

.bg-primary-600 {
  background-color: #2563eb;
}

.bg-primary-800 {
  background-color: #1e40af;
}

.text-primary-600 {
  color: #2563eb;
}

/* Card component styles */
.card-paper {
  background-color: var(--paper-50);
  border: 1px solid var(--paper-200);
  border-radius: 0.5rem;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.card-paper:hover {
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Button styles */
.btn-outline {
  background-color: transparent;
  border: 1px solid var(--ink-300);
  color: var(--ink-800);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-family: Georgia, 'Times New Roman', serif;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: var(--paper-100);
  border-color: var(--ink-400);
}

/* Shadow utilities */
.shadow-paper-lg {
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Focus ring utilities */
.focus\:ring-ink-primary:focus {
  --tw-ring-color: var(--ink-800);
}

.focus\:border-ink-primary:focus {
  border-color: var(--ink-800);
}

/* Hover utilities */
.hover\:bg-paper-section:hover {
  background-color: var(--paper-100);
}

.hover\:text-ink-primary:hover {
  color: var(--ink-800);
}

.hover\:shadow-paper-lg:hover {
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Transition utilities */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

/* Print styles */
@media print {
  .monochrome-theme {
    background: white !important;
    color: black !important;
  }

  .shadow-paper,
  .shadow-paper-lg,
  .shadow-paper-xl {
    box-shadow: none !important;
  }

  .bg-paper-texture,
  .bg-paper-lines,
  .bg-paper-dots {
    background-image: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .monochrome-theme {
    --paper-50: #ffffff;
    --paper-100: #f0f0f0;
    --paper-200: #d0d0d0;
    --paper-300: #b0b0b0;
    --ink-700: #000000;
    --ink-800: #000000;
    --ink-900: #000000;
  }

  .crossword-cell {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .monochrome-button,
  .monochrome-card,
  .monochrome-input,
  .crossword-cell {
    transition: none;
  }

  .monochrome-modal-backdrop,
  .monochrome-modal {
    animation: none;
  }
}
