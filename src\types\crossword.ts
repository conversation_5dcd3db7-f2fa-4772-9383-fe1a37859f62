export type Direction = 'across' | 'down';

export interface Cell {
  char: string;
  wordIds: number[];
}

export interface WordPosition {
  direction: Direction;
  row: number;
  col: number;
  number: number;
  id?: number; // ID of the word in the words array
}

export type GameState = 'not-started' | 'playing' | 'paused' | 'completed';

export interface CrosswordState {
  gridSize: number;
  grid: Cell[][];
  words: string[];
  clues: {
    across: Record<number, string>;
    down: Record<number, string>;
  };
  wordPositions: WordPosition[];
  wordNumber: number;
  selectedWordId: number | null;
  selectedCell?: [number, number] | null; // Currently selected cell [row, col]
  selectedDirection?: Direction | null; // Currently selected direction
  mode: 'edit' | 'play';
  userAnswers?: string[][];
  hintsRemaining?: number;
  revealedCells?: [number, number][]; // Array of [row, col] for revealed cells
  progress?: number; // Percentage of correctly filled cells (0-100)
  isSurrendered?: boolean; // Whether the user has surrendered
  gameState?: GameState; // Current game state for play mode
  timeSpent?: number; // Time spent in seconds
  gameStartTime?: number; // Timestamp when game started
  pausedTime?: number; // Total time spent paused
}

export type PlacementScore = [number, Direction, number, number];

export type CrosswordAction =
  | { type: 'ADD_WORD'; word: string; direction: Direction; row: number; col: number; clue?: string }
  | { type: 'AUTO_PLACE_WORD'; word: string; clue?: string }
  | { type: 'REMOVE_WORD'; wordId: number }
  | { type: 'SELECT_WORD'; wordId: number | null }
  | { type: 'SELECT_CELL'; row: number; col: number }
  | { type: 'SET_GRID_SIZE'; size: number }
  | { type: 'OPTIMIZE_GRID' }
  | { type: 'RESET' }
  | { type: 'SET_MODE'; mode: 'edit' | 'play' }
  | { type: 'SET_STATE'; state: CrosswordState }
  | { type: 'UPDATE_USER_ANSWER'; row: number; col: number; value: string }
  | { type: 'TOGGLE_DIRECTION' } // Toggle between across and down
  | { type: 'LOAD_PUZZLE'; payload: {
      gridSize: number;
      grid: Cell[][];
      words: string[];
      clues: {
        across: Record<number, string>;
        down: Record<number, string>;
      };
      wordPositions: WordPosition[];
      userAnswers?: string[][];
      revealedCells?: [number, number][];
      progress?: number;
      isSurrendered?: boolean;
      timeSpent?: number;
    }
  }
  | { type: 'RESET_USER_ANSWERS' }
  | { type: 'USE_HINT'; row: number; col: number }
  | { type: 'UPDATE_PROGRESS' }
  | { type: 'CHECK_ANSWERS'; showResults: boolean }
  | { type: 'SURRENDER' }
  | { type: 'START_GAME' } // Start the game and timer
  | { type: 'PAUSE_GAME' } // Pause the game and timer
  | { type: 'RESUME_GAME' } // Resume the game and timer
  | { type: 'COMPLETE_GAME' } // Complete the game and stop the timer
  | { type: 'UPDATE_TIMER'; timeSpent: number } // Update the timer value
  | { type: 'INCREMENT_TIMER' } // Increment timer by 1 second
  | { type: 'RESET_TIMER' } // Reset the timer
  | { type: 'PLAY_AGAIN' }; // Reset everything for a fresh game attempt
