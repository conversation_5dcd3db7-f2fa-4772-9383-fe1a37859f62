export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          description: string
          image_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          image_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          image_url?: string | null
          created_at?: string
        }
      }
      crosswords: {
        Row: {
          id: string
          title: string
          description: string | null
          grid_size: number
          grid_data: Json
          words: Json
          clues: Json
          difficulty: string
          created_at: string
          updated_at: string
          user_id: string
          is_public: boolean
          plays: number
          rating: number | null
          category_id: string | null
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          grid_size: number
          grid_data: Json
          words: Json
          clues: Json
          difficulty: string
          created_at?: string
          updated_at?: string
          user_id: string
          is_public?: boolean
          plays?: number
          rating?: number | null
          category_id?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          grid_size?: number
          grid_data?: Json
          words?: Json
          clues?: Json
          difficulty?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          is_public?: boolean
          plays?: number
          rating?: number | null
          category_id?: string | null
        }
      }
      user_profiles: {
        Row: {
          id: string
          username: string
          display_name: string | null
          avatar_url: string | null
          bio: string | null
          created_at: string
          subscription_tier: string | null
          subscription_expires: string | null
        }
        Insert: {
          id: string
          username: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          subscription_tier?: string | null
          subscription_expires?: string | null
        }
        Update: {
          id?: string
          username?: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          subscription_tier?: string | null
          subscription_expires?: string | null
        }
      }
      user_progress: {
        Row: {
          id: string
          user_id: string
          crossword_id: string
          progress_data: Json
          is_completed: boolean
          time_spent: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          crossword_id: string
          progress_data: Json
          is_completed?: boolean
          time_spent?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          crossword_id?: string
          progress_data?: Json
          is_completed?: boolean
          time_spent?: number | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}