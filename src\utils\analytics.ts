/**
 * Analytics utility functions
 *
 * This module uses a lazy-loading approach to reduce initial bundle size
 * and only loads analytics when needed in production.
 */

// Define the gtag function globally
declare global {
  interface Window {
    gtag: (
      command: string,
      action: string,
      params?: Record<string, any>
    ) => void;
    dataLayer: any[];
  }
}

// Only load analytics in production
const isProduction = import.meta.env.PROD;

/**
 * Track a page view
 * @param path - The page path
 * @param title - The page title
 * @param contentType - The type of content being viewed (optional)
 */
export const trackPageView = (path: string, title: string, contentType?: string): void => {
  if (!isProduction) return;

  if (typeof window !== 'undefined' && window.gtag) {
    const params: Record<string, string> = {
      page_path: path,
      page_title: title,
    };

    // Add content type if provided
    if (contentType) {
      params.content_type = contentType;
    }

    window.gtag('config', import.meta.env.VITE_GOOGLE_ANALYTICS_ID as string, params);
  }
};

/**
 * Track an event
 * @param action - The event action
 * @param category - The event category
 * @param label - The event label
 * @param value - The event value
 */
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
): void => {
  if (!isProduction) return;

  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

/**
 * Initialize analytics with environment variables
 */
export const initAnalytics = (): void => {
  if (!isProduction) return;

  if (typeof window !== 'undefined' && window.gtag) {
    // Update the measurement ID from environment variables
    const measurementId = import.meta.env.VITE_GOOGLE_ANALYTICS_ID;
    if (measurementId) {
      window.gtag('config', measurementId);
    }
  }
};

/**
 * Track user engagement
 * @param userId - The user ID (if authenticated)
 */
export const trackUserEngagement = (userId?: string): void => {
  if (!isProduction) return;

  if (typeof window !== 'undefined' && window.gtag && userId) {
    window.gtag('set', { user_id: userId });
  }
};

// Export a default object with all functions
const analytics = {
  trackPageView,
  trackEvent,
  initAnalytics,
  trackUserEngagement,
};

export default analytics;
