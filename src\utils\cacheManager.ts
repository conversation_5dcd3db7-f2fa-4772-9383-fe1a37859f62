/**
 * Cache Manager untuk menyimpan dan mengambil data puzzle dari localStorage
 * Membantu mengurangi request API dan memungkinkan offline play
 */

import { CrosswordData } from '../services/api';

// Konfigurasi cache
const CACHE_CONFIG = {
  // Prefix untuk key di localStorage
  PREFIX: 'tts_cache_',
  // Prefix untuk key metadata cache
  META_PREFIX: 'tts_meta_',
  // Waktu kedaluwarsa cache dalam milidetik (7 hari)
  EXPIRY_TIME: 7 * 24 * 60 * 60 * 1000,
  // Batas ukuran cache dalam byte (5MB)
  MAX_SIZE: 5 * 1024 * 1024,
  // Versi cache untuk migrasi
  VERSION: '1.0.0',
};

// Interface untuk metadata cache
interface CacheMetadata {
  timestamp: number;
  size: number;
  version: string;
  lastAccessed: number;
}

// Interface untuk item cache
interface CacheItem<T> {
  data: T;
  metadata: CacheMetadata;
}

/**
 * Menyimpan data ke cache
 * @param key - Kunci untuk menyimpan data
 * @param data - Data yang akan disimpan
 * @returns boolean - Berhasil atau tidak
 */
export function saveToCache<T>(key: string, data: T): boolean {
  try {
    const cacheKey = `${CACHE_CONFIG.PREFIX}${key}`;
    const metaKey = `${CACHE_CONFIG.META_PREFIX}${key}`;
    
    // Konversi data ke string untuk disimpan
    const dataString = JSON.stringify(data);
    
    // Hitung ukuran data
    const size = new Blob([dataString]).size;
    
    // Buat metadata
    const metadata: CacheMetadata = {
      timestamp: Date.now(),
      size,
      version: CACHE_CONFIG.VERSION,
      lastAccessed: Date.now(),
    };
    
    // Buat item cache
    const cacheItem: CacheItem<T> = {
      data,
      metadata,
    };
    
    // Simpan data dan metadata
    localStorage.setItem(cacheKey, JSON.stringify(cacheItem));
    localStorage.setItem(metaKey, JSON.stringify(metadata));
    
    // Bersihkan cache jika melebihi batas ukuran
    cleanupCacheIfNeeded();
    
    return true;
  } catch (error) {
    console.error('Error saving to cache:', error);
    return false;
  }
}

/**
 * Mengambil data dari cache
 * @param key - Kunci untuk mengambil data
 * @returns T | null - Data atau null jika tidak ditemukan atau kedaluwarsa
 */
export function getFromCache<T>(key: string): T | null {
  try {
    const cacheKey = `${CACHE_CONFIG.PREFIX}${key}`;
    const metaKey = `${CACHE_CONFIG.META_PREFIX}${key}`;
    
    // Ambil data dari localStorage
    const cacheItemString = localStorage.getItem(cacheKey);
    
    if (!cacheItemString) {
      return null;
    }
    
    // Parse data
    const cacheItem: CacheItem<T> = JSON.parse(cacheItemString);
    
    // Periksa apakah data kedaluwarsa
    if (Date.now() - cacheItem.metadata.timestamp > CACHE_CONFIG.EXPIRY_TIME) {
      // Hapus data kedaluwarsa
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(metaKey);
      return null;
    }
    
    // Update waktu akses terakhir
    const metadata = { ...cacheItem.metadata, lastAccessed: Date.now() };
    localStorage.setItem(metaKey, JSON.stringify(metadata));
    
    return cacheItem.data;
  } catch (error) {
    console.error('Error getting from cache:', error);
    return null;
  }
}

/**
 * Menghapus data dari cache
 * @param key - Kunci untuk menghapus data
 */
export function removeFromCache(key: string): void {
  try {
    const cacheKey = `${CACHE_CONFIG.PREFIX}${key}`;
    const metaKey = `${CACHE_CONFIG.META_PREFIX}${key}`;
    
    localStorage.removeItem(cacheKey);
    localStorage.removeItem(metaKey);
  } catch (error) {
    console.error('Error removing from cache:', error);
  }
}

/**
 * Membersihkan cache jika melebihi batas ukuran
 * Menghapus item yang paling jarang diakses terlebih dahulu
 */
function cleanupCacheIfNeeded(): void {
  try {
    // Hitung total ukuran cache
    let totalSize = 0;
    const metadataItems: { key: string; metadata: CacheMetadata }[] = [];
    
    // Iterasi semua item di localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      
      if (key && key.startsWith(CACHE_CONFIG.META_PREFIX)) {
        const metadataString = localStorage.getItem(key);
        
        if (metadataString) {
          const metadata: CacheMetadata = JSON.parse(metadataString);
          const itemKey = key.replace(CACHE_CONFIG.META_PREFIX, '');
          
          totalSize += metadata.size;
          metadataItems.push({ key: itemKey, metadata });
        }
      }
    }
    
    // Jika ukuran melebihi batas, hapus item yang paling jarang diakses
    if (totalSize > CACHE_CONFIG.MAX_SIZE) {
      // Urutkan berdasarkan waktu akses terakhir (paling lama dulu)
      metadataItems.sort((a, b) => a.metadata.lastAccessed - b.metadata.lastAccessed);
      
      // Hapus item sampai ukuran di bawah batas
      let currentSize = totalSize;
      
      for (const { key, metadata } of metadataItems) {
        if (currentSize <= CACHE_CONFIG.MAX_SIZE) {
          break;
        }
        
        removeFromCache(key);
        currentSize -= metadata.size;
      }
    }
  } catch (error) {
    console.error('Error cleaning up cache:', error);
  }
}

/**
 * Menyimpan data puzzle ke cache
 * @param puzzleId - ID puzzle
 * @param data - Data puzzle
 */
export function cachePuzzle(puzzleId: string, data: CrosswordData): boolean {
  return saveToCache<CrosswordData>(`puzzle_${puzzleId}`, data);
}

/**
 * Mengambil data puzzle dari cache
 * @param puzzleId - ID puzzle
 * @returns CrosswordData | null - Data puzzle atau null jika tidak ditemukan
 */
export function getCachedPuzzle(puzzleId: string): CrosswordData | null {
  return getFromCache<CrosswordData>(`puzzle_${puzzleId}`);
}

/**
 * Menyimpan daftar puzzle ke cache
 * @param cacheKey - Kunci cache (misalnya 'all', 'category_1', dll)
 * @param data - Daftar puzzle
 */
export function cachePuzzleList(cacheKey: string, data: CrosswordData[]): boolean {
  return saveToCache<CrosswordData[]>(`puzzlelist_${cacheKey}`, data);
}

/**
 * Mengambil daftar puzzle dari cache
 * @param cacheKey - Kunci cache (misalnya 'all', 'category_1', dll)
 * @returns CrosswordData[] | null - Daftar puzzle atau null jika tidak ditemukan
 */
export function getCachedPuzzleList(cacheKey: string): CrosswordData[] | null {
  return getFromCache<CrosswordData[]>(`puzzlelist_${cacheKey}`);
}

/**
 * Memeriksa apakah cache tersedia
 * @returns boolean - true jika localStorage tersedia
 */
export function isCacheAvailable(): boolean {
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Membersihkan semua cache
 */
export function clearAllCache(): void {
  try {
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i);
      
      if (key && (key.startsWith(CACHE_CONFIG.PREFIX) || key.startsWith(CACHE_CONFIG.META_PREFIX))) {
        localStorage.removeItem(key);
      }
    }
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}
