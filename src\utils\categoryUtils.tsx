/**
 * Utility functions for category-related operations
 * Extracted to reduce bundle size and improve code organization
 * Uses dynamic keyword matching for more flexible category handling
 */

import React from 'react';
import { BookIcon, FilmIcon, BeakerIcon, LandmarkIcon, TrophyIcon, GlobeIcon, MusicIcon, HeartIcon } from 'lucide-react';

// Default icon and color for unknown categories
const defaultIcon = <GlobeIcon className="w-8 h-8" />;
const defaultColor = 'bg-primary-500';

// Category keyword mappings for dynamic matching
const categoryKeywords = {
  umum: ['umum', 'pengetahuan', 'general', 'knowledge'],
  film: ['film', 'movie', 'tv', 'televisi', 'cinema', 'bioskop', 'hiburan', 'entertainment'],
  sains: ['sains', 'science', 'teknologi', 'technology', 'ilmu', 'penemuan', 'discovery', 'innovation'],
  sejarah: ['sejarah', 'history', 'kuno', 'ancient', 'warisan', 'heritage', 'budaya', 'culture'],
  olahraga: ['olah<PERSON>', 'sport', 'atletik', 'athletic', 'kompetisi', 'competition', 'pertandingan'],
  musik: ['musik', 'music', 'lagu', 'song', 'band', 'konser', 'concert', 'melodi', 'melody'],
  sastra: ['sastra', 'literature', 'buku', 'book', 'novel', 'puisi', 'poetry', 'cerita', 'story'],
  kesehatan: ['kesehatan', 'health', 'medis', 'medical', 'dokter', 'doctor', 'obat', 'medicine']
};

/**
 * Check if a category matches a specific type based on keywords
 * @param categoryId The category ID
 * @param categoryName The category name (optional)
 * @param categoryDesc The category description (optional)
 * @param type The category type to check against
 * @returns boolean indicating if the category matches the type
 */
const matchesCategoryType = (
  categoryId: string,
  categoryName?: string,
  categoryDesc?: string,
  type: keyof typeof categoryKeywords = 'umum'
): boolean => {
  // Check if the ID directly matches
  if (categoryId === type) return true;

  // Convert all inputs to lowercase for case-insensitive matching
  const idLower = categoryId.toLowerCase();
  const nameLower = categoryName?.toLowerCase() || '';
  const descLower = categoryDesc?.toLowerCase() || '';

  // Get keywords for this category type
  const keywords = categoryKeywords[type];

  // Check if any of the keywords are found in the category ID, name, or description
  return keywords.some(keyword =>
    idLower.includes(keyword) ||
    nameLower.includes(keyword) ||
    descLower.includes(keyword)
  );
};

/**
 * Get the icon component for a category
 * @param categoryId The category ID
 * @param categoryName The category name (optional, for better matching)
 * @param categoryDesc The category description (optional, for better matching)
 * @returns JSX Element with the appropriate icon
 */
export const getCategoryIcon = (
  categoryId: string,
  categoryName?: string,
  categoryDesc?: string
): JSX.Element => {
  // Map of category icons by type
  const categoryIcons = {
    umum: <GlobeIcon className="w-8 h-8" />,
    film: <FilmIcon className="w-8 h-8" />,
    sains: <BeakerIcon className="w-8 h-8" />,
    sejarah: <LandmarkIcon className="w-8 h-8" />,
    olahraga: <TrophyIcon className="w-8 h-8" />,
    musik: <MusicIcon className="w-8 h-8" />,
    sastra: <BookIcon className="w-8 h-8" />,
    kesehatan: <HeartIcon className="w-8 h-8" />
  };

  // Check for direct match first (backward compatibility)
  if (categoryId in categoryIcons) {
    return categoryIcons[categoryId as keyof typeof categoryIcons];
  }

  // Try to match by keywords
  for (const type of Object.keys(categoryKeywords) as Array<keyof typeof categoryKeywords>) {
    if (matchesCategoryType(categoryId, categoryName, categoryDesc, type)) {
      return categoryIcons[type];
    }
  }

  // Return default icon if no match found
  return defaultIcon;
};

/**
 * Get the color class for a category
 * @param categoryId The category ID
 * @param categoryName The category name (optional, for better matching)
 * @param categoryDesc The category description (optional, for better matching)
 * @returns Tailwind CSS class for the category color
 */
export const getCategoryColor = (
  categoryId: string,
  categoryName?: string,
  categoryDesc?: string
): string => {
  // Map of category colors by type - using monochromatic grayscale variations
  const categoryColors = {
    umum: 'bg-primary-500',
    film: 'bg-primary-600',
    sains: 'bg-primary-700',
    sejarah: 'bg-primary-800',
    olahraga: 'bg-ink-light',
    musik: 'bg-ink',
    sastra: 'bg-primary-400',
    kesehatan: 'bg-primary-900'
  };

  // Check for direct match first (backward compatibility)
  if (categoryId in categoryColors) {
    return categoryColors[categoryId as keyof typeof categoryColors];
  }

  // Try to match by keywords
  for (const type of Object.keys(categoryKeywords) as Array<keyof typeof categoryKeywords>) {
    if (matchesCategoryType(categoryId, categoryName, categoryDesc, type)) {
      return categoryColors[type];
    }
  }

  // Return default color if no match found
  return defaultColor;
};
