import { CrosswordState, Direction, PlacementScore } from '../types/crossword';

/**
 * Finds all possible placements for a word in the grid
 */
export function findPossiblePlacements(word: string, state: CrosswordState): PlacementScore[] {
  const { grid, gridSize, words } = state;
  const placements: PlacementScore[] = [];

  // If grid is empty, place the first word in the middle
  if (words.length === 0) {
    const midRow = Math.floor(gridSize / 2);
    const midCol = Math.floor((gridSize - word.length) / 2);
    return [[0, 'across', midRow, midCol]];
  }

  // Try to find intersections with existing words
  for (let charIdx = 0; charIdx < word.length; charIdx++) {
    const char = word[charIdx];

    // Look for matching characters in the grid
    for (let r = 0; r < gridSize; r++) {
      for (let c = 0; c < gridSize; c++) {
        if (grid[r][c].char === char) {
          // Try placing horizontally
          if (tryPlacement(word, 'across', r, c - charIdx, charIdx, state)) {
            const score = calculatePlacementScore(word, 'across', r, c - charIdx, state);
            placements.push([score, 'across', r, c - charIdx]);
          }

          // Try placing vertically
          if (tryPlacement(word, 'down', r - charIdx, c, charIdx, state)) {
            const score = calculatePlacementScore(word, 'down', r - charIdx, c, state);
            placements.push([score, 'down', r - charIdx, c]);
          }
        }
      }
    }
  }

  // Sort placements by score (highest first)
  return placements.sort((a, b) => b[0] - a[0]);
}

/**
 * Checks if a word can be placed at a specific position
 */
function tryPlacement(
  word: string,
  direction: Direction,
  startRow: number,
  startCol: number,
  intersectionIdx: number,
  state: CrosswordState
): boolean {
  const { grid, gridSize } = state;

  // Check if word fits on the grid
  if (
    startRow < 0 ||
    startCol < 0 ||
    (direction === 'across' && startCol + word.length > gridSize) ||
    (direction === 'down' && startRow + word.length > gridSize)
  ) {
    return false;
  }

  // Check characters before and after the word
  if (direction === 'across') {
    if (startCol > 0 && grid[startRow][startCol - 1].char !== ' ') {
      return false;
    }
    if (startCol + word.length < gridSize && grid[startRow][startCol + word.length].char !== ' ') {
      return false;
    }
  } else {
    if (startRow > 0 && grid[startRow - 1][startCol].char !== ' ') {
      return false;
    }
    if (startRow + word.length < gridSize && grid[startRow + word.length][startCol].char !== ' ') {
      return false;
    }
  }

  // Check for conflicts with existing characters
  let intersections = 0;
  for (let i = 0; i < word.length; i++) {
    const r = direction === 'across' ? startRow : startRow + i;
    const c = direction === 'across' ? startCol + i : startCol;

    // If the cell already has a character
    if (grid[r][c].char !== ' ') {
      // If characters don't match, there's a conflict
      if (grid[r][c].char !== word[i]) {
        return false;
      }
      intersections++;
    } else {
      // Check adjacent characters perpendicular to the direction
      if (direction === 'across') {
        if ((r > 0 && grid[r - 1][c].char !== ' ') ||
            (r < gridSize - 1 && grid[r + 1][c].char !== ' ')) {
          if (i !== intersectionIdx) {
            return false;
          }
        }
      } else {
        if ((c > 0 && grid[r][c - 1].char !== ' ') ||
            (c < gridSize - 1 && grid[r][c + 1].char !== ' ')) {
          if (i !== intersectionIdx) {
            return false;
          }
        }
      }
    }
  }

  // The word can be placed if it intersects with at least one existing character
  return intersections > 0;
}

/**
 * Improved algorithm to calculate placement score
 */
function calculatePlacementScore(
  word: string,
  direction: Direction,
  startRow: number,
  startCol: number,
  state: CrosswordState
): number {
  const { grid } = state;
  let score = 0;
  let intersections = 0;

  // Reward intersections more heavily
  for (let i = 0; i < word.length; i++) {
    const r = direction === 'across' ? startRow : startRow + i;
    const c = direction === 'across' ? startCol + i : startCol;

    if (grid[r][c].char === word[i]) {
      score += 3; // Increased from 1 to 3
      intersections++;
    }
  }

  // Penalize words that are placed far from the center
  const centerRow = Math.floor(state.gridSize / 2);
  const centerCol = Math.floor(state.gridSize / 2);
  const rowDistance = Math.abs(startRow - centerRow);
  const colDistance = Math.abs(startCol - centerCol);
  const distancePenalty = (rowDistance + colDistance) * 0.1;

  // Reward words that create more compact puzzles
  const compactness = intersections > 0 ? 2 : 0;

  // Prefer words that use less common letters (more valuable for crosswords)
  const uncommonLetterBonus = countUncommonLetters(word) * 0.5;

  return score + compactness + uncommonLetterBonus - distancePenalty;
}

/**
 * Count uncommon letters in a word (J, K, Q, X, Z)
 */
function countUncommonLetters(word: string): number {
  const uncommonLetters = ['J', 'K', 'Q', 'X', 'Z'];
  return word.split('').filter(char => uncommonLetters.includes(char.toUpperCase())).length;
}

/**
 * Optimizes the grid by removing empty rows and columns
 */
export function optimizeGrid(state: CrosswordState): CrosswordState {
  const { grid, gridSize, wordPositions } = state;

  // Find boundaries of the grid content
  let minRow = gridSize;
  let minCol = gridSize;
  let maxRow = 0;
  let maxCol = 0;

  for (let r = 0; r < gridSize; r++) {
    for (let c = 0; c < gridSize; c++) {
      if (grid[r][c].char !== ' ') {
        minRow = Math.min(minRow, r);
        minCol = Math.min(minCol, c);
        maxRow = Math.max(maxRow, r);
        maxCol = Math.max(maxCol, c);
      }
    }
  }

  // Add some padding
  minRow = Math.max(0, minRow - 1);
  minCol = Math.max(0, minCol - 1);
  maxRow = Math.min(gridSize - 1, maxRow + 1);
  maxCol = Math.min(gridSize - 1, maxCol + 1);

  // New dimensions
  const newHeight = maxRow - minRow + 1;
  const newWidth = maxCol - minCol + 1;
  const newSize = Math.max(newHeight, newWidth);

  // Create new grid
  const newGrid = Array(newSize).fill(null).map(() =>
    Array(newSize).fill(null).map(() => ({ char: ' ', wordIds: [] }))
  );

  // Copy content to new grid
  for (let r = minRow; r <= maxRow; r++) {
    for (let c = minCol; c <= maxCol; c++) {
      newGrid[r - minRow][c - minCol] = grid[r][c];
    }
  }

  // Update word positions
  const newWordPositions = wordPositions.map(pos => ({
    ...pos,
    row: pos.row - minRow,
    col: pos.col - minCol,
  }));

  return {
    ...state,
    gridSize: newSize,
    grid: newGrid,
    wordPositions: newWordPositions,
  };
}

/**
 * Gets word numbers for displaying on the grid
 */
export function getWordNumbers(state: CrosswordState): { [key: string]: number } {
  const numberMap: { [key: string]: number } = {};

  state.wordPositions.forEach(pos => {
    const key = `${pos.row},${pos.col}`;
    numberMap[key] = pos.number;
  });

  return numberMap;
}

/**
 * Optimizes word placement to create a more compact and interconnected crossword
 * @param originalState The original crossword state to optimize
 * @param maxIterations Maximum number of iterations to try
 * @returns Optimized crossword state
 */
export function optimizeWordPlacement(originalState: CrosswordState, maxIterations = 100): CrosswordState {
  const words = originalState.words;
  const originalClues = originalState.clues;

  // Create a map to store the original clues for each word
  const wordToClueMap = new Map<number, {word: string, direction: Direction, clue: string}>();

  // Map words to their original clues by word ID
  originalState.wordPositions.forEach(position => {
    const wordId = position.id;
    if (wordId !== undefined) {
      const direction = position.direction;
      const clue = originalClues[direction][position.number];
      const word = words[wordId - 1]; // wordId is 1-based, array is 0-based

      if (word && clue) {
        wordToClueMap.set(wordId, {
          word,
          direction,
          clue
        });
      }
    }
  });

  // Initialize best state with grid size from original state
  const initialGridSize = originalState.gridSize || 15;
  let bestState: CrosswordState = {
    gridSize: initialGridSize,
    grid: Array(initialGridSize).fill(null).map(() => Array(initialGridSize).fill({ char: ' ', wordIds: [] })),
    words: [],
    clues: { across: {}, down: {} },
    wordPositions: [],
    wordNumber: 1,
    selectedWordId: null,
    mode: 'edit',
  };

  let bestScore = -1;
  let bestPlacedWords = 0;

  // Try different word orderings
  for (let iteration = 0; iteration < maxIterations; iteration++) {
    // Try different strategies for word ordering
    let shuffledWords: string[];

    if (iteration % 3 === 0) {
      // Strategy 1: Sort by length (longest first)
      shuffledWords = [...words].sort((a, b) => b.length - a.length);
    } else if (iteration % 3 === 1) {
      // Strategy 2: Sort by uncommon letters (most uncommon first)
      shuffledWords = [...words].sort((a, b) => countUncommonLetters(b) - countUncommonLetters(a));
    } else {
      // Strategy 3: Random shuffle
      shuffledWords = [...words].sort(() => Math.random() - 0.5);
    }

    // Create a new state using the original grid size
    const state: CrosswordState = {
      gridSize: initialGridSize,
      grid: Array(initialGridSize).fill(null).map(() => Array(initialGridSize).fill({ char: ' ', wordIds: [] })),
      words: [],
      clues: { across: {}, down: {} },
      wordPositions: [],
      wordNumber: 1,
      selectedWordId: null,
      mode: 'edit',
    };

    // Place words one by one
    for (const word of shuffledWords) {
      const placements = findPossiblePlacements(word, state);
      if (placements.length > 0) {
        // Get the best placement (highest score)
        const [, direction, row, col] = placements[0];

        // Add word to grid
        const wordId = state.words.length + 1;
        const newGrid = state.grid.map(row => [...row]);

        for (let i = 0; i < word.length; i++) {
          const r = direction === 'across' ? row : row + i;
          const c = direction === 'across' ? col + i : col;

          newGrid[r][c] = {
            char: word[i],
            wordIds: [...newGrid[r][c].wordIds, wordId],
          };
        }

        // Find the original clue for this word
        let clue = `Clue for ${word}`;

        // Look for the word in the original clues map
        for (const [id, entry] of wordToClueMap.entries()) {
          if (entry.word === word) {
            // If we have a clue for this word in the same direction, use it
            if (entry.direction === direction) {
              clue = entry.clue;
              break;
            } else if (clue === `Clue for ${word}`) {
              // If we don't have a clue in the same direction but have one in the other direction,
              // use it as a fallback
              clue = entry.clue;
            }
          }
        }

        // Cari ID asli kata ini dari state asli
        let originalWordId = wordId;
        const wordIndex = originalState.words.indexOf(word);
        if (wordIndex !== -1) {
          // Jika kata ditemukan di state asli, gunakan ID aslinya
          const originalPosition = originalState.wordPositions.find(
            pos => pos.id === wordIndex + 1
          );
          if (originalPosition) {
            originalWordId = originalPosition.id || wordId;
          }
        }

        // Update state
        state.grid = newGrid;
        state.words.push(word);
        state.wordPositions.push({
          direction,
          row,
          col,
          number: state.wordNumber,
          id: originalWordId
        });
        state.clues[direction][state.wordNumber] = clue;
        state.wordNumber++;
      }
    }

    // Calculate score for this placement
    const score = calculateGridScore(state);
    const placedWords = state.words.length;

    // Update best state if this one is better
    // Prioritize placing more words, then use score as tiebreaker
    if (placedWords > bestPlacedWords || (placedWords === bestPlacedWords && score > bestScore)) {
      bestScore = score;
      bestPlacedWords = placedWords;
      bestState = JSON.parse(JSON.stringify(state)); // Deep copy
    }
  }

  // If we couldn't place all words, show a warning in console
  if (bestPlacedWords < words.length) {
    console.warn(`Could only place ${bestPlacedWords} out of ${words.length} words in the optimized layout.`);
  }

  // Create a map of words to their original clues from the original state
  const wordClueMap = new Map<string, string>();

  // Collect all original clues
  for (const direction of ['across', 'down'] as Direction[]) {
    for (const [numberStr, clue] of Object.entries(originalState.clues[direction])) {
      const number = parseInt(numberStr);
      const position = originalState.wordPositions.find(
        pos => pos.direction === direction && pos.number === number
      );

      if (position && position.id !== undefined) {
        const word = originalState.words[position.id - 1];
        if (word) {
          wordClueMap.set(word, clue);
        }
      }
    }
  }

  // Update any missing clues in the best state with original clues
  for (let i = 0; i < bestState.words.length; i++) {
    const word = bestState.words[i];
    const wordId = i + 1;

    // Find the position for this word
    const position = bestState.wordPositions.find(pos => pos.id === wordId);
    if (position) {
      const direction = position.direction;
      const number = position.number;

      // If the clue is the default one, try to find the original
      if (bestState.clues[direction][number] === `Clue for ${word}` && wordClueMap.has(word)) {
        bestState.clues[direction][number] = wordClueMap.get(word)!;
      }
    }
  }

  // Optimize the grid size to make it more compact
  return optimizeGrid(bestState);
}

/**
 * Calculate overall grid score based on compactness and intersections
 */
function calculateGridScore(state: CrosswordState): number {
  const { grid, words } = state;

  if (words.length === 0) return 0;

  // Count intersections
  let intersections = 0;
  let filledCells = 0;

  for (let r = 0; r < state.gridSize; r++) {
    for (let c = 0; c < state.gridSize; c++) {
      if (grid[r][c].char !== ' ') {
        filledCells++;
        if (grid[r][c].wordIds.length > 1) {
          intersections++;
        }
      }
    }
  }

  // Calculate grid boundaries
  let minRow = state.gridSize;
  let minCol = state.gridSize;
  let maxRow = 0;
  let maxCol = 0;

  for (let r = 0; r < state.gridSize; r++) {
    for (let c = 0; c < state.gridSize; c++) {
      if (grid[r][c].char !== ' ') {
        minRow = Math.min(minRow, r);
        minCol = Math.min(minCol, c);
        maxRow = Math.max(maxRow, r);
        maxCol = Math.max(maxCol, c);
      }
    }
  }

  const width = maxCol - minCol + 1;
  const height = maxRow - minRow + 1;
  const area = width * height;

  // Calculate density
  const density = filledCells / area;

  // Calculate intersection ratio
  const intersectionRatio = intersections / words.length;

  // Final score combines density and intersections
  return (density * 50) + (intersectionRatio * 50);
}



