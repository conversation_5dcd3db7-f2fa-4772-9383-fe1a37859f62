import { CrosswordState } from '../types/crossword';

/**
 * Interface for description template parameters
 */
interface DescriptionParams {
  NAMA_TTS?: string;
  TINGKAT_KESULITAN?: string;
  TEMA_UTAMA?: string;
  JUMLAH_KOTAK?: number;
  JUMLAH_SOAL?: number;
  KATEGORI_1?: string;
  KATEGORI_2?: string;
  KATEGORI_3?: string;
  NAMA_KREATOR?: string;
  TAHUN_PENGALAMAN?: number;
  JENIS_TANTANGAN?: string;
  ESTIMASI_WAKTU?: number;
  FORMAT_TTS?: string;
  FITUR_KHUSUS?: string;
  TARGET_PEMAIN?: string;
  MANFAAT_KOGNITIF?: string;
  EDISI_VOLUME?: string | number;
  JUMLAH_KATA_BARU?: number;
  JUMLAH_KATA_TERSEMBUNYI?: number;
  BONUS_HADIAH?: string;
}

/**
 * Default values for description parameters
 */
const defaultParams: DescriptionParams = {
  TINGKAT_KESULITAN: 'sedang',
  TEMA_UTAMA: 'umum',
  KATEGORI_1: 'pengetahuan umum',
  KATEGORI_2: 'budaya',
  KATEGORI_3: 'hiburan',
  NAMA_KREATOR: 'Admin',
  TAHUN_PENGALAMAN: 5,
  JENIS_TANTANGAN: 'tantangan kognitif',
  ESTIMASI_WAKTU: 15,
  FORMAT_TTS: 'digital',
  FITUR_KHUSUS: 'petunjuk interaktif',
  TARGET_PEMAIN: 'semua kalangan',
  MANFAAT_KOGNITIF: 'kosakata dan daya ingat',
  EDISI_VOLUME: 1,
  JUMLAH_KATA_BARU: 10,
  JUMLAH_KATA_TERSEMBUNYI: 3,
  BONUS_HADIAH: 'poin bonus'
};

/**
 * Description template
 */
const DESCRIPTION_TEMPLATE = `Uji ketajaman pikiran Anda dengan [NAMA_TTS], teka-teki silang [TINGKAT_KESULITAN] yang menantang dengan tema [TEMA_UTAMA]. Terdiri dari [JUMLAH_KOTAK] kotak dan [JUMLAH_SOAL] soal yang mencakup bidang [KATEGORI_1], [KATEGORI_2], dan [KATEGORI_3].

[NAMA_TTS] dirancang oleh [NAMA_KREATOR], kreator puzzle berpengalaman dengan [TAHUN_PENGALAMAN] tahun dalam menciptakan teka-teki yang mengasah otak. Setiap pertanyaan telah disusun secara cermat untuk memberikan [JENIS_TANTANGAN] yang menyenangkan namun tetap menantang.

Selesaikan dalam waktu [ESTIMASI_WAKTU] menit untuk menguji kecepatan berpikir Anda. Tersedia dalam [FORMAT_TTS] dengan [FITUR_KHUSUS] yang memudahkan pengerjaan. Cocok untuk [TARGET_PEMAIN] yang ingin meningkatkan [MANFAAT_KOGNITIF] dan memperluas pengetahuan umum.`;

/**
 * Extended description template with optional parameters
 */
const EXTENDED_DESCRIPTION_TEMPLATE = `${DESCRIPTION_TEMPLATE}

[NAMA_TTS] edisi [EDISI_VOLUME] ini menghadirkan [JUMLAH_KATA_BARU] kata baru yang belum pernah muncul pada edisi sebelumnya. Temukan [JUMLAH_KATA_TERSEMBUNYI] kata tersembunyi untuk mendapatkan [BONUS_HADIAH] istimewa!`;

/**
 * Count the total number of clues in a crossword
 * @param clues The clues object from CrosswordState
 * @returns The total number of clues
 */
export const countTotalClues = (clues: { across: Record<number, string>; down: Record<number, string> }): number => {
  const acrossCount = Object.keys(clues.across).length;
  const downCount = Object.keys(clues.down).length;
  return acrossCount + downCount;
};

/**
 * Count the total number of filled cells in a crossword grid
 * @param grid The grid from CrosswordState
 * @returns The total number of filled cells
 */
const countFilledCells = (grid: { char: string; wordIds: number[] }[][]): number => {
  let count = 0;
  for (let row of grid) {
    for (let cell of row) {
      if (cell.char !== ' ' && cell.wordIds.length > 0) {
        count++;
      }
    }
  }
  return count;
};

/**
 * Estimate the difficulty level based on grid size and word count
 * @param gridSize The size of the grid
 * @param wordCount The number of words
 * @returns The difficulty level in Indonesian
 */
const estimateDifficulty = (gridSize: number, wordCount: number): string => {
  if (gridSize <= 10 && wordCount <= 15) {
    return 'mudah';
  } else if (gridSize >= 15 && wordCount >= 25) {
    return 'sulit';
  } else {
    return 'sedang';
  }
};

/**
 * Estimate the time needed to complete the crossword
 * @param difficulty The difficulty level
 * @param clueCount The number of clues
 * @returns The estimated time in minutes
 */
const estimateTime = (difficulty: string, clueCount: number): number => {
  const baseTime = {
    'mudah': 10,
    'sedang': 15,
    'sulit': 25
  }[difficulty] || 15;

  return Math.max(5, Math.round(baseTime * (clueCount / 15)));
};

/**
 * Generate a description for a crossword
 * @param title The title of the crossword
 * @param state The CrosswordState object
 * @param params Optional parameters to customize the description
 * @param useExtendedTemplate Whether to use the extended template with optional sections
 * @returns The generated description
 */
export const generateDescription = (
  title: string,
  state: CrosswordState,
  params: Partial<DescriptionParams> = {},
  useExtendedTemplate: boolean = false
): string => {
  const clueCount = countTotalClues(state.clues);
  const filledCells = countFilledCells(state.grid);
  const difficulty = params.TINGKAT_KESULITAN || estimateDifficulty(state.gridSize, state.words.length);

  // Combine default params with calculated values and provided params
  const finalParams: DescriptionParams = {
    ...defaultParams,
    NAMA_TTS: title,
    TINGKAT_KESULITAN: difficulty,
    JUMLAH_KOTAK: filledCells,
    JUMLAH_SOAL: clueCount,
    ESTIMASI_WAKTU: estimateTime(difficulty, clueCount),
    ...params
  };

  // Choose template based on whether to use extended version
  const template = useExtendedTemplate ? EXTENDED_DESCRIPTION_TEMPLATE : DESCRIPTION_TEMPLATE;

  // Replace placeholders with actual values
  let description = template;
  for (const [key, value] of Object.entries(finalParams)) {
    if (value !== undefined) {
      const regex = new RegExp(`\\[${key}\\]`, 'g');
      description = description.replace(regex, String(value));
    }
  }

  return description;
};
