import { CrosswordState } from '../types/crossword';

/**
 * Interface for description template parameters
 */
interface DescriptionParams {
  NAMA_TTS?: string;
  TINGKAT_KESULITAN?: string;
  TEMA_UTAMA?: string;
  JUMLAH_KOTAK?: number;
  JUMLAH_SOAL?: number;
  KATEGORI_1?: string;
  KATEGORI_2?: string;
  KATEGORI_3?: string;
  NAMA_KREATOR?: string;
  TAHUN_PENGALAMAN?: number;
  JENIS_TANTANGAN?: string;
  ESTIMASI_WAKTU?: number;
  FORMAT_TTS?: string;
  FITUR_KHUSUS?: string;
  TARGET_PEMAIN?: string;
  MANFAAT_KOGNITIF?: string;
  EDISI_VOLUME?: string | number;
  JUMLAH_KATA_BARU?: number;
  JUMLAH_KATA_TERSEMBUNYI?: number;
  BONUS_HADIAH?: string;
  KATA_TERPANJANG?: string;
  KATA_TERPENDEK?: string;
  HURUF_LANGKA?: string[];
  RASIO_INTERSEKSI?: number;
  KEPADATAN_GRID?: number;
}

/**
 * Default values for description parameters
 */
const defaultParams: DescriptionParams = {
  TINGKAT_KESULITAN: 'sedang',
  TEMA_UTAMA: 'umum',
  KATEGORI_1: 'pengetahuan umum',
  KATEGORI_2: 'budaya',
  KATEGORI_3: 'hiburan',
  NAMA_KREATOR: 'Admin',
  TAHUN_PENGALAMAN: 5,
  JENIS_TANTANGAN: 'tantangan kognitif',
  ESTIMASI_WAKTU: 15,
  FORMAT_TTS: 'digital',
  FITUR_KHUSUS: 'petunjuk interaktif',
  TARGET_PEMAIN: 'semua kalangan',
  MANFAAT_KOGNITIF: 'kosakata dan daya ingat',
  EDISI_VOLUME: 1,
  JUMLAH_KATA_BARU: 10,
  JUMLAH_KATA_TERSEMBUNYI: 3,
  BONUS_HADIAH: 'poin bonus'
};

/**
 * Variasi kalimat pembuka
 */
const OPENING_PHRASES = [
  "Uji ketajaman pikiran Anda dengan [NAMA_TTS],",
  "Tantang kemampuan berpikir Anda dengan [NAMA_TTS],",
  "Asah otak Anda dengan [NAMA_TTS],",
  "Jelajahi dunia kata-kata dengan [NAMA_TTS],",
  "Tingkatkan kosakata Anda dengan [NAMA_TTS],",
  "Bersiaplah untuk petualangan kata dengan [NAMA_TTS],",
  "Hadapi tantangan verbal dengan [NAMA_TTS],",
  "Eksplorasi pengetahuan Anda dengan [NAMA_TTS],",
  "Buktikan keahlian Anda dalam [NAMA_TTS],",
  "Temukan keseruan bermain [NAMA_TTS],",
];

/**
 * Variasi kalimat penutup
 */
const CLOSING_PHRASES = [
  "Selamat bermain dan semoga berhasil!",
  "Siap untuk menguji kemampuan Anda?",
  "Tantangan menunggu Anda!",
  "Buktikan bahwa Anda bisa menyelesaikannya!",
  "Seberapa cepat Anda bisa menyelesaikannya?",
  "Bagikan dengan teman untuk menambah keseruan!",
  "Jadilah yang tercepat dalam menyelesaikan teka-teki ini!",
  "Temukan semua jawaban dan raih skor tertinggi!",
  "Setiap kata yang Anda temukan adalah langkah menuju kemenangan!",
  "Apakah Anda siap untuk tantangan ini?",
];

/**
 * Variasi template deskripsi
 */
const DESCRIPTION_TEMPLATES = [
  // Template 1 (yang sudah ada)
  `[OPENING_PHRASE] teka-teki silang [TINGKAT_KESULITAN] yang menantang dengan tema [TEMA_UTAMA]. Terdiri dari [JUMLAH_KOTAK] kotak dan [JUMLAH_SOAL] soal yang mencakup bidang [KATEGORI_1], [KATEGORI_2], dan [KATEGORI_3].

[NAMA_TTS] dirancang oleh [NAMA_KREATOR], kreator puzzle berpengalaman dengan [TAHUN_PENGALAMAN] tahun dalam menciptakan teka-teki yang mengasah otak. Setiap pertanyaan telah disusun secara cermat untuk memberikan [JENIS_TANTANGAN] yang menyenangkan namun tetap menantang.

Selesaikan dalam waktu [ESTIMASI_WAKTU] menit untuk menguji kecepatan berpikir Anda. Tersedia dalam [FORMAT_TTS] dengan [FITUR_KHUSUS] yang memudahkan pengerjaan. Cocok untuk [TARGET_PEMAIN] yang ingin meningkatkan [MANFAAT_KOGNITIF] dan memperluas pengetahuan umum.

[CLOSING_PHRASE]`,

  // Template 2 (variasi)
  `[OPENING_PHRASE] sebuah teka-teki silang [TINGKAT_KESULITAN] bertema [TEMA_UTAMA]. Dengan [JUMLAH_KOTAK] kotak dan [JUMLAH_SOAL] pertanyaan menarik, teka-teki ini akan menguji pengetahuan Anda tentang [KATEGORI_1], [KATEGORI_2], dan berbagai aspek [KATEGORI_3].

Dibuat dengan teliti oleh [NAMA_KREATOR] yang telah berpengalaman [TAHUN_PENGALAMAN] tahun dalam dunia teka-teki silang. [NAMA_TTS] menawarkan [JENIS_TANTANGAN] yang akan mengasah kemampuan berpikir Anda.

Dengan estimasi waktu penyelesaian [ESTIMASI_WAKTU] menit, teka-teki ini cocok untuk [TARGET_PEMAIN]. Nikmati fitur [FITUR_KHUSUS] yang akan membantu Anda meningkatkan [MANFAAT_KOGNITIF].

[CLOSING_PHRASE]`,

  // Template 3 (variasi lain)
  `[OPENING_PHRASE] teka-teki silang tingkat [TINGKAT_KESULITAN] yang akan memperluas wawasan Anda tentang [TEMA_UTAMA]. Dengan total [JUMLAH_SOAL] pertanyaan dalam [JUMLAH_KOTAK] kotak, Anda akan menjelajahi topik-topik seperti [KATEGORI_1], [KATEGORI_2], dan [KATEGORI_3].

[NAMA_TTS] adalah karya [NAMA_KREATOR], seorang pembuat teka-teki dengan pengalaman [TAHUN_PENGALAMAN] tahun. Dirancang untuk memberikan [JENIS_TANTANGAN] yang menyenangkan, teka-teki ini akan menguji dan memperluas pengetahuan Anda.

Rata-rata waktu penyelesaian adalah [ESTIMASI_WAKTU] menit, menjadikannya pilihan sempurna untuk [TARGET_PEMAIN]. Dengan [FITUR_KHUSUS] yang inovatif, teka-teki ini akan membantu meningkatkan [MANFAAT_KOGNITIF] Anda.

[CLOSING_PHRASE]`,

  // Template 4 (fokus pada kata-kata)
  `[OPENING_PHRASE] koleksi [JUMLAH_SOAL] kata menarik dalam format teka-teki silang [TINGKAT_KESULITAN]. Teka-teki ini menampilkan berbagai kata dari tema [TEMA_UTAMA], termasuk bidang [KATEGORI_1], [KATEGORI_2], dan [KATEGORI_3].

Dari kata terpendek "[KATA_TERPENDEK]" hingga kata terpanjang "[KATA_TERPANJANG]", setiap kata dipilih dengan cermat untuk memberikan [JENIS_TANTANGAN] yang optimal. [NAMA_TTS] dirancang untuk diselesaikan dalam waktu sekitar [ESTIMASI_WAKTU] menit.

Cocok untuk [TARGET_PEMAIN], teka-teki ini akan membantu meningkatkan [MANFAAT_KOGNITIF] Anda melalui [FITUR_KHUSUS] yang tersedia dalam format [FORMAT_TTS].

[CLOSING_PHRASE]`,

  // Template 5 (fokus pada tantangan)
  `[OPENING_PHRASE] tantangan [TINGKAT_KESULITAN] untuk para pecinta teka-teki silang. Dengan [JUMLAH_KOTAK] kotak yang tersusun rapi, [NAMA_TTS] menghadirkan [JUMLAH_SOAL] pertanyaan seputar [TEMA_UTAMA], [KATEGORI_1], [KATEGORI_2], dan [KATEGORI_3].

Tantangan utama teka-teki ini terletak pada kepadatan kata sebesar [KEPADATAN_GRID]% dan rasio persilangan kata yang mencapai [RASIO_INTERSEKSI]%. Dirancang oleh [NAMA_KREATOR], teka-teki ini menawarkan [JENIS_TANTANGAN] yang akan menguji batas kemampuan Anda.

Perkiraan waktu penyelesaian adalah [ESTIMASI_WAKTU] menit, menjadikannya aktivitas yang sempurna untuk [TARGET_PEMAIN] yang ingin meningkatkan [MANFAAT_KOGNITIF].

[CLOSING_PHRASE]`
];

/**
 * Extended template additions
 */
const EXTENDED_TEMPLATE_ADDITIONS = [
  `\n\n[NAMA_TTS] edisi [EDISI_VOLUME] ini menghadirkan [JUMLAH_KATA_BARU] kata baru yang belum pernah muncul pada edisi sebelumnya. Temukan [JUMLAH_KATA_TERSEMBUNYI] kata tersembunyi untuk mendapatkan [BONUS_HADIAH] istimewa!`,
  
  `\n\nEdisi [EDISI_VOLUME] dari [NAMA_TTS] ini memiliki [JUMLAH_KATA_BARU] kata yang dipilih khusus untuk menantang Anda. Dapatkan [BONUS_HADIAH] dengan menemukan [JUMLAH_KATA_TERSEMBUNYI] kata tersembunyi!`,
  
  `\n\nSebagai edisi ke-[EDISI_VOLUME], [NAMA_TTS] kali ini menyajikan [JUMLAH_KATA_BARU] kata baru dan [JUMLAH_KATA_TERSEMBUNYI] kata tersembunyi yang bisa memberi Anda [BONUS_HADIAH] tambahan!`
];

/**
 * Variasi sinonim untuk kata-kata umum
 */
const WORD_VARIATIONS: Record<string, string[]> = {
  'menantang': ['menantang', 'menguji', 'mengasah', 'memacu', 'merangsang'],
  'menarik': ['menarik', 'mengasyikkan', 'seru', 'memikat', 'menghibur'],
  'sulit': ['sulit', 'kompleks', 'rumit', 'tidak mudah', 'menantang'],
  'mudah': ['mudah', 'sederhana', 'ringan', 'tidak rumit', 'santai'],
  'sedang': ['sedang', 'moderat', 'menengah', 'standar', 'cukup menantang'],
  'pengetahuan': ['pengetahuan', 'wawasan', 'informasi', 'pemahaman', 'ilmu'],
  'meningkatkan': ['meningkatkan', 'mengembangkan', 'memperkuat', 'mempertajam', 'memperluas']
};

/**
 * Count the total number of clues in a crossword
 * @param clues The clues object from CrosswordState
 * @returns The total number of clues
 */
export const countTotalClues = (clues: { across: Record<number, string>; down: Record<number, string> }): number => {
  const acrossCount = Object.keys(clues.across).length;
  const downCount = Object.keys(clues.down).length;
  return acrossCount + downCount;
};

/**
 * Count the total number of filled cells in a crossword grid
 * @param grid The grid from CrosswordState
 * @returns The total number of filled cells
 */
const countFilledCells = (grid: { char: string; wordIds: number[] }[][]): number => {
  let count = 0;
  for (let row of grid) {
    for (let cell of row) {
      if (cell.char !== ' ' && cell.wordIds.length > 0) {
        count++;
      }
    }
  }
  return count;
};

/**
 * Calculate the grid density (percentage of filled cells)
 * @param grid The grid from CrosswordState
 * @returns The grid density as a percentage
 */
const calculateGridDensity = (grid: { char: string; wordIds: number[] }[][]): number => {
  const totalCells = grid.length * grid[0].length;
  const filledCells = countFilledCells(grid);
  return Math.round((filledCells / totalCells) * 100);
};

/**
 * Calculate the intersection ratio (average number of intersections per word)
 * @param grid The grid from CrosswordState
 * @param wordCount The number of words
 * @returns The intersection ratio
 */
const calculateIntersectionRatio = (grid: { char: string; wordIds: number[] }[][], wordCount: number): number => {
  let intersectionCount = 0;
  
  for (let row of grid) {
    for (let cell of row) {
      if (cell.wordIds.length > 1) {
        intersectionCount += cell.wordIds.length - 1;
      }
    }
  }
  
  return wordCount > 0 ? Math.round((intersectionCount / wordCount) * 10) / 10 : 0;
};

/**
 * Estimate the difficulty level based on grid size and word count
 * @param gridSize The size of the grid
 * @param wordCount The number of words
 * @returns The difficulty level in Indonesian
 */
const estimateDifficulty = (gridSize: number, wordCount: number): string => {
  if (gridSize <= 10 && wordCount <= 15) {
    return 'mudah';
  } else if (gridSize >= 15 && wordCount >= 25) {
    return 'sulit';
  } else {
    return 'sedang';
  }
};

/**
 * Estimate the time needed to complete the crossword
 * @param difficulty The difficulty level
 * @param clueCount The number of clues
 * @returns The estimated time in minutes
 */
const estimateTime = (difficulty: string, clueCount: number): number => {
  const baseTime = {
    'mudah': 10,
    'sedang': 15,
    'sulit': 25
  }[difficulty] || 15;
  
  return Math.max(5, Math.round(baseTime * (clueCount / 15)));
};

/**
 * Analyze words in the crossword
 * @param words The words array from CrosswordState
 * @returns Analysis results
 */
const analyzeWords = (words: any[]): { 
  longestWord: string, 
  shortestWord: string,
  rareLetters: string[]
} => {
  if (!words || words.length === 0) {
    return {
      longestWord: '',
      shortestWord: '',
      rareLetters: []
    };
  }
  
  const rareLettersInIndonesian = ['Q', 'X', 'Z', 'V', 'J'];
  const foundRareLetters = new Set<string>();
  
  // Extract word strings from words array (which might contain objects or strings)
  const wordStrings = words.map(w => typeof w === 'string' ? w : w.word);
  
  let longestWord = wordStrings[0];
  let shortestWord = wordStrings[0];
  
  for (const word of wordStrings) {
    if (word.length > longestWord.length) longestWord = word;
    if (word.length < shortestWord.length) shortestWord = word;
    
    // Check for rare letters
    for (const letter of word.toUpperCase()) {
      if (rareLettersInIndonesian.includes(letter)) {
        foundRareLetters.add(letter);
      }
    }
  }
  
  return {
    longestWord,
    shortestWord,
    rareLetters: Array.from(foundRareLetters)
  };
};

/**
 * Get a random variation of a word
 * @param word The original word
 * @returns A random variation or the original word
 */
const getRandomWordVariation = (word: string): string => {
  const variations = WORD_VARIATIONS[word.toLowerCase()];
  if (!variations || variations.length === 0) return word;
  
  const randomIndex = Math.floor(Math.random() * variations.length);
  return variations[randomIndex];
};

/**
 * Get a random opening phrase
 * @returns A random opening phrase
 */
const getRandomOpeningPhrase = (): string => {
  const randomIndex = Math.floor(Math.random() * OPENING_PHRASES.length);
  return OPENING_PHRASES[randomIndex];
};

/**
 * Get a random closing phrase
 * @returns A random closing phrase
 */
const getRandomClosingPhrase = (): string => {
  const randomIndex = Math.floor(Math.random() * CLOSING_PHRASES.length);
  return CLOSING_PHRASES[randomIndex];
};

/**
 * Get a random description template
 * @returns A random description template
 */
const getRandomDescriptionTemplate = (): string => {
  const randomIndex = Math.floor(Math.random() * DESCRIPTION_TEMPLATES.length);
  return DESCRIPTION_TEMPLATES[randomIndex];
};

/**
 * Get a random extended template addition
 * @returns A random extended template addition
 */
const getRandomExtendedTemplateAddition = (): string => {
  const randomIndex = Math.floor(Math.random() * EXTENDED_TEMPLATE_ADDITIONS.length);
  return EXTENDED_TEMPLATE_ADDITIONS[randomIndex];
};

/**
 * Apply word variations to a text
 * @param text The original text
 * @returns The text with word variations
 */
const applyWordVariations = (text: string): string => {
  let result = text;
  
  for (const [word, variations] of Object.entries(WORD_VARIATIONS)) {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    result = result.replace(regex, () => getRandomWordVariation(word));
  }
  
  return result;
};

/**
 * Generate a description for a crossword
 * @param title The title of the crossword
 * @param state The CrosswordState object
 * @param params Optional parameters to customize the description
 * @param useExtendedTemplate Whether to use the extended template with optional sections
 * @returns The generated description
 */
export const generateDescription = (
  title: string,
  state: CrosswordState,
  params: Partial<DescriptionParams> = {},
  useExtendedTemplate: boolean = false
): string => {
  // Calculate basic metrics
  const clueCount = countTotalClues(state.clues);
  const filledCells = countFilledCells(state.grid);
  const difficulty = params.TINGKAT_KESULITAN || estimateDifficulty(state.gridSize, state.words.length);
  
  // Calculate advanced metrics
  const gridDensity = calculateGridDensity(state.grid);
  const intersectionRatio = calculateIntersectionRatio(state.grid, state.words.length);
  
  // Analyze words
  const wordAnalysis = analyzeWords(state.words);
  
  // Get random phrases
  const openingPhrase = getRandomOpeningPhrase();
  const closingPhrase = getRandomClosingPhrase();
  
  // Combine default params with calculated values and provided params
  const finalParams: DescriptionParams = {
    ...defaultParams,
    NAMA_TTS: title,
    TINGKAT_KESULITAN: difficulty,
    JUMLAH_KOTAK: filledCells,
    JUMLAH_SOAL: clueCount,
    ESTIMASI_WAKTU: estimateTime(difficulty, clueCount),
    KATA_TERPANJANG: wordAnalysis.longestWord,
    KATA_TERPENDEK: wordAnalysis.shortestWord,
    HURUF_LANGKA: wordAnalysis.rareLetters,
    KEPADATAN_GRID: gridDensity,
    RASIO_INTERSEKSI: intersectionRatio,
    ...params
  };
  
  // Choose template
  let template = getRandomDescriptionTemplate();
  
  // Replace opening and closing phrases
  template = template.replace('[OPENING_PHRASE]', openingPhrase);
  template = template.replace('[CLOSING_PHRASE]', closingPhrase);
  
  // Add extended template if requested
  if (useExtendedTemplate) {
    const extendedAddition = getRandomExtendedTemplateAddition();
    template += extendedAddition;
    
    // Add information about longest word if available
    if (wordAnalysis.longestWord) {
      template += `\n\nTantangan spesial: temukan kata terpanjang "${wordAnalysis.longestWord}" yang tersembunyi dalam teka-teki ini!`;
    }
    
    // Add information about rare letters if available
    if (wordAnalysis.rareLetters.length > 0) {
      template += `\n\nTeka-teki ini mengandung huruf-huruf langka seperti ${wordAnalysis.rareLetters.join(', ')} yang akan menguji keterampilan Anda.`;
    }
  }
  
  // Replace placeholders with actual values
  let description = template;
  for (const [key, value] of Object.entries(finalParams)) {
    if (value !== undefined) {
      const regex = new RegExp(`\\[${key}\\]`, 'g');
      if (Array.isArray(value)) {
        // If value is an array (like HURUF_LANGKA), join it
        if (value.length > 0) {
          description = description.replace(regex, value.join(', '));
        }
      } else {
        description = description.replace(regex, String(value));
      }
    }
  }
  
  // Apply word variations for more uniqueness
  description = applyWordVariations(description);
  
  return description;
};
