import { toastUtils } from '../components/ui/ToastProvider';

/**
 * Error types for better error handling
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Standardized error structure
 */
export interface AppError {
  type: ErrorType;
  message: string;
  statusCode?: number;
  originalError?: any;
}

/**
 * Map HTTP status codes to error types
 */
const mapStatusToErrorType = (status: number): ErrorType => {
  if (status === 404) return ErrorType.NOT_FOUND;
  if (status === 401) return ErrorType.AUTHENTICATION;
  if (status === 403) return ErrorType.AUTHORIZATION;
  if (status >= 400 && status < 500) return ErrorType.VALIDATION;
  if (status >= 500) return ErrorType.SERVER;
  return ErrorType.UNKNOWN;
};

/**
 * Get user-friendly error message based on error type and status code
 */
export const getUserFriendlyErrorMessage = (error: AppError): string => {
  // Default messages for each error type
  const defaultMessages = {
    [ErrorType.NETWORK]: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
    [ErrorType.SERVER]: 'Terjadi kesalahan pada server. Silakan coba lagi nanti.',
    [ErrorType.VALIDATION]: 'Data yang Anda masukkan tidak valid.',
    [ErrorType.AUTHENTICATION]: 'Sesi Anda telah berakhir. Silakan login kembali.',
    [ErrorType.AUTHORIZATION]: 'Anda tidak memiliki izin untuk mengakses halaman ini.',
    [ErrorType.NOT_FOUND]: 'Data yang Anda cari tidak ditemukan.',
    [ErrorType.TIMEOUT]: 'Permintaan memakan waktu terlalu lama. Silakan coba lagi.',
    [ErrorType.UNKNOWN]: 'Terjadi kesalahan yang tidak diketahui.'
  };

  // Use custom message if available, otherwise use default message
  return error.message || defaultMessages[error.type];
};

/**
 * Create a standardized error object
 */
export const createAppError = (
  type: ErrorType,
  message: string = '',
  statusCode?: number,
  originalError?: any
): AppError => {
  return {
    type,
    message,
    statusCode,
    originalError
  };
};

/**
 * Handle fetch errors and convert to standardized AppError
 */
export const handleFetchError = (error: any): AppError => {
  // Network errors (no response)
  if (error instanceof TypeError && error.message === 'Failed to fetch') {
    return createAppError(
      ErrorType.NETWORK,
      'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
      undefined,
      error
    );
  }

  // Timeout errors
  if (error.name === 'AbortError') {
    return createAppError(
      ErrorType.TIMEOUT,
      'Permintaan memakan waktu terlalu lama. Silakan coba lagi.',
      undefined,
      error
    );
  }

  // If it's already an AppError, return it
  if (error.type && Object.values(ErrorType).includes(error.type)) {
    return error as AppError;
  }

  // Default to unknown error
  return createAppError(
    ErrorType.UNKNOWN,
    error.message || 'Terjadi kesalahan yang tidak diketahui.',
    undefined,
    error
  );
};

/**
 * Handle API response errors
 */
export const handleApiResponseError = (response: Response): AppError => {
  const errorType = mapStatusToErrorType(response.status);
  let message = '';

  switch (response.status) {
    case 404:
      message = 'Data yang Anda cari tidak ditemukan.';
      break;
    case 401:
      message = 'Sesi Anda telah berakhir. Silakan login kembali.';
      break;
    case 403:
      message = 'Anda tidak memiliki izin untuk mengakses data ini.';
      break;
    case 500:
      message = 'Terjadi kesalahan pada server. Silakan coba lagi nanti.';
      break;
    default:
      message = `Error ${response.status}: ${response.statusText}`;
  }

  return createAppError(errorType, message, response.status);
};

/**
 * Show appropriate error toast based on error type
 */
export const showErrorToast = (error: AppError): void => {
  const message = getUserFriendlyErrorMessage(error);
  toastUtils.error(message);
};

/**
 * Log error to console with additional context
 */
export const logError = (error: AppError, context?: string): void => {
  console.error(
    `[${error.type}]${context ? ` [${context}]` : ''} ${error.message}`,
    error.originalError || ''
  );
};
