/**
 * Session Initialization Utility
 *
 * This utility is kept for backward compatibility but doesn't do anything
 * since we're using SameSite cookies for CSRF protection.
 */

/**
 * Initialize session security
 *
 * This function is a no-op since we're using SameSite cookies for CSRF protection.
 * It's kept for backward compatibility.
 */
export const initCsrf = async (): Promise<void> => {
  // No-op function kept for backward compatibility
};

export default initCsrf;
