/**
 * Utility functions for SEO-related operations
 * Extracted to reduce bundle size and improve code organization
 */

/**
 * Get structured data for the homepage
 * @returns Array of structured data objects for the homepage
 */
export const getStructuredData = (): Record<string, any>[] => {
  const origin = typeof window !== 'undefined' ? window.location.origin : '';

  // Website structured data - Enhanced with more properties
  const websiteStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Tekateki.io",
    "url": origin,
    "potentialAction": {
      "@type": "SearchAction",
      "target": `${origin}/play?search={search_term_string}`,
      "query-input": "required name=search_term_string"
    },
    "description": "Platform teka-teki silang online gratis dalam bahasa Indonesia. Mainkan ribuan teka-teki silang atau buat sendiri dan bagikan ke dunia.",
    "inLanguage": "id-ID",
    "publisher": {
      "@type": "Organization",
      "name": "Tekateki.io",
      "logo": {
        "@type": "ImageObject",
        "url": `${origin}/images/logo.png`,
        "width": "192",
        "height": "192"
      }
    }
  };

  // Organization structured data - Enhanced with more properties
  const organizationStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Tekateki.io",
    "url": origin,
    "logo": {
      "@type": "ImageObject",
      "url": `${origin}/images/logo.png`,
      "width": "192",
      "height": "192"
    },
    "sameAs": [
      "https://facebook.com/tekateki.io",
      "https://twitter.com/tekateki_io",
      "https://instagram.com/tekateki.io"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer support",
      "email": "<EMAIL>"
    },
    "description": "Platform teka-teki silang online terbaik di Indonesia. Kami menyediakan ribuan teka-teki silang gratis dalam bahasa Indonesia."
  };

  // Breadcrumb structured data
  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Beranda",
        "item": origin
      }
    ]
  };

  // FAQ structured data - New addition
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Apa itu Teka-Teki Silang Online?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Teka-Teki Silang Online adalah platform permainan kata silang interaktif berbahasa Indonesia yang dapat dimainkan langsung di browser tanpa perlu mengunduh aplikasi. Tersedia ribuan puzzle dengan berbagai tema dan tingkat kesulitan."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah bermain teka-teki silang gratis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, semua teka-teki silang di platform kami dapat dimainkan secara gratis. Anda dapat mengakses ribuan puzzle tanpa biaya apapun."
        }
      },
      {
        "@type": "Question",
        "name": "Apa manfaat bermain teka-teki silang?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Bermain teka-teki silang memiliki banyak manfaat, di antaranya meningkatkan kosakata, melatih kemampuan berpikir logis, meningkatkan konsentrasi, mencegah penurunan fungsi kognitif, dan sebagai sarana hiburan yang mendidik."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana cara bermain teka-teki silang online?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Cukup kunjungi halaman 'Main' di website kami, pilih teka-teki yang ingin dimainkan, dan mulai mengisi kotak-kotak dengan jawaban berdasarkan petunjuk yang diberikan. Anda dapat bermain di komputer maupun perangkat mobile."
        }
      },
      {
        "@type": "Question",
        "name": "Bisakah saya membuat teka-teki silang sendiri?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, Anda dapat membuat teka-teki silang sendiri dengan mendaftar akun di platform kami. Setelah login, kunjungi halaman 'Buat TTS' dan ikuti panduan yang tersedia."
        }
      }
    ]
  };

  // HowTo structured data - New addition
  const howToStructuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "Cara Bermain Teka-Teki Silang Online",
    "description": "Panduan langkah demi langkah untuk bermain teka-teki silang online di platform kami.",
    "totalTime": "PT5M",
    "tool": {
      "@type": "HowToTool",
      "name": "Komputer atau perangkat mobile dengan koneksi internet"
    },
    "step": [
      {
        "@type": "HowToStep",
        "name": "Pilih Teka-Teki",
        "text": "Kunjungi halaman 'Main' dan pilih teka-teki silang yang ingin dimainkan berdasarkan kategori atau tingkat kesulitan.",
        "url": `${origin}/play`,
        "image": `${origin}/images/howto-step1.jpg`
      },
      {
        "@type": "HowToStep",
        "name": "Baca Petunjuk",
        "text": "Baca petunjuk 'Mendatar' dan 'Menurun' untuk memahami kata-kata yang harus diisi.",
        "url": `${origin}/play`,
        "image": `${origin}/images/howto-step2.jpg`
      },
      {
        "@type": "HowToStep",
        "name": "Isi Kotak-Kotak",
        "text": "Klik pada kotak yang ingin diisi dan ketikkan jawaban berdasarkan petunjuk yang diberikan.",
        "url": `${origin}/play`,
        "image": `${origin}/images/howto-step3.jpg`
      },
      {
        "@type": "HowToStep",
        "name": "Periksa Jawaban",
        "text": "Setelah selesai mengisi semua kotak, klik tombol 'Periksa' untuk melihat apakah jawaban Anda benar.",
        "url": `${origin}/play`,
        "image": `${origin}/images/howto-step4.jpg`
      }
    ]
  };

  // Return combined structured data
  return [
    websiteStructuredData,
    organizationStructuredData,
    breadcrumbStructuredData,
    faqStructuredData,
    howToStructuredData
  ];
};
