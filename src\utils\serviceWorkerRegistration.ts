/**
 * Service Worker Registration
 * Mendukung offline play dan caching resource
 */

// Kunci untuk menyimpan versi service worker di localStorage
const SW_VERSION_KEY = 'tts_sw_version';

// Periksa apakah service worker didukung oleh browser
export function isServiceWorkerSupported(): boolean {
  return 'serviceWorker' in navigator;
}

// Dapatkan versi service worker yang tersimpan
function getSavedServiceWorkerVersion(): string | null {
  try {
    return localStorage.getItem(SW_VERSION_KEY);
  } catch (e) {
    console.warn('Failed to get service worker version from localStorage:', e);
    return null;
  }
}

// Simpan versi service worker
function saveServiceWorkerVersion(version: string): void {
  try {
    localStorage.setItem(SW_VERSION_KEY, version);
  } catch (e) {
    console.warn('Failed to save service worker version to localStorage:', e);
  }
}

// Periksa versi service worker yang sedang aktif
async function checkServiceWorkerVersion(registration: ServiceWorkerRegistration): Promise<string | null> {
  if (!navigator.serviceWorker.controller) {
    return null;
  }

  return new Promise((resolve) => {
    // Buat channel untuk komunikasi dengan service worker
    const messageChannel = new MessageChannel();

    // Set up handler untuk menerima pesan dari service worker
    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.type === 'VERSION') {
        resolve(event.data.version);
      } else {
        resolve(null);
      }
    };

    // Kirim pesan ke service worker untuk mendapatkan versi
    navigator.serviceWorker.controller.postMessage(
      { type: 'GET_VERSION' },
      [messageChannel.port2]
    );

    // Set timeout untuk menghindari hanging jika tidak ada respons
    setTimeout(() => resolve(null), 1000);
  });
}

// Daftar service worker dengan penanganan error yang lebih baik
export function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (!isServiceWorkerSupported()) {
    console.log('Service workers are not supported by this browser');
    return Promise.resolve(null);
  }

  // Gunakan URL statis untuk service worker
  const swUrl = '/service-worker.js';

  return navigator.serviceWorker
    .register(swUrl, {
      scope: '/',
      // Gunakan updateViaCache: 'all' untuk menghindari pembaruan yang tidak perlu
      updateViaCache: 'all'
    })
    .then(async (registration) => {
      console.log('Service worker registered successfully:', registration.scope);

      // Dapatkan versi service worker yang tersimpan
      const savedVersion = getSavedServiceWorkerVersion();

      // Periksa versi service worker yang sedang aktif
      const currentVersion = await checkServiceWorkerVersion(registration);

      console.log('Service worker versions - Saved:', savedVersion, 'Current:', currentVersion);

      // Jika versi berbeda dan keduanya ada, berarti ada pembaruan
      const hasUpdate = savedVersion && currentVersion && savedVersion !== currentVersion;

      // Simpan versi saat ini jika ada
      if (currentVersion) {
        saveServiceWorkerVersion(currentVersion);
      }

      // Periksa pembaruan service worker
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;

        if (newWorker) {
          // Log perubahan state service worker
          newWorker.addEventListener('statechange', async () => {
            console.log(`Service worker state changed to: ${newWorker.state}`);

            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // Periksa versi service worker yang baru diinstal
              const newVersion = await checkServiceWorkerVersion(registration);
              console.log('New service worker version:', newVersion);

              // Jika versi berbeda dari yang tersimpan, berarti ada pembaruan
              if (newVersion && savedVersion && newVersion !== savedVersion) {
                console.log('New service worker available with version change');

                // Simpan versi baru
                saveServiceWorkerVersion(newVersion);

                // Tampilkan notifikasi pembaruan ke pengguna
                showUpdateNotification();
              }
            }

            if (newWorker.state === 'redundant') {
              console.warn('Service worker became redundant - it may have failed during installation');
            }
          });

          // Tangani error pada service worker
          newWorker.addEventListener('error', (event) => {
            console.error('Service worker installation error:', event);
          });
        }
      });

      // Tampilkan notifikasi pembaruan jika terdeteksi pembaruan versi
      if (hasUpdate) {
        console.log('Service worker version change detected during registration');
        showUpdateNotification();
      }

      return registration;
    })
    .catch((error) => {
      console.error('Service worker registration failed:', error);
      return null;
    });
}

// Kunci untuk menandai bahwa notifikasi pembaruan sudah ditampilkan
const UPDATE_NOTIFICATION_SHOWN_KEY = 'tts_update_notification_shown';

// Tampilkan notifikasi pembaruan
function showUpdateNotification(): void {
  // Periksa apakah notifikasi sudah ditampilkan dalam sesi ini
  try {
    const notificationShown = sessionStorage.getItem(UPDATE_NOTIFICATION_SHOWN_KEY);
    if (notificationShown === 'true') {
      console.log('Update notification already shown in this session, skipping');
      return;
    }

    // Tandai bahwa notifikasi sudah ditampilkan
    sessionStorage.setItem(UPDATE_NOTIFICATION_SHOWN_KEY, 'true');

    // Implementasi notifikasi pembaruan
    // Bisa menggunakan toast, alert, atau komponen notifikasi lainnya

    // Contoh sederhana menggunakan confirm
    if (confirm('Aplikasi telah diperbarui. Muat ulang untuk menggunakan versi terbaru?')) {
      // Hapus penanda notifikasi sebelum reload
      sessionStorage.removeItem(UPDATE_NOTIFICATION_SHOWN_KEY);
      window.location.reload();
    }
  } catch (e) {
    console.warn('Failed to check or set update notification status:', e);

    // Fallback jika sessionStorage tidak tersedia
    if (confirm('Aplikasi telah diperbarui. Muat ulang untuk menggunakan versi terbaru?')) {
      window.location.reload();
    }
  }
}

// Perbarui service worker
export function updateServiceWorker(): void {
  if (!isServiceWorkerSupported()) {
    return;
  }

  navigator.serviceWorker.ready.then((registration) => {
    registration.update();
  });
}

// Kirim pesan ke service worker
export function sendMessageToServiceWorker(message: any): void {
  if (!isServiceWorkerSupported() || !navigator.serviceWorker.controller) {
    return;
  }

  navigator.serviceWorker.controller.postMessage(message);
}

// Periksa status koneksi
export function isOnline(): boolean {
  return navigator.onLine;
}

// Tambahkan listener untuk perubahan status koneksi
export function addConnectionStatusListeners(
  onlineCallback: () => void,
  offlineCallback: () => void
): void {
  window.addEventListener('online', onlineCallback);
  window.addEventListener('offline', offlineCallback);
}

// Hapus listener untuk perubahan status koneksi
export function removeConnectionStatusListeners(
  onlineCallback: () => void,
  offlineCallback: () => void
): void {
  window.removeEventListener('online', onlineCallback);
  window.removeEventListener('offline', offlineCallback);
}

// Reset penanda notifikasi pembaruan
function resetUpdateNotificationFlag(): void {
  try {
    // Hapus penanda notifikasi saat halaman dimuat
    // Ini memastikan notifikasi bisa muncul lagi jika ada pembaruan baru
    sessionStorage.removeItem(UPDATE_NOTIFICATION_SHOWN_KEY);
  } catch (e) {
    console.warn('Failed to reset update notification flag:', e);
  }
}

// Inisialisasi service worker dan status koneksi
export function initServiceWorkerAndConnectionStatus(
  onOnline: () => void = () => {},
  onOffline: () => void = () => {}
): void {
  // Reset penanda notifikasi pembaruan
  resetUpdateNotificationFlag();

  // Daftar service worker
  registerServiceWorker();

  // Tambahkan listener untuk perubahan status koneksi
  addConnectionStatusListeners(onOnline, onOffline);

  // Periksa status koneksi awal
  if (isOnline()) {
    onOnline();
  } else {
    onOffline();
  }
}
