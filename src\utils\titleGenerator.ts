import { CrosswordState } from '../types/crossword';
import { countTotalClues } from './descriptionGeneratorV2';

/**
 * Interface untuk parameter judul
 */
interface TitleParams {
  KATEGORI?: string;
  TINGKAT_KESULITAN?: string;
  JUMLAH_SOAL?: number;
  UKURAN_GRID?: number;
  KATA_KUNCI?: string[];
  TEMA_SPESIFIK?: string;
}

/**
 * Pola-pola judul
 */
const TITLE_PATTERNS = [
  "TTS [KATEGORI] [TINGKAT_KESULITAN]",
  "Teka-<PERSON><PERSON> [KATEGORI] [TINGKAT_KESULITAN]",
  "TTS [KATEGORI] - [JUMLAH_SOAL] Soal",
  "Tantangan [KATEGORI] Level [TINGKAT_KESULITAN]",
  "[KATEGORI] Edisi [TEMA_SPESIFIK]",
  "Puzzle [KATEGORI] [TINGKAT_KESULITAN]",
  "TTS [UKURAN_GRID]x[UKURAN_GRID] [KATEGORI]",
  "Asah Otak: [KATEGORI] [TINGKAT_KESULITAN]",
  "Permainan Kata [KATEGORI]",
  "Silang Kata [KATEGORI] [TINGKAT_KESULITAN]"
];

/**
 * Variasi kata untuk tingkat kesulitan
 */
const DIFFICULTY_VARIATIONS: Record<string, string[]> = {
  'mudah': ['Mudah', 'Pemula', 'Dasar', 'Santai', 'Ringan'],
  'sedang': ['Sedang', 'Menengah', 'Standar', 'Medium', 'Moderat'],
  'sulit': ['Sulit', 'Ahli', 'Pakar', 'Lanjutan', 'Menantang', 'Expert']
};

/**
 * Variasi kata untuk kategori
 */
const CATEGORY_VARIATIONS: Record<string, string[]> = {
  'umum': ['Pengetahuan Umum', 'General Knowledge', 'Umum', 'Serba-Serbi'],
  'film': ['Film', 'Perfilman', 'Sinema', 'Movie', 'Hiburan Layar'],
  'sains': ['Sains', 'Ilmu Pengetahuan', 'Teknologi', 'Science', 'Penemuan'],
  'sejarah': ['Sejarah', 'Historis', 'Masa Lalu', 'Peristiwa Bersejarah'],
  'olahraga': ['Olahraga', 'Sport', 'Atletik', 'Kompetisi'],
  'musik': ['Musik', 'Lagu', 'Melodi', 'Nada', 'Irama'],
  'sastra': ['Sastra', 'Literatur', 'Buku', 'Karya Tulis'],
  'kesehatan': ['Kesehatan', 'Medis', 'Wellness', 'Hidup Sehat']
};

/**
 * Tema spesifik berdasarkan kategori
 */
const SPECIFIC_THEMES: Record<string, string[]> = {
  'umum': ['Trivia', 'Fakta Menarik', 'Pengetahuan Populer', 'Wawasan Dunia'],
  'film': ['Hollywood', 'Perfilman Indonesia', 'Box Office', 'Klasik', 'Blockbuster'],
  'sains': ['Penemuan', 'Inovasi', 'Teknologi Modern', 'Alam Semesta', 'Biologi'],
  'sejarah': ['Kemerdekaan', 'Peradaban Kuno', 'Tokoh Bersejarah', 'Perang Dunia'],
  'olahraga': ['Olimpiade', 'Piala Dunia', 'Bola', 'Atletik', 'Olahraga Air'],
  'musik': ['Pop', 'Rock', 'Klasik', 'Jazz', 'Dangdut', 'Musisi Legendaris'],
  'sastra': ['Puisi', 'Novel', 'Penulis Terkenal', 'Karya Klasik', 'Sastra Modern'],
  'kesehatan': ['Nutrisi', 'Kebugaran', 'Kesehatan Mental', 'Gaya Hidup Sehat']
};

/**
 * Mengekstrak kata kunci dari kata-kata dalam teka-teki
 * @param words Array kata-kata dalam teka-teki
 * @returns Array kata kunci yang diekstrak
 */
const extractKeywords = (words: any[]): string[] => {
  if (!words || words.length === 0) return [];

  // Ambil kata-kata yang panjangnya lebih dari 5 huruf
  const wordStrings = words.map(w => typeof w === 'string' ? w : w.word);
  const longWords = wordStrings.filter(word => word && word.length > 5);

  // Ambil maksimal 3 kata terpanjang
  return longWords
    .sort((a, b) => b.length - a.length)
    .slice(0, 3);
};

/**
 * Mendapatkan variasi acak untuk kategori
 * @param categoryId ID kategori
 * @param categoryName Nama kategori
 * @returns Variasi nama kategori
 */
const getRandomCategoryVariation = (categoryId: string, categoryName: string): string => {
  const variations = CATEGORY_VARIATIONS[categoryId];
  if (!variations || variations.length === 0) return categoryName;

  const randomIndex = Math.floor(Math.random() * variations.length);
  return variations[randomIndex];
};

/**
 * Mendapatkan variasi acak untuk tingkat kesulitan
 * @param difficulty Tingkat kesulitan
 * @returns Variasi nama tingkat kesulitan
 */
const getRandomDifficultyVariation = (difficulty: string): string => {
  const variations = DIFFICULTY_VARIATIONS[difficulty];
  if (!variations || variations.length === 0) return difficulty;

  const randomIndex = Math.floor(Math.random() * variations.length);
  return variations[randomIndex];
};

/**
 * Mendapatkan tema spesifik acak berdasarkan kategori
 * @param categoryId ID kategori
 * @returns Tema spesifik
 */
const getRandomSpecificTheme = (categoryId: string): string => {
  const themes = SPECIFIC_THEMES[categoryId];
  if (!themes || themes.length === 0) return '';

  const randomIndex = Math.floor(Math.random() * themes.length);
  return themes[randomIndex];
};

/**
 * Generate judul untuk teka-teki silang
 * @param state State teka-teki silang
 * @param categoryId ID kategori
 * @param categoryName Nama kategori
 * @param difficulty Tingkat kesulitan
 * @returns Judul yang digenerate
 */
export const generateTitle = (
  state: CrosswordState,
  categoryId: string = 'umum',
  categoryName: string = 'Umum',
  difficulty: string = 'sedang'
): string => {
  // Hitung jumlah soal
  const clueCount = countTotalClues(state.clues);

  // Ekstrak kata kunci dari kata-kata dalam teka-teki
  const keywords = extractKeywords(state.words);

  // Siapkan parameter untuk judul
  const params: TitleParams = {
    KATEGORI: getRandomCategoryVariation(categoryId, categoryName),
    TINGKAT_KESULITAN: getRandomDifficultyVariation(difficulty),
    JUMLAH_SOAL: clueCount,
    UKURAN_GRID: state.gridSize,
    KATA_KUNCI: keywords,
    TEMA_SPESIFIK: getRandomSpecificTheme(categoryId)
  };

  // Pilih pola judul secara acak
  const randomIndex = Math.floor(Math.random() * TITLE_PATTERNS.length);
  let titlePattern = TITLE_PATTERNS[randomIndex];

  // Jika tidak ada tema spesifik, ganti pola yang menggunakan tema spesifik
  if (!params.TEMA_SPESIFIK && titlePattern.includes('[TEMA_SPESIFIK]')) {
    titlePattern = TITLE_PATTERNS[0]; // Gunakan pola default
  }

  // Ganti placeholder dengan nilai sebenarnya
  let title = titlePattern;
  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined) {
      const regex = new RegExp(`\\[${key}\\]`, 'g');
      if (Array.isArray(value)) {
        // Jika nilai adalah array (seperti KATA_KUNCI), gunakan nilai pertama
        if (value.length > 0) {
          title = title.replace(regex, value[0]);
        }
      } else {
        title = title.replace(regex, String(value));
      }
    }
  }

  return title;
};
