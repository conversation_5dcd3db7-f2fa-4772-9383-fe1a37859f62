/**
 * Menganalisis kompatibilitas kata-kata untuk teka-teki silang
 * @param words Array kata-kata untuk dianalisis
 * @returns Skor kompatibilitas dan rekomendasi
 */
export function analyzeWordCompatibility(words: string[]): { 
  score: number; 
  recommendations: string[] 
} {
  if (words.length === 0) {
    return { score: 0, recommendations: [] };
  }
  
  // Hitung frekuensi huruf
  const letterFrequency: Record<string, number> = {};
  words.forEach(word => {
    for (const char of word) {
      letterFrequency[char] = (letterFrequency[char] || 0) + 1;
    }
  });
  
  // Hitung skor kompatibilitas berdasarkan frekuensi huruf umum
  const commonLetters = ['A', 'E', 'I', 'N', 'R', 'S', 'T'];
  let commonLetterCount = 0;
  let totalLetters = 0;
  
  for (const letter in letterFrequency) {
    totalLetters += letterFrequency[letter];
    if (commonLetters.includes(letter)) {
      commonLetterCount += letterFrequency[letter];
    }
  }
  
  // Hitung skor kompatibilitas (0-100)
  const commonLetterRatio = commonLetterCount / totalLetters;
  const score = Math.round(commonLetterRatio * 100);
  
  // Buat rekomendasi
  const recommendations: string[] = [];
  
  // Periksa panjang kata
  const shortWords = words.filter(word => word.length < 4);
  const longWords = words.filter(word => word.length > 10);
  
  if (shortWords.length > words.length * 0.3) {
    recommendations.push('Tambahkan lebih banyak kata dengan panjang sedang (4-8 huruf)');
  }
  
  if (longWords.length > words.length * 0.3) {
    recommendations.push('Tambahkan lebih banyak kata dengan panjang sedang (4-8 huruf)');
  }
  
  // Periksa huruf yang jarang
  const rareLetters = ['Q', 'X', 'Z', 'J', 'K', 'V', 'W', 'Y'];
  let rareLetterCount = 0;
  
  for (const letter of rareLetters) {
    rareLetterCount += letterFrequency[letter] || 0;
  }
  
  if (rareLetterCount > totalLetters * 0.15) {
    recommendations.push('Kurangi penggunaan huruf yang jarang (Q, X, Z, J, K, V, W, Y)');
  }
  
  // Jika skor rendah, tambahkan rekomendasi umum
  if (score < 50) {
    recommendations.push('Tambahkan kata-kata dengan huruf umum (A, E, I, N, R, S, T)');
  }
  
  // Jika tidak ada masalah, berikan pujian
  if (recommendations.length === 0) {
    recommendations.push('Kata-kata Anda memiliki kompatibilitas yang baik untuk teka-teki silang!');
  }
  
  return { score, recommendations };
}
