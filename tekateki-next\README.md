# TekaTeki - Aplikasi Teka-Teki Silang Indonesia

TekaTeki adalah aplikasi teka-teki silang interaktif dalam bahasa Indonesia yang dibangun dengan Next.js. Aplikasi ini menyediakan berbagai teka-teki silang dengan tema dan tingkat kesulitan yang berbeda untuk mengasah kemampuan bahasa dan pengetahuan umum pengguna.

## Fitur Utama

- **Koleksi Teka-Teki <PERSON>**: Berbagai teka-teki silang dengan tema dan tingkat kesulitan yang berbeda
- **Kategori**: Pengelompokan teka-teki silang berdasarkan tema
- **Permainan Interaktif**: Antarmuka permainan yang intuitif dengan fitur petunjuk, pem<PERSON><PERSON><PERSON> jawaban, dan pelacakan kemajuan
- **Sistem Pengguna**: Registrasi, login, dan profil pengguna
- **Pelacakan Kemajuan**: Menyimpan dan menampilkan kemajuan pengguna dalam menyelesaikan teka-teki silang
- **Blog**: Art<PERSON>l dan tips seputar teka-teki silang
- **SEO Friendly**: Optimasi mesin pencari dengan metadata, sitemap, dan URL yang SEO-friendly

## Teknologi

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: API PHP (terpisah)
- **Autentikasi**: JWT, Google OAuth
- **SEO**: Next.js Metadata, Sitemap, Robots.txt

## Memulai

### Prasyarat

- Node.js 18.0.0 atau lebih baru
- npm atau yarn
- API backend yang berjalan di http://localhost:1111/api

### Instalasi

1. Clone repositori
   ```bash
   git clone https://github.com/username/tekateki-next.git
   cd tekateki-next
   ```

2. Instal dependensi
   ```bash
   npm install
   # atau
   yarn install
   ```

3. Jalankan server pengembangan
   ```bash
   npm run dev
   # atau
   yarn dev
   ```

4. Buka [http://localhost:3000](http://localhost:3000) di browser Anda

## Struktur Proyek

```
tekateki-next/
├── public/             # Aset statis
├── src/
│   ├── app/            # Halaman aplikasi (Next.js App Router)
│   ├── components/     # Komponen React
│   ├── context/        # Konteks React
│   └── lib/            # Utilitas dan fungsi API
├── .gitignore
├── next.config.ts      # Konfigurasi Next.js
├── package.json
├── README.md
└── tailwind.config.ts  # Konfigurasi Tailwind CSS
```

## Deployment

Untuk men-deploy aplikasi ke cPanel:

1. Jalankan script deployment
   ```bash
   deploy.bat
   ```

2. Upload file zip yang dihasilkan ke cPanel
   - Login ke cPanel
   - Buka File Manager
   - Navigasi ke folder public_html atau folder khusus untuk aplikasi ini
   - Upload dan ekstrak file tekateki-next.zip
   - Pastikan file .htaccess sudah berada di folder yang sama dengan file-file aplikasi

## Kontribusi

Kontribusi selalu diterima! Silakan buat pull request atau buka issue untuk perbaikan atau fitur baru.

## Lisensi

Proyek ini dilisensikan di bawah Lisensi MIT - lihat file LICENSE untuk detail lebih lanjut.

## Kontak

Email: <EMAIL>
