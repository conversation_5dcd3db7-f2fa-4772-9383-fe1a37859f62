@echo off
echo <PERSON><PERSON><PERSON> proses deployment TekaTeki Next.js ke cPanel...

REM 1. Bersihkan cache dan node_modules untuk build yang bersih
echo Membersihkan cache dan node_modules...
if exist ".next" rd /s /q .next
if exist "node_modules\.cache" rd /s /q node_modules\.cache

REM 2. Install dependencies
echo Menginstall dependencies...
call npm ci

REM 3. Build aplikasi Next.js dengan optimasi produksi
echo Building aplikasi Next.js untuk produksi...
set NODE_ENV=production
call npm run build

REM 4. Buat folder deploy jika belum ada
if not exist "deploy" mkdir deploy

REM 5. Buat file .htaccess untuk cPanel dengan optimasi caching
echo Membuat file .htaccess dengan optimasi caching...
(
echo RewriteEngine On
echo.
echo # Handle Next.js routes
echo RewriteRule ^_next/(.*)$ /_next/$1 [L]
echo RewriteRule ^api/(.*)$ /api/$1 [L]
echo RewriteRule ^static/(.*)$ /static/$1 [L]
echo RewriteRule ^.*$ /index.html [L]
echo.
echo # Enable compression
echo ^<IfModule mod_deflate.c^>
echo   AddOutputFilterByType DEFLATE text/plain
echo   AddOutputFilterByType DEFLATE text/html
echo   AddOutputFilterByType DEFLATE text/xml
echo   AddOutputFilterByType DEFLATE text/css
echo   AddOutputFilterByType DEFLATE application/xml
echo   AddOutputFilterByType DEFLATE application/xhtml+xml
echo   AddOutputFilterByType DEFLATE application/rss+xml
echo   AddOutputFilterByType DEFLATE application/javascript
echo   AddOutputFilterByType DEFLATE application/x-javascript
echo   AddOutputFilterByType DEFLATE application/json
echo ^</IfModule^>
echo.
echo # Set caching headers
echo ^<IfModule mod_expires.c^>
echo   ExpiresActive On
echo.
echo   # Images
echo   ExpiresByType image/jpeg "access plus 1 year"
echo   ExpiresByType image/gif "access plus 1 year"
echo   ExpiresByType image/png "access plus 1 year"
echo   ExpiresByType image/webp "access plus 1 year"
echo   ExpiresByType image/svg+xml "access plus 1 year"
echo   ExpiresByType image/x-icon "access plus 1 year"
echo.
echo   # CSS, JavaScript
echo   ExpiresByType text/css "access plus 1 month"
echo   ExpiresByType text/javascript "access plus 1 month"
echo   ExpiresByType application/javascript "access plus 1 month"
echo.
echo   # Next.js static assets
echo   ^<FilesMatch "^_next/static/.*$"^>
echo     ExpiresDefault "access plus 1 year"
echo   ^</FilesMatch^>
echo ^</IfModule^>
) > .next\.htaccess

REM 6. Kompres folder .next untuk upload
echo Mengompres folder .next untuk upload...
powershell -command "Compress-Archive -Path .next\* -DestinationPath deploy\tekateki-next.zip -Force"

REM 7. Buat file robots.txt untuk SEO
echo Membuat robots.txt untuk SEO...
(
echo User-agent: *
echo Allow: /
echo.
echo Sitemap: https://tekateki.id/sitemap.xml
) > .next\robots.txt

REM 8. Tambahkan robots.txt ke zip
powershell -command "Compress-Archive -Path .next\robots.txt -Update -DestinationPath deploy\tekateki-next.zip"

echo Deployment selesai! File zip tersedia di folder deploy\tekateki-next.zip
echo.
echo Petunjuk upload ke cPanel:
echo 1. Login ke cPanel
echo 2. Buka File Manager
echo 3. Navigasi ke folder public_html atau folder khusus untuk aplikasi ini
echo 4. Upload dan ekstrak file tekateki-next.zip
echo 5. Pastikan file .htaccess dan robots.txt sudah berada di folder yang sama dengan file-file aplikasi
echo.
echo Selamat! Aplikasi TekaTeki Next.js Anda siap digunakan.
