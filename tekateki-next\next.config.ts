import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* Performance optimizations */
  images: {
    domains: ['picsum.photos', 'example.com', 'tts-api.widiyanata.com'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
  },
  // Enable React strict mode for better development experience
  reactStrictMode: true,
  // Optimize production builds
  swcMinify: true,
  // Configure compression
  compress: true,
  // Configure output options
  output: 'standalone',
  // Configure webpack for better performance
  webpack: (config, { dev, isServer }) => {
    // Only run in production client builds
    if (!dev && !isServer) {
      // Enable tree shaking and dead code elimination
      config.optimization.usedExports = true;

      // Split chunks for better caching
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: 25,
        minSize: 20000,
      };
    }

    return config;
  },
  // Configure production environment variables
  env: {
    API_BASE_URL: 'https://tts-api.widiyanata.com/api',
  },
  // Configure powered by header
  poweredByHeader: false,
};

export default nextConfig;
