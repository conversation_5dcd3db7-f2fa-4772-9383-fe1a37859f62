{"name": "tekateki-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "deploy.bat"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.2", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "sharp": "^0.34.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.6.0", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "@eslint/eslintrc": "^3", "postcss": "^8.4.31", "autoprefixer": "^10.4.16"}}