import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { blogAPI } from "@/lib/api";

// Fungsi untuk menghasilkan metadata dinamis
export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  // Pastikan params.slug diawaitkan terlebih dahulu
  const slug = params.slug;

  try {
    const response = await blogAPI.getBySlug(slug);

    if (response.status === 'success' && response.data) {
      const blog = response.data;

      return {
        title: `${blog.title} | Blog TekaTeki Indonesia`,
        description: blog.excerpt || `Baca artikel "${blog.title}" di blog TekaTeki Indonesia.`,
        openGraph: {
          title: `${blog.title} | Blog TekaTeki Indonesia`,
          description: blog.excerpt || `Baca artikel "${blog.title}" di blog TekaTeki Indonesia.`,
          url: `https://tekateki.id/blog/${slug}`,
          siteName: "Teka-Teki <PERSON>",
          locale: "id_ID",
          type: "article",
          images: blog.featured_image ? [{ url: blog.featured_image }] : [],
        },
      };
    }
  } catch (error) {
    console.error('Error fetching blog metadata:', error);
  }

  return {
    title: "Blog Teka-Teki Silang | TekaTeki Indonesia",
    description: "Artikel dan tips seputar teka-teki silang dalam bahasa Indonesia.",
  };
}

// Fungsi untuk mengambil data blog
async function getBlog(slug: string) {
  try {
    const response = await blogAPI.getBySlug(slug);
    return response.status === 'success' ? response.data : null;
  } catch (error) {
    console.error('Error fetching blog:', error);
    return null;
  }
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const blog = await getBlog(slug);

  if (!blog) {
    return (
      <>
        <Header />
        <main className="container mx-auto px-4 py-12">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4">
            <strong className="font-bold">Error! </strong>
            <span className="block sm:inline">Artikel tidak ditemukan.</span>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  // Fungsi untuk memformat tanggal
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  return (
    <>
      <Header />
      <main>
        {/* Blog Header */}
        <section className="bg-blue-600 text-white py-8">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                {blog.title}
              </h1>
              <div className="flex items-center text-sm mb-2">
                <div className="flex items-center mr-4">
                  <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-600 mr-2">
                    {blog.author_name.charAt(0)}
                  </div>
                  <span>{blog.author_name}</span>
                </div>
                <span>{formatDate(blog.created_at)}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Content */}
        <article className="py-8">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              {blog.featured_image && (
                <div className="relative w-full h-64 md:h-96 mb-8 rounded-lg overflow-hidden">
                  <Image
                    src={blog.featured_image}
                    alt={blog.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}

              <div className="prose prose-lg max-w-none">
                <div dangerouslySetInnerHTML={{ __html: blog.content }} />
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <Link
                  href="/blog"
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  &larr; Kembali ke Blog
                </Link>
              </div>
            </div>
          </div>
        </article>
      </main>
      <Footer />
    </>
  );
}
