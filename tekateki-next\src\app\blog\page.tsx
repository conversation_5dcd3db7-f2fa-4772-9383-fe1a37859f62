import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import BlogList from "@/components/blog/BlogList";
import { blogAPI } from "@/lib/api";

export const metadata: Metadata = {
  title: "Blog Teka-Teki <PERSON> | TekaTeki Indonesia",
  description: "Artikel dan tips seputar teka-teki silang, strategi permainan, dan informasi menarik lainnya dalam bahasa Indonesia.",
  openGraph: {
    title: "Blog Teka-Teki Silang | TekaTeki Indonesia",
    description: "Artikel dan tips seputar teka-teki silang, strategi permainan, dan informasi menarik lainnya dalam bahasa Indonesia.",
    url: "https://tekateki.id/blog",
    siteName: "Teka-Teki Silang Indonesia",
    locale: "id_ID",
    type: "website",
  },
};

// Fungsi untuk mengambil data blog
async function getBlogs() {
  try {
    const response = await blogAPI.getAll({
      status: 'published',
      limit: 10,
    });
    return response.status === 'success' ? response.data : [];
  } catch (error) {
    console.error('Error fetching blogs:', error);
    return [];
  }
}

export default async function BlogPage() {
  const blogs = await getBlogs();
  
  return (
    <>
      <Header />
      <main>
        {/* Blog Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Blog Teka-Teki Silang
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              Artikel dan tips seputar teka-teki silang, strategi permainan, dan informasi menarik lainnya dalam bahasa Indonesia.
            </p>
          </div>
        </section>
        
        {/* Blog List */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <BlogList initialBlogs={blogs} />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
