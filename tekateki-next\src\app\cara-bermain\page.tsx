import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import HelpNav from "@/components/layout/HelpNav";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Cara Bermain Teka-Teki <PERSON> | TekaTeki Indonesia",
  description: "Panduan lengkap cara bermain teka-teki silang online di TekaTeki Indonesia. Pelajari aturan dasar, tips, dan trik untuk menyelesaikan teka-teki silang dengan mudah.",
  openGraph: {
    title: "Cara Bermain Teka-Teki Silang | TekaTeki Indonesia",
    description: "Panduan lengkap cara bermain teka-teki silang online di TekaTeki Indonesia. Pelajari aturan dasar, tips, dan trik untuk menyelesaikan teka-teki silang dengan mudah.",
    url: "https://tekateki.id/cara-bermain",
    siteName: "Teka-Teki <PERSON> Indonesia",
    locale: "id_ID",
    type: "website",
  },
};

export default function HowToPlayPage() {
  return (
    <>
      <Header />
      <main>
        {/* How to Play Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Cara Bermain Teka-Teki Silang
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              Panduan lengkap untuk memulai dan menguasai teka-teki silang online di TekaTeki Indonesia.
            </p>
          </div>
        </section>

        {/* Introduction */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <HelpNav />
              <h2 className="text-2xl font-bold mb-6">Pengenalan Teka-Teki Silang</h2>

              <div className="prose prose-lg">
                <p>
                  Teka-teki silang adalah permainan kata yang terdiri dari kotak-kotak kosong yang harus diisi dengan huruf-huruf untuk membentuk kata. Kata-kata disusun secara horizontal (mendatar) dan vertikal (menurun) berdasarkan petunjuk yang diberikan.
                </p>

                <p>
                  Di TekaTeki Indonesia, Anda dapat menikmati berbagai teka-teki silang dalam bahasa Indonesia dengan beragam tema dan tingkat kesulitan. Panduan ini akan membantu Anda memahami cara bermain teka-teki silang online di platform kami.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Step by Step Guide */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-8">Panduan Langkah demi Langkah</h2>

              <div className="space-y-12">
                {/* Step 1 */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">
                      1
                    </div>
                    <h3 className="text-xl font-semibold">Memilih Teka-Teki</h3>
                  </div>

                  <div className="prose prose-lg">
                    <p>
                      Mulailah dengan memilih teka-teki yang ingin Anda mainkan:
                    </p>
                    <ul>
                      <li>Kunjungi halaman <Link href="/teka-teki" className="text-blue-600 hover:text-blue-800">Teka-Teki</Link> untuk melihat semua teka-teki yang tersedia</li>
                      <li>Gunakan filter untuk menemukan teka-teki berdasarkan kategori atau tingkat kesulitan</li>
                      <li>Klik pada teka-teki yang ingin Anda mainkan</li>
                    </ul>
                    <p>
                      Setiap teka-teki memiliki informasi tentang tingkat kesulitan, kategori, dan jumlah kata yang terdapat di dalamnya.
                    </p>
                  </div>

                  <div className="mt-4 bg-gray-100 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 italic">
                      Tip: Jika Anda baru memulai, pilih teka-teki dengan tingkat kesulitan "Mudah" untuk membiasakan diri dengan permainan.
                    </p>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">
                      2
                    </div>
                    <h3 className="text-xl font-semibold">Memahami Antarmuka Permainan</h3>
                  </div>

                  <div className="prose prose-lg">
                    <p>
                      Antarmuka permainan teka-teki silang terdiri dari beberapa elemen utama:
                    </p>
                    <ul>
                      <li><strong>Papan Teka-Teki</strong>: Grid kotak-kotak tempat Anda mengisi jawaban</li>
                      <li><strong>Petunjuk Mendatar</strong>: Daftar petunjuk untuk kata-kata yang ditulis secara horizontal</li>
                      <li><strong>Petunjuk Menurun</strong>: Daftar petunjuk untuk kata-kata yang ditulis secara vertikal</li>
                      <li><strong>Kontrol Permainan</strong>: Tombol untuk memeriksa jawaban, menggunakan petunjuk, atau mengatur ulang teka-teki</li>
                    </ul>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">
                      3
                    </div>
                    <h3 className="text-xl font-semibold">Mengisi Jawaban</h3>
                  </div>

                  <div className="prose prose-lg">
                    <p>
                      Untuk mengisi jawaban pada teka-teki silang:
                    </p>
                    <ol>
                      <li>Klik pada kotak kosong di papan teka-teki atau klik pada petunjuk di daftar</li>
                      <li>Ketik jawaban Anda menggunakan keyboard</li>
                      <li>Huruf akan otomatis dimasukkan ke kotak yang dipilih dan kursor akan berpindah ke kotak berikutnya</li>
                      <li>Tekan tombol Tab untuk berpindah ke kata berikutnya</li>
                      <li>Tekan tombol Shift+Tab untuk kembali ke kata sebelumnya</li>
                      <li>Klik pada kotak lain untuk berpindah secara langsung</li>
                    </ol>
                    <p>
                      Saat Anda mengklik kotak, seluruh kata yang terkait dengan kotak tersebut akan disorot, dan petunjuk yang sesuai akan ditampilkan.
                    </p>
                  </div>

                  <div className="mt-4 bg-gray-100 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 italic">
                      Tip: Gunakan tombol spasi untuk beralih antara arah mendatar dan menurun saat mengisi kotak.
                    </p>
                  </div>
                </div>

                {/* Step 4 */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">
                      4
                    </div>
                    <h3 className="text-xl font-semibold">Menggunakan Fitur Bantuan</h3>
                  </div>

                  <div className="prose prose-lg">
                    <p>
                      TekaTeki Indonesia menyediakan beberapa fitur bantuan untuk membantu Anda:
                    </p>
                    <ul>
                      <li><strong>Periksa</strong>: Memeriksa jawaban Anda dan menandai kotak yang salah</li>
                      <li><strong>Petunjuk</strong>: Mengungkapkan satu huruf dari jawaban (jumlah petunjuk terbatas)</li>
                      <li><strong>Selesaikan Kata</strong>: Mengungkapkan seluruh kata yang sedang dipilih</li>
                      <li><strong>Selesaikan Semua</strong>: Mengungkapkan seluruh jawaban teka-teki</li>
                    </ul>
                    <p>
                      Perhatikan bahwa menggunakan fitur bantuan akan mempengaruhi skor akhir Anda.
                    </p>
                  </div>
                </div>

                {/* Step 5 */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">
                      5
                    </div>
                    <h3 className="text-xl font-semibold">Menyelesaikan Teka-Teki</h3>
                  </div>

                  <div className="prose prose-lg">
                    <p>
                      Setelah Anda mengisi semua kotak dengan jawaban yang benar, teka-teki akan dianggap selesai. Anda akan melihat pesan selamat dan statistik permainan Anda, termasuk:
                    </p>
                    <ul>
                      <li>Waktu yang dibutuhkan untuk menyelesaikan teka-teki</li>
                      <li>Jumlah petunjuk yang digunakan</li>
                      <li>Skor akhir</li>
                    </ul>
                    <p>
                      Jika Anda sudah login, kemajuan dan statistik Anda akan disimpan ke profil Anda.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tips and Strategies */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-6">Tips dan Strategi</h2>

              <div className="prose prose-lg">
                <p>
                  Berikut adalah beberapa tips untuk membantu Anda menyelesaikan teka-teki silang dengan lebih efektif:
                </p>

                <ul>
                  <li><strong>Mulai dengan yang Anda tahu</strong>: Isi terlebih dahulu jawaban yang Anda yakin benar</li>
                  <li><strong>Perhatikan panjang kata</strong>: Jumlah kotak menunjukkan jumlah huruf dalam jawaban</li>
                  <li><strong>Cari kata-kata pendek</strong>: Kata dengan 2-3 huruf biasanya lebih mudah ditebak</li>
                  <li><strong>Manfaatkan persilangan</strong>: Kata-kata yang bersilangan dapat memberikan petunjuk tambahan</li>
                  <li><strong>Perhatikan bentuk kata</strong>: Petunjuk sering mengindikasikan bentuk gramatikal (kata benda, kata kerja, dll.)</li>
                  <li><strong>Jangan ragu untuk menebak</strong>: Terkadang mengisi jawaban yang mungkin benar dapat membantu menemukan jawaban lain</li>
                </ul>

                <p>
                  Untuk tips dan strategi lebih lanjut, kunjungi halaman <Link href="/tips-strategi-tts" className="text-blue-600 hover:text-blue-800">Tips & Strategi TTS</Link>.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-6">Pertanyaan Umum</h2>

              <div className="space-y-4">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apakah saya perlu membuat akun untuk bermain?</h3>
                  <p>
                    Tidak, Anda dapat bermain teka-teki silang tanpa membuat akun. Namun, dengan membuat akun, Anda dapat menyimpan kemajuan, melacak statistik, dan membuat teka-teki silang sendiri.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Apakah saya bisa bermain di perangkat mobile?</h3>
                  <p>
                    Ya, TekaTeki Indonesia dirancang responsif dan dapat dimainkan dengan baik di smartphone dan tablet.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold mb-3">Bagaimana cara menyimpan kemajuan permainan?</h3>
                  <p>
                    Kemajuan permainan akan otomatis disimpan jika Anda sudah login ke akun Anda. Anda dapat melanjutkan permainan kapan saja dengan mengunjungi kembali teka-teki yang sama.
                  </p>
                </div>
              </div>

              <div className="mt-8 text-center">
                <Link href="/bantuan/faq" className="text-blue-600 hover:text-blue-800 font-medium">
                  Lihat semua pertanyaan umum &rarr;
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-12 bg-blue-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">Siap Untuk Bermain?</h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Sekarang Anda sudah mengetahui cara bermain teka-teki silang, saatnya menguji kemampuan Anda!
            </p>
            <Link href="/teka-teki" className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-md font-medium text-lg">
              Mulai Bermain Sekarang
            </Link>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
