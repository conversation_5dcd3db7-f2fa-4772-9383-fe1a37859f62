'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export default function RegisterPage() {
  const router = useRouter();
  const { register, googleLogin, error: authError, loading } = useAuth();
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    displayName: '',
  });
  
  const [formError, setFormError] = useState<string | null>(null);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    
    // Validasi form
    if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword || !formData.displayName) {
      setFormError('Semua field harus diisi');
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setFormError('Password dan konfirmasi password tidak cocok');
      return;
    }
    
    if (formData.password.length < 8) {
      setFormError('Password harus minimal 8 karakter');
      return;
    }
    
    try {
      await register(
        formData.username,
        formData.email,
        formData.password,
        formData.displayName
      );
      router.push('/');
    } catch (err) {
      console.error('Register error:', err);
    }
  };
  
  const handleGoogleLogin = async () => {
    // Implementasi login dengan Google
    // Biasanya menggunakan OAuth provider seperti @react-oauth/google
    // Untuk contoh ini, kita akan menggunakan data dummy
    try {
      await googleLogin(
        'dummy-token',
        '<EMAIL>',
        'Google User',
        'https://example.com/avatar.jpg'
      );
      router.push('/');
    } catch (err) {
      console.error('Google login error:', err);
    }
  };
  
  return (
    <>
      <Header />
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div className="py-4 px-6 bg-blue-600 text-white text-center">
              <h1 className="text-2xl font-bold">Daftar Akun Baru</h1>
            </div>
            
            <div className="py-8 px-6">
              {(authError || formError) && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                  <span className="block sm:inline">{authError || formError}</span>
                </div>
              )}
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label
                    htmlFor="username"
                    className="block text-gray-700 text-sm font-bold mb-2"
                  >
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="Username Anda"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label
                    htmlFor="email"
                    className="block text-gray-700 text-sm font-bold mb-2"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="Email Anda"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label
                    htmlFor="displayName"
                    className="block text-gray-700 text-sm font-bold mb-2"
                  >
                    Nama Tampilan
                  </label>
                  <input
                    type="text"
                    id="displayName"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="Nama yang akan ditampilkan"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label
                    htmlFor="password"
                    className="block text-gray-700 text-sm font-bold mb-2"
                  >
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="Password Anda (min. 8 karakter)"
                    required
                    minLength={8}
                  />
                </div>
                
                <div className="mb-6">
                  <label
                    htmlFor="confirmPassword"
                    className="block text-gray-700 text-sm font-bold mb-2"
                  >
                    Konfirmasi Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    placeholder="Konfirmasi password Anda"
                    required
                  />
                </div>
                
                <div className="flex items-center justify-between mb-6">
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
                    disabled={loading}
                  >
                    {loading ? 'Memproses...' : 'Daftar'}
                  </button>
                </div>
              </form>
              
              <div className="relative flex items-center justify-center mb-6">
                <div className="border-t border-gray-300 flex-grow"></div>
                <span className="mx-4 text-gray-500 text-sm">atau</span>
                <div className="border-t border-gray-300 flex-grow"></div>
              </div>
              
              <button
                onClick={handleGoogleLogin}
                className="flex items-center justify-center w-full bg-white border border-gray-300 rounded-lg shadow-md px-6 py-2 text-sm font-medium text-gray-800 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 mb-4"
                disabled={loading}
              >
                <svg
                  className="h-6 w-6 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Daftar dengan Google
              </button>
              
              <div className="text-center mt-4">
                <p className="text-sm text-gray-600">
                  Sudah memiliki akun?{' '}
                  <Link
                    href="/masuk"
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Masuk sekarang
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
