import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import HelpNav from "@/components/layout/HelpNav";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Kamus Istilah Teka-Teki <PERSON> | TekaTeki Indonesia",
  description: "Kamus lengkap istilah-istilah dalam teka-teki silang. Pelajari terminologi TTS untuk meningkatkan kemampuan bermain Anda.",
  openGraph: {
    title: "Kamus Istilah Teka-Teki Silang | TekaTeki Indonesia",
    description: "Kamus lengkap istilah-istilah dalam teka-teki silang. Pelajari terminologi TTS untuk meningkatkan kemampuan bermain Anda.",
    url: "https://tekateki.id/kamus-istilah-tts",
    siteName: "Teka-Teki Silang Indonesia",
    locale: "id_ID",
    type: "website",
  },
};

export default function GlossaryPage() {
  // Alphabet links for quick navigation
  const alphabetLinks = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");

  return (
    <>
      <Header />
      <main>
        {/* Glossary Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Kamus Istilah Teka-Teki Silang
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              Pelajari terminologi dan istilah-istilah dalam dunia teka-teki silang untuk meningkatkan kemampuan bermain Anda.
            </p>
          </div>
        </section>

        {/* Alphabet Navigation */}
        <section className="py-6 bg-gray-50 sticky top-0 z-10 shadow-sm">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <div className="flex flex-wrap justify-center gap-2">
                {alphabetLinks.map((letter) => (
                  <a
                    key={letter}
                    href={`#${letter}`}
                    className="w-8 h-8 flex items-center justify-center bg-white text-blue-600 rounded-full hover:bg-blue-100 font-medium"
                  >
                    {letter}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Glossary Content */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <HelpNav />
              <div className="prose prose-lg">
                <p>
                  Kamus istilah ini berisi terminologi yang umum digunakan dalam dunia teka-teki silang. Memahami istilah-istilah ini akan membantu Anda menjadi pemain teka-teki silang yang lebih baik dan memahami petunjuk dengan lebih efektif.
                </p>
              </div>

              {/* A */}
              <div id="A" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">A</span>
                  A
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Acak</h3>
                    <p>
                      Petunjuk yang mengindikasikan bahwa jawaban merupakan anagram (huruf-huruf yang diacak) dari kata atau frasa lain yang disebutkan dalam petunjuk.
                    </p>
                    <p className="mt-2 text-sm text-gray-600">
                      Contoh: "Acak PESTA menjadi buah" (Jawaban: APEL)
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Arah</h3>
                    <p>
                      Mengacu pada orientasi kata dalam teka-teki silang, bisa mendatar (horizontal) atau menurun (vertikal).
                    </p>
                  </div>
                </div>
              </div>

              {/* B */}
              <div id="B" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">B</span>
                  B
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Blok</h3>
                    <p>
                      Kotak hitam atau terisi yang memisahkan kata-kata dalam teka-teki silang.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Bingung</h3>
                    <p>
                      Kata dalam petunjuk yang mengindikasikan anagram atau pengacakan huruf.
                    </p>
                    <p className="mt-2 text-sm text-gray-600">
                      Contoh: "Bingung mencari PENA di kebun" (Jawaban: ANEP [anagram dari PENA])
                    </p>
                  </div>
                </div>
              </div>

              {/* G */}
              <div id="G" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">G</span>
                  G
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Grid</h3>
                    <p>
                      Pola kotak-kotak yang membentuk teka-teki silang, biasanya berbentuk persegi atau persegi panjang.
                    </p>
                  </div>
                </div>
              </div>

              {/* H */}
              <div id="H" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">H</span>
                  H
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Homonim</h3>
                    <p>
                      Kata yang memiliki bunyi yang sama tetapi arti dan ejaan yang berbeda, sering digunakan dalam petunjuk teka-teki silang.
                    </p>
                    <p className="mt-2 text-sm text-gray-600">
                      Contoh: "Bank" bisa merujuk pada lembaga keuangan atau tepi sungai.
                    </p>
                  </div>
                </div>
              </div>

              {/* K */}
              <div id="K" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">K</span>
                  K
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Kriptik</h3>
                    <p>
                      Jenis petunjuk yang memerlukan pemikiran lateral atau tidak langsung untuk dipecahkan, sering menggunakan permainan kata.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Kotak</h3>
                    <p>
                      Sel individual dalam grid teka-teki silang yang dapat diisi dengan satu huruf.
                    </p>
                  </div>
                </div>
              </div>

              {/* M */}
              <div id="M" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">M</span>
                  M
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Mendatar</h3>
                    <p>
                      Kata yang ditulis secara horizontal dari kiri ke kanan dalam teka-teki silang.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Menurun</h3>
                    <p>
                      Kata yang ditulis secara vertikal dari atas ke bawah dalam teka-teki silang.
                    </p>
                  </div>
                </div>
              </div>

              {/* P */}
              <div id="P" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">P</span>
                  P
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Petunjuk</h3>
                    <p>
                      Deskripsi atau definisi yang membantu pemain menebak kata yang harus diisi dalam teka-teki silang.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Persilangan</h3>
                    <p>
                      Titik di mana kata mendatar dan menurun bertemu dan berbagi huruf yang sama.
                    </p>
                  </div>
                </div>
              </div>

              {/* S */}
              <div id="S" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">S</span>
                  S
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Simetri</h3>
                    <p>
                      Pola grid teka-teki silang yang memiliki keseimbangan visual, biasanya dengan pola kotak hitam yang simetris.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Sinonim</h3>
                    <p>
                      Kata yang memiliki arti yang sama atau mirip dengan kata lain, sering digunakan dalam petunjuk teka-teki silang.
                    </p>
                    <p className="mt-2 text-sm text-gray-600">
                      Contoh: "Bahagia" adalah sinonim dari "senang" atau "gembira".
                    </p>
                  </div>
                </div>
              </div>

              {/* T */}
              <div id="T" className="mt-12">
                <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 flex items-center">
                  <span className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-3">T</span>
                  T
                </h2>

                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">Tema</h3>
                    <p>
                      Subjek atau topik utama yang menjadi dasar teka-teki silang, seperti olahraga, sejarah, atau geografi.
                    </p>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold mb-2">TTS</h3>
                    <p>
                      Singkatan dari Teka-Teki Silang.
                    </p>
                  </div>
                </div>
              </div>

              {/* Back to Top Button */}
              <div className="mt-12 text-center">
                <a href="#" className="inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition">
                  Kembali ke Atas
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Related Pages */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-6">Halaman Terkait</h2>

              <div className="grid md:grid-cols-2 gap-6">
                <Link href="/cara-bermain" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition">
                  <h3 className="text-xl font-semibold mb-2">Cara Bermain</h3>
                  <p className="text-gray-600">
                    Panduan lengkap cara bermain teka-teki silang untuk pemula.
                  </p>
                </Link>

                <Link href="/tips-strategi-tts" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition">
                  <h3 className="text-xl font-semibold mb-2">Tips & Strategi</h3>
                  <p className="text-gray-600">
                    Kumpulan tips dan strategi untuk menyelesaikan teka-teki silang dengan lebih efektif.
                  </p>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
