import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import CrosswordList from "@/components/crossword/CrosswordList";
import { categoryAPI, crosswordAPI } from "@/lib/api";

// Fungsi untuk menghasilkan metadata dinamis
export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  // Pastikan params.slug diawaitkan terlebih dahulu
  const slug = params.slug;

  try {
    const response = await categoryAPI.getOne(slug);

    if (response.status === 'success' && response.data) {
      const category = response.data;

      return {
        title: `Teka-Teki <PERSON> ${category.name} | TekaTeki Indonesia`,
        description: category.description || `Kumpulan teka-teki silang dalam kategori ${category.name}. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.`,
        openGraph: {
          title: `Teka-Te<PERSON> ${category.name} | TekaTeki Indonesia`,
          description: category.description || `Kumpulan teka-teki silang dalam kategori ${category.name}. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.`,
          url: `https://tekateki.id/kategori/${slug}`,
          siteName: "Teka-Teki Silang Indonesia",
          locale: "id_ID",
          type: "website",
        },
      };
    }
  } catch (error) {
    console.error('Error fetching category metadata:', error);
  }

  return {
    title: "Kategori Teka-Teki Silang | TekaTeki Indonesia",
    description: "Jelajahi berbagai kategori teka-teki silang dalam bahasa Indonesia.",
  };
}

// Fungsi untuk mengambil data kategori dan teka-teki silang
async function getData(slug: string) {
  try {
    // Ambil data kategori
    const categoryResponse = await categoryAPI.getOne(slug);
    const category = categoryResponse.status === 'success' ? categoryResponse.data : null;

    // Ambil teka-teki silang berdasarkan kategori
    const crosswordsResponse = await crosswordAPI.getAll({
      category_id: category?.id,
      is_public: 1,
      limit: 12,
    });
    const crosswords = crosswordsResponse.status === 'success' ? crosswordsResponse.data : [];

    return {
      category,
      crosswords,
    };
  } catch (error) {
    console.error('Error fetching category data:', error);
    return {
      category: null,
      crosswords: [],
    };
  }
}

export default async function CategoryPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const { category, crosswords } = await getData(slug);

  if (!category) {
    return (
      <>
        <Header />
        <main className="container mx-auto px-4 py-12">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4">
            <strong className="font-bold">Error! </strong>
            <span className="block sm:inline">Kategori tidak ditemukan.</span>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <main>
        {/* Category Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Teka-Teki Silang: {category.name}
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              {category.description}
            </p>
          </div>
        </section>

        {/* Crosswords List */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold text-gray-800 mb-8">
              Teka-Teki dalam Kategori Ini
            </h2>

            <CrosswordList initialCrosswords={crosswords} categoryId={category.id} />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
