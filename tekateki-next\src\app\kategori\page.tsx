import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import CategoryList from "@/components/category/CategoryList";
import { categoryAPI } from "@/lib/api";

export const metadata: Metadata = {
  title: "Kategori Teka-Teki <PERSON> | TekaTeki Indonesia",
  description: "Jelajahi berbagai kategori teka-teki silang dalam bahasa Indonesia. Temukan teka-teki silang berdasarkan tema yang Anda minati.",
  openGraph: {
    title: "Kategori Teka-Teki Silang | TekaTeki Indonesia",
    description: "Jelajahi berbagai kategori teka-teki silang dalam bahasa Indonesia. Temukan teka-teki silang berdasarkan tema yang Anda minati.",
    url: "https://tekateki.id/kategori",
    siteName: "Teka-Teki Silang Indonesia",
    locale: "id_ID",
    type: "website",
  },
};

// Fungsi untuk mengambil data kategori
async function getCategories() {
  try {
    const response = await categoryAPI.getAll();
    return response.status === 'success' ? response.data : [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export default async function CategoriesPage() {
  const categories = await getCategories();
  
  return (
    <>
      <Header />
      <main>
        {/* Categories Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Kategori Teka-Teki Silang
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              Jelajahi berbagai kategori teka-teki silang dalam bahasa Indonesia. Pilih kategori yang sesuai dengan minat dan tingkat kemampuan Anda.
            </p>
          </div>
        </section>
        
        {/* Categories List */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <CategoryList initialCategories={categories} />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
