import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { AuthProvider } from "@/context/AuthContext";
import { CrosswordProvider } from "@/context/CrosswordContext";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Tekateki.io | Asah Otak dengan Puzzle Interaktif",
  description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.",
  keywords: "teka-teki silang, TTS, puzzle Indonesia, asah otak, permainan kata, game edukasi, Tekateki.io",
  authors: [{ name: "<PERSON>katek<PERSON>.io" }],
  openGraph: {
    title: "Tekateki.io | Asah Otak dengan Puzzle Interaktif",
    description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.",
    url: "https://tekateki.io",
    siteName: "Tekateki.io",
    locale: "id_ID",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tekateki.io | Asah Otak dengan Puzzle Interaktif",
    description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <CrosswordProvider>
            {children}
          </CrosswordProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
