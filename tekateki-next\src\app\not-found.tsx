import React from 'react';
import Link from 'next/link';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export default function NotFound() {
  return (
    <>
      <Header />
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto text-center">
            <h1 className="text-3xl font-bold text-blue-600 mb-4">404 - Halaman Tidak Ditemukan</h1>
            <p className="text-gray-600 mb-6">
              <PERSON><PERSON>, halaman yang Anda cari tidak ditemukan. Silakan kembali ke beranda atau coba tautan lain.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                <PERSON><PERSON><PERSON> ke Beranda
              </Link>
              <Link
                href="/teka-teki"
                className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Jelajahi Teka-Teki
              </Link>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
