import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import CrosswordList from "@/components/crossword/CrosswordList";
import CategoryList from "@/components/category/CategoryList";
import BlogList from "@/components/blog/BlogList";
import { crosswordAPI, categoryAPI, blogAPI } from "@/lib/api";

export const metadata: Metadata = {
  title: "Teka-Teki Silang Indonesia | Asah Otak dengan Puzzle Interaktif",
  description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.",
};

// Fungsi untuk mengambil data dari server
export async function getData() {
  try {
    // Ambil teka-teki silang unggulan
    const featuredCrosswordsResponse = await crosswordAPI.getFeatured();
    const featuredCrosswords = featuredCrosswordsResponse.status === 'success' ? featuredCrosswordsResponse.data : [];

    // Ambil teka-teki silang terbaru
    const latestCrosswordsResponse = await crosswordAPI.getAll({ limit: 8 });
    const latestCrosswords = latestCrosswordsResponse.status === 'success' ? latestCrosswordsResponse.data : [];

    // Ambil kategori
    const categoriesResponse = await categoryAPI.getAll();
    const categories = categoriesResponse.status === 'success' ? categoriesResponse.data : [];

    // Ambil blog terbaru
    const latestBlogsResponse = await blogAPI.getAll({ limit: 3 });
    const latestBlogs = latestBlogsResponse.status === 'success' ? latestBlogsResponse.data : [];

    return {
      featuredCrosswords,
      latestCrosswords,
      categories,
      latestBlogs,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      featuredCrosswords: [],
      latestCrosswords: [],
      categories: [],
      latestBlogs: [],
    };
  }
}

export default async function Home() {
  const { featuredCrosswords, latestCrosswords, categories, latestBlogs } = await getData();

  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-blue-600 text-white py-16">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-8 md:mb-0">
                <h1 className="text-4xl md:text-5xl font-bold mb-4">
                  Asah Otak dengan Teka-Teki Silang Interaktif
                </h1>
                <p className="text-xl mb-6">
                  Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link
                    href="/teka-teki"
                    className="bg-white text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-md font-medium text-center"
                  >
                    Mulai Bermain
                  </Link>
                  <Link
                    href="/kategori"
                    className="bg-transparent border border-white text-white hover:bg-blue-700 px-6 py-3 rounded-md font-medium text-center"
                  >
                    Jelajahi Kategori
                  </Link>
                </div>
              </div>
              <div className="md:w-1/2 flex justify-center">
                <div className="relative w-full max-w-md h-64 md:h-80">
                  <Image
                    src="/images/teka-teki-silang.jpg"
                    alt="Teka-Teki Silang"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Crosswords Section */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                Teka-Teki Pilihan
              </h2>
              <Link
                href="/teka-teki"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Lihat Semua &rarr;
              </Link>
            </div>
            <CrosswordList initialCrosswords={featuredCrosswords} />
          </div>
        </section>

        {/* Categories Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                Kategori Teka-Teki
              </h2>
              <Link
                href="/kategori"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Lihat Semua &rarr;
              </Link>
            </div>
            <CategoryList initialCategories={categories.slice(0, 4)} />
          </div>
        </section>

        {/* Latest Crosswords Section */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                Teka-Teki Terbaru
              </h2>
              <Link
                href="/teka-teki"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Lihat Semua &rarr;
              </Link>
            </div>
            <CrosswordList initialCrosswords={latestCrosswords} />
          </div>
        </section>

        {/* Blog Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                Artikel Terbaru
              </h2>
              <Link
                href="/blog"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Lihat Semua &rarr;
              </Link>
            </div>
            <BlogList initialBlogs={latestBlogs} />
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-blue-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Siap Untuk Menguji Kemampuan Anda?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Bergabunglah dengan ribuan pemain lainnya dan asah kemampuan bahasa serta pengetahuan umum Anda dengan teka-teki silang interaktif.
            </p>
            <Link
              href="/daftar"
              className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-md font-medium text-lg inline-block"
            >
              Daftar Sekarang - Gratis!
            </Link>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
