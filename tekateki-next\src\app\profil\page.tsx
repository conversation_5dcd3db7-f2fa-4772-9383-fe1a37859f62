'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { progressAPI } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export default function ProfilePage() {
  const router = useRouter();
  const { user, updateProfile, loading: authLoading } = useAuth();
  
  const [activeTab, setActiveTab] = useState<'profile' | 'progress'>('profile');
  const [editMode, setEditMode] = useState(false);
  const [profileData, setProfileData] = useState({
    display_name: '',
    bio: '',
    avatar_url: '',
  });
  const [progress, setProgress] = useState<any[]>([]);
  const [loadingProgress, setLoadingProgress] = useState(false);
  
  // Mengisi data profil dari user
  useEffect(() => {
    if (user) {
      setProfileData({
        display_name: user.display_name || '',
        bio: user.bio || '',
        avatar_url: user.avatar_url || '',
      });
    } else {
      // Redirect ke halaman login jika tidak ada user
      router.push('/masuk');
    }
  }, [user, router]);
  
  // Mengambil data kemajuan pengguna
  useEffect(() => {
    if (user) {
      fetchProgress();
    }
  }, [user]);
  
  const fetchProgress = async () => {
    setLoadingProgress(true);
    try {
      const response = await progressAPI.getAll();
      if (response.status === 'success' && response.data) {
        setProgress(response.data);
      }
    } catch (err) {
      console.error('Error fetching progress:', err);
    } finally {
      setLoadingProgress(false);
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await updateProfile(profileData);
      setEditMode(false);
    } catch (err) {
      console.error('Update profile error:', err);
    }
  };
  
  // Format tanggal
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };
  
  // Format waktu
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes} menit ${remainingSeconds} detik`;
  };
  
  if (!user) {
    return (
      <>
        <Header />
        <main className="py-12">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <p className="text-gray-600">Memuat...</p>
            </div>
          </div>
        </main>
        <Footer />
      </>
    );
  }
  
  return (
    <>
      <Header />
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Profile Header */}
              <div className="bg-blue-600 text-white p-6">
                <div className="flex flex-col md:flex-row items-center">
                  <div className="mb-4 md:mb-0 md:mr-6">
                    {profileData.avatar_url ? (
                      <Image
                        src={profileData.avatar_url}
                        alt={profileData.display_name}
                        width={100}
                        height={100}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-24 h-24 bg-blue-500 rounded-full flex items-center justify-center text-white text-3xl">
                        {profileData.display_name.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold mb-1">{profileData.display_name}</h1>
                    <p className="text-blue-100">@{user.username}</p>
                    {user.role === 'admin' && (
                      <span className="inline-block bg-yellow-500 text-white text-xs px-2 py-1 rounded mt-2">
                        Admin
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Tabs */}
              <div className="border-b border-gray-200">
                <nav className="flex">
                  <button
                    className={`py-4 px-6 font-medium ${
                      activeTab === 'profile'
                        ? 'text-blue-600 border-b-2 border-blue-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('profile')}
                  >
                    Profil
                  </button>
                  <button
                    className={`py-4 px-6 font-medium ${
                      activeTab === 'progress'
                        ? 'text-blue-600 border-b-2 border-blue-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveTab('progress')}
                  >
                    Kemajuan
                  </button>
                </nav>
              </div>
              
              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'profile' ? (
                  <div>
                    {editMode ? (
                      <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                          <label
                            htmlFor="display_name"
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            Nama Tampilan
                          </label>
                          <input
                            type="text"
                            id="display_name"
                            name="display_name"
                            value={profileData.display_name}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            required
                          />
                        </div>
                        
                        <div className="mb-4">
                          <label
                            htmlFor="avatar_url"
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            URL Avatar
                          </label>
                          <input
                            type="text"
                            id="avatar_url"
                            name="avatar_url"
                            value={profileData.avatar_url}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                          />
                        </div>
                        
                        <div className="mb-6">
                          <label
                            htmlFor="bio"
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            Bio
                          </label>
                          <textarea
                            id="bio"
                            name="bio"
                            value={profileData.bio}
                            onChange={handleChange}
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-32"
                          ></textarea>
                        </div>
                        
                        <div className="flex gap-2">
                          <button
                            type="submit"
                            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            disabled={authLoading}
                          >
                            {authLoading ? 'Menyimpan...' : 'Simpan'}
                          </button>
                          <button
                            type="button"
                            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            onClick={() => setEditMode(false)}
                          >
                            Batal
                          </button>
                        </div>
                      </form>
                    ) : (
                      <div>
                        <div className="mb-6">
                          <h2 className="text-xl font-semibold mb-2">Informasi Profil</h2>
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="mb-4">
                              <h3 className="text-sm font-medium text-gray-500">Email</h3>
                              <p className="text-gray-800">{user.email}</p>
                            </div>
                            <div className="mb-4">
                              <h3 className="text-sm font-medium text-gray-500">Username</h3>
                              <p className="text-gray-800">@{user.username}</p>
                            </div>
                            <div className="mb-4">
                              <h3 className="text-sm font-medium text-gray-500">Bio</h3>
                              <p className="text-gray-800">{profileData.bio || 'Belum ada bio'}</p>
                            </div>
                          </div>
                        </div>
                        
                        <button
                          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                          onClick={() => setEditMode(true)}
                        >
                          Edit Profil
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Kemajuan Teka-Teki Silang</h2>
                    
                    {loadingProgress ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                        <p className="mt-2 text-gray-600">Memuat kemajuan...</p>
                      </div>
                    ) : progress.length === 0 ? (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <p className="text-gray-600">Anda belum memainkan teka-teki silang apa pun.</p>
                        <button
                          className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                          onClick={() => router.push('/teka-teki')}
                        >
                          Mulai Bermain
                        </button>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white">
                          <thead>
                            <tr className="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                              <th className="py-3 px-6 text-left">Teka-Teki</th>
                              <th className="py-3 px-6 text-center">Kemajuan</th>
                              <th className="py-3 px-6 text-center">Status</th>
                              <th className="py-3 px-6 text-center">Waktu</th>
                              <th className="py-3 px-6 text-center">Tanggal</th>
                              <th className="py-3 px-6 text-center">Aksi</th>
                            </tr>
                          </thead>
                          <tbody className="text-gray-600 text-sm">
                            {progress.map((item) => (
                              <tr key={item.id} className="border-b border-gray-200 hover:bg-gray-50">
                                <td className="py-3 px-6 text-left">
                                  <div className="font-medium">{item.crossword_title}</div>
                                  <div className="text-xs text-gray-500">{item.difficulty}</div>
                                </td>
                                <td className="py-3 px-6 text-center">
                                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                                    <div
                                      className="bg-blue-600 h-2.5 rounded-full"
                                      style={{ width: `${item.progress_data.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs">{item.progress_data.progress}%</span>
                                </td>
                                <td className="py-3 px-6 text-center">
                                  {item.is_completed ? (
                                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                      Selesai
                                    </span>
                                  ) : (
                                    <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                      Dalam Progres
                                    </span>
                                  )}
                                </td>
                                <td className="py-3 px-6 text-center">
                                  {formatTime(item.time_spent)}
                                </td>
                                <td className="py-3 px-6 text-center">
                                  {formatDate(item.updated_at)}
                                </td>
                                <td className="py-3 px-6 text-center">
                                  <button
                                    className="bg-blue-600 hover:bg-blue-700 text-white text-xs py-1 px-2 rounded"
                                    onClick={() => router.push(`/teka-teki/${item.slug}`)}
                                  >
                                    Lanjutkan
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
