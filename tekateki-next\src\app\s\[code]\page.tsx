'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { fetchAPI } from '@/lib/api';

export default function ShareRedirect({ params }: { params: { code: string } }) {
  const router = useRouter();
  const { code } = params;

  useEffect(() => {
    const redirectToCrossword = async () => {
      try {
        // Fetch the crossword ID from the share code
        const response = await fetchAPI<{ crossword_id: string }>(`/share/${code}`);
        
        if (response.status === 'success' && response.data?.crossword_id) {
          // Redirect to the crossword page
          router.push(`/teka-teki/${response.data.crossword_id}`);
        } else {
          // Redirect to the home page if the share code is invalid
          router.push('/');
        }
      } catch (error) {
        console.error('Error fetching share link:', error);
        // Redirect to the home page on error
        router.push('/');
      }
    };

    redirectToCrossword();
  }, [code, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-800">Mengalihkan ke teka-teki silang...</h1>
      </div>
    </div>
  );
}
