import { MetadataRoute } from 'next';
import { crosswordAPI, categoryAPI, blogAPI } from '@/lib/api';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://tekateki.id';

  // Halaman statis
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/teka-teki`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/kategori`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    // Halaman bantuan dan informasi
    {
      url: `${baseUrl}/bantuan/faq`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/cara-bermain`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/kamus-istilah-tts`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/manfaat-tts`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/tips-strategi-tts`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    // Halaman autentikasi
    {
      url: `${baseUrl}/masuk`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/daftar`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
  ];

  // Ambil data teka-teki silang
  let crosswordPages: MetadataRoute.Sitemap = [];
  try {
    const response = await crosswordAPI.getAll({ is_public: 1, limit: 100 });
    if (response.status === 'success' && response.data) {
      crosswordPages = response.data.map((crossword) => ({
        url: `${baseUrl}/teka-teki/${crossword.slug}`,
        lastModified: new Date(crossword.updated_at),
        changeFrequency: 'monthly' as const,
        priority: 0.7,
      }));
    }
  } catch (error) {
    console.error('Error fetching crosswords for sitemap:', error);
  }

  // Ambil data kategori
  let categoryPages: MetadataRoute.Sitemap = [];
  try {
    const response = await categoryAPI.getAll();
    if (response.status === 'success' && response.data) {
      categoryPages = response.data.map((category) => ({
        url: `${baseUrl}/kategori/${category.slug}`,
        lastModified: new Date(category.created_at),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      }));
    }
  } catch (error) {
    console.error('Error fetching categories for sitemap:', error);
  }

  // Ambil data blog
  let blogPages: MetadataRoute.Sitemap = [];
  try {
    const response = await blogAPI.getAll({ status: 'published', limit: 100 });
    if (response.status === 'success' && response.data) {
      blogPages = response.data.map((blog) => ({
        url: `${baseUrl}/blog/${blog.slug}`,
        lastModified: new Date(blog.updated_at),
        changeFrequency: 'monthly' as const,
        priority: 0.7,
      }));
    }
  } catch (error) {
    console.error('Error fetching blogs for sitemap:', error);
  }

  return [...staticPages, ...crosswordPages, ...categoryPages, ...blogPages];
}
