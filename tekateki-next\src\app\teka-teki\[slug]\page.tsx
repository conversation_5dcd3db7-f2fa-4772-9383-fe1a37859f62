import { Metada<PERSON> } from "next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { crosswordAPI, categoryAPI } from "@/lib/api";
import { generateCrosswordMetadata } from "@/lib/meta";
import Link from "next/link";
import { ArrowLeftIcon, TagIcon, ChevronDown } from "lucide-react";
import Breadcrumb from "@/components/ui/Breadcrumb";
import CrosswordPlay from "@/components/crossword/CrosswordPlay";

// Fungsi untuk menghasilkan metadata dinamis
export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  const slug = params.slug;

  try {
    const response = await crosswordAPI.getOne(slug);

    if (response.status === 'success' && response.data) {
      const crossword = response.data;

      // Generate metadata using our helper function
      const metadata = generateCrosswordMetadata(crossword);

      // Generate game structured data
      const gameStructuredData = {
        "@context": "https://schema.org",
        "@type": "Game",
        "name": `${crossword.title} - Te<PERSON>-<PERSON><PERSON>`,
        "description": crossword.description || `Teka-teki silang "${crossword.title}" dengan tema ${crossword.category_name}. Tingkat kesulitan: ${crossword.difficulty}.`,
        "genre": "Puzzle",
        "gamePlatform": "Web Browser",
        "applicationCategory": "Game",
        "author": {
          "@type": "Person",
          "name": crossword.creator || "Anonymous"
        },
        "aggregateRating": crossword.rating ? {
          "@type": "AggregateRating",
          "ratingValue": crossword.rating,
          "ratingCount": crossword.plays,
          "bestRating": "5",
          "worstRating": "1"
        } : undefined,
        "audience": {
          "@type": "Audience",
          "audienceType": "Puzzle Enthusiasts"
        },
        "inLanguage": "id",
        "difficulty": crossword.difficulty
      };

      // Create breadcrumb structured data
      const breadcrumbStructuredData = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Beranda",
            "item": "https://tekateki.id"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Teka-Teki Silang",
            "item": "https://tekateki.id/teka-teki"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": crossword.title,
            "item": `https://tekateki.id/teka-teki/${slug}`
          }
        ]
      };

      // Add category to breadcrumb if available
      if (crossword.category_id && crossword.category_name && crossword.category_slug) {
        breadcrumbStructuredData.itemListElement.splice(2, 0, {
          "@type": "ListItem",
          "position": 3,
          "name": crossword.category_name,
          "item": `https://tekateki.id/kategori/${crossword.category_slug}`
        });

        // Update the position of the last item
        breadcrumbStructuredData.itemListElement[3].position = 4;
      }

      // Combine all structured data
      const structuredDataArray = [gameStructuredData, breadcrumbStructuredData];

      // Add structured data to metadata
      return {
        ...metadata,
        other: {
          "structured-data": JSON.stringify(structuredDataArray),
        },
      };
    }
  } catch (error) {
    console.error('Error fetching crossword metadata:', error);
  }

  return {
    title: "Teka-Teki Silang | TekaTeki Indonesia",
    description: "Mainkan teka-teki silang interaktif dalam bahasa Indonesia.",
  };
}

// Fungsi untuk mengambil data teka-teki silang
async function getCrossword(slug: string) {
  try {
    const response = await crosswordAPI.getOne(slug);
    return response.status === 'success' ? response.data : null;
  } catch (error) {
    console.error('Error fetching crossword:', error);
    return null;
  }
}

// Fungsi untuk mengambil data kategori
async function getCategory(categoryId: string) {
  if (!categoryId) return null;

  try {
    const response = await categoryAPI.getOne(categoryId);
    return response.status === 'success' ? response.data : null;
  } catch (error) {
    console.error('Error fetching category:', error);
    return null;
  }
}

// Fungsi untuk mendapatkan warna latar belakang berdasarkan tingkat kesulitan
const getDifficultyColor = (difficulty: string) => {
  switch (difficulty.toLowerCase()) {
    case 'mudah':
      return 'bg-green-100 text-green-800';
    case 'sedang':
      return 'bg-yellow-100 text-yellow-800';
    case 'sulit':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Fungsi untuk memformat tanggal
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  return new Date(dateString).toLocaleDateString('id-ID', options);
};

export default async function CrosswordPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const crossword = await getCrossword(slug);

  if (!crossword) {
    return (
      <>
        <Header />
        <main className="container mx-auto px-4 py-12">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4">
            <strong className="font-bold">Error! </strong>
            <span className="block sm:inline">Teka-teki silang tidak ditemukan.</span>
          </div>
        </main>
        <Footer />
      </>
    );
  }

  const category = await getCategory(crossword.category_id);

  // Structured data is added via metadata

  return (
    <div className="min-h-screen bg-slate-50">
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb navigation */}
        <Breadcrumb
          items={[
            { name: 'Beranda', href: '/' },
            { name: 'Teka-Teki', href: '/teka-teki' },
            category ? { name: category.name, href: `/kategori/${category.slug}` } : null,
            { name: crossword.title, href: `/teka-teki/${slug}`, current: true }
          ].filter(Boolean)}
        />

        <div className="flex items-center justify-between mb-6">
          <Link href="/teka-teki" className="flex items-center text-blue-600 hover:text-blue-800">
            <ArrowLeftIcon className="w-4 h-4 mr-1" />
            <span>Kembali ke Daftar</span>
          </Link>
        </div>

        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{crossword.title}</h1>
          <div className="flex flex-wrap items-center gap-3">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(crossword.difficulty)}`}>
              {crossword.difficulty}
            </span>
            {category && (
              <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded flex items-center">
                <TagIcon className="w-3 h-3 mr-1" />
                {category.name}
              </span>
            )}
            <span className="text-gray-600 text-sm">
              Dibuat oleh: {crossword.creator || 'Anonymous'}
            </span>
            <span className="text-gray-600 text-sm">
              {formatDate(crossword.created_at)}
            </span>
          </div>

          {crossword.description && (
            <div className="mt-4 bg-white rounded-lg shadow-sm p-4">
              <div className="flex justify-between items-center cursor-pointer">
                <h3 className="text-lg font-semibold">Deskripsi</h3>
                <ChevronDown className="w-5 h-5 text-gray-500" />
              </div>
              <div className="mt-2">
                <p className="text-gray-700">{crossword.description}</p>
              </div>
            </div>
          )}
        </div>

        {/* Crossword Play Page */}
        <CrosswordPlay crossword={crossword} />
        {/* <PlayPage crossword={crossword} /> */}
      </div>
      <Footer />
    </div>
  );
}
