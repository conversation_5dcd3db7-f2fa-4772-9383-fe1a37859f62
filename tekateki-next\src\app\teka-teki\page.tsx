import { Metadata } from "next";
import { useState } from "react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import CrosswordList from "@/components/crossword/CrosswordList";
import { crosswordAPI, categoryAPI } from "@/lib/api";

export const metadata: Metadata = {
  title: "Teka-Teki Silang Indonesia | Kumpulan Puzzle Interaktif",
  description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.",
  openGraph: {
    title: "Teka-Teki Silang Indonesia | Kumpulan Puzzle Interaktif",
    description: "Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.",
    url: "https://tekateki.id/teka-teki",
    siteName: "Teka-Teki Silang <PERSON>",
    locale: "id_ID",
    type: "website",
  },
};

// Fungsi untuk mengambil data awal
async function getData() {
  try {
    // Ambil teka-teki silang
    const crosswordsResponse = await crosswordAPI.getAll({
      is_public: 1,
      limit: 12,
    });
    const crosswords = crosswordsResponse.status === 'success' ? crosswordsResponse.data : [];
    
    // Ambil kategori untuk filter
    const categoriesResponse = await categoryAPI.getAll();
    const categories = categoriesResponse.status === 'success' ? categoriesResponse.data : [];
    
    return {
      crosswords,
      categories,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      crosswords: [],
      categories: [],
    };
  }
}

export default async function CrosswordsPage() {
  const { crosswords, categories } = await getData();
  
  return (
    <>
      <Header />
      <main>
        {/* Crosswords Header */}
        <section className="bg-blue-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Teka-Teki Silang Indonesia
            </h1>
            <p className="text-lg mb-0 max-w-3xl">
              Kumpulan teka-teki silang dalam bahasa Indonesia dengan berbagai tema dan tingkat kesulitan. Asah kemampuan bahasa dan pengetahuan umum Anda secara interaktif.
            </p>
          </div>
        </section>
        
        {/* Filters */}
        <section className="py-6 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Filter Teka-Teki
              </h2>
              
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Category Filter */}
                <div className="w-full sm:w-auto">
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                    Kategori
                  </label>
                  <select
                    id="category"
                    name="category"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Semua Kategori</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Difficulty Filter */}
                <div className="w-full sm:w-auto">
                  <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 mb-1">
                    Tingkat Kesulitan
                  </label>
                  <select
                    id="difficulty"
                    name="difficulty"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Semua Tingkat</option>
                    <option value="mudah">Mudah</option>
                    <option value="sedang">Sedang</option>
                    <option value="sulit">Sulit</option>
                  </select>
                </div>
                
                {/* Sort Filter */}
                <div className="w-full sm:w-auto">
                  <label htmlFor="sort" className="block text-sm font-medium text-gray-700 mb-1">
                    Urutkan
                  </label>
                  <select
                    id="sort"
                    name="sort"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="newest">Terbaru</option>
                    <option value="popular">Terpopuler</option>
                    <option value="rating">Rating Tertinggi</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Crosswords List */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <CrosswordList initialCrosswords={crosswords} />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
