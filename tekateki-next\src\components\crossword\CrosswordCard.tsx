'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Crossword } from '@/lib/api';

interface CrosswordCardProps {
  crossword: Crossword;
}

export default function CrosswordCard({ crossword }: CrosswordCardProps) {
  // Fungsi untuk mendapatkan warna latar belakang berdasarkan tingkat kesulitan
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'mudah':
        return 'bg-green-100 text-green-800';
      case 'sedang':
        return 'bg-yellow-100 text-yellow-800';
      case 'sulit':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Fungsi untuk memformat tanggal
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  // Fungsi untuk membuat mini preview grid
  const renderMiniGrid = () => {
    const gridSize = Math.min(5, crossword.grid_size); // Batasi ukuran preview
    const cells = [];

    console.log(crossword.state);

    // Periksa apakah crossword.state dan crossword.state.grid ada
    if (!crossword.state || !crossword.state.grid) {
      return null;
    }

    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        const isBlack = crossword.state.grid[i]?.[j] === '#';
        cells.push(
          <div
            key={`${i}-${j}`}
            className={`w-3 h-3 border border-gray-300 ${
              isBlack ? 'bg-black' : 'bg-white'
            }`}
          ></div>
        );
      }
    }

    return (
      <div
        className="grid gap-[1px]"
        style={{
          gridTemplateColumns: `repeat(${gridSize}, minmax(0, 1fr))`,
        }}
      >
        {cells}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link href={`/teka-teki/${crossword.slug}`}>
        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-lg font-semibold text-gray-800 line-clamp-2">
              {crossword.title}
            </h3>
            <div className="ml-2 flex-shrink-0">{renderMiniGrid()}</div>
          </div>

          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {crossword.description}
          </p>

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(
                  crossword.difficulty
                )}`}
              >
                {crossword.difficulty}
              </span>
            </div>

            <div className="flex items-center text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              <span>{crossword.plays}</span>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
            <div className="flex items-center">
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center text-white mr-1">
                {crossword.creator.charAt(0)}
              </div>
              <span>{crossword.creator}</span>
            </div>
            <span>{formatDate(crossword.created_at)}</span>
          </div>
        </div>
      </Link>
    </div>
  );
}
