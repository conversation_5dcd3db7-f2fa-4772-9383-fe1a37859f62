'use client';

import React, { useState } from 'react';
import { Crossword } from '@/lib/api';

interface CrosswordCluesProps {
  crossword: Crossword;
  selectedWordId: string | null;
  onSelectWord: (wordId: string) => void;
}

export default function CrosswordClues({
  crossword,
  selectedWordId,
  onSelectWord,
}: CrosswordCluesProps) {
  const [activeTab, setActiveTab] = useState<'across' | 'down'>('across');
  const acrossCluesRef = React.useRef<HTMLUListElement>(null);
  const downCluesRef = React.useRef<HTMLUListElement>(null);

  // Get clues for a specific direction
  const getCluesForDirection = (direction: 'across' | 'down') => {
    const clues: { id: string; number: number; text: string }[] = [];

    // Use the clues structure that has across and down properties
    const cluesForDirection = crossword.state.clues[direction];

    // Convert clues object to array
    Object.entries(cluesForDirection).forEach(([numberStr, text]) => {
      const number = parseInt(numberStr);

      // Find word ID based on number and direction
      const wordPosition = crossword.state.wordPositions.find(
        (pos) => pos.number === number && pos.direction === direction
      );

      if (wordPosition) {
        clues.push({
          id: wordPosition.id,
          number: number,
          text: text || '',
        });
      }
    });

    // Urutkan berdasarkan nomor
    return clues.sort((a, b) => a.number - b.number);
  };

  const acrossClues = getCluesForDirection('across');
  const downClues = getCluesForDirection('down');

  // Efek untuk mengatur tab aktif dan scroll ke petunjuk yang dipilih
  React.useEffect(() => {
    if (!selectedWordId) return;

    console.log(`CrosswordClues: selectedWordId changed to ${selectedWordId}`);

    // Convert IDs to strings for comparison to handle both string and number IDs
    const selectedWord = crossword.state.wordPositions.find(
      pos => String(pos.id) === String(selectedWordId)
    );

    if (!selectedWord) {
      console.log(`CrosswordClues: No word found with id ${selectedWordId}`);
      return;
    }

    console.log(`CrosswordClues: Found word with direction ${selectedWord.direction}`);

    // Set active tab based on word direction
    setActiveTab(selectedWord.direction);

    // Scroll to the selected clue
    setTimeout(() => {
      const elementId = `clue-${String(selectedWordId)}`;
      console.log(`CrosswordClues: Looking for element with id ${elementId}`);
      const selectedClueElement = document.getElementById(elementId);

      if (selectedClueElement) {
        console.log(`CrosswordClues: Found element with id ${elementId}`);
      } else {
        console.log(`CrosswordClues: Element with id ${elementId} not found`);
      }

      if (selectedClueElement) {
        // First make sure the parent container is scrolled to show the element
        const cluesContainer = activeTab === 'across' ? acrossCluesRef.current : downCluesRef.current;
        if (cluesContainer) {
          // Calculate the position of the element relative to the container
          const containerRect = cluesContainer.getBoundingClientRect();
          const elementRect = selectedClueElement.getBoundingClientRect();
          const relativeTop = elementRect.top - containerRect.top;

          // Scroll the container to show the element
          cluesContainer.scrollTop = relativeTop - containerRect.height / 2 + elementRect.height / 2;
        }

        // Also use scrollIntoView as a fallback
        selectedClueElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  }, [selectedWordId, crossword.state.wordPositions, activeTab]);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          className={`flex-1 py-2 px-4 text-center font-medium ${
            activeTab === 'across'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('across')}
        >
          Mendatar (Across)
        </button>
        <button
          className={`flex-1 py-2 px-4 text-center font-medium ${
            activeTab === 'down'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('down')}
        >
          Menurun (Down)
        </button>
      </div>

      {/* Clues List */}
      <div className="h-96 overflow-y-auto p-4 relative">
        {activeTab === 'across' ? (
          <ul ref={acrossCluesRef} className="space-y-2">
            {acrossClues.map((clue, index) => (
              <li
                id={`clue-${String(clue.id)}`}
                key={`across-${clue.number}-${index}`}
                className={`p-2 rounded cursor-pointer ${
                  String(selectedWordId) === String(clue.id)
                    ? 'bg-blue-500 text-white font-medium'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => onSelectWord(clue.id)}
              >
                <span className="font-semibold">{clue.number}.</span> {clue.text}
              </li>
            ))}
          </ul>
        ) : (
          <ul ref={downCluesRef} className="space-y-2">
            {downClues.map((clue, index) => (
              <li
                id={`clue-${String(clue.id)}`}
                key={`down-${clue.number}-${index}`}
                className={`p-2 rounded cursor-pointer ${
                  String(selectedWordId) === String(clue.id)
                    ? 'bg-blue-500 text-white font-medium'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => onSelectWord(clue.id)}
              >
                <span className="font-semibold">{clue.number}.</span> {clue.text}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
