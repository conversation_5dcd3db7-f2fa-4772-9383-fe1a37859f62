'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Crossword } from '@/lib/api';
import { Award, Clock, HelpCircle } from 'lucide-react';
import { useCrossword } from '@/context/CrosswordContext';
import ShareButton from '@/components/ui/ShareButton';

interface CrosswordCompletionModalProps {
  onClose: () => void;
  onPlayAgain: () => void;
}

export default function CrosswordCompletionModal({
  onClose,
  onPlayAgain,
}: CrosswordCompletionModalProps) {
  const { currentCrossword, timeSpent, hintsUsed } = useCrossword();

  if (!currentCrossword) return null;
  // Format waktu
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes} menit ${remainingSeconds} detik`;
  };

  // Menghitung skor berdasarkan waktu dan petunjuk yang digunakan
  const calculateScore = () => {
    const baseScore = 1000;
    const timeDeduction = Math.floor(timeSpent / 10); // Kurangi 1 poin setiap 10 detik
    const hintDeduction = hintsUsed * 50; // Kurangi 50 poin untuk setiap petunjuk

    let score = baseScore - timeDeduction - hintDeduction;
    return Math.max(0, score); // Skor minimal 0
  };

  // Mendapatkan pesan berdasarkan skor
  const getScoreMessage = (score: number) => {
    if (score >= 900) return 'Luar biasa! Anda seorang ahli teka-teki silang!';
    if (score >= 700) return 'Sangat bagus! Anda memiliki kemampuan yang hebat!';
    if (score >= 500) return 'Bagus! Terus berlatih untuk meningkatkan kemampuan Anda.';
    if (score >= 300) return 'Tidak buruk. Anda bisa lebih baik dengan latihan.';
    return 'Terus berlatih. Anda akan menjadi lebih baik!';
  };

  const score = calculateScore();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <Award className="w-8 h-8 text-green-600" />
          </div>

          <h2 className="text-2xl font-bold text-green-600 mb-2">Selamat!</h2>
          <p className="text-lg mb-4">
            Anda telah menyelesaikan teka-teki silang "{currentCrossword.title}"
          </p>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-700 mb-3">Statistik Anda:</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white p-3 rounded-md shadow-sm">
                <div className="flex items-center justify-center mb-1">
                  <Clock className="w-5 h-5 text-blue-600 mr-1" />
                  <span className="text-sm text-gray-500">Waktu</span>
                </div>
                <div className="font-medium">{formatTime(timeSpent)}</div>
              </div>

              <div className="bg-white p-3 rounded-md shadow-sm">
                <div className="flex items-center justify-center mb-1">
                  <HelpCircle className="w-5 h-5 text-yellow-600 mr-1" />
                  <span className="text-sm text-gray-500">Bantuan</span>
                </div>
                <div className="font-medium">{hintsUsed} digunakan</div>
              </div>

              <div className="bg-white p-3 rounded-md shadow-sm col-span-2">
                <div className="flex items-center justify-center mb-1">
                  <Award className="w-5 h-5 text-purple-600 mr-1" />
                  <span className="text-sm text-gray-500">Skor</span>
                </div>
                <div className="font-medium text-xl">{score} poin</div>
              </div>
            </div>
          </div>

          <p className="text-gray-700 mb-6">{getScoreMessage(score)}</p>

          {/* Share Button */}
          <div className="mb-6 flex justify-center">
            <ShareButton
              title={currentCrossword.title}
              isCompleted={true}
              score={score}
              timeSpent={timeSpent}
              variant="outline"
              className="w-full"
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              onClick={onPlayAgain}
            >
              Main Lagi
            </button>

            <Link
              href="/teka-teki"
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-center"
            >
              Teka-Teki Lainnya
            </Link>

            <button
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={onClose}
            >
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
