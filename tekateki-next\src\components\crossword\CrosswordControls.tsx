'use client';

import React from 'react';
import { CheckIcon, RefreshCwIcon, ArrowUpCircleIcon, HelpCircleIcon } from 'lucide-react';

interface CrosswordControlsProps {
  onToggleDirection: () => void;
  onCheckAnswers: () => void;
  onResetCrossword: () => void;
  onProvideHint?: () => void;
  isCompleted: boolean;
  showResults?: boolean;
  hintsRemaining?: number;
}

export default function CrosswordControls({
  onToggleDirection,
  onCheckAnswers,
  onResetCrossword,
  onProvideHint,
  isCompleted,
  showResults = false,
  hintsRemaining = 0,
}: CrosswordControlsProps) {
  return (
    <div className="flex flex-wrap justify-center gap-2">
      <button
        onClick={onToggleDirection}
        className="flex items-center justify-center px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed"
        title="Ubah arah (mendatar/menurun)"
        disabled={isCompleted}
      >
        <ArrowUpCircleIcon className="w-5 h-5 mr-2" />
        <span>Ubah Arah</span>
      </button>

      <button
        onClick={onCheckAnswers}
        className="flex items-center justify-center px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
        title="Periksa jawaban"
      >
        <CheckIcon className="w-5 h-5 mr-2" />
        <span>Periksa</span>
      </button>

      {onProvideHint && (
        <button
          onClick={onProvideHint}
          className={`flex items-center justify-center px-4 py-2 ${
            hintsRemaining > 0
              ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          } rounded-md transition-colors`}
          title="Dapatkan bantuan"
          disabled={hintsRemaining <= 0}
        >
          <HelpCircleIcon className="w-5 h-5 mr-2" />
          <span>Bantuan ({hintsRemaining})</span>
        </button>
      )}

      <button
        onClick={onResetCrossword}
        className="flex items-center justify-center px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
        title="Reset teka-teki"
      >
        <RefreshCwIcon className="w-5 h-5 mr-2" />
        <span>Reset</span>
      </button>

      {isCompleted && (
        <div className="mt-4 w-full bg-green-100 text-green-800 p-4 rounded-md">
          <p className="font-bold">Selamat! Anda telah menyelesaikan teka-teki silang ini.</p>
        </div>
      )}
    </div>
  );
}
