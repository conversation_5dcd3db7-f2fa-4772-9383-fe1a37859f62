'use client';

import React, { useEffect, useState } from 'react';
import { useCrossword } from '@/context/CrosswordContext';
import CrosswordGrid from './CrosswordGrid';
import CrosswordClues from './CrosswordClues';
import CrosswordControls from './CrosswordControls';
import CrosswordCompletionModal from './CrosswordCompletionModal';

interface CrosswordGameProps {
  crosswordId: string;
}

export default function CrosswordGame({ crosswordId }: CrosswordGameProps) {
  const {
    currentCrossword,
    userAnswers,
    selectedCell,
    selectedDirection,
    selectedWordId,
    isCompleted,
    timeSpent,
    hintsUsed,
    loading,
    error,
    loadCrossword,
    selectCell,
    toggleDirection,
    updateAnswer,
    revealCell,
    revealWord,
    checkAnswers,
    saveProgress,
    resetCrossword,
  } = useCrossword();

  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [checkResult, setCheckResult] = useState<{
    correct: boolean;
    incorrectCells: number[][];
  } | null>(null);

  // Memuat teka-teki silang saat komponen dimuat
  useEffect(() => {
    loadCrossword(crosswordId);
  }, [crosswordId]);

  // Menampilkan modal saat teka-teki selesai
  useEffect(() => {
    if (isCompleted) {
      setShowCompletionModal(true);
    }
  }, [isCompleted]);

  // Menyimpan kemajuan secara otomatis setiap 30 detik
  useEffect(() => {
    const saveInterval = setInterval(() => {
      if (currentCrossword && !isCompleted) {
        saveProgress();
      }
    }, 30000);

    return () => clearInterval(saveInterval);
  }, [currentCrossword, isCompleted]);

  // Menangani penekanan tombol keyboard
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedCell || !currentCrossword) return;

      const [row, col] = selectedCell;

      // Tombol arah
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (row > 0) selectCell(row - 1, col);
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (row < currentCrossword.grid_size - 1) selectCell(row + 1, col);
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault();
        if (col > 0) selectCell(row, col - 1);
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        if (col < currentCrossword.grid_size - 1) selectCell(row, col + 1);
      }
      // Toggle arah
      else if (e.key === 'Tab') {
        e.preventDefault();
        toggleDirection();
      }
      // Huruf
      else if (/^[a-zA-Z]$/.test(e.key)) {
        updateAnswer(row, col, e.key);
      }
      // Backspace
      else if (e.key === 'Backspace') {
        updateAnswer(row, col, '');
      }
      // Space (toggle arah)
      else if (e.key === ' ') {
        e.preventDefault();
        toggleDirection();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedCell, currentCrossword]);

  // Menangani pemeriksaan jawaban
  const handleCheckAnswers = () => {
    const result = checkAnswers();
    setCheckResult(result);

    // Jika semua jawaban benar, tandai sebagai selesai
    if (result.correct) {
      setShowCompletionModal(true);
    }

    // Hapus hasil pemeriksaan setelah 3 detik
    setTimeout(() => {
      setCheckResult(null);
    }, 3000);
  };

  // Format waktu
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12 min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <p className="ml-3 text-gray-600">Memuat teka-teki silang...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4 min-h-[200px] flex items-center justify-center">
        <div>
          <strong className="font-bold">Error! </strong>
          <span className="block sm:inline">{error}</span>
          <p className="mt-4">
            <button
              onClick={() => loadCrossword(crosswordId)}
              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            >
              Coba Lagi
            </button>
          </p>
        </div>
      </div>
    );
  }

  if (!currentCrossword) {
    return (
      <div className="text-center py-12 min-h-[400px] flex flex-col items-center justify-center">
        <p className="text-gray-600 text-xl mb-4">Teka-teki silang tidak ditemukan.</p>
        <p className="text-gray-500">ID: {crosswordId}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Game Info */}
        <div className="lg:w-1/4">
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h3 className="text-lg font-semibold mb-2">Informasi</h3>
            <div className="space-y-2">
              <p className="flex justify-between">
                <span>Waktu:</span>
                <span className="font-medium">{formatTime(timeSpent)}</span>
              </p>
              <p className="flex justify-between">
                <span>Petunjuk Digunakan:</span>
                <span className="font-medium">{hintsUsed}</span>
              </p>
              <p className="flex justify-between">
                <span>Status:</span>
                <span className={`font-medium ${isCompleted ? 'text-green-600' : 'text-blue-600'}`}>
                  {isCompleted ? 'Selesai' : 'Dalam Progres'}
                </span>
              </p>
            </div>
          </div>

          {/* Clues */}
          <CrosswordClues
            crossword={currentCrossword}
            selectedWordId={selectedWordId}
            onSelectWord={(wordId) => {
              const word = currentCrossword.state.wordPositions.find((w) => w.id === wordId);
              if (word) {
                selectCell(word.row, word.col);
              }
            }}
          />
        </div>

        {/* Grid and Controls */}
        <div className="lg:w-3/4">
          <div className="flex flex-col items-center">
            {/* Grid */}
            <div className="w-full min-h-[300px]">
              <CrosswordGrid
                crossword={currentCrossword}
                userAnswers={userAnswers}
                selectedCell={selectedCell}
                selectedWordId={selectedWordId}
                revealedCells={[]}
                incorrectCells={checkResult?.incorrectCells || []}
                onSelectCell={selectCell}
              />
            </div>

            {/* Controls */}
            <CrosswordControls
              onToggleDirection={toggleDirection}
              onRevealCell={() => {
                if (selectedCell) {
                  revealCell(selectedCell[0], selectedCell[1]);
                }
              }}
              onRevealWord={revealWord}
              onCheckAnswers={handleCheckAnswers}
              onSaveProgress={saveProgress}
              onResetCrossword={resetCrossword}
              isCompleted={isCompleted}
            />
          </div>
        </div>
      </div>

      {/* Completion Modal */}
      {showCompletionModal && (
        <CrosswordCompletionModal
          crossword={currentCrossword}
          timeSpent={timeSpent}
          hintsUsed={hintsUsed}
          onClose={() => setShowCompletionModal(false)}
          onPlayAgain={resetCrossword}
        />
      )}
    </div>
  );
}
