'use client';

import React, { useMemo, useCallback, memo } from 'react';
import { Crossword } from '@/lib/api';

interface CrosswordGridProps {
  crossword: Crossword;
  userAnswers: string[][];
  selectedCell: [number, number] | null;
  selectedWordId: string | null;
  revealedCells: number[][];
  incorrectCells: number[][];
  onSelectCell: (row: number, col: number) => void;
  showResults?: boolean;
}

// Memoized Cell component to prevent unnecessary re-renders
const Cell = memo(({
  row,
  col,
  isBlack,
  isSelected,
  isPartOfWord,
  isInRowOrColumn,
  wordNumber,
  userAnswer,
  revealed,
  incorrect,
  correctChar,
  showResults,
  selectedDirection,
  onCellClick,
}: {
  row: number;
  col: number;
  isBlack: boolean;
  isSelected: boolean;
  isPartOfWord: boolean;
  isInRowOrColumn: boolean;
  wordNumber: number | null;
  userAnswer: string;
  revealed: boolean;
  incorrect: boolean;
  correctChar: string;
  showResults: boolean;
  selectedDirection: string | null;
  onCellClick: (row: number, col: number) => void;
}) => {
  // Memoize the click handler to prevent recreating it on every render
  const handleClick = useCallback(() => {
    if (!isBlack) {
      onCellClick(row, col);
    }
  }, [isBlack, row, col, onCellClick]);

  return (
    <div
      className={`relative border border-gray-300 ${
        isBlack
          ? 'bg-black'
          : isSelected
          ? 'bg-blue-300' // Darker blue for selected cell
          : isPartOfWord
          ? 'bg-blue-200' // Medium blue for cells in the same word
          : isInRowOrColumn
          ? 'bg-blue-100' // Light blue for row/column highlighting
          : 'bg-white'
      } ${incorrect ? 'border-red-500 border-2' : ''} ${
        revealed ? 'bg-yellow-100' : ''
      } aspect-square w-full h-full min-h-[30px] transition-colors duration-150 hover:bg-blue-50`}
      onClick={handleClick}
      data-direction={selectedDirection} // Add data attribute for direction
    >
      {!isBlack && (
        <>
          {wordNumber && (
            <span className="absolute top-0 left-0.5 text-[10px] font-bold text-gray-800 p-0.5 z-10 leading-none">
              {wordNumber}
            </span>
          )}
          <span className={`flex items-center justify-center h-full text-lg font-medium ${
            showResults && incorrect ? 'text-red-600' :
            showResults && userAnswer === correctChar ? 'text-green-600' :
            isSelected ? 'text-blue-800' : ''
          }`}>
            {userAnswer || (revealed ? correctChar : '')}
          </span>
        </>
      )}
    </div>
  );
});

// Set display name for debugging
Cell.displayName = 'Cell';

// Main component with performance optimizations
function CrosswordGrid({
  crossword,
  userAnswers,
  selectedCell,
  selectedWordId,
  revealedCells,
  incorrectCells,
  onSelectCell,
  showResults = false,
}: CrosswordGridProps) {
  const { grid_size, state } = crossword;

  // Memoize the word number lookup to avoid recalculating for each cell
  const wordNumbersMap = useMemo(() => {
    const map = new Map<string, number>();

    state.wordPositions.forEach(wordPosition => {
      const key = `${wordPosition.row}-${wordPosition.col}`;
      map.set(key, wordPosition.number);
    });

    return map;
  }, [state.wordPositions]);

  // Get word number for a cell - memoized lookup
  const getWordNumber = useCallback((row: number, col: number): number | null => {
    return wordNumbersMap.get(`${row}-${col}`) || null;
  }, [wordNumbersMap]);

  // Memoize the selected word position for better performance
  const selectedWordPosition = useMemo(() => {
    if (!selectedWordId) return null;

    return state.wordPositions.find(pos =>
      String(pos.id) === String(selectedWordId)
    );
  }, [selectedWordId, state.wordPositions]);

  // Get the selected direction - memoized
  const selectedDirection = useMemo(() => {
    return selectedWordPosition?.direction || null;
  }, [selectedWordPosition]);

  // Create a memoized map of cells that are part of the selected word
  const selectedWordCellsMap = useMemo(() => {
    if (!selectedWordId) return new Map<string, boolean>();

    const map = new Map<string, boolean>();
    const selectedIdStr = String(selectedWordId);

    // Loop through the grid once instead of checking each cell individually
    for (let i = 0; i < grid_size; i++) {
      for (let j = 0; j < grid_size; j++) {
        const cell = state.grid[i]?.[j];
        if (cell && cell.char !== ' ' && cell.wordIds && cell.wordIds.length > 0) {
          if (cell.wordIds.some((id: any) => String(id) === selectedIdStr)) {
            map.set(`${i}-${j}`, true);
          }
        }
      }
    }

    return map;
  }, [selectedWordId, state.grid, grid_size]);

  // Check if a cell is part of the selected word - memoized lookup
  const isPartOfSelectedWord = useCallback((row: number, col: number): boolean => {
    return selectedWordCellsMap.has(`${row}-${col}`);
  }, [selectedWordCellsMap]);

  // Create a memoized map for revealed cells
  const revealedCellsMap = useMemo(() => {
    const map = new Map<string, boolean>();

    revealedCells.forEach(([row, col]) => {
      map.set(`${row}-${col}`, true);
    });

    return map;
  }, [revealedCells]);

  // Check if a cell is revealed - memoized lookup
  const isRevealed = useCallback((row: number, col: number): boolean => {
    return revealedCellsMap.has(`${row}-${col}`);
  }, [revealedCellsMap]);

  // Create a memoized map for incorrect cells
  const incorrectCellsMap = useMemo(() => {
    const map = new Map<string, boolean>();

    incorrectCells.forEach(([row, col]) => {
      map.set(`${row}-${col}`, true);
    });

    return map;
  }, [incorrectCells]);

  // Check if a cell is incorrect - memoized lookup
  const isIncorrect = useCallback((row: number, col: number): boolean => {
    return incorrectCellsMap.has(`${row}-${col}`);
  }, [incorrectCellsMap]);

  // Check if a cell is in the selected row or column - memoized
  const isInSelectedRowOrColumn = useCallback((row: number, col: number): boolean => {
    if (!selectedCell || !selectedWordPosition) return false;

    const [selectedRow, selectedCol] = selectedCell;
    const isPartOfWord = isPartOfSelectedWord(row, col);

    // Don't highlight if the cell is already part of the selected word
    if (isPartOfWord) return false;

    // If the cell is black, don't highlight it
    const isBlack = state.grid[row]?.[col]?.char === ' ' || !state.grid[row]?.[col]?.wordIds?.length;
    if (isBlack) return false;

    // If the selected word is across, highlight cells in the same row
    if (selectedWordPosition.direction === 'across') {
      return row === selectedRow;
    }

    // If the selected word is down, highlight cells in the same column
    if (selectedWordPosition.direction === 'down') {
      return col === selectedCol;
    }

    return false;
  }, [selectedCell, selectedWordPosition, isPartOfSelectedWord, state.grid]);

  // Memoize the grid rendering to prevent unnecessary recalculations
  const gridContent = useMemo(() => {
    const cells = [];

    for (let i = 0; i < grid_size; i++) {
      for (let j = 0; j < grid_size; j++) {
        // Periksa apakah sel kosong (tidak ada kata yang melewatinya)
        const isBlack = state.grid[i]?.[j]?.char === ' ' || !state.grid[i]?.[j]?.wordIds?.length;
        const isSelected = Boolean(selectedCell && selectedCell[0] === i && selectedCell[1] === j);
        const isPartOfWord = isPartOfSelectedWord(i, j);
        const isInRowOrColumn = isInSelectedRowOrColumn(i, j);
        const wordNumber = getWordNumber(i, j);
        const userAnswer = userAnswers[i]?.[j] || '';
        const revealed = isRevealed(i, j);
        const incorrect = isIncorrect(i, j);
        const correctChar = state.grid[i]?.[j]?.char || '';

        cells.push(
          <Cell
            key={`${i}-${j}`}
            row={i}
            col={j}
            isBlack={isBlack}
            isSelected={isSelected}
            isPartOfWord={isPartOfWord}
            isInRowOrColumn={isInRowOrColumn}
            wordNumber={wordNumber}
            userAnswer={userAnswer}
            revealed={revealed}
            incorrect={incorrect}
            correctChar={correctChar}
            showResults={showResults}
            selectedDirection={selectedDirection}
            onCellClick={onSelectCell}
          />
        );
      }
    }

    return (
      <div
        className="grid gap-0.5 mx-auto"
        style={{
          gridTemplateColumns: `repeat(${grid_size}, minmax(0, 1fr))`,
          width: `min(100%, ${grid_size * 2}rem)`,
          height: `min(100%, ${grid_size * 2}rem)`,
          minHeight: '300px',
          aspectRatio: '1 / 1',
        }}
      >
        {cells}
      </div>
    );
  }, [
    grid_size,
    state.grid,
    selectedCell,
    isPartOfSelectedWord,
    isInSelectedRowOrColumn,
    getWordNumber,
    userAnswers,
    isRevealed,
    isIncorrect,
    showResults,
    selectedDirection,
    onSelectCell
  ]);

  return (
    <div className="w-full overflow-auto p-4 min-h-[350px] flex items-center justify-center">
      {gridContent}
    </div>
  );
}

// Export as memoized component to prevent unnecessary re-renders
export default memo(CrosswordGrid);
