'use client';

import React, { useState, useEffect } from 'react';
import CrosswordCard from './CrosswordCard';
import { Crossword, crosswordAPI } from '@/lib/api';

interface CrosswordListProps {
  initialCrosswords?: Crossword[];
  categoryId?: string;
  difficulty?: string;
  limit?: number;
}

export default function CrosswordList({
  initialCrosswords,
  categoryId,
  difficulty,
  limit = 12,
}: CrosswordListProps) {
  const [crosswords, setCrosswords] = useState<Crossword[]>(initialCrosswords || []);
  const [loading, setLoading] = useState(!initialCrosswords);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (!initialCrosswords) {
      loadCrosswords();
    }
  }, [initialCrosswords, categoryId, difficulty, page]);

  const loadCrosswords = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await crosswordAPI.getAll({
        category_id: categoryId,
        difficulty,
        is_public: 1,
        limit,
        page,
      });

      if (response.status === 'success' && response.data) {
        setCrosswords(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.totalPages);
        }
      } else {
        setError(response.message || 'Gagal memuat teka-teki silang');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat memuat teka-teki silang');
      console.error('Load crosswords error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4">
        <strong className="font-bold">Error! </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  if (crosswords.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Tidak ada teka-teki silang yang ditemukan.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {crosswords.map((crossword) => (
          <CrosswordCard key={crossword.id} crossword={crossword} />
        ))}
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <nav className="flex items-center">
            <button
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
              className={`px-3 py-1 rounded-l-md border ${
                page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-blue-600 hover:bg-blue-50'
              }`}
            >
              &laquo; Sebelumnya
            </button>

            <div className="flex">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNumber;
                if (totalPages <= 5) {
                  pageNumber = i + 1;
                } else if (page <= 3) {
                  pageNumber = i + 1;
                } else if (page >= totalPages - 2) {
                  pageNumber = totalPages - 4 + i;
                } else {
                  pageNumber = page - 2 + i;
                }

                return (
                  <button
                    key={pageNumber}
                    onClick={() => handlePageChange(pageNumber)}
                    className={`px-3 py-1 border-t border-b ${
                      page === pageNumber
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-blue-600 hover:bg-blue-50'
                    }`}
                  >
                    {pageNumber}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
              className={`px-3 py-1 rounded-r-md border ${
                page === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-blue-600 hover:bg-blue-50'
              }`}
            >
              Selanjutnya &raquo;
            </button>
          </nav>
        </div>
      )}
    </div>
  );
}
