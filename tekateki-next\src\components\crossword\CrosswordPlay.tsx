'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Crossword } from '@/lib/api';
import CrosswordGrid from './CrosswordGrid';
import CrosswordClues from './CrosswordClues';
import CrosswordControls from './CrosswordControls';
import { TagIcon, XIcon, Maximize2Icon, Minimize2Icon, PauseIcon, PlayIcon, HelpCircle, Clock } from 'lucide-react';

interface CrosswordPlayProps {
  crossword: Crossword;
}

export default function CrosswordPlay({ crossword }: CrosswordPlayProps) {
  // State for the crossword grid
  const [userAnswers, setUserAnswers] = useState<string[][]>([]);
  const [selectedCell, setSelectedCell] = useState<[number, number] | null>(null);
  const [selectedDirection, setSelectedDirection] = useState<'across' | 'down'>('across');
  const [selectedWordId, setSelectedWordId] = useState<string | null>(null);
  const [incorrectCells, setIncorrectCells] = useState<number[][]>([]);
  const [revealedCells, setRevealedCells] = useState<number[][]>([]);
  const [showResults, setShowResults] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  // New state for enhanced features
  const [timeSpent, setTimeSpent] = useState(0);
  const [hintsUsed, setHintsUsed] = useState(0);
  const [hintsRemaining, setHintsRemaining] = useState(3); // Default 3 hints
  const [isMobile, setIsMobile] = useState(false);
  const [showFloatingClue, setShowFloatingClue] = useState(false);
  const [floatingClue, setFloatingClue] = useState<{
    text: string;
    number: number;
    direction: 'across' | 'down';
  } | null>(null);
  const [focusMode, setFocusMode] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  // Refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const keyboardHandlerRef = useRef<(e: KeyboardEvent) => void>(() => {});

  // Initialize the grid and detect mobile when component mounts
  useEffect(() => {
    // Initialize grid
    const gridSize = crossword.grid_size;
    const emptyAnswers = Array(gridSize).fill(null).map(() => Array(gridSize).fill(''));
    setUserAnswers(emptyAnswers);

    // Detect mobile device
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Start timer
    const startTimer = () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      timerRef.current = setInterval(() => {
        if (!isPaused) {
          setTimeSpent(prev => prev + 1);
        }
      }, 1000);
    };

    // Call once and add event listeners
    checkMobile();
    startTimer();
    window.addEventListener('resize', checkMobile);

    // Cleanup when component unmounts
    return () => {
      window.removeEventListener('resize', checkMobile);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [crossword.grid_size, isPaused]);

  // Handle cell selection - memoized with useCallback
  const handleCellSelect = useCallback((row: number, col: number) => {
    setSelectedCell([row, col]);

    // Find the word that contains this cell
    const cell = crossword.state.grid[row][col];
    if (!cell || cell.char === ' ' || !cell.wordIds || cell.wordIds.length === 0) {
      return;
    }

    // If there's only one word, select it
    if (cell.wordIds.length === 1) {
      setSelectedWordId(cell.wordIds[0].toString());

      // Determine direction based on the word position
      const wordPosition = crossword.state.wordPositions.find(
        pos => pos.id === cell.wordIds[0]
      );
      if (wordPosition) {
        setSelectedDirection(wordPosition.direction as 'across' | 'down');
      }
      return;
    }

    // If there are multiple words, try to select based on current direction
    const wordInCurrentDirection = cell.wordIds.find((wordId: number) => {
      const wordPosition = crossword.state.wordPositions.find(
        pos => pos.id === wordId
      );
      return wordPosition && wordPosition.direction === selectedDirection;
    });

    if (wordInCurrentDirection) {
      setSelectedWordId(wordInCurrentDirection.toString());
    } else {
      // Toggle direction if no word in current direction
      const newDirection = selectedDirection === 'across' ? 'down' : 'across';
      setSelectedDirection(newDirection);

      // Find word in new direction
      const wordInNewDirection = cell.wordIds.find((wordId: number) => {
        const wordPosition = crossword.state.wordPositions.find(
          pos => pos.id === wordId
        );
        return wordPosition && wordPosition.direction === newDirection;
      });

      if (wordInNewDirection) {
        setSelectedWordId(wordInNewDirection.toString());
      } else {
        // If still no match, just select the first word
        setSelectedWordId(cell.wordIds[0].toString());
      }
    }

    // Update floating clue for mobile
    updateFloatingClue();
  }, [crossword.state.grid, crossword.state.wordPositions, selectedDirection]);

  // Update floating clue based on selected word
  const updateFloatingClue = useCallback(() => {
    if (!selectedWordId) return;

    const wordPosition = crossword.state.wordPositions.find(
      (w) => String(w.id) === selectedWordId
    );

    if (wordPosition) {
      const { direction, number } = wordPosition;
      const dir = direction as 'across' | 'down';
      const clueText = crossword.state.clues[dir][number] || '';

      setFloatingClue({
        text: clueText,
        number,
        direction: dir
      });

      // Automatically show floating clue on mobile
      if (isMobile) {
        setShowFloatingClue(true);
      }
    }
  }, [crossword.state.clues, crossword.state.wordPositions, isMobile, selectedWordId]);

  // Handle keyboard input with memoized handler
  useEffect(() => {
    // Create a memoized handler that we can store in a ref
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard events when paused
      if (isPaused || !selectedCell) return;

      const [row, col] = selectedCell;
      const gridSize = crossword.grid_size;

      // Handle letter input (a-z, A-Z)
      if (/^[a-zA-Z]$/.test(e.key)) {
        updateAnswer(row, col, e.key.toUpperCase());
        moveToNextCell();
      }
      // Handle backspace
      else if (e.key === 'Backspace') {
        updateAnswer(row, col, '');
        moveToPreviousCell();
      }
      // Handle arrow keys for navigation
      else if (e.key === 'ArrowRight') {
        navigateToCell(row, Math.min(col + 1, gridSize - 1));
      }
      else if (e.key === 'ArrowLeft') {
        navigateToCell(row, Math.max(col - 1, 0));
      }
      else if (e.key === 'ArrowDown') {
        navigateToCell(Math.min(row + 1, gridSize - 1), col);
      }
      else if (e.key === 'ArrowUp') {
        navigateToCell(Math.max(row - 1, 0), col);
      }
      // Handle space to toggle direction
      else if (e.key === ' ') {
        e.preventDefault();
        toggleDirection();
      }
      // Handle Tab to move to next word
      else if (e.key === 'Tab') {
        e.preventDefault();
        moveToNextWord(e.shiftKey);
      }
    };

    // Store the current handler in the ref
    keyboardHandlerRef.current = handleKeyDown;

    // The actual event listener uses the ref to always get the latest handler
    const eventListener = (e: KeyboardEvent) => {
      if (keyboardHandlerRef.current) {
        keyboardHandlerRef.current(e);
      }
    };

    window.addEventListener('keydown', eventListener);
    return () => window.removeEventListener('keydown', eventListener);
  }, [selectedCell, selectedDirection, isPaused, crossword.grid_size]);

  // Move to next word in the puzzle (for Tab navigation)
  const moveToNextWord = useCallback((reverse: boolean = false) => {
    if (!selectedWordId) return;

    // Get all word positions
    const { wordPositions } = crossword.state;

    // Find current word index
    const currentIndex = wordPositions.findIndex(w => String(w.id) === selectedWordId);
    if (currentIndex === -1) return;

    // Calculate next word index (with wrap-around)
    const nextIndex = reverse
      ? (currentIndex - 1 + wordPositions.length) % wordPositions.length
      : (currentIndex + 1) % wordPositions.length;

    // Select the next word
    const nextWord = wordPositions[nextIndex];
    setSelectedWordId(String(nextWord.id));
    setSelectedCell([nextWord.row, nextWord.col]);
    setSelectedDirection(nextWord.direction as 'across' | 'down');

    // Update floating clue
    updateFloatingClue();
  }, [crossword.state, selectedWordId, updateFloatingClue]);

  // Update answer in a cell - memoized
  const updateAnswer = useCallback((row: number, col: number, value: string) => {
    setUserAnswers(prev => {
      const newAnswers = [...prev];
      if (!newAnswers[row]) {
        newAnswers[row] = [];
      }
      newAnswers[row][col] = value;
      return newAnswers;
    });
  }, []);

  // Move to the next cell in the current direction - memoized
  const moveToNextCell = useCallback(() => {
    if (!selectedCell || !selectedWordId) return;

    const [row, col] = selectedCell;
    const wordPosition = crossword.state.wordPositions.find(
      pos => pos.id === parseInt(selectedWordId)
    );

    if (!wordPosition) return;

    if (wordPosition.direction === 'across') {
      // Move right
      const nextCol = col + 1;
      if (nextCol < crossword.grid_size &&
          crossword.state.grid[row][nextCol]?.char !== ' ' &&
          crossword.state.grid[row][nextCol]?.wordIds.includes(parseInt(selectedWordId))) {
        setSelectedCell([row, nextCol]);
      }
    } else {
      // Move down
      const nextRow = row + 1;
      if (nextRow < crossword.grid_size &&
          crossword.state.grid[nextRow][col]?.char !== ' ' &&
          crossword.state.grid[nextRow][col]?.wordIds.includes(parseInt(selectedWordId))) {
        setSelectedCell([nextRow, col]);
      }
    }
  }, [crossword.grid_size, crossword.state.grid, crossword.state.wordPositions, selectedCell, selectedWordId]);

  // Move to the previous cell in the current direction - memoized
  const moveToPreviousCell = useCallback(() => {
    if (!selectedCell || !selectedWordId) return;

    const [row, col] = selectedCell;
    const wordPosition = crossword.state.wordPositions.find(
      pos => pos.id === parseInt(selectedWordId)
    );

    if (!wordPosition) return;

    if (wordPosition.direction === 'across') {
      // Move left
      const prevCol = col - 1;
      if (prevCol >= 0 &&
          crossword.state.grid[row][prevCol]?.char !== ' ' &&
          crossword.state.grid[row][prevCol]?.wordIds.includes(parseInt(selectedWordId))) {
        setSelectedCell([row, prevCol]);
      }
    } else {
      // Move up
      const prevRow = row - 1;
      if (prevRow >= 0 &&
          crossword.state.grid[prevRow][col]?.char !== ' ' &&
          crossword.state.grid[prevRow][col]?.wordIds.includes(parseInt(selectedWordId))) {
        setSelectedCell([prevRow, col]);
      }
    }
  }, [crossword.state.grid, crossword.state.wordPositions, selectedCell, selectedWordId]);



  // Navigate to a specific cell - memoized
  const navigateToCell = useCallback((row: number, col: number) => {
    if (row >= 0 && row < crossword.grid_size &&
        col >= 0 && col < crossword.grid_size &&
        crossword.state.grid[row][col]?.char !== ' ') {
      setSelectedCell([row, col]);

      // Update selected word based on the new cell
      const cell = crossword.state.grid[row][col];
      if (cell && cell.wordIds && cell.wordIds.length > 0) {
        // Try to find a word in the current direction
        const wordInCurrentDirection = cell.wordIds.find((wordId: number) => {
          const wordPosition = crossword.state.wordPositions.find(
            pos => pos.id === wordId
          );
          return wordPosition && wordPosition.direction === selectedDirection;
        });

        if (wordInCurrentDirection) {
          setSelectedWordId(wordInCurrentDirection.toString());
        } else if (cell.wordIds.length > 0) {
          // If no word in current direction, select the first word
          setSelectedWordId(cell.wordIds[0].toString());

          // Update direction based on the selected word
          const wordPosition = crossword.state.wordPositions.find(
            pos => pos.id === cell.wordIds[0]
          );
          if (wordPosition) {
            setSelectedDirection(wordPosition.direction as 'across' | 'down');
          }
        }

        // Update floating clue
        updateFloatingClue();
      }
    }
  }, [crossword.grid_size, crossword.state.grid, crossword.state.wordPositions, selectedDirection, updateFloatingClue]);

  // Toggle direction between across and down - memoized
  const toggleDirection = useCallback(() => {
    const newDirection = selectedDirection === 'across' ? 'down' : 'across';
    setSelectedDirection(newDirection);

    // Update selected word based on new direction if possible
    if (selectedCell) {
      const [row, col] = selectedCell;
      const cell = crossword.state.grid[row][col];

      if (cell && cell.wordIds && cell.wordIds.length > 0) {
        const wordInNewDirection = cell.wordIds.find((wordId: number) => {
          const wordPosition = crossword.state.wordPositions.find(
            pos => pos.id === wordId
          );
          return wordPosition && wordPosition.direction === newDirection;
        });

        if (wordInNewDirection) {
          setSelectedWordId(wordInNewDirection.toString());
          updateFloatingClue();
        }
      }
    }
  }, [crossword.state.grid, crossword.state.wordPositions, selectedCell, selectedDirection, updateFloatingClue]);

  // Check if the grid is completely filled - memoized
  const isGridFilled = useCallback(() => {
    for (let row = 0; row < crossword.grid_size; row++) {
      for (let col = 0; col < crossword.grid_size; col++) {
        const cell = crossword.state.grid[row][col];

        if (cell && cell.char !== ' ' && cell.wordIds && cell.wordIds.length > 0) {
          const userAnswer = userAnswers[row]?.[col] || '';

          if (userAnswer === '') {
            return false;
          }
        }
      }
    }

    return true;
  }, [crossword.grid_size, crossword.state.grid, userAnswers]);

  // Check answers - memoized
  const checkAnswers = useCallback(() => {
    const incorrectCells: number[][] = [];

    for (let row = 0; row < crossword.grid_size; row++) {
      for (let col = 0; col < crossword.grid_size; col++) {
        const cell = crossword.state.grid[row][col];

        if (cell && cell.char !== ' ' && cell.wordIds && cell.wordIds.length > 0) {
          const userAnswer = userAnswers[row]?.[col] || '';

          if (userAnswer !== '' && userAnswer !== cell.char) {
            incorrectCells.push([row, col]);
          }
        }
      }
    }

    setIncorrectCells(incorrectCells);
    setShowResults(true);

    // Hide results after 3 seconds
    setTimeout(() => {
      setShowResults(false);
      setIncorrectCells([]);
    }, 3000);

    // Check if all answers are correct
    const isComplete = incorrectCells.length === 0 && isGridFilled();
    if (isComplete) {
      setIsCompleted(true);
    }

    return {
      correct: incorrectCells.length === 0,
      incorrectCells
    };
  }, [crossword.grid_size, crossword.state.grid, userAnswers, isGridFilled]);

  // Provide a hint - new feature
  const provideHint = useCallback(() => {
    if (!selectedCell || hintsRemaining <= 0) return;

    const [row, col] = selectedCell;
    const cell = crossword.state.grid[row][col];

    if (cell && cell.char !== ' ') {
      // Reveal the correct letter
      updateAnswer(row, col, cell.char);

      // Add to revealed cells
      setRevealedCells(prev => [...prev, [row, col]]);

      // Update hints used/remaining
      setHintsUsed(prev => prev + 1);
      setHintsRemaining(prev => prev - 1);
    }
  }, [crossword.state.grid, hintsRemaining, selectedCell, updateAnswer]);

  // Toggle focus mode
  const toggleFocusMode = useCallback(() => {
    setFocusMode(prev => !prev);
  }, []);

  // Toggle pause state
  const togglePause = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);



  // Reset the crossword - memoized
  const resetCrossword = useCallback(() => {
    const gridSize = crossword.grid_size;
    const emptyAnswers = Array(gridSize).fill(null).map(() => Array(gridSize).fill(''));
    setUserAnswers(emptyAnswers);
    setRevealedCells([]);
    setIncorrectCells([]);
    setShowResults(false);
    setIsCompleted(false);
    setTimeSpent(0);
    setHintsUsed(0);
    setHintsRemaining(3);
  }, [crossword.grid_size]);

  // Memoize the stats data for potential future use
  useMemo(() => {
    return {
      timeSpent,
      hintsUsed,
      hintsRemaining,
      progress: calculateProgress(),
      score: calculateScore()
    };
  }, [timeSpent, hintsUsed, hintsRemaining, userAnswers, crossword.grid_size, crossword.state.grid]);

  // Calculate progress percentage
  function calculateProgress() {
    let totalCells = 0;
    let filledCells = 0;

    for (let i = 0; i < crossword.grid_size; i++) {
      for (let j = 0; j < crossword.grid_size; j++) {
        const cell = crossword.state.grid[i]?.[j];
        if (cell && cell.char !== ' ' && cell.wordIds && cell.wordIds.length > 0) {
          totalCells++;
          if (userAnswers[i]?.[j]) {
            filledCells++;
          }
        }
      }
    }

    return totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0;
  }

  // Calculate score based on time and hints
  function calculateScore() {
    const baseScore = 1000;
    const timeDeduction = Math.floor(timeSpent / 10); // Deduct 1 point per 10 seconds
    const hintDeduction = hintsUsed * 50; // Deduct 50 points per hint

    let score = baseScore - timeDeduction - hintDeduction;
    return Math.max(0, score); // Minimum score is 0
  }

  return (
    <div className={`relative ${focusMode ? 'bg-gray-100' : ''}`}>
      {/* Floating Clue for Mobile */}
      {isMobile && showFloatingClue && floatingClue && (
        <div className="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg z-50 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <span className="font-bold">{floatingClue.number} {floatingClue.direction === 'across' ? 'Mendatar' : 'Menurun'}: </span>
              <span>{floatingClue.text}</span>
            </div>
            <button
              onClick={() => setShowFloatingClue(false)}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              <XIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}

      {/* Game Controls */}
      <div className="bg-white p-3 mb-4 shadow-sm flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleFocusMode}
            className="p-2 rounded-full hover:bg-gray-100"
            title={focusMode ? "Exit Focus Mode" : "Focus Mode"}
          >
            {focusMode ? <Minimize2Icon className="w-5 h-5" /> : <Maximize2Icon className="w-5 h-5" />}
          </button>
          <button
            onClick={togglePause}
            className="p-2 rounded-full hover:bg-gray-100"
            title={isPaused ? "Resume" : "Pause"}
          >
            {isPaused ? <PlayIcon className="w-5 h-5" /> : <PauseIcon className="w-5 h-5" />}
          </button>
        </div>

        {/* Game Stats Compact View */}
        <div className="flex justify-between items-center">
          <div className="flex items-center mx-2">
            <Clock className="w-4 h-4 mr-1 text-blue-600" />
            <span className="text-sm">{Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}</span>
          </div>
          <div className="flex items-center mx-2">
            <HelpCircle className="w-4 h-4 mr-1 text-yellow-600" />
            <span className="text-sm">{hintsRemaining}/3</span>
          </div>
        </div>
      </div>

      {/* Main Game Area */}
      <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${focusMode ? 'max-w-4xl mx-auto' : ''}`}>
        <div className="">
          {/* CrosswordGrid */}
          <div className="bg-white rounded-lg shadow-md aspect-square mx-auto">
            <CrosswordGrid
              crossword={crossword}
              userAnswers={userAnswers}
              selectedCell={selectedCell}
              selectedWordId={selectedWordId}
              revealedCells={revealedCells}
              incorrectCells={incorrectCells}
              onSelectCell={handleCellSelect}
              showResults={showResults}
            />
          </div>

          {/* Controls */}
          <div className="mt-6">
            <CrosswordControls
              onToggleDirection={toggleDirection}
              onCheckAnswers={checkAnswers}
              onResetCrossword={resetCrossword}
              onProvideHint={provideHint}
              isCompleted={isCompleted}
              hintsRemaining={hintsRemaining}
            />
          </div>
        </div>

        <div className={`space-y-6 ${focusMode && !isMobile ? 'hidden' : ''}`}>
          {/* Clues */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Petunjuk</h2>
              {crossword.category_name && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded flex items-center">
                  <TagIcon className="w-3 h-3 mr-1" />
                  {crossword.category_name}
                </span>
              )}
            </div>
            <CrosswordClues
              crossword={crossword}
              selectedWordId={selectedWordId}
              onSelectWord={(wordId) => {
                setSelectedWordId(wordId);
                const word = crossword.state.wordPositions.find((w) => String(w.id) === wordId);
                if (word) {
                  setSelectedCell([word.row, word.col]);
                  setSelectedDirection(word.direction as 'across' | 'down');
                  updateFloatingClue();
                }
              }}
            />
          </div>

          {/* Game Stats */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-lg font-semibold mb-3">Statistik Permainan</h3>

            <div className="space-y-4">
              {/* Progress Bar */}
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Penyelesaian</span>
                  <span className="text-sm font-medium">{calculateProgress()}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${calculateProgress()}%` }}
                  ></div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-sm text-gray-500">Waktu</div>
                  <div className="text-lg font-semibold">
                    {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="text-sm text-gray-500">Bantuan</div>
                  <div className="text-lg font-semibold">
                    {hintsUsed} digunakan
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pause Overlay */}
      {isPaused && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-xl text-center">
            <h2 className="text-2xl font-bold mb-4">Permainan Dijeda</h2>
            <p className="mb-6">Klik tombol di bawah untuk melanjutkan permainan.</p>
            <button
              onClick={togglePause}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Lanjutkan Bermain
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
