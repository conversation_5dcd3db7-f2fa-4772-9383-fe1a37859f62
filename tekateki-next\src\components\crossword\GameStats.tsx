'use client';

import React from 'react';
import { useCrossword } from '@/context/CrosswordContext';
import { <PERSON><PERSON><PERSON>, CheckCircle, Clock, HelpCircle, Award } from 'lucide-react';

interface GameStatsProps {
  compact?: boolean;
}

export default function GameStats({ compact = false }: GameStatsProps) {
  const {
    currentCrossword,
    userAnswers,
    timeSpent,
    hintsUsed,
    hintsRemaining,
    maxHints,
    isCompleted,
  } = useCrossword();

  if (!currentCrossword) return null;

  // Hitung persentase penyelesaian
  const calculateProgress = () => {
    if (!currentCrossword) return 0;

    const gridSize = currentCrossword.grid_size;
    let totalCells = 0;
    let filledCells = 0;

    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        const cell = currentCrossword.state.grid[i]?.[j];
        if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
          totalCells++;
          if (userAnswers[i]?.[j]) {
            filledCells++;
          }
        }
      }
    }

    return totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0;
  };

  // Format waktu dalam menit dan detik
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Hitung skor berdasarkan waktu dan petunjuk yang digunakan
  const calculateScore = () => {
    const baseScore = 1000;
    const timeDeduction = Math.floor(timeSpent / 10); // Kurangi 1 poin setiap 10 detik
    const hintDeduction = hintsUsed * 50; // Kurangi 50 poin untuk setiap petunjuk
    
    let score = baseScore - timeDeduction - hintDeduction;
    return Math.max(0, score); // Skor minimal 0
  };

  const progress = calculateProgress();
  const score = calculateScore();

  if (compact) {
    return (
      <div className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm">
        <div className="flex items-center">
          <Clock className="w-4 h-4 mr-1 text-blue-600" />
          <span className="text-sm">{formatTime(timeSpent)}</span>
        </div>
        <div className="flex items-center">
          <HelpCircle className="w-4 h-4 mr-1 text-yellow-600" />
          <span className="text-sm">{hintsRemaining}/{maxHints}</span>
        </div>
        <div className="flex items-center">
          <BarChart className="w-4 h-4 mr-1 text-green-600" />
          <span className="text-sm">{progress}%</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold mb-3 flex items-center">
        <Award className="w-5 h-5 mr-2 text-blue-600" />
        Statistik Permainan
      </h3>
      
      <div className="space-y-4">
        {/* Progress Bar */}
        <div>
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium">Penyelesaian</span>
            <span className="text-sm font-medium">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
        
        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Waktu</div>
            <div className="text-lg font-semibold flex items-center">
              <Clock className="w-4 h-4 mr-1 text-blue-600" />
              {formatTime(timeSpent)}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Bantuan</div>
            <div className="text-lg font-semibold flex items-center">
              <HelpCircle className="w-4 h-4 mr-1 text-yellow-600" />
              {hintsUsed} digunakan
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Status</div>
            <div className="text-lg font-semibold flex items-center">
              <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
              {isCompleted ? 'Selesai' : 'Dalam Progres'}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Skor</div>
            <div className="text-lg font-semibold flex items-center">
              <Award className="w-4 h-4 mr-1 text-purple-600" />
              {score} poin
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
