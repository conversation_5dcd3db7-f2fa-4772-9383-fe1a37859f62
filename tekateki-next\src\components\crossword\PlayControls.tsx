'use client';

import React, { useState } from 'react';
import { useCrossword } from '@/context/CrosswordContext';
import { CheckIcon, RefreshCwIcon, HelpCircleIcon, FlagIcon, RotateCwIcon, SaveIcon } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface PlayControlsProps {
  onToggleDirection: () => void;
  onRevealCell: () => void;
  onRevealWord: () => void;
  onCheckAnswers: () => void;
  onSaveProgress: () => void;
  onResetCrossword: () => void;
  onSurrender: () => void;
  isCompleted: boolean;
  showResults?: boolean;
}

export default function PlayControls({
  onToggleDirection,
  onRevealCell,
  onRevealWord,
  onCheckAnswers,
  onSaveProgress,
  onResetCrossword,
  onSurrender,
  isCompleted,
  showResults = false,
}: PlayControlsProps) {
  const { currentCrossword, hintsUsed, hintsRemaining, maxHints, timeSpent, userAnswers, isCompleted: crosswordCompleted, canUseHint } = useCrossword();
  const [showHintConfirm, setShowHintConfirm] = useState(false);
  const [showSurrenderConfirm, setShowSurrenderConfirm] = useState(false);

  // Format time spent in minutes and seconds
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Calculate progress percentage based on filled cells
  const calculateProgress = () => {
    if (!currentCrossword || !userAnswers) return 0;

    let totalCells = 0;
    let filledCells = 0;
    const gridSize = currentCrossword.grid_size;

    // Count filled cells and total valid cells
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const cell = currentCrossword.state.grid[row]?.[col];

        // If cell is valid (not empty and has wordIds)
        if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
          totalCells++;

          // Count filled cells (either correct or incorrect)
          if (userAnswers[row]?.[col]) {
            filledCells++;
          }
        }
      }
    }

    return totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0;
  };

  return (
    <div className="space-y-4">
      {/* Progress Bar */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">Progress</h3>
          <span className="text-lg font-bold">{calculateProgress()}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${calculateProgress()}%` }}
          ></div>
        </div>

        <div className="flex justify-between mt-2 text-sm text-gray-600">
          <span>Waktu: {formatTime(timeSpent)}</span>
          <span>Petunjuk: {hintsUsed} digunakan / {hintsRemaining} tersisa</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-3 justify-center">
        <button
          onClick={onCheckAnswers}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          <CheckIcon className="w-4 h-4 mr-2" />
          Periksa Jawaban
        </button>

        <button
          onClick={onResetCrossword}
          className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          <RefreshCwIcon className="w-4 h-4 mr-2" />
          Reset Jawaban
        </button>

        <button
          onClick={onToggleDirection}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <RotateCwIcon className="w-4 h-4 mr-2" />
          Ubah Arah
        </button>

        <button
          onClick={() => setShowHintConfirm(true)}
          disabled={isCompleted || !canUseHint()}
          className={`flex items-center px-4 py-2 rounded-md ${
            isCompleted || !canUseHint()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-yellow-500 text-white hover:bg-yellow-600'
          }`}
          title={!canUseHint() ? 'Tidak ada petunjuk tersisa' : 'Gunakan petunjuk'}
        >
          <HelpCircleIcon className="w-4 h-4 mr-2" />
          Bantuan ({hintsRemaining})
        </button>

        <button
          onClick={onSaveProgress}
          className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          <SaveIcon className="w-4 h-4 mr-2" />
          Simpan Progres
        </button>

        <button
          onClick={() => setShowSurrenderConfirm(true)}
          disabled={isCompleted}
          className={`flex items-center px-4 py-2 rounded-md ${
            isCompleted
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-purple-600 text-white hover:bg-purple-700'
          }`}
        >
          <FlagIcon className="w-4 h-4 mr-2" />
          Menyerah
        </button>
      </div>

      {/* Hint Confirmation Modal */}
      {showHintConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold mb-4">Gunakan Bantuan?</h3>
            <p className="mb-4">
              Apakah Anda yakin ingin menggunakan bantuan? Ini akan mengungkapkan jawaban untuk sel yang dipilih.
              Anda memiliki {hintsRemaining} petunjuk tersisa.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowHintConfirm(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Batal
              </button>
              <button
                onClick={() => {
                  onRevealCell();
                  setShowHintConfirm(false);
                }}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
              >
                Gunakan Bantuan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Surrender Confirmation Modal */}
      {showSurrenderConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold mb-4">Menyerah?</h3>
            <p className="mb-4">
              Apakah Anda yakin ingin menyerah? Semua jawaban yang benar akan ditampilkan.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSurrenderConfirm(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Batal
              </button>
              <button
                onClick={() => {
                  onSurrender();
                  setShowSurrenderConfirm(false);
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Menyerah
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
