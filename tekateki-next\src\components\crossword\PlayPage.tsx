'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Crossword } from '@/lib/api';
import { useAuth } from '@/context/AuthContext';
import { useCrossword } from '@/context/CrosswordContext';
import CrosswordGrid from './CrosswordGrid';
import CrosswordClues from './CrosswordClues';
import PlayControls from './PlayControls';
import GameStats from './GameStats';
import CrosswordCompletionModal from './CrosswordCompletionModal';
import ShareButton from '@/components/ui/ShareButton';
import { TagIcon, XIcon, Maximize2Icon, Minimize2Icon, PauseIcon, PlayIcon } from 'lucide-react';

interface PlayPageProps {
  crossword: Crossword;
}

export default function PlayPage({ crossword }: PlayPageProps) {
  const { user } = useAuth();
  const {
    currentCrossword,
    userAnswers,
    selectedCell,
    selectedDirection,
    selectedWordId,
    isCompleted,
    timeSpent,
    hintsUsed,
    hintsRemaining,
    maxHints,
    loading,
    error,
    loadCrossword,
    selectCell,
    toggleDirection,
    updateAnswer,
    revealCell,
    revealWord,
    checkAnswers,
    saveProgress,
    resetCrossword,
    surrender,
  } = useCrossword();

  const [showResults, setShowResults] = useState(false);
  const [progressSaved, setProgressSaved] = useState(false);
  const [checkResult, setCheckResult] = useState<{
    correct: boolean;
    incorrectCells: number[][];
  } | null>(null);

  // State untuk fitur baru
  const [isMobile, setIsMobile] = useState(false);
  const [showFloatingClue, setShowFloatingClue] = useState(false);
  const [floatingClue, setFloatingClue] = useState<{
    text: string;
    number: number;
    direction: 'across' | 'down';
  } | null>(null);
  const [focusMode, setFocusMode] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Load crossword when component mounts and detect mobile
  useEffect(() => {
    console.log(`PlayPage: Loading crossword with id ${crossword.id}`);
    loadCrossword(crossword.id);

    // Detect mobile device
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Call once and add event listener
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Cleanup when component unmounts
    return () => {
      if (user && currentCrossword) {
        saveProgress();
      }
      window.removeEventListener('resize', checkMobile);
    };
  }, [crossword.id, loadCrossword, user, currentCrossword, saveProgress]);

  // Update floating clue when selectedWordId changes
  useEffect(() => {
    if (!selectedWordId || !currentCrossword) return;

    const wordPosition = currentCrossword.state.wordPositions.find(
      (w) => String(w.id) === String(selectedWordId)
    );

    if (wordPosition) {
      const { direction, number } = wordPosition;
      // Type assertion to ensure TypeScript knows direction is 'across' | 'down'
      const dir = direction as 'across' | 'down';
      const clueText = currentCrossword.state.clues[dir][number] || '';

      setFloatingClue({
        text: clueText,
        number,
        direction: dir
      });

      // Automatically show floating clue on mobile
      if (isMobile) {
        setShowFloatingClue(true);
      }
    }
  }, [selectedWordId, currentCrossword, isMobile]);

  // Handle keyboard input with improved navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedCell || !currentCrossword) return;

      const [row, col] = selectedCell;
      const gridSize = currentCrossword.grid_size;

      // Find the current word and direction
      const currentWordPosition = selectedWordId
        ? currentCrossword.state.wordPositions.find(pos => String(pos.id) === String(selectedWordId))
        : null;
      const currentDirection = currentWordPosition?.direction || selectedDirection;

      // Helper function to find the next valid cell in a direction
      const findNextValidCell = (startRow: number, startCol: number, rowDelta: number, colDelta: number): [number, number] | null => {
        let r = startRow + rowDelta;
        let c = startCol + colDelta;

        // Check bounds
        while (r >= 0 && r < gridSize && c >= 0 && c < gridSize) {
          // Check if cell is valid (not black)
          const cell = currentCrossword.state.grid[r]?.[c];
          if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
            return [r, c];
          }
          r += rowDelta;
          c += colDelta;
        }

        return null; // No valid cell found
      };

      // Helper function to move to the next cell in the current word
      const moveToNextCellInWord = () => {
        if (!currentWordPosition) return;

        const { direction, row: wordRow, col: wordCol, length } = currentWordPosition;

        // Calculate position within the word
        let posInWord = 0;
        if (direction === 'across') {
          posInWord = col - wordCol;
        } else {
          posInWord = row - wordRow;
        }

        // If we're at the end of the word, don't move
        if (posInWord >= length - 1) return;

        // Move to the next position
        if (direction === 'across') {
          selectCell(row, col + 1);
        } else {
          selectCell(row + 1, col);
        }
      };

      // Helper function to move to the previous cell in the current word
      const moveToPrevCellInWord = () => {
        if (!currentWordPosition) return;

        const { direction, row: wordRow, col: wordCol } = currentWordPosition;

        // Calculate position within the word
        let posInWord = 0;
        if (direction === 'across') {
          posInWord = col - wordCol;
        } else {
          posInWord = row - wordRow;
        }

        // If we're at the beginning of the word, don't move
        if (posInWord <= 0) return;

        // Move to the previous position
        if (direction === 'across') {
          selectCell(row, col - 1);
        } else {
          selectCell(row - 1, col);
        }
      };

      // Arrow keys with smart navigation
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (currentDirection === 'down') {
          // If moving in the direction of the word, move to previous cell in word
          moveToPrevCellInWord();
        } else {
          // Otherwise, find the next valid cell in that direction
          const nextCell = findNextValidCell(row, col, -1, 0);
          if (nextCell) {
            selectCell(nextCell[0], nextCell[1]);
          }
        }
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (currentDirection === 'down') {
          // If moving in the direction of the word, move to next cell in word
          moveToNextCellInWord();
        } else {
          // Otherwise, find the next valid cell in that direction
          const nextCell = findNextValidCell(row, col, 1, 0);
          if (nextCell) {
            selectCell(nextCell[0], nextCell[1]);
          }
        }
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault();
        if (currentDirection === 'across') {
          // If moving in the direction of the word, move to previous cell in word
          moveToPrevCellInWord();
        } else {
          // Otherwise, find the next valid cell in that direction
          const nextCell = findNextValidCell(row, col, 0, -1);
          if (nextCell) {
            selectCell(nextCell[0], nextCell[1]);
          }
        }
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        if (currentDirection === 'across') {
          // If moving in the direction of the word, move to next cell in word
          moveToNextCellInWord();
        } else {
          // Otherwise, find the next valid cell in that direction
          const nextCell = findNextValidCell(row, col, 0, 1);
          if (nextCell) {
            selectCell(nextCell[0], nextCell[1]);
          }
        }
      }
      // Toggle direction
      else if (e.key === 'Tab') {
        e.preventDefault();
        toggleDirection();
      }
      // Letters with auto-advance
      else if (/^[a-zA-Z]$/.test(e.key)) {
        updateAnswer(row, col, e.key.toUpperCase());

        // Auto-advance to next cell in the current word
        if (currentWordPosition) {
          const { direction, row: wordRow, col: wordCol, length } = currentWordPosition;

          // Calculate position within the word
          let posInWord = 0;
          if (direction === 'across') {
            posInWord = col - wordCol;
          } else {
            posInWord = row - wordRow;
          }

          // If we're not at the end of the word, move to the next cell
          if (posInWord < length - 1) {
            if (direction === 'across') {
              selectCell(row, col + 1);
            } else {
              selectCell(row + 1, col);
            }
          }
        }
      }
      // Backspace with auto-retreat
      else if (e.key === 'Backspace') {
        // If cell is empty and we're not at the beginning of the word, move back first
        if (!userAnswers[row]?.[col] && currentWordPosition) {
          const { direction, row: wordRow, col: wordCol } = currentWordPosition;

          // Calculate position within the word
          let posInWord = 0;
          if (direction === 'across') {
            posInWord = col - wordCol;
          } else {
            posInWord = row - wordRow;
          }

          // If we're not at the beginning of the word, move back
          if (posInWord > 0) {
            if (direction === 'across') {
              selectCell(row, col - 1);
            } else {
              selectCell(row - 1, col);
            }
          }
        } else {
          // Clear the current cell
          updateAnswer(row, col, '');
        }
      }
      // Space (toggle direction)
      else if (e.key === ' ') {
        e.preventDefault();
        toggleDirection();
      }
      // Enter (check answers)
      else if (e.key === 'Enter') {
        e.preventDefault();
        handleCheckAnswers();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedCell, currentCrossword, selectedWordId, selectedDirection, userAnswers]);

  const handleCheckAnswers = () => {
    if (!currentCrossword) return;

    const result = checkAnswers();
    setCheckResult(result);
    setShowResults(true);

    // If all answers are correct, mark as completed
    if (result.correct) {
      setProgressSaved(true);
    }

    // Save progress after checking answers
    if (user) {
      saveProgress();
    }

    // Hide results after 3 seconds
    setTimeout(() => {
      setCheckResult(null);
    }, 3000);
  };

  const handleResetAnswers = () => {
    resetCrossword();
    setShowResults(false);
    setProgressSaved(false);
  };

  // Calculate score based on time spent, hints used, and completion
  const calculateScore = () => {
    if (!currentCrossword) return 0;

    // Base score is 1000
    let score = 1000;

    // Deduct points for time spent (10 points per minute)
    const timeDeduction = Math.floor(timeSpent / 60) * 10;
    score -= timeDeduction;

    // Deduct points for hints used (50 points per hint)
    const hintsDeduction = hintsUsed * 50;
    score -= hintsDeduction;

    // Ensure score doesn't go below 0
    return Math.max(0, score);
  };

  // Toggle focus mode
  const toggleFocusMode = () => {
    setFocusMode(!focusMode);
  };

  // Toggle pause game
  const togglePause = () => {
    setIsPaused(!isPaused);

    if (!isPaused) {
      // Pause the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    } else {
      // Resume the timer by starting a new one
      // Note: This is just UI state, the actual timer is managed in CrosswordContext
      // We would need to modify CrosswordContext to fully implement pause functionality
    }
  };

  if (loading || !currentCrossword) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${focusMode ? 'bg-gray-100' : ''}`}>
      {/* Floating Clue for Mobile */}
      {isMobile && showFloatingClue && floatingClue && (
        <div className="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg z-50 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <span className="font-bold">{floatingClue.number} {floatingClue.direction === 'across' ? 'Mendatar' : 'Menurun'}: </span>
              <span>{floatingClue.text}</span>
            </div>
            <button
              onClick={() => setShowFloatingClue(false)}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              <XIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}

      {/* Game Controls */}
      <div className="bg-white p-3 mb-4 shadow-sm flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleFocusMode}
            className="p-2 rounded-full hover:bg-gray-100"
            title={focusMode ? "Exit Focus Mode" : "Focus Mode"}
          >
            {focusMode ? <Minimize2Icon className="w-5 h-5" /> : <Maximize2Icon className="w-5 h-5" />}
          </button>
          <button
            onClick={togglePause}
            className="p-2 rounded-full hover:bg-gray-100"
            title={isPaused ? "Resume" : "Pause"}
          >
            {isPaused ? <PlayIcon className="w-5 h-5" /> : <PauseIcon className="w-5 h-5" />}
          </button>
          <ShareButton
            title={currentCrossword.title}
            isCompleted={isCompleted}
            score={calculateScore()}
            timeSpent={timeSpent}
            variant="ghost"
            size="icon"
            showText={false}
          />
        </div>

        {/* Game Stats Compact View */}
        <GameStats compact={true} />
      </div>

      {/* Main Game Area */}
      <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${focusMode ? 'max-w-4xl mx-auto' : ''}`}>
        <div className="">
          {/* CrosswordGrid with showResults prop */}
          <div className="bg-white rounded-lg shadow-md aspect-square mx-auto">
            <CrosswordGrid
              crossword={currentCrossword}
              userAnswers={userAnswers}
              selectedCell={selectedCell}
              selectedWordId={selectedWordId}
              revealedCells={[]}
              incorrectCells={checkResult?.incorrectCells || []}
              onSelectCell={selectCell}
              showResults={showResults}
            />
          </div>

          {/* Play Controls */}
          <div className="mt-6">
            <PlayControls
              onToggleDirection={toggleDirection}
              onRevealCell={() => {
                if (selectedCell) {
                  revealCell(selectedCell[0], selectedCell[1]);
                }
              }}
              onRevealWord={revealWord}
              onCheckAnswers={handleCheckAnswers}
              onSaveProgress={saveProgress}
              onResetCrossword={handleResetAnswers}
              onSurrender={surrender}
              isCompleted={isCompleted}
              showResults={showResults}
            />
          </div>
        </div>

        <div className={`space-y-6 ${focusMode && !isMobile ? 'hidden' : ''}`}>
          {/* Clues */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Clues</h2>
              {crossword.category_name && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded flex items-center">
                  <TagIcon className="w-3 h-3 mr-1" />
                  {crossword.category_name}
                </span>
              )}
            </div>
            <CrosswordClues
              crossword={currentCrossword}
              selectedWordId={selectedWordId}
              onSelectWord={(wordId) => {
                const word = currentCrossword.state.wordPositions.find((w) => w.id === wordId);
                if (word) {
                  selectCell(word.row, word.col);
                }
              }}
            />
          </div>

          {/* Game Stats */}
          <GameStats />
        </div>
      </div>

      {/* Pause Overlay */}
      {isPaused && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-xl text-center">
            <h2 className="text-2xl font-bold mb-4">Game Paused</h2>
            <p className="mb-6">Click the button below to resume playing.</p>
            <button
              onClick={togglePause}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Resume Game
            </button>
          </div>
        </div>
      )}

      {/* Show completion modal when game is completed */}
      {isCompleted && (
        <CrosswordCompletionModal
          onClose={() => {}}
          onPlayAgain={handleResetAnswers}
        />
      )}
    </div>
  );
}
