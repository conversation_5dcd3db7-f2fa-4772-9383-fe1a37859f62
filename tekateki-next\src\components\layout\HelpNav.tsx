'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function HelpNav() {
  const pathname = usePathname();
  
  const helpLinks = [
    { href: '/cara-bermain', label: '<PERSON> Bermain' },
    { href: '/tips-strategi-tts', label: 'Tips & Strategi' },
    { href: '/kamus-istilah-tts', label: '<PERSON><PERSON>' },
    { href: '/manfaat-tts', label: 'Manfaat TTS' },
    { href: '/bantuan/faq', label: 'FAQ' },
  ];
  
  return (
    <div className="bg-gray-100 rounded-lg p-4 mb-8">
      <h3 className="text-lg font-semibold mb-3">Halaman Bantuan</h3>
      <div className="flex flex-wrap gap-2">
        {helpLinks.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={`px-3 py-1 rounded-full text-sm ${
              pathname === link.href
                ? 'bg-blue-600 text-white'
                : 'bg-white text-blue-600 hover:bg-blue-50'
            }`}
          >
            {link.label}
          </Link>
        ))}
      </div>
    </div>
  );
}
