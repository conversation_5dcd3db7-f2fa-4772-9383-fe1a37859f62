'use client';

import React, { useState, useEffect } from 'react';
import { Share2, Twitter, Facebook, Copy, X, MessageCircle } from 'lucide-react';
import { Button } from './button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu';

interface ShareButtonProps {
  title: string;
  url?: string;
  message?: string;
  showText?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  isCompleted?: boolean;
  score?: number;
  timeSpent?: number;
}

export default function ShareButton({
  title,
  url = '',
  message = '',
  showText = true,
  variant = 'outline',
  size = 'default',
  className = '',
  isCompleted = false,
  score,
  timeSpent,
}: ShareButtonProps) {
  const [copied, setCopied] = useState(false);

  // Format time spent in minutes and seconds
  const formatTime = (seconds: number) => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // State for the share URL
  const [shareUrl, setShareUrl] = useState<string>(url || (typeof window !== 'undefined' ? window.location.href : ''));
  const [isGeneratingUrl, setIsGeneratingUrl] = useState<boolean>(false);

  // Function to generate a short share URL
  const generateShareUrl = async () => {
    if (isGeneratingUrl) return;

    try {
      setIsGeneratingUrl(true);

      // Extract the crossword ID from the URL
      const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
      const match = currentUrl.match(/\/teka-teki\/([^\/]+)/);

      if (match && match[1]) {
        const crosswordId = match[1];

        // Call the API to generate a short URL
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1111'}/api/share/${crosswordId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (data.status === 'success' && data.data?.share_url) {
          // Use the short URL for sharing
          const baseUrl = typeof window !== 'undefined' ? `${window.location.protocol}//${window.location.host}` : '';
          setShareUrl(`${baseUrl}/s/${data.data.short_code}`);
        }
      }
    } catch (error) {
      console.error('Error generating share URL:', error);
    } finally {
      setIsGeneratingUrl(false);
    }
  };

  // Generate share URL when component mounts
  useEffect(() => {
    generateShareUrl();
  }, []);

  // Generate the sharing message based on whether the puzzle is completed
  const generateMessage = () => {
    if (message) return message;

    if (isCompleted && score !== undefined && timeSpent !== undefined) {
      return `Saya baru saja menyelesaikan teka-teki silang "${title}" dengan skor ${score} dalam waktu ${formatTime(timeSpent)}! #TekaTeki #TTS`;
    } else {
      return `Coba selesaikan teka-teki silang "${title}" ini! #TekaTeki #TTS`;
    }
  };

  const shareMessage = generateMessage();

  // Share handlers
  const shareToTwitter = () => {
    window.open(
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessage)}&url=${encodeURIComponent(shareUrl)}`,
      '_blank'
    );
  };

  const shareToFacebook = () => {
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      '_blank'
    );
  };

  const shareToWhatsApp = () => {
    window.open(
      `https://wa.me/?text=${encodeURIComponent(`${shareMessage} ${shareUrl}`)}`,
      '_blank'
    );
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(`${shareMessage} ${shareUrl}`).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Share2 className="h-4 w-4 mr-2" />
          {showText && "Bagikan"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={shareToTwitter} className="cursor-pointer">
          <X className="h-4 w-4 mr-2" />
          Twitter
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareToFacebook} className="cursor-pointer">
          <Facebook className="h-4 w-4 mr-2" />
          Facebook
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareToWhatsApp} className="cursor-pointer">
          <MessageCircle className="h-4 w-4 mr-2" />
          WhatsApp
        </DropdownMenuItem>
        <DropdownMenuItem onClick={copyToClipboard} className="cursor-pointer">
          <Copy className="h-4 w-4 mr-2" />
          {copied ? "Tersalin!" : "Salin Tautan"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
