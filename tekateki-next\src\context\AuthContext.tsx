'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, userAPI } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  googleLogin: (token: string, email: string, displayName: string, avatarUrl: string) => Promise<void>;
  register: (username: string, email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: { display_name?: string; avatar_url?: string; bio?: string }) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Memeriksa apakah pengguna sudah login saat aplikasi dimuat
  useEffect(() => {
    const checkUser = async () => {
      try {
        const response = await userAPI.getCurrentUser();
        if (response.status === 'success' && response.data?.user) {
          setUser(response.data.user);
        }
      } catch (err) {
        console.error('Error checking user:', err);
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, []);

  // Fungsi untuk login
  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.login({ email, password });
      if (response.status === 'success' && response.data?.user) {
        setUser(response.data.user);
      } else {
        setError(response.message || 'Login gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat login');
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk login dengan Google
  const googleLogin = async (token: string, email: string, displayName: string, avatarUrl: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.googleLogin({
        token,
        email,
        display_name: displayName,
        avatar_url: avatarUrl,
      });
      if (response.status === 'success' && response.data?.user) {
        setUser(response.data.user);
      } else {
        setError(response.message || 'Login dengan Google gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat login dengan Google');
      console.error('Google login error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk registrasi
  const register = async (username: string, email: string, password: string, displayName: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.register({
        username,
        email,
        password,
        display_name: displayName,
      });
      if (response.status === 'success') {
        // Setelah registrasi berhasil, langsung login
        await login(email, password);
      } else {
        setError(response.message || 'Registrasi gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat registrasi');
      console.error('Register error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk logout
  const logout = async () => {
    setLoading(true);
    try {
      const response = await userAPI.logout();
      if (response.status === 'success') {
        setUser(null);
      } else {
        setError(response.message || 'Logout gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat logout');
      console.error('Logout error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk memperbarui profil
  const updateProfile = async (data: { display_name?: string; avatar_url?: string; bio?: string }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await userAPI.updateProfile(data);
      if (response.status === 'success' && response.data?.user) {
        setUser(response.data.user);
      } else {
        setError(response.message || 'Pembaruan profil gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat memperbarui profil');
      console.error('Update profile error:', err);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    error,
    login,
    googleLogin,
    register,
    logout,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
