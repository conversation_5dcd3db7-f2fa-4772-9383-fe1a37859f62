'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { Crossword, CrosswordState, UserProgress, crosswordAPI, progressAPI } from '@/lib/api';
import { useAuth } from './AuthContext';

interface CrosswordContextType {
  currentCrossword: Crossword | null;
  userProgress: UserProgress | null;
  userAnswers: string[][];
  revealedCells: number[][];
  selectedCell: [number, number] | null;
  selectedDirection: 'across' | 'down';
  selectedWordId: string | null;
  isCompleted: boolean;
  isSurrendered: boolean;
  timeSpent: number;
  hintsUsed: number;
  hintsRemaining: number;
  maxHints: number;
  loading: boolean;
  error: string | null;
  loadCrossword: (idOrSlug: string) => Promise<void>;
  selectCell: (row: number, col: number) => void;
  toggleDirection: () => void;
  updateAnswer: (row: number, col: number, value: string) => void;
  revealCell: (row: number, col: number) => void;
  revealWord: () => void;
  checkAnswers: () => { correct: boolean; incorrectCells: number[][] };
  saveProgress: (isCompleted?: boolean) => Promise<void>;
  resetCrossword: () => void;
  surrender: () => void;
  canUseHint: () => boolean;
}

const CrosswordContext = createContext<CrosswordContextType | undefined>(undefined);

export function CrosswordProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [currentCrossword, setCurrentCrossword] = useState<Crossword | null>(null);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [userAnswers, setUserAnswers] = useState<string[][]>([]);
  const [revealedCells, setRevealedCells] = useState<number[][]>([]);
  const [selectedCell, setSelectedCell] = useState<[number, number] | null>(null);
  const [selectedDirection, setSelectedDirection] = useState<'across' | 'down'>('across');
  const [selectedWordId, setSelectedWordId] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSurrendered, setIsSurrendered] = useState(false);
  const [timeSpent, setTimeSpent] = useState(0);
  const [hintsUsed, setHintsUsed] = useState(0);
  const [hintsRemaining, setHintsRemaining] = useState(3); // Default to 3 hints
  const [maxHints, setMaxHints] = useState(3); // Default maximum hints
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [currentCrosswordId, setCurrentCrosswordId] = useState<string | null>(null);

  // Memilih sel pertama yang valid - memoized to prevent unnecessary recreations
  const selectFirstValidCell = useCallback((state: CrosswordState) => {
    if (state.wordPositions && state.wordPositions.length > 0) {
      // Cari sel pertama yang valid (memiliki karakter dan wordIds)
      for (let row = 0; row < state.gridSize; row++) {
        for (let col = 0; col < state.gridSize; col++) {
          const cell = state.grid[row]?.[col];
          if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
            // Temukan wordPosition untuk sel ini
            const wordId = cell.wordIds[0];
            const wordPosition = state.wordPositions.find(pos => pos.id === wordId);

            if (wordPosition) {
              setSelectedCell([row, col]);
              setSelectedDirection(wordPosition.direction);
              setSelectedWordId(wordPosition.id);
              return;
            }
          }
        }
      }

      // Fallback ke metode lama jika tidak ada sel valid yang ditemukan
      const firstWord = state.wordPositions[0];
      setSelectedCell([firstWord.row, firstWord.col]);
      setSelectedDirection(firstWord.direction);
      setSelectedWordId(firstWord.id);
    }
  }, []);

  // Memulai timer - memoized to prevent unnecessary recreations
  const startTimer = useCallback(() => {
    if (timer) {
      clearInterval(timer);
    }

    const newTimer = setInterval(() => {
      setTimeSpent((prev) => prev + 1);
    }, 1000);

    setTimer(newTimer);
  }, [timer]);

  // Load saved state from localStorage - memoized to prevent unnecessary recalculations
  const loadSavedState = useCallback((crosswordId: string) => {
    if (typeof window === 'undefined') return null;

    try {
      const savedStateJson = localStorage.getItem(`crossword_state_${crosswordId}`);
      if (savedStateJson) {
        const savedState = JSON.parse(savedStateJson);

        // Check if the saved state is valid and not too old (24 hours)
        const now = new Date().getTime();
        if (savedState && savedState.timestamp && (now - savedState.timestamp < 24 * 60 * 60 * 1000)) {
          console.log(`Loaded saved state for crossword ${crosswordId} from localStorage`);
          return savedState;
        }
      }
    } catch (error) {
      console.error('Error loading saved state from localStorage:', error);
    }

    return null;
  }, []);

  // Save current state to localStorage - memoized to prevent unnecessary recreations
  const saveStateToLocalStorage = useCallback((crosswordId: string) => {
    if (typeof window === 'undefined' || !currentCrossword) return;

    try {
      const stateToSave = {
        userAnswers,
        revealedCells,
        timeSpent,
        hintsUsed,
        hintsRemaining,
        isCompleted,
        isSurrendered,
        timestamp: new Date().getTime()
      };

      localStorage.setItem(`crossword_state_${crosswordId}`, JSON.stringify(stateToSave));
      console.log(`Saved state for crossword ${crosswordId} to localStorage`);
    } catch (error) {
      console.error('Error saving state to localStorage:', error);
    }
  }, [currentCrossword, userAnswers, revealedCells, timeSpent, hintsUsed, hintsRemaining, isCompleted, isSurrendered]);

  // Memuat teka-teki silang berdasarkan ID atau slug - memoized to prevent unnecessary recreations
  const loadCrossword = useCallback(async (idOrSlug: string) => {
    setLoading(true);
    setError(null);
    try {
      // Check if we already have this crossword loaded
      if (currentCrossword && (currentCrossword.id === idOrSlug || currentCrossword.slug === idOrSlug)) {
        console.log(`Crossword ${idOrSlug} is already loaded, skipping API call`);
        setLoading(false);
        return;
      }

      // Use performance measurement in development
      const startTime = performance.now();

      const response = await crosswordAPI.getOne(idOrSlug);

      if (process.env.NODE_ENV === 'development') {
        const endTime = performance.now();
        console.log(`API call to get crossword took ${Math.round(endTime - startTime)}ms`);
      }

      if (response.status === 'success' && response.data) {
        const crosswordData = response.data;
        setCurrentCrossword(crosswordData);
        setCurrentCrosswordId(crosswordData.id);

        // Inisialisasi userAnswers dengan array kosong sesuai ukuran grid
        const gridSize = crosswordData.state.gridSize;
        let initialUserAnswers = Array(gridSize).fill(null).map(() => Array(gridSize).fill(''));
        let initialRevealedCells: number[][] = [];
        let initialTimeSpent = 0;
        let initialHintsUsed = 0;
        let initialHintsRemaining = 3;
        let initialIsCompleted = false;
        let initialIsSurrendered = false;

        // Reset hints based on difficulty
        const difficulty = crosswordData.difficulty?.toLowerCase() || 'medium';
        let hintCount = 3; // Default for medium

        if (difficulty === 'easy') {
          hintCount = 5;
        } else if (difficulty === 'hard') {
          hintCount = 2;
        } else if (difficulty === 'expert') {
          hintCount = 1;
        }

        initialHintsRemaining = hintCount;

        // Try to load saved state from localStorage first
        const savedState = loadSavedState(crosswordData.id);

        if (savedState) {
          console.log('Using saved state from localStorage');
          initialUserAnswers = savedState.userAnswers || initialUserAnswers;
          initialRevealedCells = savedState.revealedCells || initialRevealedCells;
          initialTimeSpent = savedState.timeSpent || initialTimeSpent;
          initialHintsUsed = savedState.hintsUsed || initialHintsUsed;
          initialHintsRemaining = savedState.hintsRemaining || initialHintsRemaining;
          initialIsCompleted = savedState.isCompleted || initialIsCompleted;
          initialIsSurrendered = savedState.isSurrendered || initialIsSurrendered;
        }

        // Record play in a non-blocking way
        crosswordAPI.recordPlay(crosswordData.id).catch(err => {
          console.error('Error recording play:', err);
        });

        // If user is logged in, try to load progress from API
        // This takes precedence over localStorage if available
        if (user) {
          try {
            const progressResponse = await progressAPI.getOne(crosswordData.id);
            if (progressResponse.status === 'success' && progressResponse.data) {
              console.log('Using saved progress from API');
              setUserProgress(progressResponse.data);
              initialUserAnswers = progressResponse.data.progress_data.userAnswers || initialUserAnswers;
              initialRevealedCells = progressResponse.data.progress_data.revealedCells || initialRevealedCells;
              initialIsCompleted = progressResponse.data.is_completed === 1 || initialIsCompleted;
              initialTimeSpent = progressResponse.data.time_spent || initialTimeSpent;
            }
          } catch (progressError) {
            console.error('Error loading progress:', progressError);
            // Continue with localStorage data if API fails
          }
        }

        // Batch state updates for better performance
        const batchedUpdates = () => {
          setUserAnswers(initialUserAnswers);
          setRevealedCells(initialRevealedCells);
          setTimeSpent(initialTimeSpent);
          setHintsUsed(initialHintsUsed);
          setMaxHints(hintCount);
          setHintsRemaining(initialHintsRemaining);
          setIsCompleted(initialIsCompleted);
          setIsSurrendered(initialIsSurrendered);
        };

        // Use React's batched updates
        batchedUpdates();

        // Pilih sel pertama yang valid
        selectFirstValidCell(crosswordData.state);

        // Mulai timer jika belum selesai
        if (!initialIsCompleted && !initialIsSurrendered) {
          startTimer();
        }
      } else {
        setError(response.message || 'Gagal memuat teka-teki silang');
      }
    } catch (err) {
      setError('Terjadi kesalahan saat memuat teka-teki silang');
      console.error('Load crossword error:', err);
    } finally {
      setLoading(false);
    }
  }, [currentCrossword, user, loadSavedState, selectFirstValidCell, startTimer]);

  // Membersihkan timer saat komponen unmount
  useEffect(() => {
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [timer]);

  // Memilih sel
  const selectCell = (row: number, col: number) => {
    if (!currentCrossword) return;

    // Periksa apakah sel valid (bukan sel kosong)
    const cell = currentCrossword.state.grid[row]?.[col];
    if (!cell || cell.char === ' ' || !cell.wordIds.length) return;

    // Jika sel yang sama dipilih, toggle arah
    if (selectedCell && selectedCell[0] === row && selectedCell[1] === col) {
      toggleDirection();
      return;
    }

    setSelectedCell([row, col]);

    // Cari kata yang berisi sel ini dalam arah yang dipilih
    const wordPositions = currentCrossword.state.wordPositions.filter(word => {
      // Convert IDs to strings for comparison
      const wordIdStr = String(word.id);
      const cellWordIds = cell.wordIds.map((id: any) => String(id));

      return cellWordIds.includes(wordIdStr) && word.direction === selectedDirection;
    });

    if (wordPositions.length > 0) {
      // Pilih kata pertama yang ditemukan
      const newWordId = wordPositions[0].id;
      console.log(`Setting selectedWordId to ${newWordId} from wordPositions`);
      setSelectedWordId(newWordId);
    } else {
      // Jika tidak ada kata dalam arah yang dipilih, coba arah lain
      const otherDirection = selectedDirection === 'across' ? 'down' : 'across';
      const otherWordPositions = currentCrossword.state.wordPositions.filter(word => {
        // Convert IDs to strings for comparison
        const wordIdStr = String(word.id);
        const cellWordIds = cell.wordIds.map((id: any) => String(id));

        return cellWordIds.includes(wordIdStr) && word.direction === otherDirection;
      });

      if (otherWordPositions.length > 0) {
        setSelectedDirection(otherDirection);
        setSelectedWordId(otherWordPositions[0].id);
      } else if (cell.wordIds.length > 0) {
        // Jika masih tidak ditemukan, pilih kata pertama dari wordIds
        const anyWordPosition = currentCrossword.state.wordPositions.find(word => {
          // Convert IDs to strings for comparison
          const wordIdStr = String(word.id);
          const cellWordIds = cell.wordIds.map((id: any) => String(id));

          return cellWordIds.includes(wordIdStr);
        });

        if (anyWordPosition) {
          setSelectedDirection(anyWordPosition.direction);
          setSelectedWordId(anyWordPosition.id);
        }
      }
    }
  };

  // Toggle arah (across/down)
  const toggleDirection = () => {
    const newDirection = selectedDirection === 'across' ? 'down' : 'across';
    setSelectedDirection(newDirection);

    // Perbarui selectedWordId berdasarkan arah baru
    if (selectedCell && currentCrossword) {
      const [row, col] = selectedCell;
      const cell = currentCrossword.state.grid[row]?.[col];

      if (cell && cell.char !== ' ' && cell.wordIds && cell.wordIds.length > 0) {
        // Cari kata dalam arah baru yang berisi sel ini
        const wordPositions = currentCrossword.state.wordPositions.filter(word => {
          // Convert IDs to strings for comparison
          const wordIdStr = String(word.id);
          const cellWordIds = cell.wordIds.map((id: any) => String(id));

          return cellWordIds.includes(wordIdStr) && word.direction === newDirection;
        });

        if (wordPositions.length > 0) {
          // Pilih kata pertama yang ditemukan
          setSelectedWordId(wordPositions[0].id);
        }
      }
    }
  };

  // Memperbarui jawaban
  const updateAnswer = (row: number, col: number, value: string) => {
    const newUserAnswers = [...userAnswers];
    if (!newUserAnswers[row]) {
      newUserAnswers[row] = [];
    }
    newUserAnswers[row][col] = value.toUpperCase();
    setUserAnswers(newUserAnswers);

    // Pindah ke sel berikutnya
    if (selectedCell && currentCrossword) {
      moveToNextCell();
    }

    // Periksa apakah teka-teki sudah selesai
    checkCompletion();
  };

  // Pindah ke sel berikutnya
  const moveToNextCell = () => {
    if (!selectedCell || !currentCrossword) return;

    const [row, col] = selectedCell;
    const gridSize = currentCrossword.state.gridSize;

    if (selectedDirection === 'across') {
      // Pindah ke kanan
      if (col + 1 < gridSize) {
        selectCell(row, col + 1);
      }
    } else {
      // Pindah ke bawah
      if (row + 1 < gridSize) {
        selectCell(row + 1, col);
      }
    }
  };

  // Check if hints can be used
  const canUseHint = (): boolean => {
    return hintsRemaining > 0 && !isCompleted && !isSurrendered;
  };

  // Mengungkapkan sel (hint)
  const revealCell = (row: number, col: number) => {
    if (!currentCrossword || !canUseHint()) return;

    // Tambahkan sel ke daftar sel yang diungkapkan
    setRevealedCells((prev) => [...prev, [row, col]]);
    setHintsUsed((prev) => prev + 1);
    setHintsRemaining((prev) => prev - 1);

    // Isi sel dengan jawaban yang benar
    const correctAnswer = getCorrectAnswer(row, col);
    if (correctAnswer) {
      const newUserAnswers = [...userAnswers];
      if (!newUserAnswers[row]) {
        newUserAnswers[row] = [];
      }
      newUserAnswers[row][col] = correctAnswer;
      setUserAnswers(newUserAnswers);
    }

    // Periksa apakah teka-teki sudah selesai
    checkCompletion();
  };

  // Mengungkapkan kata saat ini (hint)
  const revealWord = () => {
    if (!currentCrossword || !selectedWordId || !canUseHint()) return;

    const wordPosition = currentCrossword.state.wordPositions.find(
      (word) => word.id === selectedWordId
    );

    if (wordPosition) {
      const { row, col, direction, length } = wordPosition;
      const newRevealedCells = [...revealedCells];
      const newUserAnswers = [...userAnswers];
      let cellsRevealed = 0;

      // Check if we have enough hints remaining
      const unrevealedCells = [];
      for (let i = 0; i < length; i++) {
        const currentRow = direction === 'across' ? row : row + i;
        const currentCol = direction === 'across' ? col + i : col;

        // Check if cell is already revealed
        if (!revealedCells.some(([r, c]) => r === currentRow && c === currentCol)) {
          unrevealedCells.push([currentRow, currentCol]);
        }
      }

      // If not enough hints, only reveal what we can
      const cellsToReveal = Math.min(unrevealedCells.length, hintsRemaining);
      if (cellsToReveal === 0) return;

      // Reveal cells
      for (let i = 0; i < length; i++) {
        const currentRow = direction === 'across' ? row : row + i;
        const currentCol = direction === 'across' ? col + i : col;

        // Periksa apakah sel valid
        const cell = currentCrossword.state.grid[currentRow]?.[currentCol];
        if (cell && cell.char !== ' ' && cell.wordIds.includes(selectedWordId)) {
          // Only reveal if not already revealed and we have hints left
          if (!newRevealedCells.some(([r, c]) => r === currentRow && c === currentCol) && cellsRevealed < cellsToReveal) {
            newRevealedCells.push([currentRow, currentCol]);

            // Isi sel dengan jawaban yang benar
            if (!newUserAnswers[currentRow]) {
              newUserAnswers[currentRow] = [];
            }
            newUserAnswers[currentRow][currentCol] = cell.char;
            cellsRevealed++;
          }
        }
      }

      setRevealedCells(newRevealedCells);
      setUserAnswers(newUserAnswers);
      setHintsUsed((prev) => prev + cellsRevealed);
      setHintsRemaining((prev) => prev - cellsRevealed);

      // Periksa apakah teka-teki sudah selesai
      checkCompletion();
    }
  };

  // Mendapatkan jawaban yang benar untuk sel tertentu
  const getCorrectAnswer = (row: number, col: number): string => {
    if (!currentCrossword) return '';

    // Ambil karakter langsung dari grid
    const cell = currentCrossword.state.grid[row]?.[col];
    if (cell && cell.char !== ' ') {
      return cell.char;
    }

    return '';
  };

  // Memeriksa jawaban
  const checkAnswers = () => {
    if (!currentCrossword) return { correct: false, incorrectCells: [] };

    const incorrectCells: number[][] = [];
    const gridSize = currentCrossword.state.gridSize;

    // Periksa setiap sel dalam grid
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const cell = currentCrossword.state.grid[row]?.[col];

        // Jika sel memiliki karakter (bukan sel kosong) dan memiliki wordIds
        if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
          const correctLetter = cell.char;
          const userLetter = userAnswers[row]?.[col] || '';

          if (userLetter !== correctLetter) {
            incorrectCells.push([row, col]);
          }
        }
      }
    }

    return {
      correct: incorrectCells.length === 0,
      incorrectCells,
    };
  };

  // Memeriksa apakah teka-teki sudah selesai
  const checkCompletion = () => {
    const { correct } = checkAnswers();
    if (correct) {
      setIsCompleted(true);
      if (timer) {
        clearInterval(timer);
        setTimer(null);
      }
      saveProgress();
    }
  };

  // Menyimpan kemajuan
  const saveProgress = async (completed = false) => {
    if (!currentCrossword) return;

    try {
      const progress = calculateProgress();
      const isComplete = completed || isCompleted || isSurrendered;

      // Always save to localStorage regardless of user login status
      if (currentCrossword.id) {
        saveStateToLocalStorage(currentCrossword.id);
      }

      // Only save to API if user is logged in
      if (user) {
        await progressAPI.save(currentCrossword.id, {
          progress_data: {
            userAnswers,
            revealedCells,
            progress,
          },
          is_completed: isComplete ? 1 : 0,
          time_spent: timeSpent,
        });
      }
    } catch (err) {
      console.error('Save progress error:', err);
    }
  };

  // Menyerah (surrender)
  const surrender = () => {
    if (!currentCrossword) return;

    setIsSurrendered(true);
    setIsCompleted(true);

    // Fill in all answers
    const gridSize = currentCrossword.state.gridSize;
    const correctAnswers = Array(gridSize).fill(null).map((_, row) =>
      Array(gridSize).fill(null).map((_, col) => {
        const cell = currentCrossword.state.grid[row]?.[col];
        return cell && cell.char !== ' ' ? cell.char : '';
      })
    );

    setUserAnswers(correctAnswers);

    // Stop the timer
    if (timer) {
      clearInterval(timer);
      setTimer(null);
    }

    // Save progress
    saveProgress(true);
  };

  // Menghitung persentase kemajuan
  const calculateProgress = (): number => {
    if (!currentCrossword) return 0;

    let totalCells = 0;
    let filledCells = 0;
    const gridSize = currentCrossword.state.gridSize;

    // Periksa setiap sel dalam grid
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const cell = currentCrossword.state.grid[row]?.[col];

        // Jika sel memiliki karakter (bukan sel kosong) dan memiliki wordIds
        if (cell && cell.char !== ' ' && cell.wordIds.length > 0) {
          totalCells++;

          // Hitung sel yang sudah diisi
          if (userAnswers[row]?.[col]) {
            filledCells++;
          }
        }
      }
    }

    return totalCells > 0 ? Math.round((filledCells / totalCells) * 100) : 0;
  };

  // Reset teka-teki silang
  const resetCrossword = () => {
    if (!currentCrossword) return;

    const gridSize = currentCrossword.state.gridSize;
    const emptyAnswers = Array(gridSize).fill(null).map(() => Array(gridSize).fill(''));
    setUserAnswers(emptyAnswers);
    setRevealedCells([]);
    setIsCompleted(false);
    setIsSurrendered(false);
    setTimeSpent(0);
    setHintsUsed(0);
    setHintsRemaining(maxHints); // Reset hints to maximum

    // Mulai timer baru
    startTimer();

    // Pilih sel pertama yang valid
    selectFirstValidCell(currentCrossword.state);
  };

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    currentCrossword,
    userProgress,
    userAnswers,
    revealedCells,
    selectedCell,
    selectedDirection,
    selectedWordId,
    isCompleted,
    isSurrendered,
    timeSpent,
    hintsUsed,
    hintsRemaining,
    maxHints,
    loading,
    error,
    loadCrossword,
    selectCell,
    toggleDirection,
    updateAnswer,
    revealCell,
    revealWord,
    checkAnswers,
    saveProgress,
    resetCrossword,
    surrender,
    canUseHint,
  }), [
    currentCrossword,
    userProgress,
    userAnswers,
    revealedCells,
    selectedCell,
    selectedDirection,
    selectedWordId,
    isCompleted,
    isSurrendered,
    timeSpent,
    hintsUsed,
    hintsRemaining,
    maxHints,
    loading,
    error,
    loadCrossword,
    selectCell,
    toggleDirection,
    updateAnswer,
    revealCell,
    revealWord,
    checkAnswers,
    saveProgress,
    resetCrossword,
    surrender,
    canUseHint,
  ]);

  return <CrosswordContext.Provider value={value}>{children}</CrosswordContext.Provider>;
}

export function useCrossword() {
  const context = useContext(CrosswordContext);
  if (context === undefined) {
    throw new Error('useCrossword must be used within a CrosswordProvider');
  }
  return context;
}
