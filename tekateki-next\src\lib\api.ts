/**
 * API client untuk Teka-Te<PERSON>
 * Berdasarkan dokumentasi API di API_DOKUMENTASI.md
 * Dengan optimasi caching dan performa
 */

// Use environment variable or fallback to hardcoded URL
const API_BASE_URL = process.env.API_BASE_URL || 'https://tts-api.widiyanata.com/api';

// Cache configuration
const CACHE_TTL = {
  SHORT: 60 * 1000, // 1 minute
  MEDIUM: 5 * 60 * 1000, // 5 minutes
  LONG: 30 * 60 * 1000, // 30 minutes
};

// Simple in-memory cache
interface CacheItem<T> {
  data: T;
  expiry: number;
}

const apiCache = new Map<string, CacheItem<any>>();

// Cache helper functions
function getCachedItem<T>(key: string): T | null {
  const item = apiCache.get(key);
  const now = Date.now();

  if (item && item.expiry > now) {
    return item.data as T;
  }

  // Remove expired item
  if (item) {
    apiCache.delete(key);
  }

  return null;
}

function setCachedItem<T>(key: string, data: T, ttl: number): void {
  apiCache.set(key, {
    data,
    expiry: Date.now() + ttl,
  });
}

// Tipe data untuk respons API
export interface ApiResponse<T> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Tipe data untuk teka-teki silang
export interface Crossword {
  id: string;
  title: string;
  slug: string;
  description: string;
  grid_size: number;
  difficulty: string;
  user_id: string;
  creator: string;
  is_public: number;
  plays: number;
  rating: number;
  category_id: string;
  category_name: string;
  category_slug: string;
  created_at: string;
  updated_at: string;
  state: CrosswordState;
}

export interface CrosswordState {
  gridSize: number;
  grid: any[];
  words: any[];
  clues: {
    across: Record<number, string>;
    down: Record<number, string>;
  };
  wordPositions: any[];
  wordNumber: number;
  selectedWordId: string | null;
  mode: string;
}

// Tipe data untuk kategori
export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  image_url: string;
  created_at: string;
  crossword_count: number;
}

// Tipe data untuk pengguna
export interface User {
  id: string;
  username: string;
  email: string;
  display_name: string;
  avatar_url: string;
  bio: string | null;
  role: string;
  auth_provider: string;
}

// Tipe data untuk kemajuan pengguna
export interface UserProgress {
  id: string;
  user_id: string;
  crossword_id: string;
  progress_data: {
    userAnswers: any[][];
    revealedCells: number[][];
    progress: number;
  };
  is_completed: number;
  time_spent: number;
  crossword_title?: string;
  difficulty?: string;
  slug?: string;
  category_id?: string;
  created_at: string;
  updated_at: string;
}

// Tipe data untuk blog
export interface Blog {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image: string;
  author_id: string;
  author_name: string;
  author_username: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// Fungsi utilitas untuk membuat permintaan API dengan caching
async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit = {},
  cacheTTL: number | null = null // null means no cache
): Promise<ApiResponse<T>> {
  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5',
    },
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
  };

  // Create a cache key based on endpoint and request method
  const cacheKey = `${mergedOptions.method || 'GET'}-${endpoint}`;

  // Only use cache for GET requests
  if (cacheTTL !== null && (!mergedOptions.method || mergedOptions.method === 'GET')) {
    // Try to get from cache first
    const cachedData = getCachedItem<ApiResponse<T>>(cacheKey);
    if (cachedData) {
      console.log(`Using cached data for ${endpoint}`);
      return cachedData;
    }
  }

  try {
    // Add performance timing for debugging in development
    const startTime = performance.now();

    const response = await fetch(`${API_BASE_URL}${endpoint}`, mergedOptions);
    const data = await response.json() as ApiResponse<T>;

    const endTime = performance.now();
    if (process.env.NODE_ENV === 'development') {
      console.log(`API call to ${endpoint} took ${Math.round(endTime - startTime)}ms`);
    }

    // Cache successful responses if cacheTTL is provided
    if (cacheTTL !== null && data.status === 'success' && (!mergedOptions.method || mergedOptions.method === 'GET')) {
      setCachedItem(cacheKey, data, cacheTTL);
    }

    return data;
  } catch (error) {
    console.error('API error:', error);
    return {
      status: 'error',
      message: 'Terjadi kesalahan saat menghubungi server',
    };
  }
}

// API untuk Teka-Teki Silang
export const crosswordAPI = {
  // Mendapatkan semua teka-teki silang dengan caching
  getAll: async (params: {
    category_id?: string;
    difficulty?: string;
    is_public?: number;
    user_id?: string;
    limit?: number;
    page?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Use medium cache for listing pages
    return fetchAPI<Crossword[]>(`/crosswords${query}`, {}, CACHE_TTL.MEDIUM);
  },

  // Mendapatkan teka-teki silang berdasarkan ID atau slug dengan caching
  getOne: async (idOrSlug: string) => {
    // Use longer cache for individual crossword data
    return fetchAPI<Crossword>(`/crosswords/${idOrSlug}`, {}, CACHE_TTL.LONG);
  },

  // Mendapatkan teka-teki silang unggulan dengan caching
  getFeatured: async () => {
    // Featured crosswords can be cached for longer periods
    return fetchAPI<Crossword[]>('/featured', {}, CACHE_TTL.LONG);
  },

  // Mencatat permainan - no caching for POST requests
  recordPlay: async (id: string) => {
    return fetchAPI<void>(`/crosswords/${id}/play`, {
      method: 'POST',
    }, null);
  },

  // Memberikan rating - no caching for POST requests
  rate: async (id: string, rating: number) => {
    return fetchAPI<void>(`/crosswords/${id}/rate`, {
      method: 'POST',
      body: JSON.stringify({ rating }),
    }, null);
  },

  // Clear cache for crosswords - useful after updates
  clearCache: () => {
    // Remove all crossword-related cache entries
    for (const key of apiCache.keys()) {
      if (key.includes('/crosswords') || key.includes('/featured')) {
        apiCache.delete(key);
      }
    }
  },
};

// API untuk Kategori
export const categoryAPI = {
  // Mendapatkan semua kategori dengan caching
  getAll: async () => {
    // Categories change infrequently, so we can cache them for longer
    return fetchAPI<Category[]>('/categories', {}, CACHE_TTL.LONG);
  },

  // Mendapatkan kategori berdasarkan ID atau slug dengan caching
  getOne: async (idOrSlug: string) => {
    // Individual categories also change infrequently
    return fetchAPI<Category>(`/categories/${idOrSlug}`, {}, CACHE_TTL.LONG);
  },

  // Clear category cache - useful after updates
  clearCache: () => {
    // Remove all category-related cache entries
    for (const key of apiCache.keys()) {
      if (key.includes('/categories')) {
        apiCache.delete(key);
      }
    }
  },
};

// API untuk Pengguna
export const userAPI = {
  // Registrasi
  register: async (userData: {
    username: string;
    email: string;
    password: string;
    display_name: string;
  }) => {
    return fetchAPI<{ userId: string }>('/users/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // Login
  login: async (credentials: { email: string; password: string }) => {
    return fetchAPI<{ user: User }>('/users/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // Login dengan Google
  googleLogin: async (data: {
    token: string;
    email: string;
    display_name: string;
    avatar_url: string;
  }) => {
    return fetchAPI<{ user: User }>('/users/google-login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Logout
  logout: async () => {
    return fetchAPI<void>('/users/logout', {
      method: 'POST',
    });
  },

  // Mendapatkan pengguna saat ini
  getCurrentUser: async () => {
    return fetchAPI<{ user: User }>('/users/me');
  },

  // Memperbarui profil
  updateProfile: async (profileData: {
    display_name?: string;
    avatar_url?: string;
    bio?: string;
  }) => {
    return fetchAPI<{ user: User }>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  },
};

// API untuk Kemajuan Pengguna
export const progressAPI = {
  // Mendapatkan semua kemajuan
  getAll: async () => {
    return fetchAPI<UserProgress[]>('/progress');
  },

  // Mendapatkan kemajuan untuk teka-teki tertentu
  getOne: async (crosswordId: string) => {
    return fetchAPI<UserProgress>(`/progress/${crosswordId}`);
  },

  // Menyimpan kemajuan
  save: async (
    crosswordId: string,
    progressData: {
      progress_data: {
        userAnswers: any[][];
        revealedCells: number[][];
        progress: number;
      };
      is_completed: number;
      time_spent: number;
    }
  ) => {
    return fetchAPI<{ id: string }>(`/progress/${crosswordId}`, {
      method: 'POST',
      body: JSON.stringify(progressData),
    });
  },

  // Menghapus kemajuan
  delete: async (crosswordId: string) => {
    return fetchAPI<void>(`/progress/${crosswordId}`, {
      method: 'DELETE',
    });
  },
};

// API untuk Blog
export const blogAPI = {
  // Mendapatkan semua blog dengan caching
  getAll: async (params: {
    status?: string;
    author_id?: string;
    limit?: number;
    page?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
    // Blog listings can be cached for a medium duration
    return fetchAPI<Blog[]>(`/blogs${query}`, {}, CACHE_TTL.MEDIUM);
  },

  // Mendapatkan blog berdasarkan ID atau slug dengan caching
  getOne: async (idOrSlug: string) => {
    // Individual blog posts can be cached for longer
    return fetchAPI<Blog>(`/blogs/${idOrSlug}`, {}, CACHE_TTL.LONG);
  },

  // Mendapatkan blog berdasarkan slug dengan caching
  getBySlug: async (slug: string) => {
    // Blog posts by slug can also be cached for longer
    return fetchAPI<Blog>(`/blogs?slug=${slug}`, {}, CACHE_TTL.LONG);
  },

  // Clear blog cache - useful after updates
  clearCache: () => {
    // Remove all blog-related cache entries
    for (const key of apiCache.keys()) {
      if (key.includes('/blogs')) {
        apiCache.delete(key);
      }
    }
  },
};

// API untuk Sitemap
export const sitemapAPI = {
  // Mendapatkan sitemap dengan caching
  get: async () => {
    // Sitemaps can be cached for a long time as they don't change frequently
    try {
      const cacheKey = 'GET-/sitemap';
      const cachedData = getCachedItem<Response>(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(`${API_BASE_URL}/sitemap`);

      // Clone the response before caching it
      const clonedResponse = response.clone();
      setCachedItem(cacheKey, clonedResponse, CACHE_TTL.LONG);

      return response;
    } catch (error) {
      console.error('Sitemap API error:', error);
      throw error;
    }
  },

  // Clear sitemap cache
  clearCache: () => {
    for (const key of apiCache.keys()) {
      if (key.includes('/sitemap')) {
        apiCache.delete(key);
      }
    }
  },
};
