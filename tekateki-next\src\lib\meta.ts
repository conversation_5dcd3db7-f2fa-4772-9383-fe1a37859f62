import { Metadata } from 'next';
import { Crossword } from './api';

/**
 * Generate metadata for a crossword puzzle page
 * 
 * @param crossword The crossword puzzle data
 * @returns Metadata object for Next.js
 */
export function generateCrosswordMetadata(crossword: Crossword): Metadata {
  const title = `${crossword.title} | Teka-Teki Silang Indonesia`;
  
  // Generate description based on crossword data
  let description = crossword.description;
  if (!description || description.trim() === '') {
    description = `Mainkan teka-teki silang "${crossword.title}" dengan tingkat kesulitan ${crossword.difficulty}. Teka-teki silang interaktif dalam bahasa Indonesia.`;
  }
  
  // Add category if available
  if (crossword.category_name) {
    description += ` Kategori: ${crossword.category_name}.`;
  }
  
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `/teka-teki/${crossword.slug || crossword.id}`,
      images: [
        {
          url: '/images/og-crossword.jpg', // Default image
          width: 1200,
          height: 630,
          alt: `Teka-Teki <PERSON>lang: ${crossword.title}`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: ['/images/og-crossword.jpg'],
    },
  };
}

/**
 * Generate metadata for a category page
 * 
 * @param categoryName The category name
 * @param categorySlug The category slug
 * @returns Metadata object for Next.js
 */
export function generateCategoryMetadata(categoryName: string, categorySlug: string): Metadata {
  const title = `${categoryName} | Teka-Teki Silang Indonesia`;
  const description = `Kumpulan teka-teki silang dalam kategori ${categoryName}. Mainkan teka-teki silang interaktif dalam bahasa Indonesia.`;
  
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `/kategori/${categorySlug}`,
      images: [
        {
          url: '/images/og-category.jpg', // Default image
          width: 1200,
          height: 630,
          alt: `Kategori Teka-Teki Silang: ${categoryName}`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: ['/images/og-category.jpg'],
    },
  };
}
