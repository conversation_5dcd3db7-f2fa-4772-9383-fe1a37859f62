<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Test - Tekateki.io</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API Key Authentication Test - Tekateki.io</h1>

    <div class="test-section">
        <h2>Test Configuration</h2>
        <p><strong>API Base URL:</strong> <span id="api-url">http://localhost:1111</span></p>
        <p><strong>API Key:</strong> 45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5</p>
        <p><strong>Endpoint Pattern:</strong> /api/* (tanpa /v1/)</p>
    </div>

    <div class="test-section">
        <h2>Test Cases</h2>
        <button onclick="testWithValidKey()">Test dengan API Key Valid</button>
        <button onclick="testWithInvalidKey()">Test dengan API Key Invalid</button>
        <button onclick="testWithoutKey()">Test tanpa API Key</button>
        <button onclick="testHealthEndpoint()">Test Health Endpoint (Tidak perlu key)</button>
        <button onclick="testCategoriesEndpoint()">Test Categories Endpoint</button>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:1111';
        const VALID_API_KEY = '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5';

        function addResult(title, success, response, error = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${success ? 'success' : 'error'}`;

            let content = `<h3>${title}</h3>`;
            content += `<p><strong>Status:</strong> ${success ? 'SUCCESS ✅' : 'FAILED ❌'}</p>`;

            if (response) {
                content += `<p><strong>Response:</strong></p><pre>${JSON.stringify(response, null, 2)}</pre>`;
            }

            if (error) {
                content += `<p><strong>Error:</strong> ${error}</p>`;
            }

            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        async function testWithValidKey() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/featured`, {
                    headers: {
                        'X-API-Key': VALID_API_KEY
                    }
                });

                const data = await response.json();
                addResult('Test dengan API Key Valid', response.ok, data);
            } catch (error) {
                addResult('Test dengan API Key Valid', false, null, error.message);
            }
        }

        async function testWithInvalidKey() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/featured`, {
                    headers: {
                        'X-API-Key': 'invalid-key-12345'
                    }
                });

                const data = await response.json();
                addResult('Test dengan API Key Invalid', !response.ok && response.status === 401, data);
            } catch (error) {
                addResult('Test dengan API Key Invalid', false, null, error.message);
            }
        }

        async function testWithoutKey() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/featured`);

                const data = await response.json();
                addResult('Test tanpa API Key', !response.ok && response.status === 401, data);
            } catch (error) {
                addResult('Test tanpa API Key', false, null, error.message);
            }
        }

        async function testHealthEndpoint() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);

                const data = await response.json();
                addResult('Test Health Endpoint (Tidak perlu key)', response.ok, data);
            } catch (error) {
                addResult('Test Health Endpoint (Tidak perlu key)', false, null, error.message);
            }
        }

        async function testCategoriesEndpoint() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/categories`, {
                    headers: {
                        'X-API-Key': VALID_API_KEY
                    }
                });

                const data = await response.json();
                addResult('Test Categories Endpoint', response.ok, data);
            } catch (error) {
                addResult('Test Categories Endpoint', false, null, error.message);
            }
        }

        // Clear results on page load
        document.getElementById('results').innerHTML = '';
    </script>
</body>
</html>
