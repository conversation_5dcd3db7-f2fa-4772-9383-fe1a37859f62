import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';
import { resolve } from 'path';
import { splitVendorChunkPlugin } from 'vite';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [
      react(),
      splitVendorChunkPlugin(), // Split vendor chunks automatically
      visualizer({
        open: false, // Set to true to automatically open the visualization after build
        gzipSize: true,
        brotliSize: true,
        filename: 'dist/stats.html',
      }),
    ],
    build: {
      minify: 'terser', // Use terser for better minification
      sourcemap: false, // Disable sourcemaps in production
      outDir: 'dist',
      assetsDir: 'assets',
      emptyOutDir: true, // Clean the output directory before build
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Create separate chunks for large dependencies
            if (id.includes('node_modules')) {
              // Check for React and React-related packages first
              if (id.includes('react') ||
                  id.includes('react-dom') ||
                  id.includes('react-router-dom') ||
                  id.includes('@react-oauth') ||
                  id.includes('react-helmet-async')) {
                return 'vendor-react';
              }

              // Handle other specific large dependencies
              if (id.includes('framer-motion/')) {
                return 'vendor-framer-motion';
              }
              if (id.includes('lucide-react/')) {
                return 'vendor-lucide';
              }

              // Group remaining node_modules into a shared vendor chunk
              return 'vendor-other';
            }

            // Split admin pages into a separate chunk
            if (id.includes('/pages/admin/')) {
              return 'admin';
            }
          },
          // Customize chunk filenames
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: 'assets/[name].[hash].[ext]',
        },
      },
      chunkSizeWarningLimit: 600, // Increase the warning limit slightly
      terserOptions: {
        compress: {
          drop_console: true, // Remove console logs in production
          drop_debugger: true,
          passes: 2, // Multiple passes for better compression
          pure_getters: true,
          unsafe: true,
          unsafe_comps: true,
          unsafe_math: true,
          unsafe_methods: true,
        },
        mangle: {
          properties: {
            regex: /^_/,  // Only mangle properties that start with underscore
          },
        },
      },
    },
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'), // Enable @ imports
      },
    },
    // Base path for production deployment
    base: mode === 'production' ? '/' : '/',
    // Server configuration
    server: {
      port: 5173,
      strictPort: true,
      host: true, // Listen on all addresses
      open: false, // Don't open browser automatically
    },
    // Preview configuration (for vite preview command)
    preview: {
      port: 4173,
      strictPort: true,
      host: true,
      open: false,
    },
  };
});
